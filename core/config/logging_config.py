import sys
import logging
from loguru import logger
from logtail import <PERSON><PERSON><PERSON>H<PERSON><PERSON>

from core.config.settings import settings
import sentry_sdk

# Sentry Initialization (using Pydantic settings)
if settings.SENTRY_DSN:  # Check if SENTRY_DSN is not empty or None
    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        environment=settings.ENVIRONMENT,
        # Consider adding traces_sample_rate and profiles_sample_rate if you use Sentry Performance
        # traces_sample_rate=1.0, 
        # profiles_sample_rate=1.0,
    )
    logger.info("Sentry initialized with DSN from Pydantic settings.")
else:
    logger.info("SENTRY_DSN not set or empty in Pydantic settings, Sentry not initialized.")


class InterceptHandler(logging.Handler):
    """
    Intercepts standard logging messages and redirects them to Loguru.
    """
    def emit(self, record: logging.LogRecord):
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        frame, depth = logging.currentframe(), 2
        while frame and frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1
        
        # Preserve extra fields from the original record
        log_extra = {key: val for key, val in record.__dict__.items()
                     if key not in logging.LogRecord('', 0, '', 0, '', None, None).__dict__ and key != "args"}

        logger.opt(depth=depth, exception=record.exc_info).bind(**log_extra).log(level, record.getMessage())


logger = logger.patch(lambda record: record["extra"].setdefault("request_id", "NO_REQ_ID"))


def formatter(record):
    # record["time"] is a datetime.datetime
    t_obj = record["time"]
    # Format to "YYYY-MM-DD HH:MM:SS.mmm"
    t = t_obj.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

    lvl = record["level"].name.ljust(8)
    req_id = record["extra"].get("request_id", "NO_REQ_ID")
    name = record["name"]
    fn = record["function"]
    # Escape angle brackets to prevent Loguru from interpreting them as color tags
    fn = fn.replace("<", "\\<").replace(">", "\\>")
    ln = record["line"]
    msg = record["message"]

    return (
        f"<green>{t}</green> | "
        f"<level>{lvl}</level> | "
        f"<cyan>{req_id}</cyan> | "
        f"<cyan>{name}:{fn}:{ln}</cyan> - "
        f"<level>{msg}</level> \n"
    )

def setup_logging():
    logger.remove()

    log_level = settings.LOG_LEVEL.upper()
    environment = settings.ENVIRONMENT.lower()

    # --- Console Sink --- 
    # For local development: human-readable, colored output.
    # For test: simplified output with higher log level to reduce noise.
    # For production: JSON output to stdout, to be captured by Gunicorn/container orchestrator.

 

    if environment in ["test", "dev"]:
        logger.add(
            sys.stdout,
            format=formatter,
            level=log_level,
            colorize=True,
            enqueue=True,
            backtrace=True,
            diagnose=True
        )
    else: 
        logger.add(
            sys.stdout,
            format="{message}",  # Loguru's JSON serializer handles the structure
            level=log_level,
            serialize=True,    # Output as JSON
            enqueue=True,
            # Backtrace and diagnose are included in the JSON if the error was logged
            # with .exception() or via @logger.catch
        )

    # --- Remote Sink (Logtail) ---
    logtail_source_token = settings.LOGTAIL_SOURCE_TOKEN 
    if environment != "dev" and logtail_source_token:
        try:
            logtail_sink_instance = LogtailHandler(source_token=logtail_source_token)
            logger.add(
                logtail_sink_instance,
                level="INFO",  # Send INFO and above to Logtail (or use log_level)
                format="{message}",  # Let Loguru serialize the whole record
                serialize=True,    # Crucial for structured logs to Logtail
                enqueue=True,
                backtrace=True,    # Ensure errors sent to Logtail have backtraces
                diagnose=True      # And diagnose info
            )
        except Exception as e:
            # Log this error using a temporary basic console logger in case Loguru isn't fully up
            print(f"ERROR: Failed to initialize or add Logtail sink: {e}", file=sys.stderr)
    elif environment != "dev" and not logtail_source_token:
        logger.warning("LOGTAIL_SOURCE_TOKEN not set. Logtail sink for non-dev environment is disabled.")

    # --- Standard Library Logging Interception ---
    # Redirect all standard logging to Loguru
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)

    # Optionally, set specific levels for noisy standard loggers
    # logging.getLogger("sqlalchemy.engine").setLevel(logging.INFO if environment != "dev" else logging.DEBUG)
    # logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    # If Uvicorn's own access logs are too noisy and not disabled

    final_log_message = f"Logging setup complete. Level: {log_level}, Environment: {environment}."
    if environment != "dev" and logtail_source_token:
        final_log_message += " Logtail sink ENABLED for production-like environment."
    elif environment != "dev" and not logtail_source_token:
        final_log_message += " Logtail sink DISABLED for production-like environment (Token not set)."
    else:
        final_log_message += " Console logging for development. Logtail sink not configured for dev."
    logger.info(final_log_message)