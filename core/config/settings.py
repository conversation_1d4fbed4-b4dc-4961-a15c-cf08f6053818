from pydantic_settings import BaseSettings
from pydantic import ConfigD<PERSON>
from typing import List, Tuple

class Settings(BaseSettings):
    

    # Database
    DATABASE_URL: str = "postgresql://user:password@localhost:5432/app_db"

    # App-Settings
    TRIAL_DURATION_DAYS: int = 7
    
   

    # Auth
    SECRET_KEY: str = "YOUR_DEFAULT_SECRET_KEY_HERE_PLEASE_CHANGE_ME"
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 30  # 30 days
    CHILD_SIGNUP_TOKEN_EXPIRE_MINUTES: int = 5  # Special short-lived token
    # PIN_ATTEMPT_LIMITS defines the account lockout policy for failed PIN attempts
    # Each tuple represents (threshold_attempts, lockout_minutes)
    # The system applies the lockout duration based on the highest threshold reached
    # For example, with 12 failed attempts, the account would be locked for 5 minutes
    # Format: [(attempts_threshold, lockout_minutes), ...]
    PIN_ATTEMPT_LIMITS: List[Tuple[int, int]] = [
        (6, 1),    # 6 attempts -> 1 minute lock
        (10, 5),   # 10 attempts -> 5 minutes
        (15, 15),  # 15 attempts -> 15 minutes
        (20, 60)   # 20+ attempts -> 1 hour lock
    ]

    LOGTAIL_SOURCE_TOKEN: str = ""

    PIN_ATTEMPT_LOCKOUT_MINUTES: int = 15
    # ObjC specific settings
    OBJC_DISABLE_INITIALIZE_FORK_SAFETY: bool = True
    
    # Logging specific settings
    LOG_LEVEL: str = "INFO"
    ENVIRONMENT: str = "dev" 

    # Platform/Deployment Configuration
    # CLIENT_IP_HEADERS defines the order of headers to check for the real client IP address.
    # Different deployment platforms use different headers:
    # - "do-connecting-ip": DigitalOcean App Platform
    # - "x-forwarded-for": Standard proxy header (most common)
    # - "x-real-ip": Nginx and some other proxies
    # - "cf-connecting-ip": Cloudflare
    # - "x-original-forwarded-for": Some CDNs
    # The middleware will check these headers in order and use the first valid one found.
    CLIENT_IP_HEADERS: List[str] = [
        "do-connecting-ip",      # DigitalOcean App Platform
        "cf-connecting-ip",      # Cloudflare (if used)
        "x-real-ip",            # Nginx and other proxies
        "x-forwarded-for",       # Standard proxy header
        "x-original-forwarded-for"  # Some CDNs
    ]
    
    # TRUSTED_PROXY_DEPTH: When using X-Forwarded-For, how many proxy hops to trust.
    # Set to 1 for most deployments. Increase if behind multiple trusted proxies.
    TRUSTED_PROXY_DEPTH: int = 1

    # Frontend / Landing Page
    FRONTEND_URL: str = "http://localhost:3000"
    LANDING_PAGE_URL: str = "http://localhost:3001"
    API_BASE_URL: str = "http://testserver" 
    
    # AWS
    AWS_ACCESS_KEY: str = ""
    AWS_SECRET_KEY: str = ""
    AWS_S3_REGION: str = "us-east-1"

    # SES Configurations
    SES_EMAIL_SOURCE: str = "<EMAIL>"
    
    # Contact Form Emails
    CONTACT_FORM_EMAILS: dict = {
        'dev': '<EMAIL>',
        'test': '<EMAIL>',
        'prod': '<EMAIL>'
    }
    
    # Email Recipients
    ADMIN_EMAIL: str = "<EMAIL>"
    SUPPORT_EMAIL_INTERNAL: str = "<EMAIL>"
    
    # AWS Region for SES
    AWS_SES_REGION: str = "eu-central-1"
    EMAIL_FROM: dict = {
        'dev': '<EMAIL>',
        'test': '<EMAIL>',
        'prod': '<EMAIL>'
    }
    
    # MailerLite Configuration
    MAILERLITE_API_KEY: str = ""
    MAILERLITE_ALL_USERS_GROUP_ID: str = ""
    MAILERLITE_CHILDREN_GROUP_ID: str = ""
    MAILERLITE_SUBSCRIBERS_GROUP_ID: str = ""
    
    # MailerLite Group IDs
    MAILERLITE_WELCOME_DE_GROUP_ID: str = ""
    MAILERLITE_WELCOME_EN_GROUP_ID: str = ""
    MAILERLITE_WELCOME_FR_GROUP_ID: str = ""
    MAILERLITE_WELCOME_LU_GROUP_ID: str = ""
    MAILERLITE_WELCOME_CHILD_GROUP_ID: str = ""
    
    MAILERLITE_DE_GROUP_ID: str = ""
    MAILERLITE_EN_GROUP_ID: str = ""
    MAILERLITE_LU_GROUP_ID: str = ""
    MAILERLITE_FR_GROUP_ID: str = ""
    
    MAILERLITE_MARKETING_DE_GROUP_ID: str = ""
    MAILERLITE_MARKETING_EN_GROUP_ID: str = ""
    MAILERLITE_MARKETING_LU_GROUP_ID: str = ""
    MAILERLITE_MARKETING_FR_GROUP_ID: str = ""
    
    MAILERLITE_POST_PURCHASE_DE_GROUP_ID: str = ""
    MAILERLITE_POST_PURCHASE_EN_GROUP_ID: str = ""
    MAILERLITE_POST_PURCHASE_LU_GROUP_ID: str = ""
    MAILERLITE_POST_PURCHASE_FR_GROUP_ID: str = ""
    
    # Stripe
    STRIPE_API_KEY: str = "YOUR_DEFAULT_STRIPE_TEST_KEY" 
    STRIPE_WEBHOOK_SECRET: str = "whsec_YOUR_DEFAULT_STRIPE_WEBHOOK_SECRET_HERE"

    LU_TAX_RATE: str = ""
    DE_TAX_RATE: str = ""
    FR_TAX_RATE: str = ""
    BE_TAX_RATE: str = ""
    DK_TAX_RATE: str = ""

    # Bunny
    BUNNY_EXERCISES_TOKEN_SECURITY_KEY: str = ""
    BUNNY_EXERCISES_VIDEO_LIBRARY_ID: str = ""
    BUNNY_LECTURES_TOKEN_SECURITY_KEY: str = ""
    BUNNY_LECTURES_VIDEO_LIBRARY_ID: str = ""

    # Cloudflare
    CLOUDFLARE_R2_ENDPOINT: str = ""
    CLOUDFLARE_R2_APP_BUCKET: str = ""
    CLOUDFLARE_R2_ACCESS_KEY_ID: str = ""
    CLOUDFLARE_R2_SECRET_ACCESS_KEY: str = ""
    CLOUDFLARE_R2_REGION_NAME: str = "auto"
    CLOUDFLARE_R2_IMAGE_BUCKET: str = ""
    CHAPTER_SUMMARIES_CLOUDFLARE_BUCKET: str = ""

    CLOUDFLARE_LANGUAGES_BUCKET: str = ""
    CLOUDFLARE_LANGUAGES_R2_ACCESS_KEY_ID: str = ""
    CLOUDFLARE_LANGUAGES_R2_SECRET_ACCESS_KEY: str = ""
    
    # Google
    GOOGLE_CAPTCHA_SITE_KEY: str = ""
    GOOGLE_CAPTCHA_SECRET_KEY: str = ""

    # MailerLite
    MAILERLITE_API_KEY: str = ""

    MAILERLITE_DE_GROUP_ID: str = ""
    MAILERLITE_EN_GROUP_ID: str = ""
    MAILERLITE_FR_GROUP_ID: str = ""
    MAILERLITE_LU_GROUP_ID: str = ""
    MAILERLITE_ALL_USERS_GROUP_ID: str = ""

    MAILERLITE_MARKETING_EN_GROUP_ID: str = ""
    MAILERLITE_MARKETING_DE_GROUP_ID: str = ""
    MAILERLITE_MARKETING_FR_GROUP_ID: str = ""
    
    MAILERLITE_WELCOME_EN_GROUP_ID: str = ""
    MAILERLITE_WELCOME_DE_GROUP_ID: str = ""
    MAILERLITE_WELCOME_FR_GROUP_ID: str = ""
    MAILERLITE_WELCOME_LU_GROUP_ID: str = ""
    
    MAILERLITE_CHILDREN_WELCOME_GROUP_ID: str = ""
    MAILERLITE_CHILDREN_GROUP_ID: str = ""

    # QStash Configuration
    QSTASH_TOKEN: str = ""
    QSTASH_URL: str = "https://qstash.upstash.io"  # Base QStash URL
    QSTASH_CURRENT_SIGNING_KEY: str = ""
    QSTASH_NEXT_SIGNING_KEY: str = ""
    QSTASH_PAUSE_ACTIVATION_URL: str = "http://localhost:8000/api/v1/tasks/internal/activate-pause"




    SUMMER_PAUSE_START_UTC: str = "2025-07-15T00:00:00Z"
    SUMMER_PAUSE_END_UTC: str = "2025-08-31T23:59:59Z"
    PAUSE_UI_VISIBLE_FROM: str = "2025-05-01T00:00:00Z"
    PAUSE_UI_VISIBLE_TO: str = "2025-07-01T00:00:00Z"
    # Other Tokens
    TASK_SCHEDULER_SECRET_TOKEN: str = "YOUR_DEFAULT_TASK_SCHEDULER_TOKEN_HERE"

    SENTRY_DSN: str = ""


    model_config = ConfigDict(
        env_file=".env" 
    )



settings = Settings()  