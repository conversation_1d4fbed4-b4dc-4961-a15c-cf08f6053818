from fastapi import Request, status
from fastapi.responses import JSONResponse
from loguru import logger
import json
import traceback
from typing import Dict, Any
from .exceptions.custom_exceptions import (
    ServiceError
)
from api.v1.common.schemas import SimpleErrorResponse, AppErrorCode

async def extract_request_info(request: Request) -> Dict[str, Any]:
    """Extract useful information from the request for logging purposes."""
    request_info = {
        "path": request.url.path,
        "method": request.method,
        "client_host": request.client.host if request.client else None,
        "query_params": dict(request.query_params),
        "headers": {k: v for k, v in request.headers.items() 
                    if k.lower() not in ('authorization', 'cookie', 'x-api-key')}  # Exclude sensitive headers
    }

    # Try to extract path params if available
    try:
        if hasattr(request, "path_params"):
            request_info["path_params"] = dict(request.path_params)
    except Exception:
        pass

    # Try to extract body for debugging, but be careful with size
    try:
        if request.method not in ("GET", "HEAD"):
            body = await request.body()
            if body and len(body) < 10000:  # Don't log huge bodies
                try:
                    # Try to parse as JSON
                    request_info["body"] = json.loads(body)
                except Exception:
                    # If not JSON, store as string with limited length
                    request_info["body"] = body.decode("utf-8", errors="replace")[:1000]
    except Exception:
        pass

    return request_info

async def service_exception_handler(request: Request, exc: ServiceError):
    # Get enhanced request information
    request_info = await extract_request_info(request)

    # Handle case where error_code might be an enum or string
    error_code_value = exc.error_code
    if hasattr(exc.error_code, 'value'):
        error_code_value = exc.error_code.value

    # Log with as much context as possible from the exception and request
    log_context = {
        "error_message": exc.log_message,  # Use internal log_message
        "user_facing_message": exc.message,
        "status_code": exc.status_code,
        "error_code": error_code_value,
        "exception_type": type(exc).__name__,
        "request": request_info
    }

    # Add any additional context from exception
    if hasattr(exc, "kwargs") and exc.kwargs:
        log_context.update(exc.kwargs)

    # Add error details if available
    if hasattr(exc, "details") and exc.details:
        if isinstance(exc.details, list):
            log_context["error_details"] = [
                {"field": getattr(d, "field", None), 
                 "code": getattr(d, "code", None), 
                 "message": getattr(d, "message", None)} 
                for d in exc.details
            ]
        else:
            log_context["error_details"] = exc.details

    # Add traceback for internal errors
    if hasattr(exc, "include_traceback") and exc.include_traceback:
        log_context["traceback"] = traceback.format_exc()

    # Determine log level from exception or default to ERROR
    log_level = getattr(exc, "log_level", "ERROR").upper()

    # Log with appropriate level, ensuring exc.log_message is safely formatted
    safe_log_message = str(exc.log_message).replace('%', '%%')

    if log_level == "WARNING":
        logger.warning(
            "ServiceError: {log_message_details}", 
            log_message_details=safe_log_message, 
            **log_context
        )
    elif log_level == "INFO":
        logger.info(
            "ServiceError (info level): {log_message_details}", 
            log_message_details=safe_log_message, 
            **log_context
        )
    elif log_level == "DEBUG":
        logger.debug(
            "ServiceError (debug level): {log_message_details}", 
            log_message_details=safe_log_message, 
            **log_context
        )
    elif log_level == "CRITICAL":
        logger.critical(
            "ServiceError (critical): {log_message_details}", 
            log_message_details=safe_log_message, 
            **log_context
        )
    else:  # Default to ERROR
        logger.error(
            "ServiceError: {log_message_details}", 
            log_message_details=safe_log_message, 
            **log_context
        )

    # Construct response
    try:
        response_content = SimpleErrorResponse(
            status="error",
            error_code=error_code_value,  # Use the processed value
            message=exc.message,  # User-facing message
            details=exc.details if hasattr(exc, "details") else None,
            request_id=request.state.request_id if hasattr(request.state, "request_id") else None
        ).model_dump(exclude_none=True)
    except Exception as e:
        # Fallback for malformed error data
        logger.error(f"Error serializing error response: {str(e)}")
        response_content = {
            "status": "error",
            "error_code": str(error_code_value),
            "message": str(exc.message)
        }

    return JSONResponse(
        status_code=exc.status_code,
        content=response_content
    )

async def generic_exception_handler(request: Request, exc: Exception):
    # Get enhanced request information
    try:
        request_info = await extract_request_info(request)
    except Exception as e:
        request_info = {"error_extracting_request_info": str(e)}

    # Generate a error reference ID (if request_id isn't available)
    error_reference = getattr(request.state, "request_id", None)

    # Log unhandled exceptions with full stack trace and request context
    logger.opt(exception=exc).critical( 
        # Pass the exception directly to logger for traceback
        f"Unhandled exception caught by generic_exception_handler: {str(exc)}",
        path=request.url.path,
        method=request.method,
        exception_type=type(exc).__name__,
        request=request_info,
        error_reference=error_reference
    )

    # Sanitize the response to avoid leaking implementation details
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=SimpleErrorResponse(
            status="error",
            error_code=AppErrorCode.UNKNOWN_ERROR,
            message="An unexpected internal server error occurred.",
            request_id=error_reference
        ).model_dump(exclude_none=True)
    )