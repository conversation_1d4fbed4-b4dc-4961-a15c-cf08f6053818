# exceptions/custom_exceptions.py
from typing import Union, List, Dict, Any, Optional
# Import AppErrorCode if you want to type hint `code` more strictly,
# otherwise using `str` is fine as AppErrorCode(str, Enum) members are strings.
from api.v1.common.schemas import AppErrorCode  # Assuming path is correct

class ServiceError(Exception):
    """Base class for all service-layer exceptions."""

    def __init__(
        self,
        message: str = "Service Error",
        log_message: Optional[str] = None,
        error_code: Union[AppErrorCode, str] = AppErrorCode.SERVICE_ERROR,
        status_code: int = 500
    ):
        super().__init__(message)
        self.message = message
        self.log_message = log_message if log_message else message
        # Ensure error_code is stored as the string value of the enum if an enum member is passed
        self.error_code = error_code.value if isinstance(error_code, AppErrorCode) else error_code
        self.status_code = status_code


class NotFoundError(ServiceError):
    """Entity not found."""
    def __init__(
        self,
        message: str = "Entity not found",
        log_message: Optional[str] = None,
        entity_name: Optional[str] = None,
        identifier: Any = None,
        error_code: Union[AppErrorCode, str] = AppErrorCode.USER_NOT_FOUND,
        status_code: int = 404
    ):
        super().__init__(message, log_message, error_code=error_code, status_code=status_code)
        self.entity_name = entity_name
        self.identifier = identifier


class ValidationError(ServiceError):
    """Business-rule validation failure."""
    def __init__(
        self,
        message: str = "Validation failed.",
        log_message: Optional[str] = None,
        details: Optional[List[Dict[str, Any]]] = None,
        error_code: Union[AppErrorCode, str] = AppErrorCode.INVALID_REQUEST,
        status_code: int = 400
    ):
        super().__init__(message, log_message, error_code=error_code, status_code=status_code)
        # Ensure details are compatible with ErrorDetail schema if passed directly
        # The ErrorDetail schema itself uses `code: str` for field-level codes.
        self.details = details


class PermissionDeniedError(ServiceError):
    """Operation not permitted for the current user."""
    def __init__(
        self,
        message: str = "Permission denied.",
        log_message: Optional[str] = None,
        error_code: Union[AppErrorCode, str] = AppErrorCode.PERMISSION_DENIED,
        status_code: int = 403
    ):
        super().__init__(message, log_message, error_code=error_code, status_code=status_code)


class ConflictError(ServiceError):
    """Resource conflict, e.g., duplicate entry."""
    def __init__(
        self,
        message: str = "A conflict occurred with an existing resource.",
        log_message: Optional[str] = None,
        error_code: Union[AppErrorCode, str] = AppErrorCode.ACCOUNT_ALREADY_EXISTS,
        status_code: int = 409
    ):
        super().__init__(message, log_message, error_code=error_code, status_code=status_code)


class ExternalServiceError(ServiceError):
    """An error occurred while interacting with an external service or database."""
    def __init__(
        self,
        message: str = "An external service error occurred.",
        log_message: Optional[str] = None,
        original_exception: Optional[Exception] = None,
        error_code: Union[AppErrorCode, str] = AppErrorCode.EXTERNAL_SERVICE_FAILURE,
        status_code: int = 503
    ):
        super().__init__(message, log_message, error_code=error_code, status_code=status_code)
        self.original_exception = original_exception


class AuthenticationError(ServiceError):
    """Authentication failed."""
    def __init__(
        self,
        message: str = "Authentication failed. Invalid credentials.",
        log_message: Optional[str] = None,
        error_code: Union[AppErrorCode, str] = AppErrorCode.INVALID_CREDENTIALS,
        status_code: int = 401
    ):
        super().__init__(message, log_message, error_code=error_code, status_code=status_code)


class AuthorizationError(ServiceError):
    """Authorization failed. Invalid or expired token, or insufficient permissions."""
    def __init__(
        self,
        message: str = "Authorization failed.",
        log_message: Optional[str] = None,
        error_code: Union[AppErrorCode, str] = AppErrorCode.INVALID_TOKEN,
        status_code: int = 401
    ):
        super().__init__(message, log_message, error_code=error_code, status_code=status_code)


class BadRequestError(ServiceError):
    """Request is malformed or invalid."""
    def __init__(
        self,
        message: str = "Bad request.",
        log_message: Optional[str] = None,
        error_code: Union[AppErrorCode, str] = AppErrorCode.BAD_REQUEST,
        status_code: int = 400
    ):
        super().__init__(message, log_message, error_code=error_code, status_code=status_code)


class TooManyRequestsError(ServiceError):
    """Too many requests. """
    def __init__(
        self,
        message: str = "Too many requests.",
        log_message: Optional[str] = None,
        error_code: Union[AppErrorCode, str] = AppErrorCode.BAD_REQUEST,
        status_code: int = 429
    ):
        super().__init__(message, log_message, error_code=error_code, status_code=status_code)
    