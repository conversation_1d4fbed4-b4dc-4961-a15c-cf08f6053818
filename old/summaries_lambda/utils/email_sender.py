import os
import boto3
from botocore.exceptions import ClientError

ses_client = boto3.client('ses')
SES_SOURCE_EMAIL = os.environ['SES_SOURCE_EMAIL']
def send_email(recipient_email, presigned_url, language):
    # Define email content based on language
    if language == 'de':  # German
        subject = "🎒 Ihr LuxEdu Download-Link"
        heading = "Ihr Download ist bereit!"
        greeting = "Hallo,"
        instruction = "Sie können Ihre Datei mit dem folgenden Link herunterladen:"
        button_text = "Datei herunterladen"
        footer = "Mit freundlichen Grüßen,\nIhr LuxEdu Team"
    elif language == 'fr':  # French
        subject = "🎒 Votre lien de téléchargement LuxEdu"
        heading = "Votre téléchargement est prêt !"
        greeting = "Bonjour,"
        instruction = "Vous pouvez télécharger votre fichier en utilisant le lien suivant :"
        button_text = "Télécharger le fichier"
        footer = "Cordialement,\nVotre équipe LuxEdu"
    elif language == 'lu':  # Luxembourgish
        subject = "🎒 Äre LuxEdu Download-Link"
        heading = "Ären Download ass prett!"
        greeting = "<PERSON><PERSON>,"
        instruction = "Dir kënnt Är Datei mat folgendem Link eroflueden:"
        button_text = "Datei eroflueden"
        footer = "Mat beschte Gréiss,\nÄr LuxEdu Equipe"
    else:  # Default to English
        subject = "🎒 Your LuxEdu Download Link"
        heading = "Your download is ready!"
        greeting = "Hello,"
        instruction = "You can download your file using the following link:"
        button_text = "Download File"
        footer = "Best regards,\nYour LuxEdu Team"

    plain_text_body = f"{greeting}\n\n{instruction}\n{presigned_url}\n\n{footer}"

    html_content = f"""
    <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background-color: #f0f0f0; padding: 20px; border-radius: 10px; text-align: center;">
                <h1 style="color: #4a4a4a; margin-bottom: 20px;">{heading}</h1>
                <p style="font-size: 16px; margin-bottom: 20px;">{greeting}</p>
                <p style="font-size: 16px; margin-bottom: 30px;">{instruction}</p>
                <a href="{presigned_url}" style="background-color: #4CAF50; color: white; padding: 14px 20px; text-align: center; text-decoration: none; display: inline-block; font-size: 16px; margin: 4px 2px; cursor: pointer; border-radius: 5px;">{button_text}</a>
            </div>
            <div style="margin-top: 30px; text-align: center; font-size: 14px; color: #777777;">
                {footer}
            </div>
        </body>
    </html>
    """

    try:
        response = ses_client.send_email(
            Source=SES_SOURCE_EMAIL,
            Destination={
                'ToAddresses': [recipient_email]
            },
            Message={
                'Subject': {
                    'Data': subject,
                    'Charset': 'UTF-8'
                },
                'Body': {
                    'Html': {
                        'Data': html_content,
                        'Charset': 'UTF-8'
                    },
                    'Text': {
                        'Data': plain_text_body,
                        'Charset': 'UTF-8'
                    }
                }
            }
        )
        return response
    except ClientError as e:
        print(f"Error sending email: {str(e)}")
        raise