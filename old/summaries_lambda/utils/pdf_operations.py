import fitz

def add_watermark(doc, email):
    for page in doc:
        watermark_page(page, email)
    return doc

def calculate_font_size(page, text, max_font_size=12, min_font_size=6, margin=10):
    rect = page.rect
    available_height = rect.height - 2 * margin

    for font_size in range(max_font_size, min_font_size - 1, -1):
        text_width = fitz.get_text_length(text, fontname="helv", fontsize=font_size)
        if text_width <= available_height:
            return font_size

    return min_font_size

def add_vertical_watermark(page, text, border='left', opacity=0.5, color=(0.78, 0.78, 0.78), margin=20, font_size=12):
    rect = page.rect
    text_width = fitz.get_text_length(text, fontname="helv", fontsize=font_size)
    y_center = rect.height / 2

    if border == 'left':
        x = margin
        y_start = y_center + (text_width / 2)
        rotation = 90
    else:  # right border
        x = rect.width - margin
        y_start = y_center - (text_width / 2)
        rotation = -90

    rgba_color = (*color, opacity)

    page.insert_text(
        (x, y_start),
        text,
        fontsize=font_size,
        fontname="helv",
        rotate=rotation,
        color=rgba_color
    )

def watermark_page(page, email):
    try:
        rotation = page.rotation
        if rotation != 0:
            page.set_rotation(0)

        font_size = calculate_font_size(page, email)

        add_vertical_watermark(page, email, border='left', font_size=font_size)
        add_vertical_watermark(page, email, border='right', font_size=font_size)

        if rotation != 0:
            page.set_rotation(rotation)

        return page
    except Exception as e:
        print(f"An error occurred while watermarking: {str(e)}")
        raise