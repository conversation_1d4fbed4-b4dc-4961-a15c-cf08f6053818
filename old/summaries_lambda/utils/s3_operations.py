import os
import boto3
from botocore.exceptions import ClientError

s3_client = boto3.client('s3')

original_pdf_bucket_name = os.environ['ORIGINAL_PDF_BUCKET_NAME']
watermarked_pdf_bucket_name = os.environ['WATERMARKED_PDF_BUCKET_NAME']

def download_pdf_from_s3(file_name):
    try:
        response = s3_client.get_object(Bucket=original_pdf_bucket_name, Key=file_name)
        return response['Body'].read()
    except ClientError as e:
        raise Exception(f"Error downloading file from S3: {str(e)}")

def upload_pdf_to_s3(file_name, pdf_content):
    try:
        s3_client.put_object(Bucket=watermarked_pdf_bucket_name, Key=file_name, Body=pdf_content)
    except ClientError as e:
        raise Exception(f"Error uploading watermarked file to S3: {str(e)}")

def generate_presigned_url(file_name):
    try:
        return s3_client.generate_presigned_url('get_object',
                                                Params={'Bucket': watermarked_pdf_bucket_name,
                                                        'Key': file_name},
                                                ExpiresIn=259200)  # URL expires in 3 days
    except ClientError as e:
        raise Exception(f"Error generating pre-signed URL: {str(e)}")