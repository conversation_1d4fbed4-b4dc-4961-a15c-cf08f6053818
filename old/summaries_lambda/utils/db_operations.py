import os
import psycopg2
db_params = {
    'host': os.environ['DB_HOST'],
    'database': os.environ['DB_NAME'],
    'user': os.environ['DB_USER'],
    'password': os.environ['DB_PASSWORD']
}

def connect_to_db():
    try:
        return psycopg2.connect(**db_params)
    except psycopg2.Error as e:
        raise Exception(f"Database connection error: {str(e)}")

def check_user_exists(cursor, email):
    cursor.execute("SELECT account_id FROM account WHERE account_email = %s", (email,))
    user = cursor.fetchone()
    if not user:
        cursor.execute("SELECT child_account_id FROM child_account WHERE child_account_email = %s", (email,))
        user = cursor.fetchone()
        if not user:
            return None, None
        else:
            return None, user[0]
    else:
        return user[0], None

def check_already_downloaded(cursor, account_id, child_account_id):
    if account_id:
        cursor.execute("SELECT summary_download_id FROM summary_download WHERE account_id = %s", (account_id,))
    else:
        cursor.execute("SELECT summary_download_id FROM summary_download WHERE child_account_id = %s", (child_account_id,))
    return cursor.fetchone() is not None

def record_download(cursor, account_id, child_account_id, file_name):
    if account_id:
        cursor.execute("INSERT INTO summary_download (account_id, downloaded_file) VALUES (%s, %s)",
                       (account_id, file_name))
    else:
        cursor.execute("INSERT INTO summary_download (child_account_id, downloaded_file) VALUES (%s, %s)",
                       (child_account_id, file_name))