from io import BytesIO
import fitz
from utils.s3_operations import download_pdf_from_s3, upload_pdf_to_s3, generate_presigned_url
from utils.email_sender import send_email
from utils.pdf_operations import add_watermark
from utils.db_operations import connect_to_db, check_user_exists, check_already_downloaded, record_download

def lambda_handler(event, context):
    email = event.get('email')
    content_id = event.get('content_id')
    language = event.get('language', 'en')

    try:
        conn = connect_to_db()
        cursor = conn.cursor()

        account_id, child_account_id = check_user_exists(cursor, email)
        if account_id is None and child_account_id is None:
            return {'statusCode': 404, 'body': {'error': 'not_registered'}}

        if check_already_downloaded(cursor, account_id, child_account_id):
            return {'statusCode': 400, 'body': {'error': 'already_downloaded'}}

        file_mapping = {
            "8922b9a5-6785-49c2-9478-4eea45f3579e": "year_07/chapter_01",
            "f74152fd-827b-4e54-baa3-27b5ed74428e": "year_07/chapter_02",
            "8b9cd159-1d56-46ca-ae09-626e90451590": "year_07/chapter_03",
            "292e0ca3-32b4-4e2b-8602-a0025c6300cb": "year_07/chapter_04",
            "0e832457-6af1-4363-9ade-c0dd0916cbc5": "year_07/chapter_05",
            "cb8112db-36bc-4638-8beb-244b819735ad": "year_07/chapter_06",
            "31341e12-6c0d-4cdd-9f2f-b48e5895295d": "year_07/chapter_07",
            "6c3d339d-6463-4275-a957-ea106a85f785": "year_06/chapter_01",
            "882fe54c-6f06-4873-8442-52c6d3138c6b": "year_06/chapter_02",
            "257bde9f-8610-4cd0-baca-b303a723c330": "year_06/chapter_03",
            "462e2fce-f987-4e49-b2e5-be3c7edb35e6": "year_06/chapter_04",
            "********-9f88-4adc-b37e-7a2b873328d7": "year_06/chapter_05",
            "a414eb4f-e8e4-4f26-846b-2b2a9c2bf953": "year_06/chapter_06",
            "78a70f54-cf84-4f0d-963a-aeeef099c534": "year_06/chapter_07",
            "00ccd713-9ef5-4c4c-a581-9e719abd4fa3": "year_06/chapter_08",
            "86c06ec7-3360-46a4-813d-3e52aa2ed356": "year_05/chapter_01",
            "36fcf80c-2e0b-43ed-b513-b3787b628d9b": "year_05/chapter_02",
            "80766d2e-117f-4061-84ad-632775a498a7": "year_05/chapter_03",
            "3a0979f6-da7b-488c-83b3-de3100b78216": "year_05/chapter_04",
            "df1da426-b6c8-405f-8a52-20b1dadec8b3": "year_05/chapter_05",
            "d2ef68a6-98aa-4027-b797-ba8131331a94": "year_05/chapter_06",
            "d7bc1db1-4d37-4c4a-bc75-93ad0ae8a8d3": "year_05/chapter_07",
            "cad11be1-7cfd-4b54-be30-c155d9438496": "year_05/chapter_08",
            "95c8ed39-dc42-4cdb-afb7-9dce9d5b093c": "year_05/chapter_09",
            "e1f3fb84-933a-4599-bc6a-03cb791bcb97": "year_05/chapter_010",
        }

        file_name = file_mapping.get(content_id, "UUID not found")
        if not file_name:
            return {'statusCode': 404, 'body': {'error': 'file_not_found'}}

        pdf_content = download_pdf_from_s3(file_name)
        doc = fitz.open(stream=pdf_content, filetype="pdf")

        watermarked_pdf = add_watermark(doc, email)

        output_stream = BytesIO()
        watermarked_pdf.save(output_stream)
        output_stream.seek(0)

        watermarked_file_name = f"watermarked_{email}_{file_name}"
        upload_pdf_to_s3(watermarked_file_name, output_stream)

        presigned_url = generate_presigned_url(watermarked_file_name)

        if account_id or child_account_id:
            send_email(email, presigned_url, language)

        record_download(cursor, account_id, child_account_id, file_name)
        conn.commit()

        return {'statusCode': 200, 'body': {'url': presigned_url}}

    except Exception as e:
        if 'conn' in locals():
            conn.rollback()
        return {'statusCode': 500, 'body': str(e)}

    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
