# Use the official AWS Lambda Python runtime as the base image
FROM public.ecr.aws/lambda/python:3.12

# Set the working directory in the container
WORKDIR ${LAMBDA_TASK_ROOT}

# Copy the requirements file
COPY requirements.txt .

# Install the Python dependencies
RUN pip install -r requirements.txt

# Copy the Lambda function code
COPY . .

# Set the CMD to your handler
C<PERSON> ["aws_lambda.lambda_handler"]