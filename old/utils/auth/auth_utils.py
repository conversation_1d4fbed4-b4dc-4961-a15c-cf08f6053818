from fastapi import Request
from utils.exceptions import CustomException

def has_valid_child_token(request: Request):
    if not request.state.decoded_child_token:
        raise CustomException ("Not authorized!", "No child_token provided", status_code=401, error_type="permission_error")

def has_valid_parent_token(request: Request) -> bool:
    if not request.state.parent_uuid:
        raise CustomException ("Not authorized!", "No parent_token provided", status_code=401, error_type="permission_error")

def has_valid_token(request: Request) -> bool:
    if not request.state.parent_uuid:
        has_valid_child_token(request)
    else:
        has_valid_parent_token(request)
    

import json
import time
from jose import jwk, jwt
from jose.utils import base64url_decode
import urllib.request
import os
from fastapi import HTTPException
from dotenv import load_dotenv
from loguru import logger
load_dotenv()

region = os.getenv('AWS_COGNITO_REGION')
userpool_id = os.getenv('AWS_COGNITO_USER_POOL_ID')
app_client_id = os.getenv('AWS_COGNITO_USER_POOL_WEB_CLIENT_ID')
keys_url = 'https://cognito-idp.{}.amazonaws.com/{}/.well-known/jwks.json'.format(
    region, userpool_id)

def verify_cognito_jwt(token: str) -> bool:
    try:
        keys = ''
        with urllib.request.urlopen(keys_url) as url:
            response = url.read()
            keys = json.loads(response.decode('utf-8'))['keys']
        # get the kid from the headers prior to verification
        headers = jwt.get_unverified_headers(token)
        kid = headers['kid']
        # search for the kid in the downloaded public keys
        key_index = -1
        for i in range(len(keys)):
            if kid == keys[i]['kid']:
                key_index = i
                break
        if key_index == -1:
            # print('Public key not found in jwks.json')
            return False
        # construct the public key
        public_key = jwk.construct(keys[key_index])
        # get the last two sections of the token,
        # message and signature (encoded in base64)
        message, encoded_signature = str(token).rsplit('.', 1)
        # decode the signature
        decoded_signature = base64url_decode(encoded_signature.encode('utf-8'))
        # verify the signature
        if not public_key.verify(message.encode("utf8"), decoded_signature):
            # print('Signature verification failed')
            return False
        # print('Signature successfully verified')
        # since we passed the verification, we can now safely
        # use the unverified claims
        claims = jwt.get_unverified_claims(token)
        # print('Printing claims ', claims)
        # additionally we can verify the token expiration
        if time.time() > claims['exp']:
            # print('Token is expired')
            return False
        # and the Audience  (use claims['client_id'] if verifying an access token)
        if claims['client_id'] != app_client_id and claims['client_id'] != app_client_id:
            # print('Token was not issued for this audience')
            return False
        # now we can use the claims
        # print(claims)
        # return claims['cognito:groups']
        return True
    except Exception as e:
        # print(e)
        # print('Error was thrown')
        return False
    