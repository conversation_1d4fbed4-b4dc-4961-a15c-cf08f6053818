from ..models.response import GetChapterSummaryResponse
from fastapi import APIRouter, Depends
from db.database import get_db
from sqlalchemy.orm import Session
from utils.exceptions import CustomException
from db.models import Account, SummaryDownload, ChildAccount
from sqlalchemy import or_
import boto3
from botocore.exceptions import ClientError
import os
from .utils.email_sender import send_email

router = APIRouter()
base_url = "/api/summaries"

CLOUDFLARE_R2_ENDPOINT = os.environ.get("CLOUDFLARE_R2_ENDPOINT")
CLOUDFLARE_R2_ACCESS_KEY_ID = os.environ.get("CLOUDFLARE_R2_ACCESS_KEY_ID")
CLOUDFLARE_R2_SECRET_ACCESS_KEY = os.environ.get(
    "CLOUDFLARE_R2_SECRET_ACCESS_KEY")
CLOUDFLARE_R2_REGION_NAME = os.environ.get("CLOUDFLARE_R2_REGION_NAME")

CLOUDFLARE_R2_APP_BUCKET = os.environ.get("CLOUDFLARE_R2_APP_BUCKET")

BUCKET_BASE_PATH = "summary_materials/chapters/"


chapter_id_mapping = {
    "year_05_chapter_01": "01 LuxEdu Fiche de Révision - 5e Calcul littéral",
    "year_05_chapter_02": "02 LuxEdu Fiche de Révision - 5e Équations",
    "year_05_chapter_03": "03 LuxEdu Fiche de Révision - 5e Puissances",
    "year_05_chapter_04": "04 LuxEdu Fiche de Révision - 5e Raisonnement déductif",
    "year_05_chapter_05": "05 LuxEdu Fiche de Révision - 5e Racines carées et cubiques",
    "year_05_chapter_06": "06 LuxEdu Fiche de Révision - 5e Théorème de Pythagore",
    "year_05_chapter_07": "07 LuxEdu Fiche de Révision - 5e Factorisation",
    "year_05_chapter_08": "08 LuxEdu Fiche de Révision - 5e Traitement de données",
    "year_05_chapter_09": "09 LuxEdu Fiche de Révision - 5e Ensembles et probabilités",
    "year_05_chapter_10": "10 LuxEdu Fiche de Révision - 5e Transformations du plan",
    "year_06_chapter_01": "01 LuxEdu Fiche de Révision - 6e Calcul littéral",
    "year_06_chapter_02": "02 LuxEdu Fiche de Révision - 6e Équations",
    "year_06_chapter_03": "03 LuxEdu Fiche de Révision - 6e Puissances",
    "year_06_chapter_04": "04 LuxEdu Fiche de Révision - 6e Division euclidienne",
    "year_06_chapter_05": "05 LuxEdu Fiche de Révision - 6e Diviseurs et multiples",
    "year_06_chapter_06": "06 LuxEdu Fiche de Révision - 6e Fractions",
    "year_06_chapter_07": "07 LuxEdu Fiche de Révision - 6e Équations du 1er Degrée",
    "year_06_chapter_08": "08 LuxEdu Fiche de Révision - 6e Solides et volumes",
    "year_06_chapter_09": "09 LuxEdu Fiche de Révision - 6e Grandeurs proportionnelles",
    "year_06_chapter_10": "10 LuxEdu Fiche de Révision - 6e Géométrie et Distances",
    "year_06_chapter_11": "11 LuxEdu Fiche de Révision - 6e Distances et triangles",
    "year_06_chapter_12": "12 LuxEdu Fiche de Révision - 6e Angles",
    "year_07_chapter_01": "01 LuxEdu Fiche de Révision - 7e Ensembles Numériques",
    "year_07_chapter_02": "02 LuxEdu Fiche de Révision - 7e Géométrie plane",
    "year_07_chapter_03": "03 LuxEdu Fiche de Révision - 7e Opérations dans ℕ",
    "year_07_chapter_04": "04 LuxEdu Fiche de Révision - 7e Nombres Entiers et Opérations dans ℤ",
    "year_07_chapter_05": "05 LuxEdu Fiche de Révision - 7e Figures planes",
    "year_07_chapter_06": "06 LuxEdu Fiche de Révision - 7e Multiples et diviseurs",
    "year_07_chapter_07": "07 LuxEdu Fiche de Révision - 7e Fractions"
}


def create_presigned_url(year: str, chapter: str, expiry=86400):
    bucket_name = CLOUDFLARE_R2_APP_BUCKET

    client = boto3.client(
        "s3",
        region_name="auto",
        endpoint_url=CLOUDFLARE_R2_ENDPOINT,
        aws_access_key_id=CLOUDFLARE_R2_ACCESS_KEY_ID,
        aws_secret_access_key=CLOUDFLARE_R2_SECRET_ACCESS_KEY,
    )

    chapter_name = chapter_id_mapping.get(year + "_" + chapter)

    try:
        response = client.generate_presigned_url('get_object',  Params={
                                                 'Bucket': bucket_name, 'Key': BUCKET_BASE_PATH + year + "/" + chapter_name + ".pdf"}, ExpiresIn=expiry)
        return response

    except ClientError as e:
        print(e)

    except Exception as e:
        print(e)
        return ""


@router.get(base_url + "/get-chapter-summary", response_model=GetChapterSummaryResponse)
def get_chapter_summary(chapter_summary_id: str, email: str, language: str, db: Session = Depends(get_db)):
    # Check if the account exists
    account = db.query(Account).filter(Account.account_email == email).first()
    child_account = db.query(ChildAccount).filter(
        ChildAccount.child_account_email == email).first()

    if not account and not child_account:
        raise CustomException(
            "Account not found", "Account not found", status_code=404, error_type="account_not_found")

    # Check if the user has already downloaded this chapter
    # check account_id or child_account_id
    if child_account and account:
        existing_download = db.query(SummaryDownload).filter(
            SummaryDownload.child_account_id == child_account.child_account_id,
            SummaryDownload.account_id == account.account_id
        ).first()
    elif child_account:
        existing_download = db.query(SummaryDownload).filter(
            SummaryDownload.child_account_id == child_account.child_account_id
        ).first()
    elif account:
        existing_download = db.query(SummaryDownload).filter(
            SummaryDownload.account_id == account.account_id
        ).first()
    else:
        raise CustomException(
            "Account not found", "Account not found", status_code=404, error_type="account_not_found")

    split_chapter_summary_id = chapter_summary_id.split("_")
    year = split_chapter_summary_id[0] + "_" + split_chapter_summary_id[1]
    chapter = split_chapter_summary_id[2] + "_" + split_chapter_summary_id[3]

    if existing_download:
        if child_account and account:
            all_downloaded_files = db.query(SummaryDownload).filter(
                or_(SummaryDownload.account_id == account.account_id,
                    SummaryDownload.child_account_id == child_account.child_account_id)
            ).all()
        elif child_account:
            all_downloaded_files = db.query(SummaryDownload).filter(
                SummaryDownload.child_account_id == child_account.child_account_id
            ).all()
        elif account:
            all_downloaded_files = db.query(SummaryDownload).filter(
                SummaryDownload.account_id == account.account_id
            ).all()

        can_download = False
        for downloaded_file in all_downloaded_files:
            if downloaded_file.downloaded_file == chapter_summary_id:
                can_download = True

        if not can_download:
            raise CustomException("Chapter summary already downloaded",
                                  "Chapter summary already downloaded", status_code=400, error_type="already_downloaded")

    if not existing_download:
        # Record the download in the database
        new_download = SummaryDownload(
            account_id=account.account_id if account else None,
            child_account_id=child_account.child_account_id if child_account else None,
            downloaded_file=chapter_summary_id,
            email=email
        )
        db.add(new_download)
        db.commit()

    presigned_url = create_presigned_url(year=year, chapter=chapter)

    try:
        send_email(email, presigned_url, language)
    except Exception as e:
        print(e)

    return {
        "message": "Chapter summary generated successfully",
        "download_url": presigned_url
    }
