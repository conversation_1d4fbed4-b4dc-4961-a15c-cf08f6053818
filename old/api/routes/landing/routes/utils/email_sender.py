import os
import boto3
from botocore.exceptions import ClientError

# Initialize AWS SES client
ses_client = boto3.client(
    'ses',
    region_name='eu-central-1',  # replace with your AWS region
    aws_access_key_id=os.environ['AWS_ACCESS_KEY'],
    aws_secret_access_key=os.environ['AWS_SECRET_KEY']
)

SES_EMAIL_SOURCE = os.environ['SES_EMAIL_SOURCE']

def send_email(recipient_email, presigned_url, language):
    # Define email content based on language
    if language == 'de':  # German
        subject = "🎒 Ihr LuxEdu Download-Link"
        heading = "Ihr Download ist bereit!"
        greeting = "Hallo,"
        instruction = "Sie können Ihre Datei mit dem folgenden Link herunterladen (der Link ist 24 Stunden gültig):"
        button_text = "Datei herunterladen"
        footer = "Mit freundlichen Grüßen,\nIhr LuxEdu Team"
    elif language == 'fr':  # French
        subject = "🎒 Votre lien de téléchargement LuxEdu"
        heading = "Votre téléchargement est prêt !"
        greeting = "Bonjour,"
        instruction = "Vous pouvez télécharger votre fichier en utilisant le lien suivant (le lien est valable pour 24 heures) :"
        button_text = "Télécharger le fichier"
        footer = "Cordialement,\nVotre équipe LuxEdu"
    elif language == 'lu':  # Luxembourgish
        subject = "🎒 Ären LuxEdu Download-Link"
        heading = "Ären Download ass prett!"
        greeting = "Moien,"
        instruction = "Dir kënnt Är Datei mat follgendem Link eroflueden (de Link ass 24 Stonnen gëlteg):"
        button_text = "Datei eroflueden"
        footer = "Mat beschte Gréiss,\nÄr LuxEdu Equipe"
    else:  # Default to English
        subject = "🎒 Your LuxEdu Download Link"
        heading = "Your download is ready!"
        greeting = "Hello,"
        instruction = "You can download your file using the following link (the link is valid for 24 hours):"
        button_text = "Download File"
        footer = "Best regards,\nYour LuxEdu Team"

    plain_text_body = f"{greeting}\n\n{instruction}\n{presigned_url}\n\n{footer}"

    html_content = f"""
    <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background-color: #f0f0f0; padding: 20px; border-radius: 10px; text-align: center;">
                <h1 style="color: #4a4a4a; margin-bottom: 20px;">{heading}</h1>
                <p style="font-size: 16px; margin-bottom: 20px;">{greeting}</p>
                <p style="font-size: 16px; margin-bottom: 30px;">{instruction}</p>
                <a href="{presigned_url}" style="background-color: #4CAF50; color: white; padding: 14px 20px; text-align: center; text-decoration: none; display: inline-block; font-size: 16px; margin: 4px 2px; cursor: pointer; border-radius: 5px;">{button_text}</a>
            </div>
            <div style="margin-top: 30px; text-align: center; font-size: 14px; color: #777777;">
                {footer}
            </div>
        </body>
    </html>
    """

    try:
        response = ses_client.send_email(
            Source=SES_EMAIL_SOURCE,
            Destination={
                'ToAddresses': [recipient_email]
            },
            Message={
                'Subject': {
                    'Data': subject,
                    'Charset': 'UTF-8'
                },
                'Body': {
                    'Html': {
                        'Data': html_content,
                        'Charset': 'UTF-8'
                    },
                    'Text': {
                        'Data': plain_text_body,
                        'Charset': 'UTF-8'
                    }
                }
            }
        )
        return response
    except ClientError as e:
        print(f"Error sending email: {str(e)}")
        raise