from ..models.response import GetPostsResponse
from fastapi import APIRouter, Depends, HTTPException
from db.database import get_db
from sqlalchemy.orm import Session
import json
from sqlalchemy import asc

from db.models import BlogPost

router = APIRouter()
base_url = "/api/blog"

PAGINATION_SIZE = 10


IMAGE_BASE_URL = "https://pub-e2b70868e474414392f797cc1f284b26.r2.dev/blog%2Fimages%2F"


@router.get(f"{base_url}/get-posts", response_model=GetPostsResponse)
def get_posts(language: str = 'en', db: Session = Depends(get_db), page: int = 0):
    items_per_page = PAGINATION_SIZE
    # Page count starts at 0
    offset = page * items_per_page

    relevant_posts = db.query(BlogPost).filter(
        BlogPost.blog_post_language == language).order_by(asc(BlogPost.blog_post_ordering)).offset(offset).limit(items_per_page + 1).all()

    has_more = len(relevant_posts) > items_per_page
    # If we have more posts, remove the extra one
    if has_more:
        relevant_posts = relevant_posts[:-1]

    posts = []
    for post in relevant_posts:
        posts.append({
            "image_url": f"{IMAGE_BASE_URL + post.blog_post_image_name}" if post.blog_post_image_name else None,
            "public_id": post.blog_post_public_id,
            "title": post.blog_post_title,
            "summary": post.blog_post_summary,
            "language": post.blog_post_language,
            "slug": post.blog_post_slug,
            "reading_time": post.blog_post_reading_time,
            "created_at": post.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            "last_updated": post.last_updated.strftime('%Y-%m-%d %H:%M:%S')
        })

    return {"posts": posts, "has_more": has_more}

