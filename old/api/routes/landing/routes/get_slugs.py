from ..models.response import GetSlugsResponse
from fastapi import APIRouter, Depends, HTTPException
from db.database import get_db
from sqlalchemy.orm import Session

from db.models import BlogPost

router = APIRouter()
base_url = "/api/blog"


@router.get(f"{base_url}/get-slugs", response_model=GetSlugsResponse)
def get_slugs(language:str, db: Session = Depends(get_db),):
    try:

        relevant_posts = db.query(BlogPost).filter_by(blog_post_language=language).all()

        slugs = []
        for post in relevant_posts:
            slugs.append(post.blog_post_slug)

        return {"slugs": slugs}

    except Exception as e:
        raise HTTPException(status_code=404, detail="Post not found")
