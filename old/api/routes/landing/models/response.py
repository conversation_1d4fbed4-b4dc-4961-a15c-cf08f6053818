from pydantic import BaseModel
from typing import List, Optional


class BlogPost(BaseModel):
    public_id: str
    title: str
    content: Optional[str]
    summary: Optional[str]
    language: str
    slug: str
    created_at: Optional[str]
    last_updated: Optional[str]
    image_url: Optional[str]
    reading_time: Optional[str]


class GetPostsResponse(BaseModel):
    posts: List[BlogPost]
    has_more: bool

class GetPostResponse(BlogPost):
    pass


class GetSlugsResponse(BaseModel):
    slugs: List[str]


class GetChapterSummaryResponse(BaseModel):
    message: str
    download_url: str

class SignupData(BaseModel):
    year: str
    system: str
    created_at: str

class GetRecentSignupsResponse(BaseModel):
    signups: List[SignupData]
