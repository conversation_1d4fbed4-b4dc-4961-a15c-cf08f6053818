import os
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Request, Depends, Header

from ..models.request import CookieConsentRequest
from utils.auth import has_valid_parent_token

from utils.exceptions import CustomException
import boto3
from loguru import logger
from db.database import get_db
from sqlalchemy.orm import Session
import json

from db.models import CookiesConsent, Account

router = APIRouter()
base_url = "/api/cookies"


@router.post(base_url + '/register-cookies-consent', status_code=200)
def submit_contact_form(request: Request, cookie_consent: CookieConsentRequest, ip_address: str = Header(None, alias='x-forwarded-for'),x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token), db: Session = Depends(get_db)):
    parent_public_id = request.state.parent_uuid

    try:
        if parent_public_id:
            relevant_account = db.query(Account).filter(
                Account.account_cognito_id == parent_public_id).first()

            new_consent = CookiesConsent(
                 cookies_consent_relevant_account_id=relevant_account.account_id, cookies_consent_account_email=relevant_account.account_email,
                 cookies_consent_choices=json.dumps(cookie_consent.cookie_consent), cookies_consent_user_agent=cookie_consent.user_agent, cookies_consent_ip_address=ip_address)
            db.add(new_consent)
            db.commit()

            if not relevant_account:
                raise CustomException("Not authorized!", "No corresponding Account found",
                                      status_code=401, error_type="permission_error")
        else:
            new_consent = CookiesConsent(cookies_consent_choices=json.dumps(cookie_consent.cookie_consent), cookies_consent_user_agent=cookie_consent.user_agent, cookies_consent_ip_address=ip_address)
      
            db.add(new_consent)
            db.commit()

        return {"message": "success"}
    
    except Exception as e:
        db.rollback()
        raise CustomException("Error", str(e), status_code=500, error_type="error")

