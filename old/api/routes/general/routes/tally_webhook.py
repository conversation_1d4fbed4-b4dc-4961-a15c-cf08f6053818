import os
import hmac
import hashlib
from fastapi import APIRouter, HTTPException
from utils.exceptions import CustomException
import boto3
from loguru import logger
import json
import base64

router = APIRouter()
base_url = "/api/tally"


# Initialize AWS SES client
ses_client = boto3.client(
    'ses',
    region_name='eu-central-1',  # replace with your AWS region
    aws_access_key_id=os.environ['AWS_ACCESS_KEY'],
    aws_secret_access_key=os.environ['AWS_SECRET_KEY']
)

relevant_email = '<EMAIL>'
if os.environ.get('ENVIRONMENT') == 'prod':
    relevant_email = '<EMAIL>'
elif os.environ.get('ENVIRONMENT') == 'test':
    relevant_email = '<EMAIL>'


def send_contact_email(message):
    try:
        response = ses_client.send_email(
            Source=relevant_email,  # replace with your verified SES email
            Destination={'ToAddresses': ["<EMAIL>"]},
            Message={
                'Body': {
                    'Text': {
                        'Data': "New contact form message: \n" + message,
                        'Charset': 'UTF-8'
                    },
                    'Html': {
                        'Data': "New contact form message: \n" + message,
                        'Charset': 'UTF-8'
                    }
                },
                'Subject': {
                    'Data':  "New contact form message",
                    'Charset': 'UTF-8'
                }
            }
        )
    except Exception as e:
        logger.error(f"Error sending contact email: {str(e)}")
        raise CustomException("Error", str(
            e), status_code=400, error_type="error")


def verify_signature(webhook_payload, received_signature, signing_secret):
    calculated_signature = hmac.new(
        signing_secret.encode(),
        msg=json.dumps(webhook_payload).encode(),
        digestmod=hashlib.sha256
    ).digest()

    return hmac.compare_digest(received_signature, base64.b64encode(calculated_signature).decode())


SIGNING_SECRET = "6755ad33-c2aa-4929-9d52-2d5ffd9d282a"

import json



@router.post(base_url + '/submit-chapter-form', status_code=200)
def submit_chapter_form(webhook_payload: dict):
    # received_signature = webhook_payload.get(
    #     'headers', {}).get('tally-signature')
    # Ensure this is set in your environment
    # if not verify_signature(webhook_payload, received_signature, SIGNING_SECRET):
    #     raise HTTPException(status_code=401, detail="Invalid signature.")


    # Assuming `tally_event` is the JSON string received from the webhook
    tally_event = json.loads(webhook_payload)  # Parse the JSON string

    # Extracting data
    event_data = tally_event["data"]
    form_id = event_data["formId"]

    # Extracting selected year
    selected_year_response_id = event_data["fields"][0]["value"]
    selected_year = next(
        (option["text"] for option in event_data["fields"][0]["options"] if option["id"] == selected_year_response_id),
        None
    )

    # Extracting selected chapter
    selected_chapter_response_id = event_data["fields"][1]["value"]
    selected_chapter = next(
        (option["text"] for option in event_data["fields"][1]["options"] if option["id"] == selected_chapter_response_id),
        None
    )

    # Extracting email
    submission_email = next(
        (field["value"] for field in event_data["fields"] if field["type"] == "INPUT_EMAIL"),
        None
    )

    # Now you have form_id, selected_year, selected_chapter, and submission_email
    logger.info(f"Form ID: {form_id}")
    logger.info(f"Selected Year: {selected_year}")
    logger.info(f"Selected Chapter: {selected_chapter}")
    logger.info(f"Submission Email: {submission_email}")



    logger.info(f"Webhook received: {webhook_payload}")
    # Process the webhook payload
    return {"message": "Webhook received and processed successfully."}
