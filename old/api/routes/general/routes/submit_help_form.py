import os
from utils.exceptions import CustomException
import boto3
from ..models.request import HelpFormRequest
from ..models.response import HelpFormResponse
from fastapi import APIRouter, Depends, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from loguru import logger

router = APIRouter()
base_url = "/api/contact"

# Initialize AWS SES client
ses_client = boto3.client(
    'ses',
    region_name='eu-central-1',  # replace with your AWS region
    aws_access_key_id=os.environ['AWS_ACCESS_KEY'],
    aws_secret_access_key=os.environ['AWS_SECRET_KEY']
)

relevant_email = '<EMAIL>'
if os.environ.get('ENVIRONMENT') == 'prod':
    relevant_email = '<EMAIL>'
elif os.environ.get('ENVIRONMENT') == 'test':
    relevant_email = '<EMAIL>'


def send_help_email(message):
    try:
        # print("message", message)
        response = ses_client.send_email(
            Source=relevant_email,  # replace with your verified SES email
            Destination={'ToAddresses': ["<EMAIL>"]},
            Message={
                'Body': {
                    'Text': {
                        'Data': "New help form message: \n" + message,
                        'Charset': 'UTF-8'
                    },
                    'Html': {
                        'Data': "New help form message: \n" + message,
                        'Charset': 'UTF-8'
                    }
                },
                'Subject': {
                    'Data':  "New help form message",
                    'Charset': 'UTF-8'
                }
            }
        )
    except Exception as e:
        logger.error(f"Error sending help email: {str(e)}")
        raise CustomException("Error", str(e), status_code=400, error_type="error")


@router.post(base_url + '/submit-help-form', status_code=200, response_model=HelpFormResponse)
def submit_help_form(contact_form: HelpFormRequest, request: Request, language: str = 'fr', x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token),  db: Session = Depends(get_db)):
    parent_public_id = request.state.parent_uuid
    if parent_public_id:
        relevant_account = db.query(Account).filter_by(
            account_cognito_id=parent_public_id).first()

        if relevant_account:
            email = relevant_account.account_email
            try:
                message = f"Email: {email} \nMessage: {contact_form.message}"
                send_help_email(message)
                return {"message": "Success"}

            except Exception as e:
                raise CustomException("Error", str(
                    e), status_code=400, error_type="error")
        else:
            raise CustomException("Not authorized!", "No parent_account found",
                                  status_code=401, error_type="permission_error")
    else:
        raise CustomException("Not authorized!", "No parent_account_public_id in token",
                              status_code=401, error_type="permission_error")
