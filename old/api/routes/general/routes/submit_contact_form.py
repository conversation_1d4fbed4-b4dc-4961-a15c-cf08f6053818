import os
from fastapi import APIRouter

from ..models.request import ContactFormRequest
from ..models.response import ContactFormResponse

from utils.exceptions import CustomException
import boto3
from loguru import logger

router = APIRouter()
base_url = "/api/contact"

# Initialize AWS SES client
ses_client = boto3.client(
    'ses',
    region_name='eu-central-1',  # replace with your AWS region
    aws_access_key_id=os.environ['AWS_ACCESS_KEY'],
    aws_secret_access_key=os.environ['AWS_SECRET_KEY']
)

relevant_email = '<EMAIL>'
if os.environ.get('ENVIRONMENT') == 'prod':
    relevant_email = '<EMAIL>'
elif os.environ.get('ENVIRONMENT') == 'test':
    relevant_email = '<EMAIL>'



def send_contact_email(message):
    try:
        response = ses_client.send_email(
            Source=relevant_email,  # replace with your verified SES email
            Destination={'ToAddresses': ["<EMAIL>"]},
            Message={
                'Body': {
                    'Text': {
                        'Data': "New contact form message: \n" + message,
                        'Charset': 'UTF-8'
                    },
                    'Html': {
                        'Data': "New contact form message: \n" + message,
                        'Charset': 'UTF-8'
                    }
                },
                'Subject': {
                    'Data':  "New contact form message",
                    'Charset': 'UTF-8'
                }
            }
        )
    except Exception as e:
        logger.error(f"Error sending contact email: {str(e)}")
        raise CustomException("Error", str(e), status_code=400, error_type="error")

@router.post(base_url + '/submit-contact-form', status_code=200, response_model=ContactFormResponse)
def submit_contact_form(contact_form: ContactFormRequest):
    try:
        message = f"Name: {contact_form.name} \nEmail: {contact_form.email} \nMessage: {contact_form.message}"
        send_contact_email(message)
        return {"message": "success"}
    except Exception as e:
        raise CustomException("Error", str(e), status_code=400, error_type="error")
    


