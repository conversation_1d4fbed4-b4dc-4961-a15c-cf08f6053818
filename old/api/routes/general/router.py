from fastapi import APIRouter
from .routes import submit_contact_form
from .routes import submit_help_form
from .routes import register_cookies_consent
from .routes import submit_tutoring_form
from .routes import tally_webhook

router = APIRouter()

# Include routes from all the files in this directory
router.include_router(submit_contact_form.router)
router.include_router(submit_help_form.router)
router.include_router(register_cookies_consent.router)
router.include_router(submit_tutoring_form.router)
router.include_router(tally_webhook.router)
