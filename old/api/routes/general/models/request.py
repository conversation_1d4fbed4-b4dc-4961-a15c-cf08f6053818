from pydantic import BaseModel
from typing import List, Optional


class ContactFormRequest(BaseModel):
    name: Optional[str]
    email: Optional[str]
    message: str


class TutoringFormRequest(BaseModel):
    name: Optional[str]
    email:  Optional[str]
    school_year:  Optional[str]
    message:  Optional[str]
    budget: Optional[str]


class HelpFormRequest(BaseModel):
    message: str


class CookieConsentRequest(BaseModel):
    cookie_consent: dict
    user_agent: str


class LoginErrorRequest(BaseModel):
    user_email: str
    error_message: str


class ChapterSummaryRequest(BaseModel):
    email: str
    content_id: str
    language: str = "en"
