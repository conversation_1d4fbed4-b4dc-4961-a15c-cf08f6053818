from pydantic import BaseModel
from typing import List, Optional

class BaseResponse(BaseModel):
    message: str

class Exercise(BaseModel):
    exercise_public_id: str
    exercise_prompt_text: str
    exercise_answer_type: str
    exercise_prompt_image_url: Optional[str]
    exercise_prompt_video_url: Optional[str]
    exercise_answer_options: Optional[List[dict]]
    exercise_info_text: Optional[str]
    exercise_status: str
    exercise_difficulty: str | None
    exercise_correct_answer: Optional[str]
    exercise_solution_video_url: Optional[str]
    exercise_completed: Optional[bool]

class Lesson(BaseModel):
    lesson_public_id: str
    lesson_title: str
    lesson_description: Optional[str]
    lesson_video_url: Optional[str]
    chapter_public_id: Optional[str]
    chapter_title: Optional[str]
    course_public_id: Optional[str]
    course_title: Optional[str]
    chapter_icon_id: Optional[str]
    is_free: Optional[bool]
    lesson_number: Optional[int]


class Chapter(BaseModel):
    chapter_public_id: str
    chapter_title: str
    chapter_description: Optional[str]
    chapter_lessons: Optional[List[Lesson]]
    course_public_id: Optional[str]
    course_title: Optional[str]
    is_free: Optional[bool]
    chapter_number: Optional[int]
    chapter_lessons: Optional[List[Lesson]]

class Course(BaseModel):
    course_public_id: str
    course_title: str
    course_description: Optional[str]
    course_chapters: Optional[List[Chapter]]