# from .routes.dev import send_login_email
from fastapi import APIRouter

from .routes import check_verification_status, create_account, update_account_language, login_child, login_parent, refresh_parent_token, pin_reset, resend_parent_verification_code, verify_parent_account

router = APIRouter()

# Include routes from all the files in this directory
router.include_router(create_account.router)
# router.include_router(send_login_email.router)
router.include_router(update_account_language.router)
router.include_router(login_child.router)
router.include_router(login_parent.router)
router.include_router(refresh_parent_token.router)
router.include_router(pin_reset.router)
router.include_router(check_verification_status.router)
router.include_router(resend_parent_verification_code.router)
router.include_router(verify_parent_account.router)
