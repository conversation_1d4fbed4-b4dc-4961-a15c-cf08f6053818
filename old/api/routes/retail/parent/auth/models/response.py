from pydantic import BaseModel
from typing import Optional
from datetime import datetime


class AuthToken(BaseModel):
    access_token: str
    id_token: str
    refresh_token: Optional[str]

class LoginEmailResponse(BaseModel):
    message: str

class CreateAccountResponse(BaseModel):
    message: str
    account_public_id: str
    access_token: Optional[str]
    id_token: Optional[str]

class UpdateAccountLanguageResponse(BaseModel):
    message: str
    updated_account_language: str
    updated_token: AuthToken

class LoginChildWithParentResponse(BaseModel):
    access_token: str
    token_type: str
    child_account_name: str
    parent_account_public_id: str

class SendLoginEmailResponse(BaseModel):
    message: str

class LoginParentResponse(BaseModel):
    message: str
    id_token: str
    access_token: str
    is_auth_migrated: bool
    refresh_token: Optional[str]
    attempts_remaining: Optional[int]
    locked_until: Optional[str]

class VerificationStatusResponse(BaseModel):
    is_auth_migrated: bool
    is_verified: Optional[bool]

class VerifyParentAccountResponse(BaseModel):
    message: str
    is_verified: bool

class ResendParentVerificationResponse(BaseModel):
    message: str

    