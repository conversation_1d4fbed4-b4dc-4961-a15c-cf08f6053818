from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime, timedelta
import secrets
import string

class LoginEmailRequest(BaseModel):
    email: str
    language: str

class CreateAccountRequest(BaseModel):
    email: str
    language: str
    system_options:List[str]
    year_options:List[str]
    marketing_consent:bool
    user_agent: str
    marketing_consent_text: str
    privacy_policy_version: str
    signup_source: Optional[str]
    signup_searchparams: Optional[str]
    parent_login_pin: str = Field(..., min_length=6, max_length=6, pattern=r'^\d+$')


class UpdateAccountLanguageRequest(BaseModel):
    new_language: str

class LoginChildWithParentRequest(BaseModel):
    child_account_public_id: str


class SendLoginEmailRequest(BaseModel):
    email: str
    language: str = 'de'

class LoginParentRequest(BaseModel):
    email: str
    login_pin: str

class CreateAccountPinRequest(BaseModel):
    email: str
    pin: str
    confirm_pin: str

class MigrateToPinRequest(BaseModel):
    email: str
    code: str  # Existing email code
    new_pin: str
    confirm_pin: str

class VerifyPinRequest(BaseModel):
    email: str
    pin: str

class RequestPinResetRequest(BaseModel):
    email: str
    language: str = 'lu'


class CompletePinResetRequest(BaseModel):
    email: str
    reset_token: str
    new_pin: str

class InAppPinChangeRequest(BaseModel):
    new_pin: str

class CheckMigrationRequest(BaseModel):
    email: str
    language: str = 'lu'

class VerifyParentAccountRequest(BaseModel):
    email: str
    verification_code: str

class ResendParentVerificationRequest(BaseModel):
    email: str
    language: str = 'en'