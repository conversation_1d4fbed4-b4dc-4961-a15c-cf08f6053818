import os
from utils.exceptions import CustomException
from ..models.response import LoginParentResponse
from db.models import  Year, ActiveSubscription, SubscriptionOption
from loguru import logger
from datetime import timedelta
from api.utils.auth_utils import create_access_token


ACCESS_TOKEN_EXPIRE_MINUTES = int(os.environ.get("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", 86400))  # Default to 60 days    

def create_login_response(account, email, db, login_email=None):
    try:
        # Check if the account has any active subscriptions
        relevant_subscribed_years = db.query(
            Year.year_public_id,
            Year.year_name
        ).join(
            SubscriptionOption, 
            SubscriptionOption.year_id == Year.year_id
        ).join(
            ActiveSubscription,
            ActiveSubscription.subscription_option_id == SubscriptionOption.subscription_option_id
        ).filter(
            ActiveSubscription.account_id == account.account_id,
            ActiveSubscription.status == 'active'
        ).all()
        
        active_subscriptions = [{
            "year_public_id": year_public_id,
            "year_name": year_name
        } for year_public_id, year_name in relevant_subscribed_years]

        token_data = {
            "sub": str(account.account_cognito_id),
            "email": email,
            "account_language": account.account_language or "en",
            "token_type": "custom",
            "active_subscriptions": active_subscriptions
        }
        
        access_token = create_access_token(
            data=token_data,
            expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        )

        if login_email:
            db.delete(login_email)
            db.commit()

        return LoginParentResponse(
            message="Login successful",
            access_token=access_token,
            id_token=access_token,
            refresh_token=None,
            is_auth_migrated=account.is_auth_migrated
        )
    
    except Exception as e:
        logger.error(f"Token creation error: {str(e)}")
        raise CustomException("Authentication failed", status_code=401, error_type="authentication_failed")
