from datetime import datetime, timedelta
from utils.exceptions import CustomException
import bcrypt

PIN_ATTEMPT_LIMITS = [
    (6, 1),   # 6 attempts -> 1 minute lock
    (10, 5),   # 10 attempts -> 5 minutes
    (15, 15),  # 15 attempts -> 15 minutes
    (20, 60)  # 20+ attempts -> 1 hour lock
]

def check_auth_lock(auth_security):
    if auth_security.locked_until and datetime.utcnow() < auth_security.locked_until:
        remaining = (auth_security.locked_until - datetime.utcnow()).total_seconds()
        
        return {
            "status": "locked",
            "message": "Account locked",
            "retry_after_minutes": int(remaining // 60),
            "status_code": 429,
            "error_type": "account_locked"
        }
    return {
        "status": "unlocked"
    }

def update_failed_attempt(auth_security):
    auth_security.failed_attempts += 1
    auth_security.last_failed_attempt = datetime.utcnow()
    
    # Find matching threshold
    for attempts, minutes in PIN_ATTEMPT_LIMITS:
        if auth_security.failed_attempts >= attempts:
            auth_security.locked_until = datetime.utcnow() + timedelta(minutes=minutes)
    
    return auth_security

def hash_pin(pin: str) -> str:
    if len(pin) != 6 or not pin.isdigit():
        raise ValueError("PIN must be 6 digits")
    return bcrypt.hashpw(pin.encode(), bcrypt.gensalt()).decode()

def verify_pin(pin, account_pin_hash):
    if len(pin) != 6 or not pin.isdigit():
        return False
    return bcrypt.checkpw(pin.encode(), account_pin_hash.encode()) 
