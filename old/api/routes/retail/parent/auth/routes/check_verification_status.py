from fastapi import APIRouter, Depends, BackgroundTasks
from sqlalchemy.orm import Session
from utils.exceptions import CustomException
from ..models.request import CheckMigrationRequest
from ..models.response import VerificationStatusResponse
from db.database import get_db
from db.models import Account, AccountAuthSecurity, PinResetRequest
from datetime import datetime, timedelta
from .pin_reset_utils.send_pin_reset_email import send_pin_reset_email
from .verification_utils.send_parent_verification_email import send_parent_verification_email
import random
router = APIRouter()
base_url = "/api/app/parent/auth"



@router.post(base_url + '/check-verification-status', response_model=VerificationStatusResponse)
async def check_migration_status(request: CheckMigrationRequest, db: Session = Depends(get_db), background_tasks: BackgroundTasks = BackgroundTasks()):
    input_email = request.email.strip().lower()

    # Check if account exists
    account = db.query(Account).filter(
        Account.account_email == input_email).first()
    if not account:
        raise CustomException("Account not found", status_code=404, error_type="account_not_found")
    
    # First we check if need migration, if so we just return that, ( we do not need verification as Pin reset will essentiallly verify the email) if already migrated we check the verification.

    if not account.is_auth_migrated:

        existing_pin_reset_request = db.query(PinResetRequest).filter(
            PinResetRequest.account_id == account.account_id
        ).first()

        pin_reset_request = existing_pin_reset_request
        # Check if the request is still valid, if not delete and create new entry, if it is valid, do nothing
        if pin_reset_request and pin_reset_request.expires_at > datetime.utcnow():
            # Check if we already have a pin reset request in the last minute
            if pin_reset_request.created_at > datetime.utcnow() - timedelta(minutes=1):
                raise CustomException(
                    "Too many requests", status_code=429, error_type="too_many_requests")
        else:
            if pin_reset_request:  # Add null check before deletion
                db.delete(pin_reset_request)
                db.flush()
            pin_reset_request = PinResetRequest(
                account_id=account.account_id,
                expires_at=datetime.utcnow() + timedelta(minutes=30)
            )
            db.add(pin_reset_request)
            db.flush()

            db.commit()

        # At this point we should either have an existing valid or a new reset request, we can then use the reset token on this object to send a new reset pin email

        background_tasks.add_task(
            send_pin_reset_email,
            input_email,
            pin_reset_request.request_token,
            request.language
        )

        return VerificationStatusResponse(
            is_auth_migrated=False,
            is_verified=False
        )
    else:

        # Check if the account is verified
        if account.is_verified == True:
            return VerificationStatusResponse(
                is_auth_migrated=True,
                is_verified=True
            )
        else:


            # Check if we have a verificatioon code on the account, if not set one
            if not account.account_verification_code:
                account.account_verification_code = str(random.randint(100000, 999999))
                db.commit()

            background_tasks.add_task(
                send_parent_verification_email,
                input_email,
                account.account_verification_code,
                account.account_language
            )

            return VerificationStatusResponse(
                is_auth_migrated=True,
                is_verified=False
            )
