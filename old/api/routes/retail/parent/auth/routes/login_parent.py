import os
from fastapi import API<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, BackgroundTasks
from sqlalchemy.orm import Session
from utils.exceptions import CustomException
from ..models.response import LoginParentResponse
from ..models.request import LoginParentRequest, RequestPinResetRequest
from db.database import get_db
from db.models import Account, LoginEmail, AccountAuthSecurity
from ..utils.utils import create_login_response
from ..utils.security import check_auth_lock, update_failed_attempt, verify_pin
from .pin_reset_utils.send_pin_reset_email import send_pin_reset_email

from .pin_reset import request_pin_reset

router = APIRouter()
base_url = "/api/app/parent/auth"

ACCESS_TOKEN_EXPIRE_MINUTES = int(os.environ.get(
    "JWT_ACCESS_TOKEN_EXPIRE_MINUTES", 86400))  # Default to 60 days


@router.post(base_url + '/login-parent', status_code=201, response_model=LoginParentResponse)
def login_parent(login_attempt: LoginParentRequest, ip_address: str = Header(None, alias='x-forwarded-for'), db: Session = Depends(get_db), background_tasks: BackgroundTasks = BackgroundTasks()):
    input_email = login_attempt.email.strip().lower()
    login_pin = login_attempt.login_pin

    # Check if parent account exists
    existing_parent_account = db.query(Account).filter(
        Account.account_email == input_email).first()
    if existing_parent_account is None:
        raise CustomException("Account does not exist",
                              status_code=409, error_type="no_matching_account")

    if existing_parent_account.is_auth_migrated:
        # New PIN-based auth
        # Get or create auth security record
        auth_security = db.query(AccountAuthSecurity).filter(
            AccountAuthSecurity.account_id == existing_parent_account.account_id
        ).first()

        if not auth_security:
            auth_security = AccountAuthSecurity(
                account_id=existing_parent_account.account_id,
                failed_attempts=0,
                locked_until=None
            )
            db.add(auth_security)
            db.commit()

        # Now we can safely check the lock status
        auth_lock_status = check_auth_lock(auth_security)

        if auth_lock_status["status"] == "locked":
            raise CustomException(
                f"Locked for {auth_lock_status['retry_after_minutes']} minutes",
                error_type="account_locked",
                status_code=429
            )

        if existing_parent_account.is_verified == False:
            raise CustomException("Account not verified", status_code=403, error_type="account_not_verified")

        if not verify_pin(login_pin, existing_parent_account.pin_hash):
            auth_security = update_failed_attempt(auth_security)
            db.commit()
            raise CustomException("Invalid PIN", status_code=401, error_type="invalid_pin")

        # Reset counters on success
        auth_security.failed_attempts = 0
        auth_security.locked_until = None
        db.commit()

        return create_login_response(existing_parent_account, input_email, db)
  
    else:
        # We need to active the convert to pincode flow

        request_object = RequestPinResetRequest(email=input_email, language='lu')
        background_tasks.add_task(
            request_pin_reset(request_object, db, background_tasks),
        )
        return {"status": "not_migrated", "message": "If the account exists, a reset code will be sent"}
