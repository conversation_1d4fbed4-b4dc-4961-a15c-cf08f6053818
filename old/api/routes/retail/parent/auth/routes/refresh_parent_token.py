import os
from fastapi import APIRouter, Depends,  Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account
from utils.exceptions import CustomException
from ..utils.utils import create_login_response

router = APIRouter()
base_url = "/api/app/parent/auth"

ACCESS_TOKEN_EXPIRE_MINUTES = int(os.environ.get("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", 86400))  # Default to 60 days    

@router.get(base_url + "/refresh-parent-token")
async def refresh_parent_token(request: Request, db: Session = Depends(get_db)):
    parent_public_id = request.state.parent_uuid
    print("parent_public_id", parent_public_id)
    account = db.query(Account).filter(Account.account_cognito_id == parent_public_id).first()
    if not account:
        raise CustomException("Not authorized!", "No corresponding Account found",
                              status_code=401, error_type="permission_error")
    return create_login_response(account, account.account_email, db)
