from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session
from utils.exceptions import CustomException
from ..models.response import UpdateAccountLanguageResponse, AuthToken
from ..models.request import UpdateAccountLanguageRequest
from db.database import get_db
from db.models import Account
from utils.auth import has_valid_parent_token
from api.utils.auth_utils import create_access_token
from datetime import timedelta
import os


ACCESS_TOKEN_EXPIRE_MINUTES = int(os.environ.get(
    "JWT_ACCESS_TOKEN_EXPIRE_MINUTES", 86400))  # Default to 60 days

router = APIRouter()
base_url = "/api/app/parent/auth"



@router.post(base_url + '/update-account-language', status_code=201, response_model=UpdateAccountLanguageResponse)
def update_account_language(request: Request, updated_language: UpdateAccountLanguageRequest, has_valid_token: bool = Depends(has_valid_parent_token),  db: Session = Depends(get_db)):
    account_cognito_id = request.state.parent_uuid

    relevant_account = db.query(Account).filter_by(
        account_cognito_id=account_cognito_id).first()
    if not relevant_account:
        raise CustomException("Not authorized!", "No corresponding Account found",
                              status_code=401, error_type="permission_error")

    relevant_account.account_language = updated_language.new_language
    db.commit()

    # We need to update the account language in the token
    token_data = {
        "sub": str(relevant_account.account_cognito_id),
        "email": relevant_account.account_email,
        "account_language": relevant_account.account_language or "en",
        "token_type": "custom"
    }

    access_token = create_access_token(
        data=token_data,
        expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    )

    updated_token = AuthToken(
        access_token=access_token,
        id_token=access_token,  # Using the same token for both, adjust if needed
        refresh_token=None  # Implement refresh token logic if required
    )
    return UpdateAccountLanguageResponse(
        message="Language updated successfully!",
        updated_account_language=relevant_account.account_language,
        updated_token=updated_token
    )
