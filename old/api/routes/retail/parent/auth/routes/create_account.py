import stripe
import uuid
import os
from fastapi import APIRouter, <PERSON>pen<PERSON>, <PERSON><PERSON>
import json
from api.utils.auth_utils import create_access_token
from sqlalchemy.orm import Session
from utils.exceptions import CustomException
from ..models.response import CreateAccountResponse
from ..models.request import CreateAccountRequest
from db.database import get_db
from db.models import Account, MarketingConsent, SignUp, Trial, ChildAccount, AccountAuthSecurity, PriceVersion, PriceEligibility
from api.routes.retail.utils import add_to_mailerlite, add_to_marketing_list
from loguru import logger
from datetime import datetime, timedelta
from ..utils.security import hash_pin
import random
from .verification_utils.send_parent_verification_email import send_parent_verification_email

ACCESS_TOKEN_EXPIRE_MINUTES = int(os.environ.get(
    "JWT_ACCESS_TOKEN_EXPIRE_MINUTES", 86400))  # Default to 60 days

router = APIRouter()
base_url = "/api/app/parent/auth"

ENVIRONMENT = os.environ.get('ENVIRONMENT')

FRONTEND_URL = os.environ.get('FRONTEND_URL')
stripe.api_key = os.environ.get('STRIPE_API_KEY')

TRIAL_DURATION = 7  # days


@router.post(base_url + '/create-account', status_code=201, response_model=CreateAccountResponse)
def create_account(new_account: CreateAccountRequest, ip_address: str = Header(None, alias='x-forwarded-for'), db: Session = Depends(get_db)):
    input_email = new_account.email.strip().lower()

    # Check if parent account already exists
    existing_parent_account = db.query(Account).filter(
        Account.account_email == input_email).first()
    if existing_parent_account is not None:
        raise CustomException("Parent account already exists", status_code=409, error_type="account_already_exists")
    
    # Check if child account already exists
    existing_child_account = db.query(ChildAccount).filter(
        ChildAccount.child_account_email == input_email).first()
    if existing_child_account is not None:
        raise CustomException("Child account already exists", status_code=409, error_type="child_account_already_exists")

    if len(new_account.parent_login_pin) != 6 or not new_account.parent_login_pin.isdigit():
        raise CustomException("PIN must be 6 digits", status_code=400, error_type="invalid_pin_format")

    # Temporary fix given that we use this account_cognito_id to refer to accounts throughout the app
    account_cognito_id = str(uuid.uuid4())
    account_public_id = str(uuid.uuid4())

    access_token = None
    id_token = None

    try:
        stripe_response = stripe.Customer.create(
            description=account_public_id, email=input_email)
    except Exception as e:
      
        response = stripe.Customer.delete(stripe_response.id)
        raise CustomException("Could not register account", "Could not register account, issue with creating Stripe account",
                              status_code=500, log_level="WARNING", error_type="internal_error")

    try:

        new_account_entry = Account(
            account_email=input_email,
            account_cognito_id=account_cognito_id,
            account_language=new_account.language,
            account_public_id=account_public_id,
            account_stripe_id=stripe_response.id,
            pin_hash=hash_pin(new_account.parent_login_pin),
            is_auth_migrated=True,
            is_verified=False,
            account_verification_code=str(random.randint(100000, 999999))
        )

        # if the email end with @pt.lu mark as verified
        if input_email and input_email.endswith("@pt.lu"):
            new_account_entry.is_verified = True
        db.add(new_account_entry)

        # Create auth security record
        new_auth_security = AccountAuthSecurity(
            account_id=new_account_entry.account_id,
            failed_attempts=0,
            locked_until=None
        )
        db.add(new_auth_security)

        db.flush()

        # Assign the current default price version to this new account
        current_price_version = db.query(PriceVersion).filter_by(
            is_current=True, is_active=True
        ).first()
        
        if current_price_version:
            # Create a price eligibility record for this new account
            new_price_eligibility = PriceEligibility(
                account_id=new_account_entry.account_id,
                price_version_id=current_price_version.price_version_id
            )
            db.add(new_price_eligibility)

        if ENVIRONMENT != 'dev':
            try:
                add_to_mailerlite(input_email, new_account.language)
            except Exception as e:
                logger.error(f"Error adding to mailerlite: {str(e)}")

        if new_account.marketing_consent:
            new_marketing_consent = MarketingConsent(
                marketing_consent_email=input_email,
                marketing_consent_relevant_account_id=new_account_entry.account_id,
                marketing_consent_user_agent=new_account.user_agent,
                marketing_consent_ip_address=ip_address,
                marketing_consent_text=new_account.marketing_consent_text,
                marketing_consent_privacy_policy_version=new_account.privacy_policy_version
            )
            db.add(new_marketing_consent)

            try:
                if ENVIRONMENT != 'dev':
                    add_to_marketing_list(input_email, new_account.language, ip_address,
                                        new_account.privacy_policy_version, new_account.marketing_consent_text)

            except Exception as e:
                logger.error(f"Error adding to mailerlite: {str(e)}")

        new_sign_up_entry = SignUp(
            sign_up_account_id=new_account_entry.account_id,
            sign_up_email=input_email,
            sign_up_language=new_account.language,
            signup_source=new_account.signup_source,
            signup_year_options=json.dumps(new_account.year_options),
            signup_system_options=json.dumps(new_account.system_options),
            signup_searchparams=new_account.signup_searchparams,
        )
        db.add(new_sign_up_entry)

        new_trial = Trial(
            trial_account_id=new_account_entry.account_id,
            trial_status='active',
            trial_start_date=datetime.utcnow(),
            trial_end_date=datetime.utcnow() + timedelta(days=TRIAL_DURATION)
        )
        db.add(new_trial)

        # CustomToken for the parent
        #    # The token shape is a leftover from the Cognito implementation
        token_data = {
                "sub": str(new_account_entry.account_cognito_id),
                # Later on we should use the public id instead and change this across routes
                "email": input_email,
                "account_language": new_account.language or "de",
                "token_type": "custom"
            }
            
        # Create access token
        access_token = create_access_token(
            data=token_data,
            expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        )
        db.commit()

        send_parent_verification_email(
            input_email,
            new_account_entry.account_verification_code,
            new_account.language
        )

    except Exception as e:
        # Issue on our backend, delete cognito account and stripe account
        db.rollback()
       
        response = stripe.Customer.delete(stripe_response.id)

        raise CustomException("Could not register account", "Could not register account, issue with our backend: " + str(e),
                              status_code=500, log_level="WARNING", error_type="internal_error")

    return {'message': 'Account created sucessfully', 'account_public_id': account_public_id, "access_token": access_token, "id_token": id_token}
