from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account
from ..models.request import ResendParentVerificationRequest
from ..models.response import ResendParentVerificationResponse
from utils.exceptions import CustomException
import random
from .verification_utils.send_parent_verification_email import send_parent_verification_email

router = APIRouter()
base_url = "/api/app/parent/auth"

@router.post(base_url + '/resend-parent-verification', status_code=200, response_model=ResendParentVerificationResponse)
def resend_parent_verification(req: ResendParentVerificationRequest, db: Session = Depends(get_db)):
    # Validate email is not empty
    if not req.email:
        raise CustomException("Email is required", status_code=400)
    
    account = db.query(Account).filter(
        Account.account_email == req.email).first()
    
    if not account:
        raise CustomException("Account not found", status_code=404)
    
    if account.is_verified:
        raise CustomException("Account already verified", status_code=400)
    
    # Generate new code if none exists
    if not account.account_verification_code:
        account.account_verification_code = str(random.randint(100000, 999999))
        db.commit()
    
    # Set default language if not provided
    language = getattr(req, 'language', 'en')
    
    # Send verification email
    send_success = send_parent_verification_email(
        req.email, 
        account.account_verification_code,
        language
    )
    
    if not send_success:
        raise CustomException("Failed to send verification email", status_code=500)
    
    return {"message": "Verification code resent"} 