import boto3
from loguru import logger
import os
from .create_reset_email import create_reset_email

def send_pin_reset_email(email_address: str, reset_token: str, language: str = 'lu'):
    # Initialize SES client
    ses_client = boto3.client(
        'ses',
        region_name='eu-central-1',
        aws_access_key_id=os.environ['AWS_ACCESS_KEY'],
        aws_secret_access_key=os.environ['AWS_SECRET_KEY']
    )

    # Determine the frontend URL based on environment
    frontend_base_url = os.environ.get('FRONTEND_URL', 'http://localhost:3000')
    reset_link = f"{frontend_base_url}/{language}/app/auth/reset-parent-pin?email={email_address}&reset_token={reset_token}"

    # Create email content using templates
    email_body_html, email_body_text, email_subject = create_reset_email(language, reset_link)

    try:
        sender_email = '<EMAIL>'
        if os.environ.get('ENVIRONMENT') == 'prod':
            sender_email = '<EMAIL>'
        if os.environ.get('ENVIRONMENT') == 'test':
            sender_email = '<EMAIL>'

        response = ses_client.send_email(
            Destination={'ToAddresses': [email_address]},
            Message={
                'Body': {
                    'Html': {'Charset': 'UTF-8', 'Data': email_body_html},
                    'Text': {'Charset': 'UTF-8', 'Data': email_body_text}
                },
                'Subject': {'Charset': 'UTF-8', 'Data': email_subject}
            },
            Source=sender_email
        )
        logger.info(f"PIN reset link sent to {email_address}")
        return True

    except Exception as e:
        logger.error(f"Error sending PIN reset email to {email_address}: {e}")
        return False
