import os
from string import Template

template_data = {
    'fr': {
        "email_subject": "🔐 Réinitialisation de votre code PIN LuxEdu",
        "html_template": {
            "greeting": "Bonjour,",
            "action": "Nous avons reçu une demande de réinitialisation de votre code PIN LuxEdu.",
            "instruction": "Pour réinitialiser votre code PIN, cliquez sur le bouton ci-dessous :",
            "button_text": "Réinitialiser le code PIN",
            "alternative": "Si le bouton ne fonctionne pas, vous pouvez copier et coller ce lien dans votre navigateur :",
            "warning": "Si vous n'avez pas demandé cette réinitialisation, veuillez ignorer cet e-mail.",
            "expiry": "Ce lien expirera dans 30 minutes pour des raisons de sécurité.",
            "questions": "Besoin d'aide ou des questions? Nous sommes là pour vous!",
            "contact_email": "<EMAIL>",
            "salutations": "Meilleures salutations,",
            "team": "LuxEdu"
        }
    },
    'en': {
        "email_subject": "🔐 Reset your LuxEdu PIN",
        "html_template": {
            "greeting": "Hello,",
            "action": "We received a request to reset your LuxEdu PIN.",
            "instruction": "To reset your PIN, click the button below:",
            "button_text": "Reset PIN",
            "alternative": "If the button doesn't work, you can copy and paste this link into your browser:",
            "warning": "If you didn't request this reset, please ignore this email.",
            "expiry": "This link will expire in 30 minutes for security reasons.",
            "questions": "Need help or have questions? We're here for you!",
            "contact_email": "<EMAIL>",
            "salutations": "Best regards,",
            "team": "LuxEdu"
        }
    },
    'de': {
        "email_subject": "🔐 Zurücksetzen Ihrer LuxEdu PIN",
        "html_template": {
            "greeting": "Hallo,",
            "action": "Wir haben eine Anfrage zum Zurücksetzen Ihrer LuxEdu PIN erhalten.",
            "instruction": "Um Ihre PIN zurückzusetzen, klicken Sie bitte auf den Button unten:",
            "button_text": "PIN zurücksetzen",
            "alternative": "Wenn der Button nicht funktioniert, können Sie diesen Link in Ihren Browser kopieren:",
            "warning": "Falls Sie diese Zurücksetzung nicht angefordert haben, ignorieren Sie bitte diese E-Mail.",
            "expiry": "Dieser Link läuft aus Sicherheitsgründen in 30 Minuten ab.",
            "questions": "Brauchen Sie Hilfe oder haben Sie Fragen? Wir sind für Sie da!",
            "contact_email": "<EMAIL>",
            "salutations": "Mit freundlichen Grüßen,",
            "team": "LuxEdu"
        }
    },
    'lu': {
        "email_subject": "🔐 Setzt Ären LuxEdu PIN zréck",
        "html_template": {
            "greeting": "Moien,",
            "action": "Mir hunn eng Ufro kritt fir Ären LuxEdu PIN zeréckzesetzen.",
            "instruction": "Fir Ären PIN zeréckzesetzen, klickt w.e.g. op de Button ënnen:",
            "button_text": "PIN zerécksetzen",
            "alternative": "Wann den Button net funktionéieren sollt, kënnt Dir dëse Link an Äre Browser kopéieren:",
            "warning": "Wann Dir dëst Zerécksetzen net ugefrot hutt, ignoréiert w.e.g. dës E-Mail.",
            "expiry": "Dëse Link leeft aus Sécherheetsgrënn an 30 Minutten of.",
            "questions": "Braucht Dir Hëllef oder hutt Dir Froen? Mir si fir Iech do!",
            "contact_email": "<EMAIL>",
            "salutations": "Mat beschte Gréiss,",
            "team": "LuxEdu"
        }
    }
}

def load_reset_email_template(filename, reset_link, substitutions):
    current_dir = os.path.dirname(os.path.abspath(__file__))
    full_path = os.path.join(current_dir, filename)

    with open(full_path, 'r') as file:
        html_template = file.read()
        template = Template(html_template)
    
    substitutions['reset_link'] = reset_link
    return template.substitute(**substitutions)

def create_reset_email(language, reset_link):
    if language not in template_data:
        language = 'en'  # Default to English if language not found

    relevant_data = template_data[language]
    relevant_substitutions = relevant_data["html_template"]

    email_body_html = load_reset_email_template(
        'reset_email_template.html', reset_link, relevant_substitutions)

    # Create plain text version
    email_body_text = f"""
{relevant_substitutions['greeting']}

{relevant_substitutions['action']}
{relevant_substitutions['instruction']}

{reset_link}

{relevant_substitutions['warning']}
{relevant_substitutions['expiry']}

{relevant_substitutions['questions']}
{relevant_substitutions['contact_email']}

{relevant_substitutions['salutations']}
{relevant_substitutions['team']}
    """

    return email_body_html, email_body_text, relevant_data["email_subject"] 