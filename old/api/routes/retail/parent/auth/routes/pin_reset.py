from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, Request, BackgroundTasks
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account, PinResetRequest, AccountAuthSecurity
from utils.exceptions import CustomException
from utils.auth import has_valid_parent_token
from ..models.request import RequestPinResetRequest, InAppPinChangeRequest
from ..models.request import CompletePinResetRequest
from ..utils.security import hash_pin
from .pin_reset_utils.send_pin_reset_email import send_pin_reset_email

router = APIRouter()
base_url = "/api/app/parent/auth"


@router.post(base_url + '/request-pin-reset')
async def request_pin_reset(
    request: RequestPinResetRequest,
    db: Session = Depends(get_db),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    email = request.email.strip().lower()
    account = db.query(Account).filter(Account.account_email == email).first()

    # Check if we already have a pin reset request in the last minute
    last_reset_request = db.query(PinResetRequest).filter(
        PinResetRequest.account_id == account.account_id,
        PinResetRequest.used == False
    ).first()

    if last_reset_request and last_reset_request.created_at > datetime.utcnow() - timedelta(minutes=1):
        raise CustomException("Too many requests",
                              status_code=429, error_type="too_many_requests")

    # Check if we already have a resetrequest, if it is not expired, we reuse that, if it is, we delete it and create a new one
    if last_reset_request and last_reset_request.expires_at > datetime.utcnow():
        background_tasks.add_task(
            send_pin_reset_email,
            email,
            last_reset_request.request_token,
            request.language
        )
        return {"status": "resent", "message": "Reset code re-sent"}
    else:
        # We have a request but it is not valid anymore, remove the old request and create a new one
        if last_reset_request:
            db.delete(last_reset_request)
            db.commit()

    if not account or not account.is_auth_migrated:
        # Return generic success to prevent email enumeration
        return {"status": "success", "message": "If the account exists, a reset code will be sent"}

    # Create reset request
    reset_request = PinResetRequest(account_id=account.account_id)
    db.add(reset_request)
    db.commit()

    # Send email with code
    background_tasks.add_task(
        send_pin_reset_email,
        email,
        reset_request.request_token,
        request.language
    )

    return {"status": "success", "message": "Reset code sent if account exists"}


@router.post(base_url + '/complete-pin-reset')
def complete_pin_reset(
    request: CompletePinResetRequest,
    db: Session = Depends(get_db)
):
    # Validate PIN
    if len(request.new_pin) != 6 or not request.new_pin.isdigit():
        raise CustomException("PIN must be 6 digits",
                              status_code=400, error_type="invalid_pin")

    # Validate reset request
    reset_request = db.query(PinResetRequest).filter(
        PinResetRequest.request_token == request.reset_token,
        PinResetRequest.expires_at > datetime.utcnow(),
        PinResetRequest.used == False
    ).first()

    if not reset_request:
        raise CustomException("Invalid or expired reset request",
                              status_code=400, error_type="invalid_reset_request")

    # Update Account instead of AuthSecurity
    account = db.query(Account).get(reset_request.account_id)
    account.pin_hash = hash_pin(request.new_pin)

    # Update security record
    auth_security = db.query(AccountAuthSecurity).filter(
        AccountAuthSecurity.account_id == reset_request.account_id
    ).first()

    if not auth_security:
        auth_security = AccountAuthSecurity(
            account_id=reset_request.account_id,
            failed_attempts=0,
            locked_until=None
        )
        db.add(auth_security)

    auth_security.failed_attempts = 0
    auth_security.locked_until = None
    reset_request.used = True

    if not account.is_auth_migrated:
        account.is_auth_migrated = True

    # If account marked as not verified, mark as verified
    account.is_verified = True

    db.commit()

    return {"status": "success", "message": "PIN successfully reset"}


# # Also need to handle in-App resets, which do not require a confirmation code using the has_valid tokenm approach

# @router.post(base_url + '/in-app-pin-change')
# def in_app_pin_change(
#     request: InAppPinChangeRequest,
#     db: Session = Depends(get_db),
#     has_valid_token: bool = Depends(has_valid_parent_token)
# ):
#     account_cognito_id = request.state.parent_uuid

#     if not account_cognito_id:
#         raise CustomException("Not authorized!", "No corresponding Account found",
#                               status_code=401, error_type="permission_error")

#     relevant_account = db.query(Account).filter(Account.account_cognito_id == account_cognito_id).first()
#     if not relevant_account:
#         raise CustomException("Not authorized!", "No corresponding Account found",
#                               status_code=401, error_type="permission_error")

#     auth_security = db.query(AccountAuthSecurity).filter(
#         AccountAuthSecurity.account_id == relevant_account.account_id,
#     ).first()

#     if auth_security.migration_status != 'completed':
#         auth_security.migration_status = 'completed'

#     if not auth_security:
#         raise CustomException("Account security record not found", status_code=404)


#     if len(request.new_pin) != 6 or not request.new_pin.isdigit():
#         raise CustomException("PIN must be 6 digits", status_code=400)

#     auth_security.pin_hash = hash_pin(request.new_pin)
#     db.commit()
#     return {"status": "success", "message": "PIN successfully changed"}
