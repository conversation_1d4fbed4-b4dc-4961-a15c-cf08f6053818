import os
from string import Template
import boto3

template_data = {
    'fr': {
        "email_subject": "🎒 Code de connexion LuxEdu",
        "email_text": "Voici votre code de connexion LuxEdu:",
        "html_template": {
            "greeting": "Vous y êtes presque!",
            "action": "Copiez et collez le code ci-dessous pour vous connecter :",
            "questions": "Besoin d'un coup de main ou des questions? Nous sommes à votre disposition!",
            "contact_email": "<EMAIL>",
            "salutations": "Meilleures salutations,",
            "team": "LuxEdu"
        }
    },
    'en': {
        "email_subject": "🎒 LuxEdu login code",
        "email_text": "Here's your LuxEdu login code:",
        "html_template": {
            "greeting": "Nearly there!",
            "action": "Just copy and paste the code below to login:",
            "questions": "Need a hand or have questions? We're just an email away!",
            "contact_email": "<EMAIL>",
            "salutations": "Kind regards,",
            "team": "LuxEdu"
        }
    },
    'de': {
        "email_subject": "🎒 LuxEdu Anmeldecode",
        "email_text": "Hier ist Ihr LuxEdu Anmeldecode:",
        "html_template": {
            "greeting": "Fast geschafft!",
            "action": "Zum Anmelden einfach den nachfolgenden Code kopieren:",
            "questions": "Brauchen Sie Hilfe oder haben Sie Fragen? Schreiben Sie uns einfach eine E-Mail!",
            "contact_email": "<EMAIL>",
            "salutations": "Mit freundlichen Grüßen",
            "team": "LuxEdu"
        }
    },
    'lu': {
        "email_subject": "🎒 LuxEdu Login Code",
        "email_text": "Hei ass Äre LuxEdu Umeldungscode:",
        "html_template": {
            "greeting": "Bal geschafft!",
            "action": "Fir Iech anzeloggen, kopéiert einfach den follgenden Code:",
            "questions": "Braucht Dir Hëllef oder hutt Dir Froen? Schreift eis einfach eng E-Mail!",
            "contact_email": "<EMAIL>",
            "salutations": "Mat beschten Gréiss",
            "team": "LuxEdu"
        }
    }
}


def load_email_template(filename, secret_login_code, substitutions):
    current_dir = os.path.dirname(os.path.abspath(__file__))
    full_path = os.path.join(current_dir, filename)

    with open(full_path, 'r') as file:
        html_template = file.read()
        template = Template(html_template)
    # We split the secret login code into two parts to make it more readable, dynamically based on the length of the secret login code
    secret_login_code_length = len(secret_login_code)
    # if secret_login_code_length > 4:
    #     secret_login_code_first_part = secret_login_code[:secret_login_code_length//2]
    #     secret_login_code_second_part = secret_login_code[secret_login_code_length//2:]
    #     substitutions['secret_login_code'] = secret_login_code_first_part + \
    #         " " + secret_login_code_second_part
    # else:
    substitutions['secret_login_code'] = secret_login_code
    return template.substitute(**substitutions)


def create_login_email(language, secret_login_code):
    if language not in template_data:
        language = 'en'  # Default to English if language not found

    relevant_data = template_data[language]
    relevant_substitutions = relevant_data["html_template"]

    email_body_html = load_email_template(
        'email_template.html', secret_login_code, relevant_substitutions)

    email_body_text = relevant_data["email_text"] + " " + secret_login_code
    email_subject = relevant_data["email_subject"]

    return email_body_html, email_body_text, email_subject
