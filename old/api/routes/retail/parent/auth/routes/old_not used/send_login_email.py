import os
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, <PERSON><PERSON>
from sqlalchemy.orm import Session
from utils.exceptions import CustomException
from ...models.response import SendLoginEmailResponse
from ...models.request import SendLoginEmailRequest
from db.database import get_db
from db.models import Account, LoginEmail
import boto3
from loguru import logger
from datetime import datetime, timedelta
import random
from .login_email_utils.create_login_email import create_login_email

# Add this dictionary for hard-coded email addresses and their fixed login codes
FIXED_LOGIN_CODES = [
    {"email": "<EMAIL>", "code": "314314", "send_email": False},
    {"email": "<EMAIL>", "code": "661661", "send_email": False},
    {"email": "<EMAIL>", "code": "314314", "send_email": False},
    {"email": "<EMAIL>", "code": "123123", "send_email": True},
    {"email": "<EMAIL>","code": "321321", "send_email": True},
    {"email": "<EMAIL>", "code": "111222", "send_email": True},
    {"email": "<EMAIL>", "code": "222111", "send_email": True},
    {"email": "<EMAIL>", "code": "222333", "send_email": True},
    {"email": "<EMAIL>", "code": "333444", "send_email": True},
    {"email": "<EMAIL>", "code": "333222", "send_email": True},
    {"email": "<EMAIL>", "code": "555666", "send_email": True},
    {"email": "<EMAIL>", "code": "777888", "send_email": True},
    {"email": "<EMAIL>", "code": "666777", "send_email": True},
    {"email": "<EMAIL>", "code": "999000", "send_email": True},
    {"email": "<EMAIL>", "code": "000111", "send_email": True}
]

router = APIRouter()
base_url = "/api/app/parent/auth"

ses_client = boto3.client(
    'ses',
    region_name='eu-central-1',  # replace with your AWS region
    aws_access_key_id=os.environ['AWS_ACCESS_KEY'],
    aws_secret_access_key=os.environ['AWS_SECRET_KEY']
)


def send_login_email(email_address, secret_code, language='de'):

    email_body_html, email_body_text, email_subject = create_login_email(
        language, secret_code)

    try:
        sender_email = '<EMAIL>'
        if os.environ.get('ENVIRONMENT') == 'prod':
            sender_email = '<EMAIL>'
        if os.environ.get('ENVIRONMENT') == 'test':
            sender_email = '<EMAIL>'
        response = ses_client.send_email(
            Destination={'ToAddresses': [email_address]},
            Message={
                'Body': {
                    'Html': {'Charset': 'UTF-8', 'Data': email_body_html},
                    'Text': {'Charset': 'UTF-8', 'Data': email_body_text}
                },
                'Subject': {'Charset': 'UTF-8', 'Data': email_subject}
            },
            Source=sender_email
        )
        logger.info(f"Login code {secret_code} sent to {email_address}")
        return True
    except Exception as e:
        logger.error(f"Error sending login code to {email_address}: {e}")
        return False


@router.post(base_url + '/send-login-email', status_code=201, response_model=SendLoginEmailResponse)
def create_account(login_attempt: SendLoginEmailRequest, ip_address: str = Header(None, alias='x-forwarded-for'), db: Session = Depends(get_db)):
    input_email = login_attempt.email.strip().lower()

    # Check if parent account exists
    existing_parent_account = db.query(Account).filter(
        Account.account_email == input_email).first()
    if existing_parent_account is None:
        raise CustomException("Account does not exist",
                              status_code=409, error_type="no_matching_account")

    # Check if the email is in the FIXED_LOGIN_CODES list
    if input_email in [entry["email"] for entry in FIXED_LOGIN_CODES]:
        secret_code = [entry["code"]
                       for entry in FIXED_LOGIN_CODES if entry["email"] == input_email][0]
        logger.info(f"Using fixed login code for {input_email}")
    else:
        # Check if we already have a login email for this account
        existing_login_email = db.query(LoginEmail).filter(
            LoginEmail.account_id == existing_parent_account.account_id,
        ).order_by(LoginEmail.created_at.desc()).first()
        # If it has been sent less than 30 seconds ago, we don't want to send another one
        if existing_login_email is not None and datetime.utcnow() - existing_login_email.created_at < timedelta(seconds=30):
            raise CustomException(
                "Login email already sent", status_code=409, error_type="login_email_already_sent")

        if existing_login_email is None:
            # create 6 digit string
            secret_code = str(random.randint(100000, 999999))
        else:
            secret_code = existing_login_email.secret_code
            db.delete(existing_login_email)

    # Create a new login email
    new_login_email = LoginEmail(
        account_id=existing_parent_account.account_id,
        email=input_email,
        secret_code=secret_code
    )
    # Send email
    try:
        if input_email in [entry["email"] for entry in FIXED_LOGIN_CODES]:
            send_email = [entry["send_email"]
                          for entry in FIXED_LOGIN_CODES if entry["email"] == input_email][0]
        else:
            send_email = True

        if send_email:
            send_login_email(input_email, secret_code,
                             language=login_attempt.language)
    except Exception as e:
        raise CustomException("Could not send login email", "Could not send login email",
                              status_code=500, log_level="WARNING", error_type="internal_error")
    db.add(new_login_email)
    db.commit()

    return {"message": "Login email sent successfully"}
