from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session
from utils.exceptions import CustomException
from ..models.response import LoginChildWithParentResponse
from ..models.request import LoginChildWithParentRequest
from db.database import get_db
from db.models import Account, ChildAccount
from utils.auth import has_valid_parent_token
import os
from api.utils.auth_utils import create_access_token
from datetime import timedelta

router = APIRouter()
base_url = "/api/app/parent/auth"


ACCESS_TOKEN_EXPIRE_MINUTES = int(
    os.environ.get("JWT_ACCESS_TOKEN_EXPIRE_MINUTES"))


@router.post(base_url + '/login-child-with-parent', status_code=201, response_model=LoginChildWithParentResponse)
def login_child(request: Request, child_account: LoginChildWithParentRequest, has_valid_token: bool = Depends(has_valid_parent_token),  db: Session = Depends(get_db)):
    account_cognito_id = request.state.parent_uuid

    relevant_parent_account = db.query(Account).filter_by(
        account_cognito_id=account_cognito_id).first()
    if not relevant_parent_account:
        raise CustomException("Not authorized!", "No corresponding Account found",
                              status_code=401, error_type="permission_error")

    relevant_child_account = db.query(ChildAccount).filter_by(
        child_account_public_id=child_account.child_account_public_id).first()
    if not relevant_child_account:
        raise CustomException("Not authorized!", "No corresponding Child Account found",
                              status_code=401, error_type="permission_error")

    if relevant_parent_account.account_id != relevant_child_account.child_account_parent_id:
        raise CustomException("Not authorized!", "Child account does not belong to the parent account",
                              status_code=401, error_type="permission_error")

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={
            "child_account_public_id": relevant_child_account.child_account_public_id,
            "parent_account_public_id": relevant_parent_account.account_public_id,
            "child_account_name": relevant_child_account.child_account_name
        },
        expires_delta=access_token_expires
    )

    return LoginChildWithParentResponse(access_token=access_token, child_account_name=relevant_child_account.child_account_name, token_type="bearer", parent_account_public_id=relevant_parent_account.account_public_id)
