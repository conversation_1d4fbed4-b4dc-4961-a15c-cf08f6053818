import os
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account
from ..models.request import VerifyParentAccountRequest
from ..models.response import VerifyParentAccountResponse
from utils.exceptions import CustomException
router = APIRouter()
base_url = "/api/app/parent/auth"

ACCESS_TOKEN_EXPIRE_MINUTES = int(
    os.environ.get("JWT_ACCESS_TOKEN_EXPIRE_MINUTES"))

@router.post(base_url + '/verify-parent-account', status_code=200, response_model=VerifyParentAccountResponse)
def verify_parent_account(verification: VerifyParentAccountRequest, db: Session = Depends(get_db)):
    account = db.query(Account).filter(
        Account.account_email == verification.email).first()
    
    if not account:
        raise CustomException("Account not found", status_code=404, error_type="account_not_found")
    
    if account.is_verified:
        raise CustomException("Account already verified", status_code=400, error_type="account_already_verified")
    
    if account.account_verification_code != verification.verification_code:
        raise CustomException("Invalid verification code", status_code=400, error_type="invalid_verification_code")
    
    account.is_verified = True
    db.commit()

    return {
        "message": "Account verified successfully",
        "is_verified": True
    } 

