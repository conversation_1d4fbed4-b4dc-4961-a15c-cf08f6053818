import os
from string import Template

template_data = {
    'fr': {
        "email_subject": "🎒 Vérification de compte LuxEdu",
        "html_template": {
            "greeting": "Bienvenue chez LuxEdu!",
            "action": "Veuillez utiliser le code suivant pour vérifier votre compte:",
            "code_instruction": "Code de vérification:",
            "button_text": "Vérifier le compte",
            "alternative": "Ou cliquez sur ce lien pour vérifier:",
            "warning": "Si vous n'avez pas créé de compte, veuillez ignorer cet e-mail.",
            "expiry": "Ce code expirera dans 15 minutes.",
            "questions": "Besoin d'aide? Contactez-nous à:",
            "contact_email": "<EMAIL>",
            "salutations": "Meilleures salutations,",
            "team": "L'équipe LuxEdu"
        }
    },
    'en': {
        "email_subject": "🎒 LuxEdu Account Verification",
        "html_template": {
            "greeting": "Welcome to LuxEdu!",
            "action": "Please use the following code to verify your account:",
            "code_instruction": "Verification Code:",
            "button_text": "Verify Account",
            "alternative": "Or click this link to verify:",
            "warning": "If you didn't create an account, please ignore this email.",
            "expiry": "This code will expire in 15 minutes.",
            "questions": "Need help? Contact us at:",
            "contact_email": "<EMAIL>",
            "salutations": "Best regards,",
            "team": "The LuxEdu Team"
        }
    },
    'de': {
        "email_subject": "🎒 LuxEdu Kontobestätigung",
        "html_template": {
            "greeting": "Willkommen bei LuxEdu!",
            "action": "Bitte verwenden Sie folgenden Code zur Kontobestätigung:",
            "code_instruction": "Bestätigungscode:",
            "button_text": "Konto bestätigen",
            "alternative": "Oder klicken Sie auf diesen Link zur Bestätigung:",
            "warning": "Falls Sie kein Konto erstellt haben, ignorieren Sie diese E-Mail.",
            "expiry": "Dieser Code läuft in 15 Minuten ab.",
            "questions": "Hilfe benötigt? Kontaktieren Sie uns:",
            "contact_email": "<EMAIL>",
            "salutations": "Mit freundlichen Grüßen,",
            "team": "Das LuxEdu Team"
        }
    },
    'lu': {
        "email_subject": "🎒 LuxEdu E-Mail Bestätegung",
        "html_template": {
            "greeting": "Wëllkomm bei LuxEdu!",
            "action": "Benotzt w.e.g. de folgende Code fir Äeren Kont ze bestätegen:",
            "code_instruction": "Bestätegungscode:",
            "button_text": "Kont bestätegen",
            "alternative": "Oder klickt op dëse Link fir ze bestätegen:",
            "warning": "Wann Dir kee Kont opgemaach hutt, ignoriéiert w.e.g. dës E-Mail.",
            "expiry": "Dëse Code leeft a 15 Minutten of.",
            "questions": "Braucht Dir Hëllef? Kontaktéiert eis:",
            "contact_email": "<EMAIL>",
            "salutations": "Mat beschte Gréiss,",
            "team": "De LuxEdu Equipe"
        }
    }
}

def load_verification_template(filename, verification_code, verification_link, substitutions):
    current_dir = os.path.dirname(os.path.abspath(__file__))
    full_path = os.path.join(current_dir, filename)

    with open(full_path, 'r') as file:
        html_template = file.read()
        template = Template(html_template)
    
    substitutions['verification_code'] = verification_code
    substitutions['verification_link'] = verification_link
    return template.substitute(**substitutions)

def create_verification_email(language, verification_code, email_address):
    if language not in template_data:
        language = 'en'

    # Create verification link
    frontend_base_url = os.environ.get('FRONTEND_URL', 'http://localhost:3000')
    verification_link = f"{frontend_base_url}/{language}/app/auth/login?email={email_address}&parent_verification_code={verification_code}"

    relevant_data = template_data[language]
    substitutions = relevant_data["html_template"]

    email_body_html = load_verification_template(
        'parent_verification_email_template.html', 
        verification_code,
        verification_link,
        substitutions
    )
    
    email_body_text = f"""
{substitutions['greeting']}

{substitutions['action']}

{substitutions['code_instruction']} {verification_code}

{substitutions['alternative']}
{verification_link}

{substitutions['warning']}
{substitutions['expiry']}

{substitutions['questions']} {substitutions['contact_email']}

{substitutions['salutations']}
{substitutions['team']}
    """
    
    return email_body_html, email_body_text, relevant_data["email_subject"] 