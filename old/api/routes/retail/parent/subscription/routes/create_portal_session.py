from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account
import os
import stripe
from ..models.request import CreatePortalSessionRequest
from ..models.response import CreatePortalSessionResponse
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
router = APIRouter()
base_url = "/api/parent/subscription"

stripe.api_key = os.environ.get('STRIPE_API_KEY')
DOMAIN_URL = os.environ.get("FRONTEND_URL")

@router.post(base_url + '/create-portal-session', status_code=200, response_model=CreatePortalSessionResponse)
def create_portal_session(request: Request, portal_session: CreatePortalSessionRequest, x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token), db: Session = Depends(get_db)):
    parent_public_id = request.state.parent_uuid
    if parent_public_id:
        relevant_account = db.query(Account).filter_by(
            account_cognito_id=parent_public_id).first()
        if relevant_account:
            return_url = DOMAIN_URL + f"/{portal_session.language}/app/parent/account-settings"

            try:
                # stripe_language = portal_session.language
                # if stripe_language == "lu":
                    # stripe_language = "lb"
                portalSession = stripe.billing_portal.Session.create(
                    customer=relevant_account.account_stripe_id,
                    # locale=stripe_language,
                    return_url=return_url,
                )
                return {"portal_session_url": portalSession.url}
            except Exception as e:
                raise CustomException("Could not create portal session", str(e), status_code=500, error_type="stripe_error")

        else:
            raise CustomException("Not authorized!", "No corresponding Account found",
                                  status_code=401, error_type="permission_error")
    else:
        raise CustomException("Not authorized!", "No parent_account_public_id in token",
                              status_code=401, error_type="permission_error")
