from pydantic import BaseModel
from fastapi import APIRouter, Depends, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account, SubscriptionOption, PriceVersion, PriceEligibility, Year
import os
import stripe
from ..models.request import CreateCheckoutSessionRequest
from ..models.response import CreateCheckoutSessionResponse
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
# This is your test secret API key.
stripe.api_key = os.environ.get('STRIPE_API_KEY')

router = APIRouter()
base_url = "/api/parent/subscription"

DOMAIN_URL = os.environ.get("FRONTEND_URL")

LU_TAX_RATE = os.environ.get("LU_TAX_RATE")
DE_TAX_RATE = os.environ.get("DE_TAX_RATE")
FR_TAX_RATE = os.environ.get("FR_TAX_RATE")
BE_TAX_RATE = os.environ.get("BE_TAX_RATE")
DK_TAX_RATE = os.environ.get("DK_TAX_RATE")

# iterate over the TAX_RATES dictionary
dynamic_tax_rates = [
    LU_TAX_RATE,
    DE_TAX_RATE,
    FR_TAX_RATE,
    BE_TAX_RATE,
    DK_TAX_RATE
]


@router.post(base_url + '/create-checkout-session', status_code=200, response_model=CreateCheckoutSessionResponse)
def create_checkout_session(request: Request, new_session: CreateCheckoutSessionRequest, x_auth_parent_token: str = Header(None),  has_valid_token: bool = Depends(has_valid_parent_token), db: Session = Depends(get_db)):
    parent_public_id = request.state.parent_uuid
    if parent_public_id:
        relevant_account = db.query(Account).filter_by(
            account_cognito_id=parent_public_id).first()
        if relevant_account:

            # Determine what price version the user is eligble for
            account_price_eligibility = db.query(PriceEligibility).filter_by(
                account_id=relevant_account.account_id).first()
            
            if account_price_eligibility:
                # User has a specific price version assigned
                price_version = db.query(PriceVersion).filter_by(
                    price_version_id=account_price_eligibility.price_version_id).first()
            else:
                # Use the current default price version
                price_version = db.query(PriceVersion).filter_by(
                    is_current=True, is_active=True).first()
            
            if not price_version:
                raise CustomException("No active price version found", 
                                      status_code=500, error_type="configuration_error")
                
            # We then get the year they would like to subscribe to
            relevant_year = db.query(Year).filter_by(
                year_public_id=new_session.year_public_id).first()
            
            if not relevant_year:
                raise CustomException("Year not found", 
                                      status_code=404, error_type="not_found")
            
            # We then get the subscription that is associated with the year and price version
            relevant_subscription = db.query(SubscriptionOption).filter_by(
                year_id=relevant_year.year_id,
                price_version_id=price_version.price_version_id).first()
            
            if not relevant_subscription:
                raise CustomException("Subscription not found", 
                                      status_code=404, error_type="not_found")
            
            # Select the appropriate Stripe price ID based on the subscription type
            if new_session.subscription_type == "monthly":
                stripe_price_id = relevant_subscription.stripe_monthly_id
            elif new_session.subscription_type == "yearly":
                stripe_price_id = relevant_subscription.stripe_yearly_id
            else:
                raise CustomException("Invalid subscription type. Must be 'monthly' or 'yearly'", 
                                     status_code=400, error_type="invalid_request")

            # Validate we have a valid price ID
            if not stripe_price_id:
                raise CustomException("Invalid price ID", 
                                      status_code=400, error_type="invalid_request")

            # Set up the base parameters for the checkout session
            checkout_session_params = {
                'customer': relevant_account.account_stripe_id,
                'line_items': [
                    {
                        'price': stripe_price_id,
                        'quantity': 1,
                        'dynamic_tax_rates': dynamic_tax_rates
                    },
                ],
                'mode': 'subscription',
                'success_url': DOMAIN_URL +
                f"/{new_session.language}/app/parent/subscriptions" +
                '?success=true&session_id={CHECKOUT_SESSION_ID}',
                'cancel_url': DOMAIN_URL +
                f"/{new_session.language}/app/parent/subscriptions?canceled=true",
            }

            # Handle discount code
            if new_session.discount_code:
                checkout_session_params['discounts'] = [{
                    'coupon': new_session.discount_code,
                }]
            else:
                checkout_session_params['allow_promotion_codes'] = True

            # Create the checkout session
            checkout_session = stripe.checkout.Session.create(**checkout_session_params)

            return {"checkout_session_id": checkout_session.id, "checkout_session_url": checkout_session.url}
        else:
            raise CustomException("Not authorized!", "No corresponding Account found",
                                  status_code=401, error_type="permission_error")
    else:
        raise CustomException("Not authorized!", "No parent_account_public_id in token",
                              status_code=401, error_type="permission_error")
