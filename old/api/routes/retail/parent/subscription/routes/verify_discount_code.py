from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depen<PERSON>, Header, Request
from sqlalchemy.orm import Session
from db.models import DiscountCode, Account
from db.database import get_db
from utils.exceptions import CustomException
from ..models.request import VerifyDiscountCodeRequest
from utils.auth import has_valid_parent_token
from loguru import logger
from datetime import datetime, timedelta
router = APIRouter()
base_url = "/api/parent/subscription"

@router.post(base_url + "/verify-discount-code")
def verify_discount_code(
    discount_request: VerifyDiscountCodeRequest,
     request: Request,
    x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token),
    db: Session = Depends(get_db),
   
):
    parent_public_id = request.state.parent_uuid

    # Check if the discount code is still valid
    if discount_request.discount_code == "START":
        relevant_account = db.query(Account).filter(
            Account.account_cognito_id == parent_public_id
        ).first()

        if not relevant_account:
            raise CustomException(
                status_code=400, log_message="Invalid discount code", return_message="Invalid discount code", error_type="invalid_discount_code")
        
        # Check if the account is older than 10 days (gives them a grace period given takes 9 days for entire email sequence), if so we trigger expired_discount_code
        if (datetime.now() - relevant_account.created_at) > timedelta(days=10):
            logger.info(f"Discount code expired {datetime.now() - relevant_account.created_at} days ago for account {parent_public_id}")
            raise CustomException(
                status_code=400, log_message="Discount code expired", return_message="Discount code expired", error_type="expired_discount_code")


    relevant_discounts = db.query(DiscountCode).filter(
        DiscountCode.is_active == True,
        DiscountCode.public_code == discount_request.discount_code
    ).all()

    if not relevant_discounts:
        raise CustomException(
            status_code=400, log_message="Invalid discount code", return_message="Invalid discount code", error_type="invalid_discount_code")

    monthly_discount_code = None
    yearly_discount_code = None

    for discount in relevant_discounts:
        if discount.stripe_id.endswith("monthly"):
            monthly_discount_code = discount.stripe_id
        elif discount.stripe_id.endswith("yearly"):
            yearly_discount_code = discount.stripe_id

    if not monthly_discount_code or not yearly_discount_code:
        raise CustomException(
            status_code=400, log_message="Invalid discount code", return_message="Invalid discount code", error_type="invalid_discount_code")

    return {
        "discount_code": {
            "monthly": monthly_discount_code,
            "yearly": yearly_discount_code
        }
    }
