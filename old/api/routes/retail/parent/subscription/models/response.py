from pydantic import BaseModel
from typing import List, Optional

class CreateCheckoutSessionResponse(BaseModel):
    checkout_session_id: str
    checkout_session_url: str

class CreatePortalSessionResponse(BaseModel):
    portal_session_url: str

class SubscriptionDetails(BaseModel):
    monthly_price: float
    yearly_price: float

class YearOption(BaseModel):
    year_name: str
    year_public_id: str
    system: str
    active_subscription: bool
    subscription_details: SubscriptionDetails

class SubscriptionOptionsResponse(BaseModel):
    subscription_options: List[YearOption]
