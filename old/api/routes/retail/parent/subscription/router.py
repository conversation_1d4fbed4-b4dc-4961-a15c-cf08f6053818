from fastapi import APIRouter
from .routes import create_checkout_session, create_portal_session, get_subscription_options,webhook
from .routes.verify_discount_code import router as verify_discount_router
    
router = APIRouter()

# Include routes from all the files in this directory
router.include_router(create_checkout_session.router)
router.include_router(create_portal_session.router)
router.include_router(get_subscription_options.router)
router.include_router(webhook.router)
router.include_router(verify_discount_router)
