from pydantic import BaseModel
from typing import List, Optional

class AddChildAccountRequest(BaseModel):
    year_public_id: str
    child_account_name: str
    child_account_email: str
    child_account_pin: str

class DeleteChildAccountRequest(BaseModel):
    child_account_public_id: str

class SubmitExerciseRequest(BaseModel):
    exercise_public_id: str
    answer: str | List[str]

class UpdateChildAccountRequest(BaseModel):
    child_account_public_id: str
    child_account_name: Optional[str]
    child_account_pin: Optional[str]
    child_account_email: Optional[str]
    year_public_id: Optional[str]


class UpdateIntroStatusRequest(BaseModel):
    show_intro: bool