from pydantic import BaseModel
from typing import List, Optional
from api.routes.retail.models.response import BaseResponse, Exercise, Lesson, Chapter, Course


class BaseResponse(BaseModel):
    message: str


class AddChildAccountResponse(BaseResponse):
    message: str
    child_account_public_id: str


class DeleteChildAccountResponse(BaseResponse):
    pass


class ExerciseStatistic(BaseModel):
    date: str
    total_correct: int
    total_incorrect: int


class GetChildAccountResponse(BaseModel):
    exercises_statistics: List[ExerciseStatistic]

# ***** GetChildrenAccountsResponse *****


class RecentChildStatistics(BaseModel):
    completed_lessons_count: int
    completed_exercises_count: int
    correct_exercises_share: float


class ChapterStatistics(BaseModel):
    chapter_public_id: str
    chapter_title: str
    completed_lessons: List[str]
    completed_lessons_count: int
    total_exercise_count: int
    completed_exercises_count: int
    correct_exercises_share: float
    total_lesson_count: int


class ChildStatistics(BaseModel):
    recent: RecentChildStatistics
    by_chapter: List[ChapterStatistics]


class Child(BaseModel):
    child_account_public_id: str
    child_account_name: str
    child_account_login_alias: Optional[str]
    child_account_email: Optional[str]
    year_public_id: str
    year_name: str
    child_account_pin: str
    statistics: dict
    recent_events: Optional[List[dict]]
    activity_calendar: Optional[List[dict]]
    has_valid_subscription: Optional[bool]
    relevant_chapters: Optional[List[dict]]
    # TODO Specify the type of statistics
    # statistics: ChildStatistics
    latest_lesson: Lesson


class SchoolYear(BaseModel):
    year_public_id: str
    year_name: str


class GetChildrenAccountsResponse(BaseModel):
    show_intro: bool
    mock_data: Optional[dict]
    children: List[Child]
    school_years: List[SchoolYear]
    trial: Optional[dict]


# GetLessonResponse
class CoursOverview(BaseModel):
    course_title: str
    course_public_id: str

    chapter_title: str
    chapter_public_id: str

    lesson_title: str
    lesson_public_id: str


class GetLessonResponse(BaseModel):
    subscription_info: Optional[dict]
    subscription_status: str
    # year_name: Optional[str]
    # year_public_id: Optional[str]
    # course_title: Optional[str]
    # course_public_id: Optional[str]
    course_overview: Optional[CoursOverview]
    lesson: Optional[Lesson]
    has_completed_video: Optional[bool]
    exercises: Optional[List[dict]]
    trial: Optional[dict]


# GetPaginatedExercisesResponse
class GetPaginatedExercisesResponse(BaseModel):
    subscription_status: str
    exercises: Optional[List[Exercise]]
    has_more: Optional[bool]

# GetYearResponse


class GetYearResponse(BaseModel):
    year_public_id: str
    year_name: str
    courses: List[Course]


class Subscription(BaseModel):
    subscription_association_public_id: str
    subscription_public_id: str
    relevant_year: SchoolYear


class LessonData(BaseModel):
    lesson_public_id: str
    lesson_title: str


class ChapterData(BaseModel):
    chapter_public_id: str
    chapter_title: str
    course_public_id: str
    chapter_lessons: List[LessonData]

class CourseData(BaseModel):
    course_public_id: str
    course_title: str
    course_chapters: List[ChapterData]


class YearData(BaseModel):
    year_public_id: str
    year_name: str
    courses: List[CourseData]
class GetYearsResponse(BaseModel):
    years: List[YearData]


# SubmitExerciseResponse


class SubmitExerciseResponse(BaseModel):
    message: str
    correctly_answered: bool
    recently_submitted: bool
    correct_answer: str | List[str]
    solution_video_url: str


class UpdateChildAccountResponse(BaseResponse):
    message: str


class UpdateIntroStatusResponse(BaseResponse):
    pass


class GetChapterResponse(BaseModel):
    chapter: Chapter


class GetCourseResponse(BaseModel):
    course_title: str
    course_description: Optional[str]
    course_public_id: str
    course_chapters: List[Chapter]
    account_meta_data: dict


class GetCoursesResponse(BaseModel):
    courses: List[Course]


# GetLanguage

class GetLanguageResponse(BaseModel):
    language: str
