from fastapi import APIRouter
from .routes import add_child_account, delete_child_account, get_chapter, get_child_account,get_children_accounts, get_course,get_courses, get_lesson,get_paginated_exercises, get_year,get_years,submit_exercise, update_child_account, update_intro_status, verify_child_link

router = APIRouter()

# Include routes from all the files in this directory
router.include_router(add_child_account.router)
router.include_router(delete_child_account.router)
router.include_router(get_chapter.router)
router.include_router(get_child_account.router)
router.include_router(get_children_accounts.router)
router.include_router(get_course.router)
router.include_router(get_courses.router)
router.include_router(get_lesson.router)
router.include_router(get_paginated_exercises.router)
router.include_router(get_year.router)
router.include_router(get_years.router)
router.include_router(submit_exercise.router)
router.include_router(update_child_account.router)
router.include_router(update_intro_status.router)
router.include_router(verify_child_link.router)