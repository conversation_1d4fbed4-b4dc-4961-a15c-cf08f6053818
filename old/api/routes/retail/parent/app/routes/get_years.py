from fastapi import API<PERSON><PERSON><PERSON>, Depends, Header, Request
from sqlalchemy.orm import Session
from sqlalchemy import select
from db.database import get_db
from db.models import Account, Year, Course, Chapter, Lesson
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from ..models.response import GetYearsResponse

router = APIRouter()
base_url = "/api/app/parent/app"

@router.get(base_url + '/get-years', status_code=200, response_model=GetYearsResponse)
def get_courses(request: Request, x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token), db: Session = Depends(get_db)):
    parent_public_id = request.state.parent_uuid
    if not parent_public_id:
        raise CustomException("Not authorized!", "No parent_account_public_id in token",
                              status_code=401, error_type="permission_error")

    account = db.query(Account).filter_by(account_cognito_id=parent_public_id).first()
    if not account:
        raise CustomException("Not authorized!", "No corresponding Account found",
                              status_code=401, error_type="permission_error")

    # Perform a single join query to fetch all required data
    query = select(Year, Course, Chapter, Lesson).join(
        Course, Year.relevant_courses
    ).join(
        Chapter, Course.chapters
    ).join(
        Lesson, Chapter.relevant_lessons
    ).order_by(Year.year_id, Course.course_id, Chapter.chapter_id, Lesson.lesson_id)

    result = db.execute(query).fetchall()

    # Process the query results
    years = []
    current_year = None
    current_course = None
    current_chapter = None
    # Iterate through each row in the result set
    for row in result:
        year, course, chapter, lesson = row

        # Check if we are still on the same year
        if current_year is None or year.year_id != current_year['year_id']:
            # If we have a current year, append it to the years list
            if current_year is not None:
                years.append(current_year)
            # Create a new year entry
            current_year = {
                'year_id': year.year_id,
                'year_public_id': year.year_public_id,
                'year_name': year.year_name,
                'courses': []  # Initialize courses list for the new year
            }
            # Reset current course and chapter
            current_course = None
            current_chapter = None

        # Check if we are still on the same course
        if current_course is None or course.course_id != current_course['course_id']:
            # Create a new course entry
            current_course = {
                'course_id': course.course_id,
                'course_public_id': course.course_public_id,
                'course_title': course.course_title,
                'course_chapters': []  # Initialize chapters list for the new course
            }
            # Append the new course to the current year's courses
            current_year['courses'].append(current_course)
            # Reset current chapter
            current_chapter = None

        # Check if we are still on the same chapter
        if current_chapter is None or chapter.chapter_id != current_chapter['chapter_id']:
            # Create a new chapter entry
            current_chapter = {
                'chapter_id': chapter.chapter_id,
                'course_public_id': course.course_public_id,
                'chapter_public_id': chapter.chapter_public_id,
                'chapter_title': chapter.chapter_title,
                'chapter_lessons': []  # Initialize lessons list for the new chapter
            }
            # Append the new chapter to the current course's chapters
            current_course['course_chapters'].append(current_chapter)

        # Append the lesson details to the current chapter's lessons
        current_chapter['chapter_lessons'].append({
            'lesson_id': lesson.lesson_id,
            'lesson_public_id': lesson.lesson_public_id,
            'lesson_title': lesson.lesson_title
        })

    if current_year is not None:
        years.append(current_year)

    return GetYearsResponse(years=years)
