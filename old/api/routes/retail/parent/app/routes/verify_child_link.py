from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON>pen<PERSON>, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account, ChildAccount, ParentChildAssignment, Trial
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from pydantic import BaseModel

router = APIRouter()
base_url = "/api/app/parent/app"

class VerifyChildLinkRequest(BaseModel):
    verification_code: str
    child_email: str

class VerifyChildLinkResponse(BaseModel):
    message: str
    child_account_public_id: str

@router.post(base_url + '/verify-child-link', status_code=200, response_model=VerifyChildLinkResponse)
def verify_child_link(request: Request, link_data: VerifyChildLinkRequest, x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token), db: Session = Depends(get_db)):
    parent_public_id = request.state.parent_uuid
    if not parent_public_id:
        raise CustomException("Not authorized!", "No parent_account_public_id in token",
                            status_code=401, error_type="permission_error")

    relevant_account = db.query(Account).filter_by(
        account_cognito_id=parent_public_id).first()
    if not relevant_account:
        raise CustomException("Not authorized!", "No corresponding Account found",
                            status_code=401, error_type="permission_error")

    # Get the child account and assignment
    relevant_child_account = db.query(ChildAccount).filter_by(
        child_account_email=link_data.child_email).first()
    if not relevant_child_account:
        raise CustomException("Child account not found!", "No child account found with this email",
                            status_code=404, error_type="child_not_found")

    # Check if child is already linked
    if relevant_child_account.child_account_parent_id:
        raise CustomException("Child account already linked!", "This child account is already linked to a parent",
                            status_code=400, error_type="child_already_linked")

    # Get the assignment
    assignment = db.query(ParentChildAssignment).filter_by(
        parent_email=relevant_account.account_email,
        relevant_child_account_id=relevant_child_account.child_account_id
    ).first()
    if not assignment:
        raise CustomException("No pending link request!", "No pending link request found for this child",
                            status_code=404, error_type="no_link_request")

    # Verify the code
    if assignment.verification_code != link_data.verification_code:
        raise CustomException("Invalid verification code!", "The verification code is incorrect",
                            status_code=400, error_type="invalid_code")

    # Link the accounts
    relevant_child_account.child_account_parent_id = relevant_account.account_id
    relevant_child_account.is_verified = True

    # Go through the trials and find the CHild entry, add the parent to the trial entry
    relevant_trials = db.query(Trial).filter_by(trial_child_account_id=relevant_child_account.child_account_id).all()
    for trial in relevant_trials:
        trial.trial_parent_account_id = relevant_account.account_id
    # Delete the assignment
    db.delete(assignment)
    db.commit()

    return {
        "message": "Child account linked successfully!",
        "child_account_public_id": relevant_child_account.child_account_public_id
    } 