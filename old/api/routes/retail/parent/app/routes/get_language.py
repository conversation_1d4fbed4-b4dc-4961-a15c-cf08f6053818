
from fastapi import API<PERSON><PERSON><PERSON>, Depends, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from ..models.response import GetLanguageResponse

router = APIRouter()
base_url = "/api/app/parent/app"

@router.get(base_url + '/get-language', status_code=200, response_model=GetLanguageResponse)
def get_courses(request: Request, x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token), db: Session = Depends(get_db)):
    parent_public_id = request.state.parent_uuid
    relevant_parent = db.query(Account).filter_by(account_cognito_id=parent_public_id).first()
    if relevant_parent:
        return {"language": relevant_parent.account_language}
    else:
        raise CustomException ("Not authorized!", "No corresponding Account found", status_code=401, error_type="permission_error")
   