
from fastapi import API<PERSON><PERSON><PERSON>, Depends, Header, Request
from sqlalchemy.orm import Session
from loguru import logger
from db.database import get_db
from api.routes.retail.utils import prepare_lesson
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from db.models import Account
from ..models.response import GetLessonResponse

router = APIRouter()
base_url = "/api/app/parent/app"

EXERCISE_PAGINATION_SIZE = 10

# Send child account with as well
@router.get(base_url + '/get-lesson', status_code=200, response_model=GetLessonResponse)
def get_lesson(lesson_public_id: str, request: Request, x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token), db: Session = Depends(get_db)):
    parent_account_public_id = request.state.parent_uuid
    if parent_account_public_id:
        # Finding the relevant account and lesson
        relevant_parent_account = db.query(Account).filter_by(
            account_cognito_id=parent_account_public_id).first()

        if relevant_parent_account:
            return prepare_lesson(lesson_public_id, 'parent', relevant_parent_account, db, EXERCISE_PAGINATION_SIZE)
        
        else:
            raise CustomException ("Not authorized!", "No corresponding Account found", status_code=401, error_type="permission_error")
    else:
        raise CustomException ("Not authorized!", "No parent_account_public_id in token", status_code=401, error_type="permission_error")