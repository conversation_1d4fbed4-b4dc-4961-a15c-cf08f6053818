
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Exercise
from api.utils.media_utils import create_signed_video_url
import json
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from ..models.request import SubmitExerciseRequest
from ..models.response import SubmitExerciseResponse
import re
router = APIRouter()
base_url = "/api/app/parent/app"


def clean_answer(answer):
    # We get rid of any white space also within the string as well as * symbols in front of variable names so 2*x becomes2x 
    answer = answer.strip()
    answer = re.sub(r'\s*\*\s*', '', answer.replace(" ", ""))
    return answer


@router.post(base_url + '/submit-exercise', status_code=200, response_model=SubmitExerciseResponse)
def submit_exercise(request: Request, submitted_exercise: SubmitExerciseRequest, x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token), db: Session = Depends(get_db)):
    parent_public_id = request.state.parent_uuid

    if parent_public_id:
        # The answer will either be a simple string in case of a simple answer
        # In case of a true/false answer, it will be a string
        # In case of a multiple choice answer, it will be the uuid of the respective answer
        # firebase_id = get_uuid(x_auth_token)
        relevant_exercise = db.query(Exercise).filter_by(
            exercise_public_id=submitted_exercise.exercise_public_id).first()

        if relevant_exercise.exercise_answer_type == "mc_simple" or relevant_exercise.exercise_answer_type == "mc_multi":
            # Check if the time difference is greater than 24 hours (86400 seconds)
            correctly_answered = True
            # We need to check if all the elements of the submitted answer are in the correct answer
            # Likely the list will be the same but no sure if we always get them in the same order

            # Need to parse the correct answer first, which will then turn the string in the db into a list
            # correct_answer = relevant_exercise.exercise_correct_answer
            if relevant_exercise.exercise_answer_type == "mc_simple":
                correct_answer = relevant_exercise.exercise_correct_answer[0]
                if correct_answer != submitted_exercise.answer:
                    correctly_answered = False
                correct_answer = relevant_exercise.exercise_correct_answer

            else:
                correct_answer = relevant_exercise.exercise_correct_answer

                for option_public_id in correct_answer:
                    # First check if any is missing
                    if option_public_id not in submitted_exercise.answer:
                        correctly_answered = False
                        break
                # Then check if any is extra
                if correctly_answered and (len(submitted_exercise.answer) != len(correct_answer)):
                    correctly_answered = False

            return {
                "message": "Exercise submitted successfully!",
                "correctly_answered": correctly_answered,
                "recently_submitted": False,
                "correct_answer": correct_answer,
                "solution_video_url": create_signed_video_url(relevant_exercise.exercise_solution_video_public_id, 'exercise')
            }
        # If it is a input or a true_false answer, we just check if the answer is correct, hence if the strings match
        else:
            correct_answer= relevant_exercise.exercise_correct_answer

            correctly_answered = False

            if clean_answer(submitted_exercise.answer) == clean_answer(correct_answer):
                correctly_answered = True

            return {
                "message": "Exercise submitted successfully!",
                "correctly_answered": correctly_answered,
                "recently_submitted": False,
                "correct_answer": correct_answer,
                "solution_video_url": create_signed_video_url(relevant_exercise.exercise_solution_video_public_id, 'exercise')
            }
    else:
        raise CustomException("Not authorized!", "No parent_account_public_id in token",
                              status_code=401, error_type="permission_error")

