import os
from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON>pen<PERSON>, <PERSON><PERSON>, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account, Year, ChildAccount, Trial, ParentChildAssignment
import uuid
import random
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from ..models.request import AddChildAccountRequest
from ..models.response import AddChildAccountResponse
from api.routes.retail.child.auth.utils.utils import send_verification_code

router = APIRouter()
base_url = "/api/app/parent/app"

@router.post(base_url + '/add-child-account', status_code=200, response_model=AddChildAccountResponse)
def add_child_account(request: Request, new_child: AddChildAccountRequest, x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token), db: Session = Depends(get_db)):
    parent_public_id = request.state.parent_uuid
    if parent_public_id:
        relevant_account = db.query(Account).filter_by(
            account_cognito_id=parent_public_id).first()

        if relevant_account:
            # Check if the email is already in use
            relevant_child_account = db.query(ChildAccount).filter_by(
                child_account_email=new_child.child_account_email).first()
            if relevant_child_account:
                # Check if child is already linked to a parent
                if relevant_child_account.child_account_parent_id:
                    raise CustomException(error_type="child_already_linked", 
                                       return_message="This child account is already linked to another parent!", 
                                       log_message="Child account already linked to parent",
                                       status_code=403)
                
                # Check if we already have an entry for ParentChildAssignment, if so re-use that code
                existing_assignment = db.query(ParentChildAssignment).filter_by(
                    parent_email=relevant_account.account_email,
                    relevant_child_account_id=relevant_child_account.child_account_id
                ).first()
                if existing_assignment:
                    verification_code = existing_assignment.verification_code
                else:
                    # Generate verification code for linking
                    verification_code = str(random.randint(100000, 999999))
                
                # Create parent-child assignment
                new_assignment = ParentChildAssignment(
                    parent_email=relevant_account.account_email,
                    relevant_child_account_id=relevant_child_account.child_account_id,
                    verification_code=verification_code,
                    language=relevant_account.account_language
                )
                db.add(new_assignment)
                db.commit()
                
                # Send verification code to child's email
                send_verification_code(relevant_child_account.child_account_email,
                                    verification_code, 
                                    relevant_account.account_language)
                
                raise CustomException(error_type="child_email_exists", 
                                   return_message="Child account exists! A verification code has been sent to the child's email.", 
                                   log_message="Child account exists, verification code sent",
                                   status_code=409)

            relevant_trial = db.query(Trial).filter_by(trial_account_id=relevant_account.account_id).first()
            relevant_year = db.query(Year).filter_by(
                year_public_id=new_child.year_public_id).first()
            child_account_public_id = str(uuid.uuid4())
            new_child_account = ChildAccount(child_account_public_id=child_account_public_id,
                                            child_account_name=new_child.child_account_name,
                                            child_account_parent_id=relevant_account.account_id,
                                            child_account_year_id=relevant_year.year_id,
                                            child_account_email=new_child.child_account_email,
                                            child_account_pin=new_child.child_account_pin,
                                            child_account_language=relevant_account.account_language,
                                            is_verified=True
                                            )
            db.add(new_child_account)
            db.flush()
            if relevant_trial:
                relevant_trial.trial_child_account_id = new_child_account.child_account_id 
            db.commit()
            return {"message": "Child account added successfully!", "child_account_public_id": child_account_public_id}

        else:
            raise CustomException("Not authorized!", "No corresponding Account found",
                                status_code=401, error_type="permission_error")
    else:
        raise CustomException("Not authorized!", "No parent_account_public_id in token",
                            status_code=401, error_type="permission_error")
