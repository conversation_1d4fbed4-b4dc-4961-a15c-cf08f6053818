
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depen<PERSON>, Header, Request
from sqlalchemy.orm import Session
from sqlalchemy import desc
from db.database import get_db
from db.models import Account, ChildAccount, ChildAccountExerciseAssociation
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from ..models.response import GetChildAccountResponse

router = APIRouter()
base_url = "/api/app/parent/app"

# Route that gets the account details for a specific Child account
@router.get(base_url + '/get-child-account', status_code=200, response_model=GetChildAccountResponse)
def get_child_account(request: Request, child_account_public_id: str, x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token),db: Session = Depends(get_db)):
    parent_public_id = request.state.parent_uuid
    if parent_public_id:
        relevant_account = db.query(Account).filter(
            Account.account_cognito_id == parent_public_id).first()

        if relevant_account:
            relevant_child_account = db.query(ChildAccount).filter(
                ChildAccount.child_account_public_id == child_account_public_id).first()

            if relevant_child_account:

                relevant_exercises = db.query(ChildAccountExerciseAssociation).filter_by(
                    relevant_child_account_id=relevant_child_account.child_account_id).order_by(desc(ChildAccountExerciseAssociation.created_at)).all()

                # Iterate over the excercises and groub them by the day they were created
                exercises_by_day = {}
                dates = set([entry.created_at.date()
                            for entry in relevant_exercises])
                for date in dates:
                    exercises_by_day[date] = [
                        entry for entry in relevant_exercises if entry.created_at.date() == date]

                statistics = []
                # iterate over the object keys of exercises_by_day
                for key in exercises_by_day:
                    # For a given date we have a list with all the exercises
                    correct_count = len(
                        [exercise for exercise in exercises_by_day[key] if exercise.association_correctly_answered == True])
                    incorrect_count = len(
                        exercises_by_day[key]) - correct_count

                    statistics.append({
                        "date": key,
                        "total_correct": correct_count,
                        "total_incorrect": incorrect_count
                    })

                return {
                    "exercises_statistics": statistics
                }
            else:
                raise CustomException("Not authorized!", "No corresponding ChildAccount found",
                                    status_code=401, error_type="permission_error")

        else:
            raise CustomException("Not authorized!", "No corresponding Account found",
                                  status_code=401, error_type="permission_error")
    else:
        raise CustomException("Not authorized!", "No parent_account_public_id in token",
                              status_code=401, error_type="permission_error")
