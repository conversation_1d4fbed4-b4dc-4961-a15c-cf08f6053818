
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from ..models.response import GetCoursesResponse

router = APIRouter()
base_url = "/api/app/parent/app"

@router.get(base_url + '/get-courses', status_code=200, response_model=GetCoursesResponse)
def get_courses(request: Request, x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token), db: Session = Depends(get_db)):
    # parent_public_id = request.state.parent_uuid
    # if parent_public_id:
    #     relevant_account = db.query(Account).filter_by(
    #         account_cognito_id=parent_public_id).first()
    #     if relevant_account:
    #         relevant_courses = relevant_account.courses
    #         courses = [{
    #             "course_public_id": course.course_public_id,
    #             "course_title": course.course_title,
    #             "course_description": course.course_description} for course in relevant_courses]

    #         return {"courses": courses}

    #     else:
    #         raise CustomException("Not authorized!", "No corresponding Account found",
    #                               status_code=401, error_type="permission_error")
    # else:
    #     raise CustomException("Not authorized!", "No parent_account_public_id in token",
    #                           status_code=401, error_type="permission_error")
    pass