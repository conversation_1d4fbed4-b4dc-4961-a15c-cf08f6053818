
from fastapi import API<PERSON><PERSON><PERSON>, Depends, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account, Year,  ChildAccount
from ..models.request import UpdateIntroStatusRequest
from ..models.response import UpdateIntroStatusResponse
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException

router = APIRouter()
base_url = "/api/app/parent/app"

@router.post(base_url + '/update-intro-status', status_code=200, response_model=UpdateIntroStatusResponse)
def update_intro_status(request: Request, status: UpdateIntroStatusRequest, x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token), db: Session = Depends(get_db)):
    parent_public_id = request.state.parent_uuid
    if parent_public_id:
        parent = db.query(Account).filter(
            Account.account_cognito_id == parent_public_id).first()

        if parent:
            parent.show_intro = status.show_intro
            db.commit()
            return {"message": "Intro status updated successfully"}
        else:
            raise CustomException("Parent not found", "Parent not found",
                                  status_code=403, log_level="INFO", error_type="internal_error")
    else:
        raise CustomException("Parent not found", "Parent not found",
                              status_code=403, log_level="INFO", error_type="internal_error")