from fastapi import API<PERSON><PERSON><PERSON>, Depen<PERSON>, Header, Request
from sqlalchemy.orm import Session, joinedload
from db.database import get_db
from db.models import Account, ChildAccount,  Year, ChildEventType, ChildEvent, Lesson, Trial, Chapter, ActiveSubscription, SubscriptionOption  
from api.utils.analytics_utils import get_recent_statistics, get_chapter_statistics
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from ..models.response import GetChildrenAccountsResponse
from sqlalchemy import desc
import json
from api.utils.media_utils import create_signed_video_url
from api.routes.retail.utils import generate_activity_calendar
from api.routes.retail.utils import prepare_recent_events
from sqlalchemy import asc
from datetime import datetime
from api.utils.trial_utils import check_trial_status

router = APIRouter()
base_url = "/api/app/parent/app"


@router.get(base_url + '/get-children-accounts', status_code=200, response_model=GetChildrenAccountsResponse)
def get_children_accounts(request: Request, x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token), db: Session = Depends(get_db)):
    parent_public_id = request.state.parent_uuid
    if parent_public_id:
        relevant_account = db.query(Account).filter(
            Account.account_cognito_id == parent_public_id).first()
        



        if relevant_account:
            # Query to get active subscriptions with their related year_id for this account
            relevant_subscriptions = db.query(
                ActiveSubscription, 
                SubscriptionOption.year_id
            ).join(
                SubscriptionOption, 
                ActiveSubscription.subscription_option_id == SubscriptionOption.subscription_option_id
            ).filter(
                ActiveSubscription.account_id == relevant_account.account_id,
                ActiveSubscription.status == 'active'
            ).all()
            
            relevant_children_accounts = relevant_account.children_accounts
            children = []
            for child_account in relevant_children_accounts:
                relevant_year = child_account.relevant_year
                relevant_subject = relevant_year.relevant_subjects[0]
                completed_lessons_count, completed_exercises_count, correct_exercises_share = get_recent_statistics(
                    db, child_account)

                chapter_statistics = get_chapter_statistics(
                    db, child_account, relevant_subject)
                # iterate over the object keys of exercises_by_day
                relevant_event_type = db.query(ChildEventType).filter_by(
                    child_event_type_name='opened_lesson').first()

                relevant_event = db.query(ChildEvent).filter_by(child_event_type_id=relevant_event_type.child_event_type_id,
                                                                relevant_child_account_id=child_account.child_account_id).order_by(desc(ChildEvent.created_at)).first()

                latest_lesson = None

                if relevant_event == None:
                    # We set the lesson the the year's first lesson
                    temp_subject = relevant_subject
                    temp_chapters = temp_subject.chapters

                    chapter_ids = sorted(
                        [chapter.chapter_id for chapter in temp_chapters if chapter.is_published])
                    first_chapter = [
                        chapter for chapter in temp_chapters if chapter.chapter_id == chapter_ids[0]][0]

                    relevant_lesson = db.query(Lesson).filter_by(
                        relevant_chapter_id=first_chapter.chapter_id).order_by(asc(Lesson.lesson_ordering)).first()

                    relevant_chapter = first_chapter

                    latest_lesson = {
                        "lesson_public_id": relevant_lesson.lesson_public_id,
                        "lesson_title": relevant_lesson.lesson_title,
                        "lesson_description": relevant_lesson.lesson_description,
                        "chapter_public_id": relevant_chapter.chapter_public_id,
                        "chapter_title": relevant_chapter.chapter_title,
                        "subject_public_id": temp_subject.subject_public_id,
                        "chapter_icon_id": relevant_chapter.chapter_icon_id,
                        "lesson_video_url": create_signed_video_url(relevant_lesson.lesson_video_id, 'lecture')
                    }

                else:
                    relevant_data = json.loads(relevant_event.child_event_data)
                    # print(f"Debug: relevant_data = {relevant_data}")
                    # Neede to also send all the other lesson data
                    relevant_lesson = db.query(Lesson).filter_by(
                        lesson_public_id=relevant_data['lesson_public_id']).first()
                    # print(f"Debug: relevant_lesson = {relevant_lesson}")

                    relevant_chapter = relevant_lesson.relevant_chapter
                    # print(f"Debug: relevant_chapter = {relevant_chapter}")

                    temp_relevant_course = relevant_chapter.relevant_course

                    latest_lesson = {
                        "lesson_public_id": relevant_lesson.lesson_public_id,
                        "lesson_title": relevant_lesson.lesson_title,
                        "lesson_description": relevant_lesson.lesson_description,
                        "chapter_public_id": relevant_chapter.chapter_public_id,
                        "chapter_title": relevant_chapter.chapter_title,
                        "course_public_id": temp_relevant_course.course_public_id,
                        "chapter_icon_id": relevant_chapter.chapter_icon_id,
                        "lesson_video_url": create_signed_video_url(relevant_lesson.lesson_video_id, 'lecture')
                    }

                # Check if the parent has a valid subscription for this specific year
                has_valid_subscription = False
                if relevant_subscriptions:
                    for subscription, year_id in relevant_subscriptions:
                        if year_id == relevant_year.year_id:
                            has_valid_subscription = True
                            break

                activity_calendar = generate_activity_calendar(
                    child_account, db)
                recent_events = prepare_recent_events(child_account, db)

                relevant_chapters = db.query(Chapter).filter_by(relevant_course_id=relevant_course.course_id).filter_by(
                    is_published=True).order_by(asc(Chapter.chapter_ordering)).all()

                # Also Fetch the lessons for each chapter in an efficient manner
                relevant_lessons = db.query(Lesson).filter(Lesson.relevant_chapter_id.in_(
                    [chapter.chapter_id for chapter in relevant_chapters])).filter_by(is_published=True).order_by(asc(Lesson.lesson_ordering)).all()

                # Create a dictionary to map chapter_id to its lessons
                chapter_lessons_map = {}
                for lesson in relevant_lessons:
                    if lesson.relevant_chapter_id not in chapter_lessons_map:
                        chapter_lessons_map[lesson.relevant_chapter_id] = []
                    chapter_lessons_map[lesson.relevant_chapter_id].append({
                        "lesson_public_id": lesson.lesson_public_id,
                        "lesson_title": lesson.lesson_title,
                        "lesson_description": lesson.lesson_description,
                        "lesson_number": lesson.lesson_ordering,
                        "is_free": lesson.is_free
                    })

                children.append({
                    "child_account_public_id": child_account.child_account_public_id,
                    "child_account_name": child_account.child_account_name,
                    "child_account_email": child_account.child_account_email,
                    # "child_account_login_alias": child_account.child_account_login_alias,
                    "year_public_id": child_account.relevant_year.year_public_id,
                    "year_name": child_account.relevant_year.year_name,
                    "child_account_pin": child_account.child_account_pin,
                    "has_valid_subscription": has_valid_subscription,
                    "statistics": {
                        "recent": {
                            "completed_lessons_count": completed_lessons_count,
                            "completed_exercises_count": completed_exercises_count,
                            "correct_exercises_share": correct_exercises_share
                        },
                        "by_chapter": chapter_statistics,
                    },
                    "recent_events": recent_events,
                    "latest_lesson": latest_lesson,
                    "relevant_chapters": [
                        {
                            "chapter_public_id": chapter.chapter_public_id,
                            "chapter_title": chapter.chapter_title,
                            "is_free": chapter.is_free,
                            "course_public_id": relevant_course.course_public_id,
                            "chapter_lessons": chapter_lessons_map.get(chapter.chapter_id, [])

                        } for chapter in relevant_chapters],
                    "activity_calendar": activity_calendar
                })

            mock_data = {
            }
            if children == []:
                relevant_year = db.query(Year).first()
                relevant_course = relevant_year.relevant_courses[0]
                first_chapter = db.query(Chapter).filter_by(relevant_course_id=relevant_course.course_id).filter_by(
                    is_published=True).order_by(asc(Chapter.chapter_ordering)).first()

                relevant_lesson = db.query(Lesson).filter_by(
                    relevant_chapter_id=first_chapter.chapter_id).order_by(asc(Lesson.lesson_ordering)).first()

                relevant_chapter = first_chapter

                mock_data["latest_lesson"] = {
                    "lesson_public_id": relevant_lesson.lesson_public_id,
                    "lesson_title": relevant_lesson.lesson_title,
                    "lesson_description": relevant_lesson.lesson_description,
                    "chapter_public_id": relevant_chapter.chapter_public_id,
                    "chapter_title": relevant_chapter.chapter_title,
                    "course_public_id": relevant_course.course_public_id,
                    "chapter_icon_id": relevant_chapter.chapter_icon_id,
                    "lesson_video_url": create_signed_video_url(relevant_lesson.lesson_video_id, 'lecture'),

                }
                relevant_chapters = db.query(Chapter).filter_by(relevant_course_id=relevant_course.course_id).filter_by(
                    is_published=True).order_by(asc(Chapter.chapter_ordering)).all()
        
                # Also Fetch the lessons for each chapter in an efficient manner
                relevant_lessons = db.query(Lesson).filter(Lesson.relevant_chapter_id.in_(
                    [chapter.chapter_id for chapter in relevant_chapters])).filter_by(is_published=True).order_by(asc(Lesson.lesson_ordering)).all()

                # Create a dictionary to map chapter_id to its lessons
                chapter_lessons_map = {}
                for lesson in relevant_lessons:
                    if lesson.relevant_chapter_id not in chapter_lessons_map:
                        chapter_lessons_map[lesson.relevant_chapter_id] = []
                    chapter_lessons_map[lesson.relevant_chapter_id].append({
                        "lesson_public_id": lesson.lesson_public_id,
                        "lesson_title": lesson.lesson_title,
                        "lesson_description": lesson.lesson_description,
                        "lesson_number": lesson.lesson_ordering,
                        "is_free": lesson.is_free
                    })
                mock_data["relevant_chapters"] = [
                    {
                        "chapter_public_id": chapter.chapter_public_id,
                        "chapter_title": chapter.chapter_title,
                        "is_free": chapter.is_free,
                        "course_public_id": relevant_course.course_public_id,
                        "chapter_lessons": chapter_lessons_map.get(chapter.chapter_id, [])
                    } for chapter in relevant_chapters]

                mock_child_account = db.query(ChildAccount).filter_by(
                    child_account_public_id="mock_child_account").first()

                mock_data["chapter_statistics"] = get_chapter_statistics(
                    db, mock_child_account, relevant_course)

            response = {
                "children": children,
                "show_intro": relevant_account.show_intro,
                "school_years":
                    [{"year_public_id": year.year_public_id, "year_name": year.year_name}
                        for year in db.query(Year).group_by(Year.year_id).all()]
            }

            if children == []:
                response['mock_data'] = mock_data
            # Query the database for a Trial record that matches the account_id of the relevant account

            relevant_trial = check_trial_status(
                relevant_account, db, account_type='parent')
            if relevant_trial:
                response['trial'] = relevant_trial

            return response

        else:
            raise CustomException("Not authorized!", "No corresponding Account found",
                                  status_code=401, error_type="permission_error")
    else:
        raise CustomException("Not authorized!", "No parent_account_public_id in token",
                              status_code=401, error_type="permission_error")
