
from fastapi import API<PERSON><PERSON><PERSON>, Depends, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account, Year,  ChildAccount
from ..models.request import UpdateChildAccountRequest
from ..models.response import UpdateChildAccountResponse
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
import re
router = APIRouter()
base_url = "/api/app/parent/app"

def validate_email(email: str):
    if not re.match(r"[^@]+@[^@]+\.[^@]+", email):
        raise CustomException("Invalid email!", "Invalid email format", status_code=400, error_type="validation_error")


def validate_pin(pin: str):
    if not pin.isdigit() or len(pin) != 4:
        raise CustomException("Invalid PIN!", "PIN must be 4 digits and numeric", status_code=400, error_type="validation_error")

@router.put(base_url + '/update-child-account', status_code=200, response_model=UpdateChildAccountResponse)
def update_child_account(request: Request, updated_child: UpdateChildAccountRequest, x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token), db: Session = Depends(get_db)):
    parent_public_id = request.state.parent_uuid
    if parent_public_id: 
        relevant_account = db.query(Account).filter_by(
            account_cognito_id=parent_public_id).first()

        if relevant_account:
            relevant_child_account = db.query(ChildAccount).filter_by(
                child_account_public_id=updated_child.child_account_public_id).first()
            # Iterate through the fields in the request body and update the relevant fields in the database
            if updated_child.child_account_email:
                validate_email(updated_child.child_account_email)
                # Check if email is already in use
                other_child_account = db.query(ChildAccount).filter_by(child_account_email=updated_child.child_account_email).first()
                if other_child_account and relevant_child_account.child_account_id != other_child_account.child_account_id:
                    raise CustomException("Email already in use!", "Email is already in use by another user", error_type='email_already_in_use', status_code=400)
                relevant_child_account.child_account_email = updated_child.child_account_email
            if updated_child.child_account_pin:
                validate_pin(updated_child.child_account_pin)
                relevant_child_account.child_account_pin = updated_child.child_account_pin
            if updated_child.year_public_id:
                relevant_new_year = db.query(Year).filter_by(
                    year_public_id=updated_child.year_public_id).first()
                if relevant_new_year:
                    relevant_child_account.child_account_year_id = relevant_new_year.year_id
            if updated_child.child_account_name:
                relevant_child_account.child_account_name = updated_child.child_account_name
            db.commit()

            return UpdateChildAccountResponse(message="Child account updated successfully!")

        else:
            raise CustomException("Not authorized!", "No corresponding Account found",
                                  status_code=401, error_type="permission_error")
    else:
        raise CustomException("Not authorized!", "No parent_account_public_id in token",
                              status_code=401, error_type="permission_error")

