
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depen<PERSON>, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account, ChildAccount,Trial
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from ..models.request  import DeleteChildAccountRequest
from ..models.response import DeleteChildAccountResponse

router = APIRouter()
base_url = "/api/app/parent/app"

EXERCISE_PAGINATION_SIZE= 5

@router.delete(base_url + '/delete-child-account', status_code=200, response_model=DeleteChildAccountResponse)
def delete_child_account(request: Request, child_account: DeleteChildAccountRequest, x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token), db: Session = Depends(get_db)):
    parent_public_id = request.state.parent_uuid
    if parent_public_id:
        relevant_account = db.query(Account).filter_by(
            account_cognito_id=parent_public_id).first()

        if relevant_account:
            relevant_child_account = db.query(ChildAccount).filter_by(
                child_account_public_id=child_account.child_account_public_id).first()
            
            # NEED TO FIX THIS LATER
            relevant_trial = db.query(Trial).filter_by(trial_child_account_id=relevant_child_account.child_account_id).first()
            if relevant_trial:
                relevant_trial.trial_child_account_id = None

            db.delete(relevant_child_account)
            db.commit()
            

            return {"message": "Child account deleted successfully!"}
        
        else:
            raise CustomException("Not authorized!", "No corresponding Account found",
                                  status_code=401, error_type="permission_error")
    else:
        raise CustomException("Not authorized!", "No parent_account_public_id in token",
                              status_code=401, error_type="permission_error")
