import json
from sqlalchemy.orm import Session
from api.utils.media_utils import create_signed_image_url
from typing import Dict, List
import random
from db.models import Exercise

from api.utils.media_utils import create_signed_video_url

def prepare_exercises(exercises: List[Exercise], randomize: bool, db: Session, private_id: bool = False) -> List[Dict]:
    exercise_list = []

    for entry in exercises:
        current_exercise = {
            "exercise_public_id": entry.exercise_public_id,
            "exercise_prompt_text": entry.exercise_prompt_text,
            "exercise_answer_type": entry.exercise_answer_type
        }
        if private_id:
            current_exercise["exercise_id"] = entry.exercise_id

        # Check if the image_public_id or the video_public_id are non null/ not empty
        # If they are non-null we need to generate signed urls based on the public ids
        if entry.exercise_prompt_image_public_id:
            current_exercise["exercise_prompt_image_url"] = create_signed_image_url(
                entry.exercise_prompt_image_public_id)
        # if entry.exercise_prompt_video_public_id:
        #     current_exercise["exercise_prompt_video_url"] = media_assets[entry.exercise_prompt_video_public_id]

        if (entry.exercise_answer_type == "mc_simple" or entry.exercise_answer_type == "mc_multi"):
            # We load the answer options which are is a list with the following shape [{"option_id": xxx, "option_text":xxx, "option_image_public_id":xxx}]
            # We then need to prepare signed url for all the images
            answer_options = entry.exercise_answer_options
            # answer_options = json.loads(
            #     entry.exercise_answer_options)
            for option in answer_options:
                try:
                    if option["option_image_public_id"]:
                        temp = create_signed_image_url(option["option_image_public_id"])
                        option["option_image_url"] = temp
                    option.pop("option_image_public_id")
                except Exception as e:
                    print("error in generating signaed image url", e)
                    pass
                # Can potentially also dop the same with videos at some point

            # Options can have an image as well as text, the text will be in the
            # option_text field and the image will be in the option_image_public_id field

            if randomize:
                random.shuffle(answer_options)
            current_exercise["exercise_answer_options"] = answer_options

        if entry.exercise_info_text:
            current_exercise["exercise_info_text"] = entry.exercise_info_text
        # We do not send over the solutions, only once they submit it via a fdifferent route

        # The type of the correct answer depends on the question type
        # If it is a input question, then the answer will be a string
        # If it is a true false question, the answer will be a boolean
        # If it is a mc_simple question, the answer will be a list with one id [option_id]
        # If it is a mc_multi question, the answer will be a list with multiple ids [option_id, option_id]

        # Given those different possibilities, the entry is stored as a json string in the db and needs to be read first
 

        current_exercise["exercise_solution_video_url"] = create_signed_video_url(entry.exercise_solution_video_public_id,'exercise') if entry.exercise_solution_video_public_id else None
        current_exercise['exercise_difficulty'] = entry.exercise_difficulty
        exercise_list.append(current_exercise)
    return exercise_list
