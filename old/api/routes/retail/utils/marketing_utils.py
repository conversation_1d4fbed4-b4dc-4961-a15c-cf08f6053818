import mailerlite as MailerLite
import os
from datetime import datetime
from utils.exceptions import CustomException
from loguru import logger
MAILERLITE_API_KEY = os.environ.get('MAILERLITE_API_KEY')
mailerlite_client = MailerLite.Client({
    'api_key': MAILERLITE_API_KEY
})


list_mapping = {
    'all_users': os.environ.get("MAILERLITE_ALL_USERS_GROUP_ID"),
    'children': os.environ.get("MAILERLITE_CHILDREN_GROUP_ID"),
    'subscribers': os.environ.get("MAILERLITE_SUBSCRIBERS_GROUP_ID"),
    'welcome': {
        'de': os.environ.get("MAILERLITE_WELCOME_DE_GROUP_ID"),
        'en': os.environ.get("MAILERLITE_WELCOME_EN_GROUP_ID"),
        'fr': os.environ.get("MAILERLITE_WELCOME_FR_GROUP_ID"),
        'lu': os.environ.get("MAILERLITE_WELCOME_LU_GROUP_ID"),
        'child': os.environ.get("MAILERLITE_WELCOME_CHILD_GROUP_ID"),
    },
    'language_specific': {
        'de': os.environ.get("MAILERLITE_DE_GROUP_ID"),
        'en': os.environ.get("MAILERLITE_EN_GROUP_ID"),
        'lu': os.environ.get("MAILERLITE_LU_GROUP_ID"),
        'fr': os.environ.get("MAILERLITE_FR_GROUP_ID"),
    },
    'marketing': {
        'de': os.environ.get("MAILERLITE_MARKETING_DE_GROUP_ID"),
        'en': os.environ.get("MAILERLITE_MARKETING_EN_GROUP_ID"),
        'lu': os.environ.get("MAILERLITE_MARKETING_LU_GROUP_ID"),
        'fr': os.environ.get("MAILERLITE_MARKETING_FR_GROUP_ID"),
    },
    'post_purchase': {
        'de': os.environ.get("MAILERLITE_POST_PURCHASE_DE_GROUP_ID"),
        'en': os.environ.get("MAILERLITE_POST_PURCHASE_EN_GROUP_ID"),
        'lu': os.environ.get("MAILERLITE_POST_PURCHASE_LU_GROUP_ID"),
        'fr': os.environ.get("MAILERLITE_POST_PURCHASE_FR_GROUP_ID"),
    },
}


def add_to_mailing_list(email='', group_type='', language='de', ):
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    try:
        if group_type == 'all_users' or group_type == 'children' or group_type == 'subscribers':
            response = mailerlite_client.subscribers.create(
                email, subscribed_at=timestamp, groups=[list_mapping[group_type]])
        else:
            response = mailerlite_client.subscribers.create(
                email, subscribed_at=timestamp, groups=[list_mapping[group_type][language]])
    except Exception as e:
        raise CustomException("Error", str(
            e), status_code=400, error_type="error")


def remove_from_mailing_list(email='', language='de', group_type=''):
    try:
        # Fetch subscriber details
        response = mailerlite_client.subscribers.get(email)
        subscriber_data = response['data']
        subscriber_id = int(subscriber_data['id'])

        # Determine the group ID based on the group_type and language
        if group_type in ['all_users', 'children', 'subscribers']:
            group_id = int(list_mapping[group_type])
        else:
            group_id = int(list_mapping[group_type][language])

        # Unassign the subscriber from the group
        response = mailerlite_client.subscribers.unassign_subscriber_from_group(
            subscriber_id, group_id)

        # If successful, you might want to log or return a success message
        return f"Successfully removed {email} from the {group_type} mailing list"

    except Exception as e:
        # Handle specific exceptions if needed
        logger.error(f"Error removing from mailing list: {e}")
        raise CustomException("Error removing from mailing list", str(
            e), status_code=400, error_type="error")


def add_to_mailerlite(email, language):
    add_to_mailing_list(email=email, group_type='all_users')
    add_to_mailing_list(email=email, group_type='welcome', language=language)
    add_to_mailing_list(
        email=email, group_type='language_specific', language=language)


def add_child_to_mailerlite(email):
    add_to_mailing_list(email=email, group_type='all_users')
    add_to_mailing_list(email=email, group_type='children')
    add_to_mailing_list(email=email, group_type='welcome', language='child')


def handle_post_purchase_email(email, language):
    add_to_mailing_list(email=email, group_type='subscribers')
    add_to_mailing_list(
        email=email, group_type='post_purchase', language=language)
    remove_from_mailing_list(
        email=email, group_type='welcome', language=language)


def remove_from_subscribers_list(email):
    remove_from_mailing_list(email=email, group_type='subscribers')


def add_to_marketing_list(email, language, ip_address, privacy_policy_version, optin_text):
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    marketing_group_id = list_mapping['marketing'][language]

    try:
        response = mailerlite_client.subscribers.create(email, ip_address=ip_address, optin_ip=ip_address, opted_in_at=timestamp, subscribed_at=timestamp, fields={
                                                        'privacy_policy_version': privacy_policy_version, 'optin_text': optin_text}, groups=[marketing_group_id])
    except Exception as e:
        raise CustomException("Error", str(
            e), status_code=400, error_type="error")
