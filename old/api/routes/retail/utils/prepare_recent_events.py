import json
from sqlalchemy.orm import Session
from db.models import  ChildA<PERSON>unt, ChildEventType, ChildEvent

def prepare_recent_events(child_acount: ChildAccount, db: Session):

    relevant_event_names = ["started_lesson", "submitted_correct_exercise", "submitted_incorrect_exercise", "completed_lesson","completed_video", "opened_lesson", "started_video"]
    # find relevant event types
    relevant_event_types = db.query(ChildEventType).filter(ChildEventType.child_event_type_name.in_(relevant_event_names)).all()

    temp_types = {}
    for event_type in relevant_event_types:
        temp_types[event_type.child_event_type_id] = event_type.child_event_type_name

    child_event_type_id = ChildEvent.child_event_type_id  # Define child_event_type_id variable

    relevant_events = db.query(ChildEvent).filter_by(relevant_child_account_id=child_acount.child_account_id).filter(child_event_type_id.in_([event_type.child_event_type_id for event_type in relevant_event_types])).order_by(ChildEvent.created_at.desc()).limit(30).all()

    # For each of the relevant event_types, filter the same events that occured for a given day and only keep the last one

    # First we collect all the days for which we have events
    dates = [event.created_at for event in relevant_events]
    days = list(set([date.date() for date in dates]))

    # We then iterate over the days, and for each day we check if there was an event of the given type
    # If there are multiple events of the same type for the same day, we check if the event_data is different
    # If it is, we keep the event, otherwise we discard it
    deduplicated_events = []
    for day in days:
        for event_type in relevant_event_types:
            temp_events = [event for event in relevant_events if event.child_event_type_id == event_type.child_event_type_id]
            temp_events = [event for event in temp_events if event.created_at.date() == day]

            finished = False
            index = 0
            while not finished:
                if index < len(temp_events):
                    for counter in range(index+1, len(temp_events)):
                        if temp_events[index].child_event_data == temp_events[counter].child_event_data:
                            temp_events.pop(counter)
                            break
                    index += 1
                else:
                    finished = True

            deduplicated_events += temp_events
    deduplicated_events = sorted(deduplicated_events, key=lambda x: x.created_at, reverse=True)
    deduplicated_events = [
        {
            "event_public_id": event.child_event_public_id, 
            "event_name": temp_types[event.child_event_type_id], 
            "event_data": json.loads(event.child_event_data), 
            "created_at": event.created_at.strftime("%y-%m-%d %H:%M:%S"), 
            "date": event.created_at.date().strftime("%y-%m-%d")  # Use strftime() instead of strformat()
        } 
        for event in deduplicated_events
    ]

    return deduplicated_events