import json
from fastapi import API<PERSON><PERSON><PERSON>,  <PERSON>pen<PERSON>, <PERSON>er, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Exercise, ChildAccount, ChildEvent, ChildEventType, Lesson, ChildActivity, ChildActivityType,ChildAccountLessonAssociation
from api.utils.media_utils import create_signed_video_url
import uuid
from sqlalchemy import desc
from datetime import datetime
from ..models.request import SubmitEventRequest, SubmitActivityBufferRequest
from ..models.response import SubmitEventResponse, SubmitActivityBufferResponse

from utils.auth import has_valid_child_token
from utils.exceptions import CustomException

from sqlalchemy import func

router = APIRouter()
base_url = "/api/app/child/events"


def has_existing_event(db, child_account_id, event_type_id, event_data, reference_id=None):

    if reference_id:
        existing_event = db.query(ChildEvent).filter_by(
            relevant_child_account_id=child_account_id,
            child_event_type_id=event_type_id,
            child_event_reference_id=reference_id
        ).first()

    else:
        existing_event = db.query(ChildEvent).filter_by(
            relevant_child_account_id=child_account_id,
            child_event_type_id=event_type_id,
            child_event_data=json.dumps(event_data)
        ).first()

    return existing_event != None


@router.post(base_url + '/submit-event', status_code=200, response_model=SubmitEventResponse)
def submit_event(request: Request, submitted_event: SubmitEventRequest, x_auth_child_token: str = Header(None), has_valid_token: bool = Depends(has_valid_child_token), db: Session = Depends(get_db)):

    child_account_public_id = request.state.decoded_child_token['child_account_public_id']
    if not child_account_public_id:
        raise CustomException("Not authorized!", "No corresponding Child Account found",
                                status_code=401, error_type="permission_error")
    # The answer will either be a simple string in case of a simple answer
    # In case of a true/false answer, it will be a string
    # In case of a multiple choice answer, it will be a list with the chosen option_public_id
    # If we have a mc_multi answer, we will have a list with multiple option_public_ids

    # We first find the relevant child account and exercise
    relevant_child_account = db.query(ChildAccount).filter_by(
        child_account_public_id=child_account_public_id).first()

    if relevant_child_account == None:
        raise CustomException("Not authorized!", "No corresponding Child Account found",
                                status_code=401, error_type="permission_error")

    if submitted_event.event_name == "started_lesson":
        relevant_event_type = db.query(ChildEventType).filter_by(
            child_event_type_name="started_lesson").first()
        lesson_public_id = submitted_event.event_data['lesson_public_id']
        relevant_lesson = db.query(Lesson).filter_by(
            lesson_public_id=lesson_public_id).first()

        if relevant_lesson == None:
            raise CustomException("Invalid lesson!", "No corresponding lesson found",
                                    status_code=400, error_type="invalid_input")
        # Check if we already have an event for this lesson for this child
        existing_event = has_existing_event(db, relevant_child_account.child_account_id, relevant_event_type.child_event_type_id, submitted_event.event_data)

        if existing_event:
            return {"message": "Event already registered!"}

        new_event = ChildEvent(
            child_event_public_id=str(uuid.uuid4()),
            child_event_type_id=relevant_event_type.child_event_type_id,
            relevant_child_account_id=relevant_child_account.child_account_id,
            child_event_data=json.dumps(submitted_event.event_data),
            created_at=datetime.now()
        )
        db.add(new_event)
        db.commit()
        return {"message": "Event registered successfully!"}

    elif submitted_event.event_name == "completed_lesson":
        relevant_event_type = db.query(ChildEventType).filter_by(
            child_event_type_name="completed_lesson").first()
        lesson_public_id = submitted_event.event_data['lesson_public_id']
        relevant_lesson = db.query(Lesson).filter_by(
            lesson_public_id=lesson_public_id).first()

        if relevant_lesson == None:
            raise CustomException("Invalid lesson!", "No corresponding lesson found",
                                    status_code=400, error_type="invalid_input")
        
        # Check if we already have an event for this lesson for this child
        existing_event = has_existing_event(db, relevant_child_account.child_account_id, relevant_event_type.child_event_type_id, submitted_event.event_data)

        if existing_event:
            return {"message": "Event already registered!"}
        
        new_child_lesson_association = ChildAccountLessonAssociation(
            relevant_child_account_id=relevant_child_account.child_account_id,
            relevant_lesson_id=relevant_lesson.lesson_id,
            lesson_public_id = submitted_event.event_data['lesson_public_id'],
            completed_video = True
        )
        db.add(new_child_lesson_association)

        new_event = ChildEvent(
            child_event_public_id=str(uuid.uuid4()),
            child_event_type_id=relevant_event_type.child_event_type_id,
            relevant_child_account_id=relevant_child_account.child_account_id,
            child_event_data=json.dumps(submitted_event.event_data),
            created_at=datetime.now()
        )
        db.add(new_event)
        db.commit()
        return {"message": "Event registered successfully!"}

    elif submitted_event.event_name == "opened_lesson":
        relevant_event_type = db.query(ChildEventType).filter_by(
            child_event_type_name="opened_lesson").first()
        lesson_public_id = submitted_event.event_data['lesson_public_id']
        relevant_lesson = db.query(Lesson).filter_by(
            lesson_public_id=lesson_public_id).first()

        if relevant_lesson == None:
            raise CustomException("Invalid lesson!", "No corresponding lesson found",
                                    status_code=400, error_type="invalid_input")
        
        relevant_chapter = relevant_lesson.relevant_chapter
        reference_id = f"lesson-{relevant_lesson.lesson_public_id}-date-{datetime.now().date()}"

        event_data = submitted_event.event_data.copy()
        event_data["chapter_public_id"] = relevant_chapter.chapter_public_id
        event_data["chapter_title"] = relevant_chapter.chapter_title
        # Check if we already have an event for this lesson for this child for this day
        existing_event = has_existing_event(db, relevant_child_account.child_account_id, relevant_event_type.child_event_type_id, event_data, reference_id=reference_id) 

        if not existing_event:
        # No check for existing as we want to keep track of the latest lesson opened
            new_event = ChildEvent(
                child_event_public_id=str(uuid.uuid4()),
                child_event_type_id=relevant_event_type.child_event_type_id,
                relevant_child_account_id=relevant_child_account.child_account_id,
                child_event_data=json.dumps(event_data),
                child_event_reference_id=reference_id,
                created_at=datetime.now()
            )
            db.add(new_event)
            db.commit()
        return {"message": "Event registered successfully!"}

    elif submitted_event.event_name == "started_video":
        relevant_event_type = db.query(ChildEventType).filter_by(
            child_event_type_name="started_video").first()
        lesson_public_id = submitted_event.event_data['lesson_public_id']
        relevant_lesson = db.query(Lesson).filter_by(
            lesson_public_id=lesson_public_id).first()

        if relevant_lesson == None:
            raise CustomException("Invalid lesson!", "No corresponding lesson found",
                                    status_code=400, error_type="invalid_input")
        
        relevant_chapter = relevant_lesson.relevant_chapter
        reference_id = f"lesson-{relevant_lesson.lesson_public_id}-date-{datetime.now().date()}"

        event_data = submitted_event.event_data.copy()
        event_data["lesson_title"] = relevant_lesson.lesson_title   
        event_data["chapter_public_id"] = relevant_chapter.chapter_public_id
        event_data["chapter_title"] = relevant_chapter.chapter_title
        # Check if we already have an event for this lesson for this child
        existing_event = has_existing_event(db, relevant_child_account.child_account_id, relevant_event_type.child_event_type_id, event_data, reference_id=reference_id)

        if existing_event:
            return {"message": "Event already registered!"}

        new_event = ChildEvent(
            child_event_public_id=str(uuid.uuid4()),
            child_event_type_id=relevant_event_type.child_event_type_id,
            relevant_child_account_id=relevant_child_account.child_account_id,
            child_event_data=json.dumps(event_data),
            child_event_reference_id=reference_id,
            created_at=datetime.now()
        )
        db.add(new_event)
        db.commit()
        return {"message": "Event registered successfully!"}



@router.post(base_url + '/submit-activity-buffer', status_code=200, response_model=SubmitActivityBufferResponse)
def submit_activity_buffer(request: Request, submitted_object: SubmitActivityBufferRequest, x_auth_child_token: str = Header(None), has_valid_token: bool = Depends(has_valid_child_token), db: Session = Depends(get_db)):
    child_account_public_id = request.state.decoded_child_token['child_account_public_id']

    if child_account_public_id:
        relevant_child_account = db.query(ChildAccount).filter_by(
            child_account_public_id=child_account_public_id).first()
        if relevant_child_account == None:
            raise CustomException("Not authorized!", "No corresponding Child Account found",
                                  status_code=401, error_type="permission_error")

        # Go over the individual events in the block and decide on the final classification
        idle_count = len(
            [event for event in submitted_object.buffer if event["activity_type"] == 'idle'])
        active_count = len(
            [event for event in submitted_object.buffer if event["activity_type"] == 'active'])
        exercise_count = len(
            [event for event in submitted_object.buffer if event["activity_type"] == 'exercise'])
        watching_lesson_count = len(
            [event for event in submitted_object.buffer if event["activity_type"] == 'watching_lesson'])

        # Find the most common event type, if two elements are equally common, we take the first one
        activity_type = max(idle_count, active_count,
                            exercise_count, watching_lesson_count)
        if activity_type == idle_count:
            activity_type = 'idle'
        elif activity_type == active_count:
            activity_type = 'active'
        elif activity_type == exercise_count:
            activity_type = 'exercise'
        elif activity_type == watching_lesson_count:
            activity_type = 'watching_lesson'

        relevant_activity_type = db.query(ChildActivityType).filter_by(
            child_activity_type_name=activity_type).first()

        meta_data = {
            "activity_name": activity_type
        }

        current_hour = datetime.now().hour
        current_date = datetime.now().date()

        new_activity = ChildActivity(
            child_activity_public_id=str(uuid.uuid4()),
            relevant_child_account_id=relevant_child_account.child_account_id,
            child_activity_type_id=relevant_activity_type.child_activity_type_id,
            child_activity_date=current_date,
            child_activity_hour=current_hour,
            child_activity_block=submitted_object.time_block,
            child_activity_data=json.dumps(meta_data),
            created_at=datetime.now()
        )

        db.add(new_activity)
        db.commit()

        return {"message": "Activity submitted successfully!"}
    else:
        raise CustomException("Not authorized!", "No corresponding Child Account found",
                              status_code=401, error_type="permission_error")
    


