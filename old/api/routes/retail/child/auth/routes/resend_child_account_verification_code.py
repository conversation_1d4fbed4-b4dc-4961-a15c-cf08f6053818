from fastapi import APIRouter, Depends, BackgroundTasks
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import ChildAccount
from ..models.request import SendChildAccountVerificationCodeRequest
from ..models.response import SendChildAcccountVerificationCodeResponse
from utils.exceptions import CustomException
import random
from .verification_utils.send_child_verification_email import send_child_verification_email

router = APIRouter()
base_url = "/api/app/child/auth"


@router.post(base_url + '/resend-child-account-verification-code', status_code=200, response_model=SendChildAcccountVerificationCodeResponse)
def resend_child_verification_code(new_child_account: SendChildAccountVerificationCodeRequest,  db: Session = Depends(get_db), background_tasks: BackgroundTasks = BackgroundTasks()):
    relevant_child_account = db.query(ChildAccount).filter(
        ChildAccount.child_account_email == new_child_account.email).first()

    if relevant_child_account is None:
        raise CustomException("Child account not found", status_code=404)

    if relevant_child_account.is_verified:
        raise CustomException(
            error_type="account_already_verified",
            log_message="Account already verified",
            return_message="Account already verified",
            status_code=400)
    verification_code = str(random.randint(100000, 999999))

    if not relevant_child_account.child_account_verification_code:
        relevant_child_account.child_account_verification_code = verification_code
        db.commit()
    else:
        verification_code = relevant_child_account.child_account_verification_code

    background_tasks.add_task(
        send_child_verification_email,
        new_child_account.email,
        verification_code,
        relevant_child_account.child_account_language
    )
    return {"message": "Verification code sent"}

