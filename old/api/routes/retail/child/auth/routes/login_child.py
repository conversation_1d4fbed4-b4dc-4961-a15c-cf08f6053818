import os
from fastapi import APIRouter, Depends
from api.utils.auth_utils import create_access_token
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import ChildAccount
from datetime import timed<PERSON><PERSON>
from ..models.request import LoginChildAcccountRequest
from ..models.response import LoginChildAcccountResponse
from utils.exceptions import CustomException

router = APIRouter()
base_url = "/api/app/child/auth"

ACCESS_TOKEN_EXPIRE_MINUTES = int(
    os.environ.get("JWT_ACCESS_TOKEN_EXPIRE_MINUTES"))


@router.post(base_url + '/login-child', status_code=200, response_model=LoginChildAcccountResponse)
def child_login(child_account: LoginChildAcccountRequest,  db: Session = Depends(get_db)):
    relevant_child_account = db.query(ChildAccount).filter(
        ChildAccount.child_account_email == child_account.child_account_email).first()
    # Fetch the relevant child and check if the pin is correct
    # If so, we return the access token

    if relevant_child_account == None:
        raise CustomException(
            status_code=401,
            error_type="incorrect_login_information",
            log_message=f"Could not sign in child with email {child_account.child_account_email}!",
            return_message="Could not sign you in",
            log_level="DEBUG"
        )


    if relevant_child_account.child_account_pin == child_account.child_account_pin:
        if relevant_child_account.is_verified == False:
            raise CustomException(
                status_code=401,
                error_type="account_not_verified",
                log_message=f"Could not sign in child with email {child_account.child_account_email}!",
                return_message="Account not verified",
                log_level="DEBUG"
            )
        relevant_parent_account = relevant_child_account.parent_account
        parent_account_public_id = None
        if relevant_parent_account != None:
            parent_account_public_id = relevant_parent_account.account_public_id
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"child_account_public_id": relevant_child_account.child_account_public_id, "parent_account_public_id": parent_account_public_id, "child_account_name": relevant_child_account.child_account_name}, expires_delta=access_token_expires
        )
        return {"access_token": access_token, "token_type": "bearer",
                "child_account_public_id": relevant_child_account.child_account_public_id,
                "parent_account_public_id": parent_account_public_id,
                "child_account_name": relevant_child_account.child_account_name}
    else:
        raise CustomException(
            status_code=401,
            error_type="incorrect_login_information",
            log_message=f"Could not sign in child with email {child_account.child_account_email}!",
            return_message="Could not sign you in",
            log_level="DEBUG"
        )
