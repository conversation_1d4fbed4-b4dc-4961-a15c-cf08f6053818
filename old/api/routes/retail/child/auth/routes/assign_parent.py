from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import ChildAccount,  ParentChildAssignment, Account
import random
from ..models.request import  ParentAssignmentRequest
from ..models.response import ParentAssignmentResponse
from utils.exceptions import CustomException

from ..utils.utils import send_verification_code

router = APIRouter()
base_url = "/api/app/child/auth"

@router.post(base_url + '/assign-parent', status_code=200, response_model=ParentAssignmentResponse)
def assign_parent(new_assignment: ParentAssignmentRequest,  db: Session = Depends(get_db)):
    relevant_child_account = db.query(ChildAccount).filter(
        ChildAccount.child_account_public_id == new_assignment.child_account_public_id).first()
    
    relevant_parent_account = db.query(Account).filter(
        Account.account_email == new_assignment.parent_email).first()
    if not relevant_child_account:
        raise CustomException("Child account not found", status_code=404)
    # 6 digit integer verification code
    # Checif we already have a parent assignment
    existing_assignment = db.query(ParentChildAssignment).filter(
        ParentChildAssignment.relevant_child_account_id == relevant_child_account.child_account_id).first()
    if existing_assignment and existing_assignment.parent_email == new_assignment.parent_email:
        send_verification_code(new_assignment.parent_email,
                               existing_assignment.verification_code, new_assignment.language)
    else:
        verification_code = str(random.randint(100000, 999999))
        new_temp_assignment = ParentChildAssignment(
            parent_email=new_assignment.parent_email, relevant_child_account_id=relevant_child_account.child_account_id, verification_code=verification_code,
            language=new_assignment.language
        )
        db.add(new_temp_assignment)
        db.commit()
        send_verification_code(new_assignment.parent_email,
                               verification_code, new_assignment.language)

    return {"message": "Verification email sent", "existing_parent_account": relevant_parent_account is not None}