import os
import random
from fastapi import APIRouter, Depends, BackgroundTasks
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import ChildAccount
from datetime import datetime
from ..models.request import CheckChildVerificationRequest
from ..models.response import CheckChildVerificationResponse
from utils.exceptions import CustomException
from .verification_utils.send_child_verification_email import send_child_verification_email

router = APIRouter()
base_url = "/api/app/child/auth"

@router.post(base_url + '/check-verification-status', response_model=CheckChildVerificationResponse)
def check_child_verification_status(
    request: CheckChildVerificationRequest,
    db: Session = Depends(get_db),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    input_email = request.email.strip().lower()

    # Check if child account exists
    child_account = db.query(ChildAccount).filter(
        ChildAccount.child_account_email == input_email
    ).first()
    
    if not child_account:
        raise CustomException(
            "Child account not found", 
            status_code=404,
            error_type="child_account_not_found"
        )

    # Return verification status if already verified
    if child_account.is_verified:
        return CheckChildVerificationResponse(
            is_verified=True
        )

    # Generate verification code if none exists
    if not child_account.child_account_verification_code:
        child_account.child_account_verification_code = str(random.randint(100000, 999999))
        db.commit()

    # Send verification email in background
    background_tasks.add_task(
        send_child_verification_email,
        input_email,
        child_account.child_account_verification_code,
        child_account.child_account_language
    )

    return CheckChildVerificationResponse(
        is_verified=False
    )
