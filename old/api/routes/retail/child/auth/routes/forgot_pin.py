import os
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import ChildAccount
from ..models.request import ForgotPinRequest
from utils.exceptions import CustomException
from ..utils.utils import send_forgot_pin

router = APIRouter()
base_url = "/api/app/child/auth"


@router.post(base_url + '/forgot-pin', status_code=200)
def forgot_pin(forgot_pin_request: ForgotPinRequest,  db: Session = Depends(get_db)):
    relevant_child_account = db.query(ChildAccount).filter(
        ChildAccount.child_account_email == forgot_pin_request.child_account_email).first()

    if relevant_child_account == None:
        raise CustomException(
            status_code=401,
            error_type="account_not_found",
            log_message=f"Could not find email {forgot_pin_request.child_account_email}!",
            return_message="Could not find email",
            log_level="DEBUG"
        )

    # if relevant_child_account.is_verified == False:
    #     raise CustomException(
    #         status_code=401,
    #         error_type="account_not_verified",
    #         log_message=f"Could not send child pin to email {forgot_pin_request.child_account_email}!",
    #         return_message="Account not verified",
    #         log_level="DEBUG"
    #     )

    send_forgot_pin(relevant_child_account.child_account_email,
                    relevant_child_account.child_account_pin, language=forgot_pin_request.language)

    return {"message": "Pin code sent"}
