from fastapi import APIRouter, Depends, BackgroundTasks, Header
from api.utils.auth_utils import create_access_token
from sqlalchemy.orm import Session
import uuid
from db.database import get_db
from db.models import ChildAccount, Year, Trial, Account
import random
from datetime import datetime, timedelta
from ..models.request import CreateChildAcccountRequest
from ..models.response import CreateChildAcccountResponse
from utils.exceptions import CustomException
import mailerlite as MailerLite
from api.routes.retail.utils.marketing_utils import add_child_to_mailerlite
from .verification_utils.send_child_verification_email import send_child_verification_email

import os 

router = APIRouter()
base_url = "/api/app/child/auth"

TRIAL_DURATION = 7  # days
ENVIRONMENT = os.environ.get('ENVIRONMENT')

MAILERLITE_CHILDREN_WELCOME_GROUP_ID = os.environ.get('MAILERLITE_CHILDREN_WELCOME_GROUP_ID')
MAILERLITE_CHILDREN_GROUP_ID = os.environ.get('MAILERLITE_CHILDREN_GROUP_ID')
JWT_ACCESS_TOKEN_EXPIRE_MINUTES = os.environ.get('JWT_ACCESS_TOKEN_EXPIRE_MINUTES')
MAILERLITE_API_KEY = os.environ.get('MAILERLITE_API_KEY')

@router.post(base_url + '/create-child-account', status_code=201, response_model=CreateChildAcccountResponse)
def create_child_account(new_child_account: CreateChildAcccountRequest,  db: Session = Depends(get_db), background_tasks: BackgroundTasks = BackgroundTasks(), ip_address: str = Header(None, alias='x-forwarded-for')):
    input_email = new_child_account.email.strip().lower()

    existing_account = db.query(ChildAccount).filter(
        ChildAccount.child_account_email == input_email).first()
    if existing_account is not None:
        raise CustomException("Account already exists", status_code=409, error_type="account_already_exists")
    
    existing_parent_account = db.query(Account).filter(
        Account.account_email == input_email).first()
    if existing_parent_account is not None:
        raise CustomException("Parent account already exists", status_code=409, error_type="parent_account_already_exists")

    relevant_school_year = db.query(Year).filter(
        Year.year_public_id == new_child_account.school_year_public_id).first()

    if relevant_school_year is None:
        raise CustomException("School year not found", status_code=404)
    public_id = str(uuid.uuid4())
    verification_code = str(random.randint(100000, 999999))
    new_account = ChildAccount(
        child_account_email=input_email,
        child_account_name=new_child_account.name,
        child_account_year_id=relevant_school_year.year_id,
        child_account_public_id=public_id,
        child_account_verification_code=verification_code,
        child_account_pin=new_child_account.pin,
        child_account_language=new_child_account.language,
        show_intro=False,
        child_account_register_source=new_child_account.searchparams
    )

    db.add(new_account)
    db.flush()

    new_trial = Trial(
        trial_child_account_id=new_account.child_account_id,
        trial_status='active',
        trial_start_date=datetime.utcnow(),
        trial_end_date=datetime.utcnow() + timedelta(days=TRIAL_DURATION)
    )
    db.add(new_trial)
    db.commit()

    # Need to adapt trials
    try: 
        if ENVIRONMENT != 'dev':
            add_child_to_mailerlite(input_email)
    except Exception as e:
        print("Error adding to mailerlite: " + str(e) + " for email: " + input_email)

    try:
        background_tasks.add_task(
            send_child_verification_email,
            input_email,
            verification_code,
            new_child_account.language,
        )

    except Exception as e:
        db.rollback()
        raise CustomException("Could not send verification code", "Could not send verification code: " + str(e),
                            status_code=500, log_level="WARNING", error_type="internal_error")
    


    # Set initial token expiration time to ?? minutes
    access_token_expires = timedelta(minutes=float(3))
    access_token = create_access_token(
        data={"child_account_public_id": public_id, "child_account_name": new_child_account.name, "parent_account_public_id": None
              }, expires_delta=access_token_expires
    )

    return {"access_token": access_token}
