import os
from string import Template

template_data = {
    'fr': {
        "email_subject": "🎒 Vérifie ton compte LuxEdu",
        "html_template": {
            "greeting": "Bienvenue sur LuxEdu,",
            "action": "Utilise ce code pour activer ton compte:",
            "code_instruction": "Code:",
            "button_text": "Activer mon compte",
            "alternative": "Ou clique sur ce lien:",
            "expiry": "Ce code est valide pour 15 minutes!",
            "questions": "Besoin d'aide? Écris-nous:",
            "contact_email": "<EMAIL>",
            "salutations": "À bientôt,",
            "team": "Ton équipe LuxEdu"
        }
    },
    'en': {
        "email_subject": "🎒 Verify your LuxEdu account",
        "html_template": {
            "greeting": "Hello young learner!",
            "action": "Use this code to activate your account:",
            "code_instruction": "Code:",
            "button_text": "Activate My Account",
            "alternative": "Or click this link:",
            "expiry": "This code will expire in 15 minutes!",
            "questions": "Need help? Write us:",
            "contact_email": "<EMAIL>",
            "salutations": "See you soon,",
            "team": "Your LuxEdu Team"
        }
    },
    'de': {
        "email_subject": "🎒 Bestätige dein LuxEdu Konto",
        "html_template": {
            "greeting": "Willkommen auf LuxEdu,",
            "action": "Benutze diesen Code um dein Konto zu aktivieren:",
            "code_instruction": "Code:",
            "button_text": "Konto aktivieren",
            "alternative": "Oder klicke auf diesen Link:",
            "expiry": "Dieser Code ist 15 Minuten gültig!",
            "questions": "Hilfe benötigt? Schreib uns einfach:",
            "contact_email": "<EMAIL>",
            "salutations": "Bis bald,",
            "team": "Dein LuxEdu Team"
        }
    },
    'lu': {
        "email_subject": "🎒 Bestäteg däin LuxEdu Kont",
        "html_template": {
            "greeting": "Wëllkomm op LuxEdu,",
            "action": "Benotz dëse Code fir däin Kont ze aktivéieren:",
            "code_instruction": "Code:",
            "button_text": "Mäi Kont aktivéieren",
            "alternative": "Oder klick op dësen Link:",
            "expiry": "Dëse Code ass 15 Minutten gülteg!",
            "questions": "Brauchs du Hëllef? Schreif eis einfach:",
            "contact_email": "<EMAIL>",
            "salutations": "Bis geschwënn,",
            "team": "Deng LuxEdu Equipe"
        }
    }
}

def load_verification_template(filename, verification_code, verification_link, substitutions):
    current_dir = os.path.dirname(os.path.abspath(__file__))
    full_path = os.path.join(current_dir, filename)

    with open(full_path, 'r') as file:
        html_template = file.read()
        template = Template(html_template)
    
    substitutions['verification_code'] = verification_code
    substitutions['verification_link'] = verification_link
    return template.substitute(**substitutions)

def create_child_verification_email(language, verification_code, email_address):
    if language not in template_data:
        language = 'en'

    frontend_base_url = os.environ.get('FRONTEND_URL', 'http://localhost:3000')
    verification_link = f"{frontend_base_url}/{language}/app/auth/login?child_email={email_address}&child_verification_code={verification_code}"

    relevant_data = template_data[language]
    substitutions = relevant_data["html_template"]

    email_body_html = load_verification_template(
        'child_verification_email_template.html',
        verification_code,
        verification_link,
        substitutions
    )
    
    email_body_text = f"""
{substitutions['greeting']}

{substitutions['action']}

{substitutions['code_instruction']} {verification_code}

{substitutions['alternative']}
{verification_link}

{substitutions['expiry']}

{substitutions['questions']} {substitutions['contact_email']}

{substitutions['salutations']}
{substitutions['team']}
    """
    
    return email_body_html, email_body_text, relevant_data["email_subject"] 