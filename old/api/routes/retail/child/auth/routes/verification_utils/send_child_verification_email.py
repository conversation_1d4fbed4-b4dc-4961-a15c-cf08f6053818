import boto3
import os
from loguru import logger
from .create_child_verification_email import create_child_verification_email

def send_child_verification_email(email_address: str, verification_code: str, language: str = 'en'):
    ses_client = boto3.client(
        'ses',
        region_name='eu-central-1',
        aws_access_key_id=os.environ['AWS_ACCESS_KEY'],
        aws_secret_access_key=os.environ['AWS_SECRET_KEY']
    )

    email_body_html, email_body_text, email_subject = create_child_verification_email(
        language=language,
        verification_code=verification_code,
        email_address=email_address
    )

    try:
        sender_email = '<EMAIL>'
        if os.environ.get('ENVIRONMENT') == 'prod':
            sender_email = '<EMAIL>'
        elif os.environ.get('ENVIRONMENT') == 'test':
            sender_email = '<EMAIL>'

        response = ses_client.send_email(
            Destination={'ToAddresses': [email_address]},
            Message={
                'Body': {
                    'Html': {'Charset': 'UTF-8', 'Data': email_body_html},
                    'Text': {'Charset': 'UTF-8', 'Data': email_body_text}
                },
                'Subject': {'Charset': 'UTF-8', 'Data': email_subject}
            },
            Source=sender_email
        )
        logger.info(f"Child verification email sent to {email_address}")
        return True
    except Exception as e:
        logger.error(f"Error sending child verification email to {email_address}: {e}")
        return False