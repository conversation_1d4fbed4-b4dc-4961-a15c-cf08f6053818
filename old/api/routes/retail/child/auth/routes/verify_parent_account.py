import os
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
import uuid
from db.database import get_db
from db.models import ChildAccount, ParentChildAssignment
import stripe
import uuid
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account,  SignUp, Trial
from api.routes.retail.utils import add_to_mailerlite
from datetime import datetime, timedelta

from ..models.request import VerifyParentAcccountRequest
from ..models.response import VerifyParentAcccountResponse
from utils.exceptions import CustomException
from loguru import logger
from api.utils.auth_utils import create_access_token
from api.routes.retail.parent.auth.utils.security import hash_pin

stripe.api_key = os.environ.get("STRIPE_API_KEY")

router = APIRouter()
base_url = "/api/app/child/auth"

TRIAL_DURATION = 7  # days


ACCESS_TOKEN_EXPIRE_MINUTES = int(os.environ.get(
    "JWT_ACCESS_TOKEN_EXPIRE_MINUTES", 86400))  # Default to 60 days


stripe.api_key = os.environ.get('STRIPE_API_KEY')


@router.post(base_url + '/verify-parent-account', status_code=200, response_model=VerifyParentAcccountResponse)
def verify_parent_account(parent_account: VerifyParentAcccountRequest,  db: Session = Depends(get_db)):

    existing_account = db.query(Account).filter(
        Account.account_email == parent_account.email).first()

    relevant_parent_assignment = db.query(ParentChildAssignment).filter(
        ParentChildAssignment.parent_email == parent_account.email).first()
    if relevant_parent_assignment is None:
        raise CustomException("Parent account not found", status_code=404)

    if relevant_parent_assignment.verification_code != parent_account.verification_code:
        raise CustomException(
            error_type="wrong_verification_code",
            log_message="Wrong verification code!",
            return_message="Wrong verification code!",
            status_code=400)
    if existing_account is not None:

        existing_account.is_verified = True

        relevant_child_account = db.query(ChildAccount).filter(
            ChildAccount.child_account_id == relevant_parent_assignment.relevant_child_account_id).first()

        relevant_child_account.child_account_parent_id = existing_account.account_id
        db.delete(relevant_parent_assignment)
        db.commit()

        # Create token payload
        # The token shape is a leftover from the Cognito implementation
        token_data = {
            "sub": str(existing_account.account_cognito_id),
            # Later on we should use the public id instead and change this across routes
            "email": existing_account.account_email,
            "account_language": existing_account.account_language or "lu",
            "token_type": "custom"
        }

        # Create access token
        access_token = create_access_token(
            data=token_data,
            expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        )

        return VerifyParentAcccountResponse(
            message="Account created sucessfully",
            account_public_id=existing_account.account_public_id,
            access_token=access_token,
            id_token=access_token,  # Using the same token for both, adjust if needed
            refresh_token=None  # Implement refresh token logic if required
        )

    else:
        account_public_id = str(uuid.uuid4())
        
        stripe_response = None
        try:
            stripe_response = stripe.Customer.create(
                description=account_public_id, email=relevant_parent_assignment.parent_email)
        except Exception as e:
            raise CustomException("Could not register account", "Could not register account, issue with creating Stripe account",
                                  status_code=500, log_level="WARNING", error_type="internal_error")

        try:
            hashed_pin = hash_pin(parent_account.login_pin)
            new_account_entry = Account(account_email=relevant_parent_assignment.parent_email,
                                        account_cognito_id=str(uuid.uuid4()),
                                        account_language=relevant_parent_assignment.language,
                                        pin_hash=hashed_pin,
                                        account_public_id=account_public_id,
                                        account_stripe_id=stripe_response.id,
                                        is_verified=True,
                                        is_auth_migrated=True
                                        )
            db.add(new_account_entry)

            db.flush()

            relevant_child_account = db.query(ChildAccount).filter(
                ChildAccount.child_account_id == relevant_parent_assignment.relevant_child_account_id).first()

            if not relevant_child_account:
                raise CustomException("Child account not found", "Child account not found",
                                      status_code=404, log_level="INFO", error_type="child_account_not_found")

            relevant_child_account.child_account_parent_id = new_account_entry.account_id

            relevant_child_account.is_verified = True
            try:
                add_to_mailerlite(relevant_parent_assignment.parent_email,
                                  relevant_parent_assignment.language)
            except Exception as e:
                logger.error(f"Error adding to mailerlite: {str(e)}")

            new_sign_up_entry = SignUp(
                sign_up_account_id=new_account_entry.account_id,
                sign_up_email=relevant_parent_assignment.parent_email,
                sign_up_language=relevant_parent_assignment.language,
                signup_source='child',
            )

            db.add(new_sign_up_entry)

            relevant_trial = db.query(Trial).filter_by(
                trial_child_account_id=relevant_child_account.child_account_id).first()

            if relevant_trial is not None:
                relevant_trial.trial_account_id = new_account_entry.account_id

            else:
                new_trial = Trial(
                    trial_account_id=new_account_entry.account_id,
                    trial_status='active',
                    trial_start_date=datetime.utcnow(),
                    trial_end_date=datetime.utcnow() + timedelta(days=TRIAL_DURATION)
                )
                db.add(new_trial)
            db.delete(relevant_parent_assignment)

            db.commit()

        except Exception as e:
            # Issue on our backend, delete cognito account and stripe account
            db.rollback()
            response = stripe.Customer.delete(stripe_response.id)

            raise CustomException("Could not register account", "Could not register account, issue with our backend: " + str(e),
                                  status_code=500, log_level="WARNING", error_type="internal_error")

        # Create token payload
        # The token shape is a leftover from the Cognito implementation
        token_data = {
            "sub": str(new_account_entry.account_cognito_id),
            # Later on we should use the public id instead and change this across routes
            "email": new_account_entry.account_email,
            "account_language": new_account_entry.account_language or "lu",
            "token_type": "custom"
        }

        # Create access token
        access_token = create_access_token(
            data=token_data,
            expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        )

        return VerifyParentAcccountResponse(
            message="Account created sucessfully",
            account_public_id=account_public_id,
            access_token=access_token,
            id_token=access_token,  # Using the same token for both, adjust if needed
            refresh_token=None  # Implement refresh token logic if required
        )
