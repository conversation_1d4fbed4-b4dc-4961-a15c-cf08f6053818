import os
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import ParentChildAssignment
import os
from fastapi import APIRouter, Depends

from sqlalchemy.orm import Session
from utils.exceptions import CustomException
from db.database import get_db

from ..models.request import SendParentVerificationCodeRequest
from ..models.response import SendParentVerificationCodeResponse
from utils.exceptions import CustomException
from ..utils.utils import send_verification_code


router = APIRouter()
base_url = "/api/app/child/auth"


@router.post(base_url + '/resend-parent-verification-code', status_code=200, response_model=SendParentVerificationCodeResponse)
def resend_parent_verification_code(new_assignment: SendParentVerificationCodeRequest,  db: Session = Depends(get_db)):
    relevant_temp_assignment = db.query(ParentChildAssignment).filter(
        ParentChildAssignment.parent_email == new_assignment.email).first()
    if not relevant_temp_assignment:
        raise CustomException("Parent account not found", status_code=404)
    send_verification_code(
        new_assignment.email, relevant_temp_assignment.verification_code, new_assignment.language)
    return {"message": "Verification code sent"}
