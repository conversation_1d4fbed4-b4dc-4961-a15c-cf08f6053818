import os
from fastapi import APIRouter, Depends
from api.utils.auth_utils import create_access_token
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import ChildAccount
from datetime import timedelta

from ..models.request import VerifyChildAcccountRequest
from ..models.response import VerifyChildAcccountResponse
from utils.exceptions import CustomException

router = APIRouter()
base_url = "/api/app/child/auth"

ACCESS_TOKEN_EXPIRE_MINUTES = int(
    os.environ.get("JWT_ACCESS_TOKEN_EXPIRE_MINUTES"))

@router.post(base_url + '/verify-child-account', status_code=200, response_model=VerifyChildAcccountResponse)
def verify_child_account(new_child_account: VerifyChildAcccountRequest, db: Session = Depends(get_db)):
    relevant_child_account = db.query(ChildAccount).filter(
        ChildAccount.child_account_email == new_child_account.email).first()
    if relevant_child_account is None:
        raise CustomException(
            error_type="account_not_found",
            log_message="Child account not found",
            return_message="Child account not found",
            status_code=404)

    if relevant_child_account.is_verified:
        raise CustomException(
            error_type="account_already_verified",
            log_message="Account already verified",
            return_message="Account already verified",
            status_code=400)
    
    if relevant_child_account.child_account_verification_code != new_child_account.verification_code:
        raise CustomException(
            error_type="wrong_verification_code",
            log_message="Wrong verification code!",
            return_message="Wrong verification code!",
            status_code=400)

    relevant_child_account.is_verified = True
    db.commit()
    
    return {"message": "Successfully verified"}

