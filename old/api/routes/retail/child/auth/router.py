from fastapi import APIRouter
from .routes import assign_parent, create_child_account, login_child, resend_parent_verification_code, resend_child_account_verification_code, verify_child_account, verify_parent_account, forgot_pin, check_child_verification

router = APIRouter()

# Include routes from all the files in this directory
router.include_router(login_child.router)
router.include_router(create_child_account.router)
router.include_router(verify_child_account.router)
router.include_router(assign_parent.router)
router.include_router(resend_child_account_verification_code.router)
router.include_router(resend_parent_verification_code.router)
router.include_router(verify_parent_account.router)
router.include_router(forgot_pin.router)
router.include_router(check_child_verification.router)