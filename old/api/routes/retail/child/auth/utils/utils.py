import os
from utils.exceptions import CustomException
import boto3


FRONTEND_URL = os.environ.get('FRONTEND_URL')


def send_verification_code(email, verification_code, language='fr', email_link=False):
    ses_client = boto3.client(
        'ses',
        region_name='eu-central-1',  # replace with your AWS region
        aws_access_key_id=os.environ['AWS_ACCESS_KEY'],
        aws_secret_access_key=os.environ['AWS_SECRET_KEY']
    )

    email_content = {
        "en": {
            "email_subject": "LuxEdu - Verification code",
            "email_body_text": f"Your verification code is: {verification_code}",
            "email_body_html": f"<p>Your verification code is: {verification_code}</p>"
        },
        "fr": {
            "email_subject": "LuxEdu - Code de vérification",
            "email_body_text": f"Le code de vérification est: {verification_code}",
            "email_body_html": f"<p>Le code de vérification est: {verification_code}</p>"
        },
        "de": {
            "email_subject": "LuxEdu - Bestätigungscode",
            "email_body_text": f"Der Bestätigungscode lautet: {verification_code}",
            "email_body_html": f"<p>Der Bestätigungscode lautet: {verification_code}</p>"
        },
        "lu": {
            "email_subject": "LuxEdu - Verifizéierungscode",
            "email_body_text": f"Den Verifizéierungscode ass: {verification_code}",
            "email_body_html": f"<p>Den Verifizéierungscode ass: {verification_code}</p>"}
    }

    if (email_link):
        email_content["en"][
            "email_body_text"] = f"Please click on the following link to verify your email address: {FRONTEND_URL}/{language}/app/auth/verification?email={email}&verification_code={verification_code}"
        email_content["en"][
            "email_body_html"] = f"<p>Please click on the following link to verify your email address: <a href='{FRONTEND_URL}/{language}/app/auth/verification?email={email}&verification_code={verification_code}'>Verify Email</a></p>"
        email_content["fr"][
            "email_body_text"] = f"Veuillez cliquer sur le lien suivant pour vérifier votre adresse e-mail: {FRONTEND_URL}/{language}/app/auth/verification?email={email}&verification_code={verification_code}"
        email_content["fr"][
            "email_body_html"] = f"<p>Veuillez cliquer sur le lien suivant pour vérifier votre adresse e-mail: <a href='{FRONTEND_URL}/{language}/app/auth/verification?email={email}&verification_code={verification_code}'>Vérifier l'email</a></p>"
        email_content["de"][
            "email_body_text"] = f"Bitte klicken Sie auf den folgenden Link, um Ihre E-Mail-Adresse zu bestätigen: {FRONTEND_URL}/{language}/app/auth/verification?email={email}&verification_code={verification_code}"
        email_content["de"][
            "email_body_html"] = f"<p>Bitte klicken Sie auf den folgenden Link, um Ihre E-Mail-Adresse zu bestätigen: <a href='{FRONTEND_URL}/{language}/app/auth/verification?email={email}&verification_code={verification_code}'>E-Mail bestätigen</a></p>"
        email_content["lu"][
            "email_body_text"] = f"Klickt w.e.g. op de follgende Link fir Är E-Mail-Adress ze verifizéieren: {FRONTEND_URL}/{language}/app/auth/verification?email={email}&verification_code={verification_code}"
        email_content["lu"]["email_body_html"] = f"<p>Klickt w.e.g. op de follgende Link fir Är E-Mail-Adress ze verifizéieren: <a href='{FRONTEND_URL}/{language}/app/auth/verification?email={email}&verification_code={verification_code}'>E-Mail verifizéieren</a></p>"

    try:
        sender_email = '<EMAIL>'
        if os.environ.get('ENVIRONMENT') == 'prod':
            sender_email = '<EMAIL>'
        if os.environ.get('ENVIRONMENT') == 'test':
            sender_email = '<EMAIL>'
        # Sending email using AWS SES

        response = ses_client.send_email(
            Source=sender_email,  # replace with your verified SES email
            Destination={'ToAddresses': [email]},
            Message={
                'Body': {
                    'Text': {
                        'Data': email_content[language]["email_body_text"],
                        'Charset': 'UTF-8'
                    },
                    'Html': {
                        'Data': email_content[language]["email_body_html"],
                        'Charset': 'UTF-8'
                    }
                },
                'Subject': {
                    'Data':  email_content[language]["email_subject"],
                    'Charset': 'UTF-8'
                }
            }
        )

    except Exception as e:
        raise CustomException("Could not generate verification child email!",
                              f"Could not generate verification child email! {e}", status_code=500, log_level="WARNING", error_type="internal_error")

    return {'message': 'Email sent sucessfully'}


def send_forgot_pin(email_address, login_pin, language='fr'):
    ses_client = boto3.client(
        'ses',
        region_name='eu-central-1',  # replace with your AWS region
        aws_access_key_id=os.environ['AWS_ACCESS_KEY'],
        aws_secret_access_key=os.environ['AWS_SECRET_KEY']
    )

    email_content = {
        "en": {
            "email_subject": "LuxEdu - Login pin",
            "email_body_text": f"Your login pin is: {login_pin}",
            "email_body_html": f"<p>Your login pin is: {login_pin}</p>"
        },
        "fr": {
            "email_subject": "LuxEdu - Pin de connexion",
            "email_body_text": f"Ton pin de connexion est: {login_pin}",
            "email_body_html": f"<p>Ton pin de connexion est: {login_pin}</p>"
        },
        "de": {
            "email_subject": "LuxEdu - Anmeldepin",
            "email_body_text": f"Dein Anmeldepin lautet: {login_pin}",
            "email_body_html": f"<p>Dein Anmeldepin lautet: {login_pin}</p>"
        },
        "lu": {
            "email_subject": "LuxEdu - Alogcode",
            "email_body_text": f"Däin Pin ass: {login_pin}",
            "email_body_html": f"<p>Däin Pin ass: {login_pin}</p>"
        }
    }

    try:
        sender_email = '<EMAIL>'
        if os.environ.get('ENVIRONMENT') == 'prod':
            sender_email = '<EMAIL>'
        if os.environ.get('ENVIRONMENT') == 'test':
            sender_email = '<EMAIL>'
        # Sending email using AWS SES

        response = ses_client.send_email(
            Source=sender_email,  # replace with your verified SES email
            Destination={'ToAddresses': [email_address]},
            Message={
                'Body': {
                    'Text': {
                        'Data': email_content[language]["email_body_text"],
                        'Charset': 'UTF-8'
                    },
                    'Html': {
                        'Data': email_content[language]["email_body_html"],
                        'Charset': 'UTF-8'
                    }
                },
                'Subject': {
                    'Data':  email_content[language]["email_subject"],
                    'Charset': 'UTF-8'
                }
            }
        )

    except Exception as e:
        raise CustomException("Could not generate forgot pin email!",
                              f"Could not generate forgot pin email! {e}", status_code=500, log_level="WARNING", error_type="internal_error")
