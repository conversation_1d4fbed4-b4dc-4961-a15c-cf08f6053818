from pydantic import BaseModel
from typing import Optional

class LoginChildAcccountRequest(BaseModel):
    child_account_login_alias: str
    child_account_pin: str


class CreateChildAcccountRequest(BaseModel):
    email: str
    name: str
    school_year_public_id: str
    language: str
    searchparams: Optional[str]
    pin: Optional[str]

class VerifyChildAcccountRequest(BaseModel):
    email: str
    verification_code: str

class LoginChildAcccountRequest(BaseModel):
    child_account_email: str
    child_account_pin: str


class SendChildAccountVerificationCodeRequest(BaseModel):
    email: str


class ParentAssignmentRequest(BaseModel):
    parent_email: str
    child_account_public_id: str
    language: str

class VerifyParentAcccountRequest(BaseModel):
    email: str
    verification_code: str
    language: str
    login_pin: str

class SendParentVerificationCodeRequest(BaseModel):
    email: str
    language: str

class ForgotPinRequest(BaseModel):
    child_account_email: str
    language: str

class CheckChildVerificationRequest(BaseModel):
    email: str
    language: str = 'en'