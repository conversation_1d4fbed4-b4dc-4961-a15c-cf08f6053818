from pydantic import BaseModel
from typing import Optional

class LoginChildAcccountResponse(BaseModel):
    access_token: str
    token_type: str
    child_account_name: str


class CreateChildAcccountResponse(BaseModel):
    access_token: str
    
class VerifyChildAcccountResponse(BaseModel):
    message: str


class LoginChildAcccountResponse(BaseModel):
    access_token: str
    child_account_public_id: str
    child_account_name: str
    parent_account_public_id: str | None

    
class SendChildAcccountVerificationCodeResponse(BaseModel):
    message: str

class SendParentVerificationCodeResponse(BaseModel):
    message: str

class ParentAssignmentResponse(BaseModel):
    message: str
    existing_parent_account: bool

class VerifyParentAcccountResponse(BaseModel):
    message: str
    access_token: str
    account_public_id: str
    id_token: str
    refresh_token: str | None

class CheckChildVerificationResponse(BaseModel):
    is_verified: bool
