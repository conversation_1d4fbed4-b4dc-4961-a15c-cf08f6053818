from fastapi import APIRouter
from .routes import change_school_year, completed_video, get_chapter, get_child_account_settings, get_child_courses, get_child_overview, get_course, get_lesson, get_paginated_exercises, get_year, submit_exercise, update_child_account

router = APIRouter()

# Include routes from all the files in this directory
router.include_router(completed_video.router)
router.include_router(get_chapter.router)
router.include_router(get_child_courses.router)
router.include_router(get_child_overview.router)
router.include_router(get_course.router)
router.include_router(get_lesson.router)
router.include_router(get_paginated_exercises.router)
router.include_router(get_year.router)
router.include_router(submit_exercise.router)
router.include_router(change_school_year.router)
router.include_router(get_child_account_settings.router)
router.include_router(update_child_account.router)
