import json
from tokenize import Name
from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPEx<PERSON>, Request
from sqlalchemy.orm import Session
from db.database import get_db
from api.routes.retail.utils.prepare_lesson import prepare_lesson
from ..models.response import GetLessonResponse
from utils.auth import has_valid_child_token
from utils.exceptions import CustomException
from db.models import  ChildAccount

router = APIRouter()
base_url = "/api/app/child/app"

EXERCISE_PAGINATION_SIZE = 10

@router.get(base_url + '/get-lesson', status_code=200, response_model=GetLessonResponse)
def get_lesson(lesson_public_id: str,request: Request, x_auth_child_token: str = Header(None), has_valid_token: bool = Depends(has_valid_child_token), db: Session = Depends(get_db)):
    
    child_account_public_id = request.state.decoded_child_token['child_account_public_id']
    if child_account_public_id:
        # Finding the relevant account and lesson
        relevant_child_account = db.query(ChildAccount).filter_by(
            child_account_public_id=child_account_public_id).first()

        if relevant_child_account:
            return prepare_lesson(lesson_public_id, 'child', relevant_child_account, db, EXERCISE_PAGINATION_SIZE)
        
        else:
            raise CustomException ("Not authorized!", "No corresponding ChildAccount found", status_code=401, error_type="permission_error")
    else:
        raise CustomException ("Not authorized!", "No child_account_public_id in token", status_code=401, error_type="permission_error")