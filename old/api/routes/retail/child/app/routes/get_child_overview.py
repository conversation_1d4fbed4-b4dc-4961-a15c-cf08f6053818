import json
from fastapi import API<PERSON><PERSON><PERSON>, <PERSON>pen<PERSON>, <PERSON><PERSON>, HTTPEx<PERSON>, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import ChildEvent, ChildEventType, Lesson,  ChildAccount, Chapter, ActiveSubscription, Account, SubscriptionOption, Year
from loguru import logger
import inspect
import logging
from sqlalchemy import desc, asc
from api.utils.analytics_utils import get_recent_statistics
from api.utils.analytics_utils import get_chapter_statistics
from utils.auth import has_valid_child_token
from ..models.response import GetChildOverviewResponse
from utils.exceptions import CustomException
from api.utils.media_utils import create_signed_video_url
import json
from datetime import datetime, timedelta
from api.routes.retail.utils import generate_activity_calendar
from api.utils.trial_utils import check_trial_status

router = APIRouter()
base_url = "/api/app/child/app"


@router.get(base_url + '/get-child-overview', status_code=200, response_model=GetChildOverviewResponse)
def get_child_overview(request: Request, x_auth_child_token: str = Header(None), has_valid_token: bool = Depends(has_valid_child_token), db: Session = Depends(get_db)):
    child_account_public_id = request.state.decoded_child_token['child_account_public_id']
    if child_account_public_id:
        relevant_child_account = db.query(ChildAccount).filter_by(
            child_account_public_id=child_account_public_id).first()
        if relevant_child_account:
            relevant_year = relevant_child_account.relevant_year
            # This will be an actual list later one, at the moment only ,maths so one entry
            # relevant_courses = relevant_year.relevant_courses
            relevant_course = relevant_year.relevant_courses[0]

            db_chapters = db.query(Chapter).filter(Chapter.relevant_course_id == relevant_course.course_id).filter_by(
                is_published=True).order_by(asc(Chapter.chapter_ordering)).all()

  # Also Fetch the lessons for each chapter in an efficient manner
            relevant_lessons = db.query(Lesson).filter(Lesson.relevant_chapter_id.in_(
                [chapter.chapter_id for chapter in db_chapters])).filter_by(is_published=True).order_by(asc(Lesson.lesson_ordering)).all()

            # Create a dictionary to map chapter_id to its lessons
            chapter_lessons_map = {}
            for lesson in relevant_lessons:
                if lesson.relevant_chapter_id not in chapter_lessons_map:
                    chapter_lessons_map[lesson.relevant_chapter_id] = []
                chapter_lessons_map[lesson.relevant_chapter_id].append({
                    "lesson_public_id": lesson.lesson_public_id,
                    "lesson_title": lesson.lesson_title,
                    "lesson_description": lesson.lesson_description,
                    "lesson_number": lesson.lesson_ordering,
                    "is_free": lesson.is_free
                })

            relevant_chapters = [
                {
                    "course_public_id": relevant_course.course_public_id,
                    "chapter_public_id": chapter.chapter_public_id,
                    "chapter_title": chapter.chapter_title,
                    "chapter_description": chapter.chapter_description,
                    "is_free": chapter.is_free,
                    "chapter_lessons": chapter_lessons_map.get(chapter.chapter_id, [])
                } for chapter in db_chapters]
            # courses = [{
            #     "course_public_id": course.course_public_id,
            #     "course_title": course.course_title,
            #     "course_description": course.course_description} for course in relevant_courses]

            # completed_lessons_count, completed_exercises_count, correct_exercises_share = get_recent_statistics(
            #     db, relevant_child_account)

            stats_by_chapter = get_chapter_statistics(
                db, relevant_child_account, relevant_course)

            statistics = {
                "by_chapter": stats_by_chapter
            }

            relevant_event_type = db.query(ChildEventType).filter_by(
                child_event_type_name='opened_lesson').first()

            relevant_event = db.query(ChildEvent).filter_by(child_event_type_id=relevant_event_type.child_event_type_id,
                                                            relevant_child_account_id=relevant_child_account.child_account_id).order_by(desc(ChildEvent.created_at)).first()

            latest_lesson = None

            if relevant_event == None:
                # We set the lesson the the year's first lesson
                first_chapter = db_chapters[0]

                relevant_lesson = db.query(Lesson).filter_by(
                    relevant_chapter_id=first_chapter.chapter_id).first()

                latest_lesson = {
                    "lesson_public_id": relevant_lesson.lesson_public_id,
                    "lesson_title": relevant_lesson.lesson_title,
                    "lesson_description": relevant_lesson.lesson_description,
                    "chapter_public_id": first_chapter.chapter_public_id,
                    "chapter_title": first_chapter.chapter_title,
                    "course_public_id": relevant_course.course_public_id,
                    "chapter_icon_id": first_chapter.chapter_icon_id,
                    "lesson_video_url": create_signed_video_url(relevant_lesson.lesson_video_id, 'lecture')
                }

            else:
                relevant_data = json.loads(relevant_event.child_event_data)
                # Neede to also send all the other lesson data
                relevant_lesson = db.query(Lesson).filter_by(
                    lesson_public_id=relevant_data['lesson_public_id']).first()
                relevant_chapter = relevant_lesson.relevant_chapter
                relevant_course = relevant_chapter.relevant_course

                latest_lesson = {
                    "lesson_public_id": relevant_lesson.lesson_public_id,
                    "lesson_title": relevant_lesson.lesson_title,
                    "lesson_description": relevant_lesson.lesson_description,
                    "chapter_public_id": relevant_chapter.chapter_public_id,
                    "chapter_title": relevant_chapter.chapter_title,
                    "course_public_id": relevant_course.course_public_id,
                    "chapter_icon_id": relevant_chapter.chapter_icon_id,
                    "lesson_video_url": create_signed_video_url(relevant_lesson.lesson_video_id, 'lecture')
                }

            activity_calendar = generate_activity_calendar(
                relevant_child_account, db)

            relevant_trial = None
            relevant_trial = check_trial_status(
                relevant_child_account, db, account_type='child')

            subscription_status = 'invalid'
            relevant_parent_account = db.query(Account).filter_by(
                account_id=relevant_child_account.child_account_parent_id).first()

            if relevant_parent_account:
                # Here we need to joing the active subscriptions against the options and the relevant years
                relevant_subscription = db.query(ActiveSubscription).filter_by(
                    account_id=relevant_parent_account.account_id, status='active').join(SubscriptionOption, ActiveSubscription.subscription_option_id == SubscriptionOption.subscription_option_id).join(Year, SubscriptionOption.year_id == Year.year_id).filter(Year.year_id == relevant_year.year_id).first()

                if relevant_subscription:
                    subscription_status = 'active'

            return GetChildOverviewResponse(
                show_intro=False,
                parent_account_public_id=relevant_parent_account.account_public_id if relevant_parent_account else None,
                latest_lesson=latest_lesson,
                relevant_chapters=relevant_chapters,
                statistics=statistics,
                activity_calendar=activity_calendar,
                trial=relevant_trial,
                subscription_status=subscription_status,
                current_year={
                    "year_name": relevant_year.year_name,
                    "year_public_id": relevant_year.year_public_id
                }
            )

        else:
            raise CustomException("Not authorized!", "No corresponding ChildAccount found",
                                  status_code=401, error_type="permission_error")
    else:
        raise CustomException("Not authorized!", "No child_account_public_id in token",
                              status_code=401, error_type="permission_error")
