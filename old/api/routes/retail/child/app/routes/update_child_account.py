from fastapi import API<PERSON><PERSON><PERSON>, Depends, Header, Request
from sqlalchemy.orm import Session
from sqlalchemy import asc
from db.database import get_db
from db.models import ChildAccount, Year
from ..models.response import UpdateChildAccountResponse
from ..models.request import UpdateChildAccountRequest
from utils.auth import has_valid_child_token
from utils.exceptions import CustomException
import re

router = APIRouter()
base_url = "/api/app/child/app"


def validate_email(email: str):
    if not re.match(r"[^@]+@[^@]+\.[^@]+", email):
        raise CustomException("Invalid email!", "Invalid email format", status_code=400,
                              error_type="validation_error")


def validate_pin(pin: str):
    if not pin.isdigit() or len(pin) != 4:
        raise CustomException("Invalid PIN!", "PIN must be 4 digits and numeric", status_code=400,
                              error_type="validation_error")


@router.post(base_url + '/update-child-account', status_code=200, response_model=UpdateChildAccountResponse)
def update_child_email(request: Request, changeRequest: UpdateChildAccountRequest, x_auth_child_token: str = Header(None), has_valid_token: bool = Depends(has_valid_child_token), db: Session = Depends(get_db)):
    child_account_public_id = request.state.decoded_child_token['child_account_public_id']
    if child_account_public_id:
        relevant_child_account = db.query(ChildAccount).filter_by(
            child_account_public_id=child_account_public_id).first()

        if relevant_child_account:
            if changeRequest.new_email:
                validate_email(changeRequest.new_email)
                # Check if email is already in use
                other_child_account = db.query(ChildAccount).filter_by(child_account_email=changeRequest.new_email).first()
                if other_child_account and relevant_child_account.child_account_id != other_child_account.child_account_id:
                    raise CustomException("Email already in use!", "Email is already in use by another user",
                                          error_type='email_already_in_use', status_code=400)
                relevant_child_account.child_account_email = changeRequest.new_email
            if changeRequest.new_pin:
                validate_pin(changeRequest.new_pin)
                relevant_child_account.child_account_pin = changeRequest.new_pin
            if changeRequest.new_name:
                relevant_child_account.child_account_name = changeRequest.new_name
            if changeRequest.new_year_public_id:
                relevant_new_year = db.query(Year).filter_by(
                    year_public_id=changeRequest.new_year_public_id).first()
                if relevant_new_year:
                    relevant_child_account.child_account_year_id = relevant_new_year.year_id
            db.commit()

            return UpdateChildAccountResponse(message="Child account updated successfully!")

        else:
            raise CustomException("Not authorized!", "No corresponding ChildAccount or Year found",
                                  status_code=401, error_type="permission_error")
    else:
        raise CustomException("Not authorized!", "No child_account_public_id in token",
                              status_code=401, error_type="permission_error")
