import json
from fastapi import API<PERSON><PERSON><PERSON>,  <PERSON>pen<PERSON>, Header, Request
from sqlalchemy.orm import Session, joinedload, load_only
from db.database import get_db
from db.models import Exercise, ChildAccount, ChildAccountExerciseAssociation, ChildEvent, ChildEventType
from api.utils.media_utils import create_signed_video_url
import uuid
from sqlalchemy import desc
from datetime import datetime, timedelta
from ..models.request import SubmitExerciseRequest
from ..models.response import SubmitExerciseResponse
from utils.auth import has_valid_child_token
from utils.exceptions import CustomException
import re
from sqlalchemy.orm import joinedload
from functools import lru_cache
from typing import Dict


router = APIRouter()
base_url = "/api/app/child/app"


def clean_answer(answer):
    # We get rid of any white space also within the string as well as * symbols in front of variable names so 2*x becomes2x 
    answer = answer.strip()
    answer = re.sub(r'\s*\*\s*', '', answer.replace(" ", ""))
    return answer


# Load all event types at module initialization
def _load_event_types() -> Dict[str, int]:
    db = next(get_db())
    event_types = db.query(ChildEventType.child_event_type_name, ChildEventType.child_event_type_id).all()
    return {name: id for name, id in event_types}

EVENT_TYPES = _load_event_types()

def get_event_type_id(event_name: str) -> int:
    """Get event type ID from pre-loaded mapping."""
    event_type_id = EVENT_TYPES.get(event_name)
    if event_type_id is None:
        raise CustomException(
            "Event type not found",
            "Invalid event type",
            status_code=404,
            error_type="not_found_error"
        )
    return event_type_id


def register_exercise_event(db, child_account, relevant_lesson, exercise_public_id, correctly_answered):
    event_type_id = get_event_type_id(
        "submitted_correct_exercise" if correctly_answered else "submitted_incorrect_exercise"
    )
    
    if not event_type_id:
        raise CustomException(
            "Event type not found",
            "Invalid event type",
            status_code=404,
            error_type="not_found_error"
        )

    event_data = {
        "lesson_public_id": relevant_lesson.lesson_public_id,
        "lesson_title": relevant_lesson.lesson_title,
        "exercise_public_id": exercise_public_id,
        "correctly_answered": correctly_answered
    }

    new_event = ChildEvent(
        child_event_public_id=str(uuid.uuid4()),
        child_event_type_id=event_type_id,
        relevant_child_account_id=child_account.child_account_id,
        child_event_data=json.dumps(event_data)
    )
    db.add(new_event)
    return new_event


def get_exercise_with_related_data(db: Session, exercise_public_id: str, child_account_public_id: str):
    """Fetch exercise and related data in a single query with specific column selection"""
    exercise = (
        db.query(Exercise)
        .filter(Exercise.exercise_public_id == exercise_public_id)
        .options(
            # Use load_only to specify which columns to eagerly load
            # while maintaining the Exercise object instance
            load_only(
                'exercise_id',
                'exercise_public_id',
                'exercise_answer_type',
                'exercise_correct_answer',
                'exercise_solution_video_public_id',
                'relevant_lesson_id'
            ),
            # Join loading for the related lesson
            joinedload(Exercise.relevant_lesson).load_only(
                'lesson_id', 
                'lesson_public_id', 
                'lesson_title'
            )
        )
        .first()
    )
    
    if not exercise:
        raise CustomException(
            "Exercise not found",
            "Invalid exercise_public_id",
            status_code=404,
            error_type="not_found_error"
        )

    child_account = (
        db.query(ChildAccount)
        .filter(ChildAccount.child_account_public_id == child_account_public_id)
        .first()
    )

    if not child_account:
        raise CustomException(
            "Child account not found",
            "Invalid child_account_public_id",
            status_code=404,
            error_type="not_found_error"
        )
    
    return exercise, child_account


def check_multiple_choice_answer(exercise, submitted_answer):
    """Validate multiple choice answers"""
    if exercise.exercise_answer_type == "mc_simple":
        correct_answer = exercise.exercise_correct_answer[0]
        return {
            "correctly_answered": correct_answer == submitted_answer,
            "correct_answer": exercise.exercise_correct_answer
        }
    
    # For mc_multi
    correct_answer = exercise.exercise_correct_answer
    correctly_answered = (
        all(option in submitted_answer for option in correct_answer) and
        len(submitted_answer) == len(correct_answer)
    )
    return {
        "correctly_answered": correctly_answered,
        "correct_answer": correct_answer
    }


def check_text_answer(exercise, submitted_answer):
    """Validate text/boolean answers"""
    correct_answer = (exercise.exercise_correct_answer[0] 
                     if isinstance(exercise.exercise_correct_answer, list)
                     else exercise.exercise_correct_answer)
    
    return {
        "correctly_answered": clean_answer(submitted_answer) == clean_answer(correct_answer),
        "correct_answer": correct_answer
    }


@router.post(base_url + '/submit-exercise', status_code=200, response_model=SubmitExerciseResponse)
def submit_exercise(
    request: Request, 
    submitted_exercise: SubmitExerciseRequest,
    x_auth_child_token: str = Header(None),
    has_valid_token: bool = Depends(has_valid_child_token),
    db: Session = Depends(get_db)
):
    child_account_public_id = request.state.decoded_child_token['child_account_public_id']
    if not child_account_public_id:
        raise CustomException(
            "Not authorized!", 
            "No child_account_public_id in token",
            status_code=401, 
            error_type="permission_error"
        )

    # Get exercise and related data in a single query
    result = get_exercise_with_related_data(
        db, 
        submitted_exercise.exercise_public_id,
        child_account_public_id
    )
    
    if not result:
        raise CustomException(
            "Exercise not found",
            "Invalid exercise_public_id",
            status_code=404,
            error_type="not_found_error"
        )

    exercise, child_account = result
    relevant_lesson = exercise.relevant_lesson

    # Check for recent submission
    has_recent_submission = (
        db.query(ChildAccountExerciseAssociation)
        .filter(
            ChildAccountExerciseAssociation.relevant_child_account_id == child_account.child_account_id,
            ChildAccountExerciseAssociation.relevant_exercise_id == exercise.exercise_id,
            ChildAccountExerciseAssociation.created_at >= datetime.now() - timedelta(days=1)
        )
        .first() is not None
    )

    # Validate answer based on exercise type
    if exercise.exercise_answer_type in ("mc_simple", "mc_multi"):
        result = check_multiple_choice_answer(exercise, submitted_exercise.answer)
    else:
        result = check_text_answer(exercise, submitted_exercise.answer)

    correctly_answered = result["correctly_answered"]
    correct_answer = result["correct_answer"]

    # Always create and add the event to the session
    new_event = register_exercise_event(
        db, 
        child_account, 
        relevant_lesson,
        exercise.exercise_public_id, 
        correctly_answered
    )   # This adds the event to the session via db.add(new_event)
    
    # Optionally create and add the association if no recent submission
    if not has_recent_submission:
        new_association = ChildAccountExerciseAssociation(
            association_public_id=str(uuid.uuid4()),
            relevant_exercise_id=exercise.exercise_id,
            association_correctly_answered=correctly_answered,
            association_given_answer=json.dumps(submitted_exercise.answer) if isinstance(submitted_exercise.answer, list) else submitted_exercise.answer,
            relevant_child_account_id=child_account.child_account_id,
            relevant_lesson_id=relevant_lesson.lesson_id
        )
        db.add(new_association)

    # Commit the session, saving both the event and (if created) the association
    db.commit()

    return {
        "message": "Recently submitted!" if has_recent_submission else "Exercise submitted successfully!",
        "correctly_answered": correctly_answered,
        "recently_submitted": has_recent_submission,
        "correct_answer": correct_answer,
        "solution_video_url": create_signed_video_url(exercise.exercise_solution_video_public_id, 'exercise')
    }
