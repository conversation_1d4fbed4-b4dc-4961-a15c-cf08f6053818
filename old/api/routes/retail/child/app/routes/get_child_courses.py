from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON>pen<PERSON>, <PERSON><PERSON>, HTTPEx<PERSON>, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import ChildAccount
from loguru import logger
import inspect
import logging
from ..models.response import GetChildCoursesResponse
from utils.auth import has_valid_child_token
from utils.exceptions import CustomException

router = APIRouter()
base_url = "/api/app/child/app"


@router.get(base_url + '/get-child-courses', status_code=200, response_model=GetChildCoursesResponse)
def get_child_courses( x_auth_child_token: str = Header(None), has_valid_token: bool = Depends(has_valid_child_token),  db: Session = Depends(get_db), request: Request = None):
    child_account_public_id = request.state.decoded_child_token['child_account_public_id']
    if child_account_public_id:
        relevant_child_account = db.query(ChildAccount).filter_by(
            child_account_public_id=child_account_public_id).first()
        if relevant_child_account:
            relevant_year = relevant_child_account.relevant_year
            relevant_courses = relevant_year.courses
            courses = [{
                "course_public_id": course.course_public_id,
                "course_title": course.course_title,
                "course_description": course.course_description} for course in relevant_courses]

            return {"relevant_courses": courses}
        else:
            raise CustomException ("Not authorized!", "No corresponding ChildAccount found", status_code=401, error_type="permission_error")
    else:
        raise CustomException ("Not authorized!", "No child_account_public_id in token", status_code=401, error_type="permission_error")