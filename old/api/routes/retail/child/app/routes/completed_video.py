from tokenize import Name
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depen<PERSON>, <PERSON><PERSON>, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Lesson, ChildAccount, ChildAccountLessonAssociation
from utils.exceptions import CustomException
from ..models.request import CompletedVideoRequest
from ..models.response import CompletedVideoResponse

from utils.auth import has_valid_child_token
import uuid


router = APIRouter()
base_url = "/api/app/child/app"


@router.post(base_url + '/completed-video', status_code=200, response_model=CompletedVideoResponse)
def completed_video(request: Request, completed_video_request: CompletedVideoRequest, x_auth_child_token: str = Header(None), has_valid_token: bool = Depends(has_valid_child_token), db: Session = Depends(get_db)):
    child_account_public_id = request.state.decoded_child_token['child_account_public_id']
    if child_account_public_id:
        relevant_child_account = db.query(ChildAccount).filter_by(
            child_account_public_id=child_account_public_id).first()

        if relevant_child_account:
            relevant_lesson = db.query(Lesson).filter_by(
                lesson_public_id=completed_video_request.lesson_public_id).first()

            relevant_association = db.query(ChildAccountLessonAssociation).filter_by(
                relevant_child_account_id=relevant_child_account.child_account_id, relevant_lesson_id=relevant_lesson.lesson_id).first()

            if not relevant_association:
                new_association = ChildAccountLessonAssociation(
                    association_public_id=str(uuid.uuid4()),
                    relevant_child_account_id=relevant_child_account.child_account_id,
                    relevant_lesson_id=relevant_lesson.lesson_id
                )
                db.add(new_association)
                db.commit()
                return {"message": "Lesson marked as completed!"}
            else:
                raise CustomException(
                    status_code=400,
                    error_type="LessonAlreadyCompleted",
                    log_message="Lesson already marked as completed!",
                    return_message="Lesson already marked as completed!",
                    log_level="DEBUG"
                )
                # raise HTTPException(
                #     status_code=400, detail="Lesson already marked as completed!")
        else:
            raise CustomException("Not authorized!", "No corresponding ChildAccount found",
                                  status_code=401, error_type="permission_error")
    else:
        raise CustomException("Not authorized!", "No child_account_public_id in token",
                              status_code=401, error_type="permission_error")
