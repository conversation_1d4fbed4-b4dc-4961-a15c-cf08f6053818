from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Header, Request
from sqlalchemy.orm import Session
from sqlalchemy import asc
from db.database import get_db
from db.models import  ChildAccount, Year
from ..models.response import ChangeYearResponse
from ..models.request import ChangeYearRequest
from utils.auth import has_valid_child_token
from utils.exceptions import CustomException


router = APIRouter()
base_url = "/api/app/child/app"


@router.post(base_url + '/change-school-year', status_code=200, response_model=ChangeYearResponse)
def change_year(request: Request, changeRequest: ChangeYearRequest, x_auth_child_token: str = Header(None), has_valid_token: bool = Depends(has_valid_child_token), db: Session = Depends(get_db)):
    child_account_public_id = request.state.decoded_child_token['child_account_public_id']
    if child_account_public_id:
        relevant_child_account = db.query(ChildAccount).filter_by(
            child_account_public_id=child_account_public_id).first()
        relevant_new_year = db.query(Year).filter_by(
            year_public_id=changeRequest.year_public_id).first()

        if relevant_child_account and relevant_new_year:
            relevant_child_account.child_account_year_id = relevant_new_year.year_id
            db.commit()
            return ChangeYearResponse(message="Year changed successfully!", new_year_public_id=relevant_new_year.year_public_id)
        else:
            raise CustomException("Not authorized!", "No corresponding ChildAccount or Year found",
                                  status_code=401, error_type="permission_error")
    else:
        raise CustomException("Not authorized!", "No child_account_public_id in token",
                              status_code=401, error_type="permission_error")
