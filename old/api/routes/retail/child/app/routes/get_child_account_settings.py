from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import ChildAccount, Year
from ..models.response import GetChildAccountSettingsResponse
from utils.auth import has_valid_child_token
from utils.exceptions import CustomException

router = APIRouter()
base_url = "/api/app/child/app"

@router.get(base_url + '/get-child-account-settings', status_code=200, response_model=GetChildAccountSettingsResponse)
def get_child_account_settings(request: Request, x_auth_child_token: str = Header(None), has_valid_token: bool = Depends(has_valid_child_token), db: Session = Depends(get_db)):
    child_account_public_id = request.state.decoded_child_token['child_account_public_id']
    if child_account_public_id:
        relevant_child_account = db.query(ChildAccount).filter_by(
            child_account_public_id=child_account_public_id).first()
        
        relevant_year = db.query(Year).filter_by(year_id=relevant_child_account.child_account_year_id).first()

        if relevant_child_account and relevant_year:
            return GetChildAccountSettingsResponse(child_account_email=relevant_child_account.child_account_email,
                                                   child_account_pin=relevant_child_account.child_account_pin,
                                                   child_account_name=relevant_child_account.child_account_name,
                                                   child_account_year_public_id=relevant_year.year_public_id)
        else:
            raise CustomException("Not authorized!", "No corresponding ChildAccount or Year found",
                                  status_code=401, error_type="permission_error")
    else:
        raise CustomException("Not authorized!", "No child_account_public_id in token",
                              status_code=401, error_type="permission_error")
