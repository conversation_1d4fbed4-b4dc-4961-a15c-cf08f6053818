from fastapi import API<PERSON><PERSON><PERSON>, Depends, Header, Request
from sqlalchemy.orm import Session
from sqlalchemy import asc
from db.database import get_db
from db.models import Chapter, Lesson
from ..models.response import GetChapterResponse
from utils.auth import has_valid_child_token
from utils.exceptions import CustomException

router = APIRouter()
base_url = "/api/app/child/app"


@router.get(base_url + '/get-chapter', status_code=200, response_model=GetChapterResponse)
def get_chapter(request: Request, chapter_public_id: str, x_auth_child_token: str = Header(None), has_valid_token: bool = Depends(has_valid_child_token),  db: Session = Depends(get_db)):
    child_account_public_id = request.state.decoded_child_token['child_account_public_id']
    if child_account_public_id:
        relevant_chapter = db.query(Chapter).filter_by(
            chapter_public_id=chapter_public_id).first()

     # Query chapters related to relevant_course and order them
        relevant_lessons = (
            db.query(Lesson)
            # Assuming relationship is defined with course_id
            .filter(Lesson.relevant_chapter_id == relevant_chapter.chapter_id)
            .filter_by(is_published=True)
            .order_by(asc(Lesson.lesson_ordering))
            .all()
        )

        relevant_lessons = [
            lesson for lesson in relevant_lessons if lesson.is_published]

        relevant_course = relevant_chapter.relevant_course
        chapter = {
            "course_public_id": relevant_course.course_public_id,
            "course_title": relevant_course.course_title,
            "chapter_title": relevant_chapter.chapter_title,
            "chapter_description": relevant_chapter.chapter_description,
            "chapter_public_id": relevant_chapter.chapter_public_id,
            "is_free": relevant_chapter.is_free,
            "chapter_lessons": [{
                "lesson_number": index + 1,
                "is_free": lesson.is_free,
                "lesson_public_id": lesson.lesson_public_id,
                "lesson_title": lesson.lesson_title,
                "lesson_description": lesson.lesson_description} for index, lesson in enumerate(relevant_lessons)]}
        return {"chapter": chapter}
    else:
        raise CustomException("Not authorized!", "No child_account_public_id in token",
                              status_code=401, error_type="permission_error")
