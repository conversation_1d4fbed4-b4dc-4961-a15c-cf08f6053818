
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depen<PERSON>, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import ChildAccount
from ..models.request import UpdateIntroStatusRequest
from ..models.response import UpdateIntroStatusResponse
from utils.auth import has_valid_child_token
from utils.exceptions import CustomException

router = APIRouter()
base_url = "/api/app/child/app"

@router.post(base_url + '/update-intro-status', status_code=200, response_model=UpdateIntroStatusResponse)
def update_intro_status(request: Request, status: UpdateIntroStatusRequest,x_auth_child_token: str = Header(None),  has_valid_token: bool = Depends(has_valid_child_token),db: Session = Depends(get_db)):
    child_account_public_id = request.state.decoded_child_token['child_account_public_id']

    if child_account_public_id:
        relevant_child = db.query(ChildAccount).filter_by(
            child_account_public_id=child_account_public_id).first()

        if relevant_child:
            relevant_child.show_intro = status.show_intro
            db.commit()
            return {"message": "Intro status updated successfully"}
        else:
            raise CustomException("Child not found", "Child not found",
                                  status_code=403, log_level="INFO", error_type="internal_error")
    else:
        raise CustomException("Child not found", "Child not found",
                              status_code=403, log_level="INFO", error_type="internal_error")
    