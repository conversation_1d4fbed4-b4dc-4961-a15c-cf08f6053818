from pydantic import BaseModel
from typing import List, Optional

from api.routes.retail.models.response import BaseResponse, Exercise, Lesson, Chapter, Course

# CompletedVideoResponse
class CompletedVideoResponse(BaseResponse):
    pass

# GetChapterResponse
class GetChapterResponse(BaseModel):
    chapter: Chapter

# SubmitExerciseResponse
class SubmitExerciseResponse(BaseModel):
    message: str
    correctly_answered: bool
    recently_submitted: bool
    correct_answer: str | List[str]
    solution_video_url: str

# GetChildCoursesResponse
class GetChildCoursesResponse(BaseModel):
    relevant_courses: List[Course]


# GetChildOverviewResponse
class ByChapterStats(BaseModel):
    chapter_public_id: str
    chapter_title: str
    completed_lessons: List[str]
    completed_lessons_count: int
    completed_exercises_count: int
    correct_exercises_share: float
    total_exercise_count: int
    total_lesson_count: int

class RecentChildStats(BaseModel):
    completed_lessons_count: int
    completed_exercises_count: int
    correct_exercises_share: float

class ChildStatistics(BaseModel):
    recent: RecentChildStats
    by_chapter: List[ByChapterStats]

class GetChildOverviewResponse(BaseModel):
    parent_account_public_id: str | None
    show_intro: bool
    latest_lesson: Lesson
    relevant_chapters: List[Chapter]
    # TODO: Specify full response
    statistics: dict
    activity_calendar: List[dict]
    trial: Optional[dict]
    subscription_status: Optional[str]
    class CurrentYear(BaseModel):
        year_name: str
        year_public_id: str
    current_year: CurrentYear

# GetCourseResponse
class GetCourseResponse(Course):
    course_chapters: List[Chapter]
    account_meta_data: dict


# GetLessonResponse
class CoursOverview(BaseModel):
    course_title: str
    course_public_id: str

    chapter_title: str
    chapter_public_id: str

    lesson_title: str
    lesson_public_id: str

class GetLessonResponse(BaseModel):
    subscription_info: Optional[dict]
    subscription_status: str
    # year_name: Optional[str]
    # year_public_id: Optional[str]
    # course_title: Optional[str]
    # course_public_id: Optional[str]
    course_overview: Optional[CoursOverview]
    lesson: Optional[Lesson]
    has_completed_video: Optional[bool]
    exercises: Optional[List[dict]]
    trial: Optional[dict]


# GetPaginatedExercisesResponse
class GetPaginatedExercisesResponse(BaseModel):
    subscription_status: str
    exercises: Optional[List[Exercise]]
    has_more: Optional[bool]

# GetYearResponse
class GetYearResponse(BaseModel):
    year_name: str
    courses: List[Course]

class UpdateChildAccountResponse(BaseResponse):
    message: str

class UpdateIntroStatusResponse(BaseResponse):
    pass

class GetChildAccountSettingsResponse(BaseModel):
    child_account_email: str
    child_account_pin: str
    child_account_name: str
    child_account_year_public_id: str


class ChangeYearResponse(BaseModel):
    message: str
    new_year_public_id: str