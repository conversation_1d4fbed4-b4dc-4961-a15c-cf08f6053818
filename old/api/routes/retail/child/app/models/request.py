from pydantic import BaseModel
from typing import List, Optional

class CompletedVideoRequest(BaseModel):
    lesson_public_id: str

class SubmitExerciseRequest(BaseModel):
# The answer will either be a string in the case of an input exercise
# or a list with the selected option_ids in the case of a multiple choice exercise
    exercise_public_id: str
    answer: str | List[str]


class UpdateIntroStatusRequest(BaseModel):
    show_intro: bool

class UpdateChildAccountRequest(BaseModel):
    new_email: Optional[str]
    new_pin: Optional[str]
    new_name: Optional[str]
    new_year_public_id: Optional[str]

class ChangeYearRequest(BaseModel):
    year_public_id: str
