
from fastapi import API<PERSON><PERSON><PERSON>, Depends, Header, Request
from sqlalchemy.orm import Session
from sqlalchemy import asc
from db.database import get_db
from db.models import Chapter, Lesson
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from ..models.response import GetChapterResponse
router = APIRouter()
base_url = "/api/app/free"


@router.get(base_url + '/get-chapter', status_code=200, response_model=GetChapterResponse)
def get_chapter(request: Request, chapter_public_id: str, db: Session = Depends(get_db)):
    relevant_chapter = db.query(Chapter).filter_by(
        chapter_public_id=chapter_public_id).first()

    relevant_lessons = db.query(Lesson).filter_by(
        relevant_chapter_id=relevant_chapter.chapter_id).all()
    relevant_course = relevant_chapter.relevant_course

    # Query chapters related to relevant_course and order them
    relevant_lessons = (
        db.query(Lesson)
        # Assuming relationship is defined with course_id
        .filter(Lesson.relevant_chapter_id == relevant_chapter.chapter_id)
        .filter_by(is_published=True)
        .order_by(asc(Lesson.lesson_ordering))
        .all()
    )

    relevant_lessons = [
        lesson for lesson in relevant_lessons if lesson.is_published]

    chapter = {
        "course_public_id": relevant_course.course_public_id,
        "course_title": relevant_course.course_title,
        "chapter_title": relevant_chapter.chapter_title,
        "chapter_description": relevant_chapter.chapter_description,
        "chapter_public_id": relevant_chapter.chapter_public_id,
        "chapter_lessons": [{
            "lesson_number": index + 1,
            "is_free": lesson.is_free,
            "lesson_public_id": lesson.lesson_public_id,
            "lesson_title": lesson.lesson_title,
            "lesson_description": lesson.lesson_description} for index, lesson in enumerate(relevant_lessons)]}

    return {"chapter": chapter}
