from fastapi import APIRouter, Depends, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Year
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from ..models.response import GetYearsResponse

router = APIRouter()
base_url = "/api/app/free"

# All routes that are related to the course overview
# This is currently a temporary fix that only returns the math courses


@router.get(base_url + '/get-years', status_code=200, response_model=GetYearsResponse)
def get_courses(request: Request, db: Session = Depends(get_db)):
    years = db.query(Year).order_by(Year.year_id).all()
    out = []
    for year in years:
        relevant_courses = year.relevant_courses
        if relevant_courses:
            relevant_course = relevant_courses[0]
            chapters = [
                {
                    "chapter_public_id": chapter.chapter_public_id,
                    "chapter_title": chapter.chapter_title,
                    "chapter_description": chapter.chapter_description,
                    "is_free": chapter.is_free,
                    "chapter_ordering": getattr(chapter, 'chapter_ordering', None)
                }
                for chapter in relevant_course.chapters
                if chapter.is_published
            ]

            # Sort chapters based on chapter_number if available, otherwise use chapter_ordering
            chapters = sorted(
                chapters, key=lambda x: x.get('chapter_ordering'))

            out.append({
                "year_public_id": year.year_public_id,
                "year_name": year.year_name,
                "chapters": chapters
            })

    return GetYearsResponse(years=out)
