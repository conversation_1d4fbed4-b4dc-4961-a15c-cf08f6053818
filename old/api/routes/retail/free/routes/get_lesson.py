
from fastapi import APIRouter, Depends, Header, Request
from sqlalchemy.orm import Session
from loguru import logger
from db.database import get_db
from api.routes.retail.utils import prepare_lesson
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from db.models import Account
from ..models.response import GetLessonResponse

router = APIRouter()
base_url = "/api/app/free"

EXERCISE_PAGINATION_SIZE = 10

# Send child account with as well


@router.get(base_url + '/get-lesson', status_code=200, response_model=GetLessonResponse)
def get_lesson(lesson_public_id: str, db: Session = Depends(get_db)):
    # Finding the relevant account and lesson
    return prepare_lesson(lesson_public_id, 'free', None, db, EXERCISE_PAGINATION_SIZE)
