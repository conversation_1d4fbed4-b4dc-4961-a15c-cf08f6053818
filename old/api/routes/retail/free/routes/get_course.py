
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Course, Chapter, Lesson
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from ..models.response import GetCourseResponse
from sqlalchemy import asc
router = APIRouter()
base_url = "/api/app/free"


@router.get(base_url + '/get-course', status_code=200, response_model=GetCourseResponse)
def get_course(request: Request, course_public_id: str, db: Session = Depends(get_db)):
    relevant_course = db.query(Course).filter_by(
        course_public_id=course_public_id).first()

    # Query chapters related to relevant_course and order them
    relevant_chapters = (
        db.query(Chapter)
        # Assuming relationship is defined with course_id
        .filter(Chapter.relevant_course_id == relevant_course.course_id)
        .filter_by(is_published=True)
        .order_by(asc(Chapter.chapter_ordering))
        .all()
    )

    # Also Fetch the lessons for each chapter in an efficient manner
    relevant_lessons = db.query(Lesson).filter(Lesson.relevant_chapter_id.in_(
        [chapter.chapter_id for chapter in relevant_chapters])).filter_by(is_published=True).order_by(asc(Lesson.lesson_ordering)).all()

    chapter_lessons_map = {}
    for lesson in relevant_lessons:
        if lesson.relevant_chapter_id not in chapter_lessons_map:
            chapter_lessons_map[lesson.relevant_chapter_id] = []
        chapter_lessons_map[lesson.relevant_chapter_id].append({
            "lesson_public_id": lesson.lesson_public_id,
            "lesson_title": lesson.lesson_title,
            "lesson_description": lesson.lesson_description,
            "lesson_number": lesson.lesson_ordering,
            "is_free": lesson.is_free
        })

    return {
        "course_title": relevant_course.course_title,
        "course_description": relevant_course.course_description,
        "course_public_id": relevant_course.course_public_id,
        "course_chapters": [{
            "chapter_number": index + 1,
            "is_free": chapter.is_free,
            "chapter_public_id": chapter.chapter_public_id,
            "chapter_title": chapter.chapter_title,
            "chapter_description": chapter.chapter_description,
            "lessons": chapter_lessons_map.get(chapter.chapter_id, [])
        } for index, chapter in enumerate(relevant_chapters)],
        "account_meta_data": {
        }
    }

