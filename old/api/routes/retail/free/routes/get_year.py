
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import  Year
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from ..models.response import GetYearResponse

router = APIRouter()
base_url = "/api/app/parent/app"

@router.get(base_url + '/get-year', status_code=200, response_model=GetYearResponse)
def get_year(request: Request, year_public_id: str, x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token),  db: Session = Depends(get_db)):
    parent_public_id = request.state.parent_uuid
    if parent_public_id:
        relevant_year = db.query(Year).filter_by(
            year_public_id=year_public_id).first()
        relevant_courses = relevant_year.relevant_courses

        courses = [{
            "course_public_id": course.course_public_id,
            "course_title": course.course_title,
            "course_description": course.course_description} for course in relevant_courses]

        return {"year_name": relevant_year.year_name, "courses": courses, "year_public_id": relevant_year.year_public_id}

    else:
        raise CustomException ("Not authorized!", "No child_account_public_id in token", status_code=401, error_type="permission_error")