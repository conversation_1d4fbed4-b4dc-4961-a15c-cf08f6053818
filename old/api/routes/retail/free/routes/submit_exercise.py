import json
from fastapi import <PERSON><PERSON><PERSON><PERSON>,  <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import  Exercise, ChildAccount, ChildAccountExerciseAssociation, ChildEvent, ChildEventType
from api.utils.media_utils import create_signed_video_url
import uuid
from sqlalchemy import desc
from datetime import datetime
from ..models.request import SubmitExerciseRequest
from ..models.response import SubmitExerciseResponse
from utils.auth import has_valid_child_token
from utils.exceptions import CustomException

router = APIRouter()
base_url = "/api/app/free"

@router.post(base_url + '/submit-exercise', status_code=200, response_model=SubmitExerciseResponse)
def submit_exercise( submitted_exercise: SubmitExerciseRequest, db: Session = Depends(get_db)):
    # The answer will either be a simple string in case of a simple answer
    # In case of a true/false answer, it will be a string
    # In case of a multiple choice answer, it will be a list with the chosen option_public_id
    # If we have a mc_multi answer, we will have a list with multiple option_public_ids

    # We first find the relevant child account and exercise
    relevant_exercise = db.query(Exercise).filter_by(
        exercise_public_id=submitted_exercise.exercise_public_id).first()

    if relevant_exercise.exercise_answer_type == "mc_simple" or relevant_exercise.exercise_answer_type == "mc_multi":
        # Check if the time difference is greater than 24 hours (86400 seconds)
        correctly_answered = True
        # We need to check if all the elements of the submitted answer are in the correct answer
        # Likely the list will be the same but no sure if we always get them in the same order

        # Need to parse the correct answer first, which will then turn the string in the db into a list
        correct_answer = relevant_exercise.exercise_correct_answer

        # if type(relevant_exercise.exercise_correct_answer) == str:
        #     correct_answer = relevant_exercise.exercise_correct_answer
        # elif type(relevant_exercise.exercise_correct_answer) == list:
        #     correct_answer= relevant_exercise.exercise_correct_answer[0]
        # correct_answer = json.loads(
        #     relevant_exercise.exercise_correct_answer)

        for option_public_id in correct_answer:
            # First check if any is missing
            if option_public_id not in submitted_exercise.answer:
                correctly_answered = False
                break

        # Then check if any is extra
        if correctly_answered and (len(submitted_exercise.answer) != len(correct_answer)):
            correctly_answered = False
        
        # TODO: Register exercise submission event

        return {
            "message": "Exercise submitted successfully!",
            "correctly_answered": correctly_answered,
            "recently_submitted": False,
            "correct_answer": correct_answer,
            "solution_video_url": create_signed_video_url(relevant_exercise.exercise_solution_video_public_id, 'exercise')

        }

    # If it is a input or a true_false answer, we just check if the answer is correct, hence if the strings match
    else:
        if type(relevant_exercise.exercise_correct_answer) == str:
            correct_answer = relevant_exercise.exercise_correct_answer
        elif type(relevant_exercise.exercise_correct_answer) == list:
            correct_answer= relevant_exercise.exercise_correct_answer[0]
        correctly_answered = False
        if submitted_exercise.answer == correct_answer:
            correctly_answered = True
            
        return {
            "message": "Exercise submitted successfully!",
            "correctly_answered": correctly_answered,
            "recently_submitted": False,
            "correct_answer": correct_answer,
            "solution_video_url": create_signed_video_url(relevant_exercise.exercise_solution_video_public_id, 'exercise')
        }