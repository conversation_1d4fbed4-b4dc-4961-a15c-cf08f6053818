

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depen<PERSON>, Header, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account, Year, ChildAccount
import uuid
from utils.auth import has_valid_parent_token
from utils.exceptions import CustomException
from ..models.request  import UpdateConsentRequest
from ..models.response import UpdateConsentResponse


router = APIRouter()
base_url = "/api/app/parent/app"

@router.post(base_url + '/update-consent', status_code=200, response_model=UpdateConsentResponse)
def add_child_account(request: Request, updated_consent: UpdateConsentRequest, x_auth_parent_token: str = Header(None), has_valid_token: bool = Depends(has_valid_parent_token), db: Session = Depends(get_db)):
    parent_account_public_id = request.state.parent_uuid
    if parent_account_public_id:
        # Finding the relevant account and lesson
        relevant_parent_account = db.query(Account).filter_by(
            account_cognito_id=parent_account_public_id).first()

        


        if relevant_parent_account:
            relevant_child_account = db.query(ChildAccount).filter_by(child_account_public_id=updated_consent.child_account_public_id).first()
            if relevant_child_account:
                relevant_child_account.consent = updated_consent.consent
                db.commit()
                return {"message": "Consent updated successfully!"}
            else:
                raise CustomException ("Not authorized!", "No corresponding Child Account found", status_code=401, error_type="permission_error")
        else:
            raise CustomException ("Not authorized!", "No corresponding Account found", status_code=401, error_type="permission_error")
    else:
        raise CustomException ("Not authorized!", "No child_account_public_id in token", status_code=401, error_type="permission_error")