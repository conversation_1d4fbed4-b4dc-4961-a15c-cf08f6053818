from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import PreSignUp
from utils.exceptions import CustomException
from ..models.request import SubmitPresignupRequest
from ..models.response import SubmitPresignupResponse
router = APIRouter()
base_url = "/api/analytics/landing"


@router.post(base_url + '/submit-pre-signup', status_code=201, response_model=SubmitPresignupResponse)
def register(pre_signup: SubmitPresignupRequest, db: Session = Depends(get_db)):
    try:
        new_pre_signup = PreSignUp(
            pre_sign_up_email=pre_signup.email,
            pre_sign_up_language=pre_signup.language,
            pre_sign_up_source=pre_signup.source,
            pre_sign_up_user_type=pre_signup.user_type)
        
        db.add(new_pre_signup)
        db.commit()
        return {"message": "Success!"}
    except Exception as e:
        raise CustomException("Error", str(
            e), status_code=500, error_type="server_error")
