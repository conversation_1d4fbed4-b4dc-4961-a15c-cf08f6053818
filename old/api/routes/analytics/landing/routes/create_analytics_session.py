
from fastapi import <PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,Depends
import uuid
from utils.exceptions import CustomException
from ..models.response import CreateSessionResponse
from ..models.request import CreateSessionRequest
from datetime import  timedelta
from api.utils.auth_utils import  create_access_token
import os
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import AnalyticsEvent
import json

router = APIRouter()
base_url = "/api/analytics/landing"

ACCESS_TOKEN_EXPIRE_MINUTES = int(os.environ.get("JWT_ACCESS_TOKEN_EXPIRE_MINUTES"))


@router.post(base_url + '/create-analytics-session', status_code=201, response_model=CreateSessionResponse)
def create_analytics_session(new_session: CreateSessionRequest, user_agent: str | None = Header(default=None), db: Session = Depends(get_db)):
    try:
        if new_session.consent:
            session_id = str(uuid.uuid4())
        else:
            session_id = "do_not_track" 

        new_session_event = AnalyticsEvent(
            analytics_event_public_id = str(uuid.uuid4()),
            analytics_event_name = "initiate_session",
            analytics_event_session_id = session_id,
            analytics_event_data = json.dumps({"event_name":"initiate_session", "entry_point": new_session.entry_point})
        )
        db.add(new_session_event)
        db.commit()

        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
                        data={"session_id": session_id, 'user_agent': new_session.user_agent}, expires_delta=access_token_expires
                    )
        return {"access_token": access_token}

    except Exception as e:
        raise CustomException ("Failed to create session", str(e), status_code=500, error_type="server_error")





# '/register-meta-event