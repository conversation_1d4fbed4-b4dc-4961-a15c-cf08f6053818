from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Questionnaire
from utils.exceptions import CustomException
from ..models.request import SubmitQuestionnaireRequest
from ..models.response import SubmitQuestionnaireResponse

router = APIRouter()
base_url = "/api/analytics/landing"


@router.post(base_url + '/submit-questionnaire', status_code=201, response_model=SubmitQuestionnaireResponse)
def register(submittedQuestionnaire: SubmitQuestionnaireRequest, db: Session = Depends(get_db)):
    try:
        new_questionnaire = Questionnaire(
            questionnaire_data=submittedQuestionnaire.data)
        
        db.add(new_questionnaire)
        db.commit()
        return {"message": "Success!"}
    except Exception as e:
        raise CustomException("Error", str(
            e), status_code=500, error_type="server_error")

