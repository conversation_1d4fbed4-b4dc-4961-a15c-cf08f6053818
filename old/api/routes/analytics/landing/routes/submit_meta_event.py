from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Header
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import AnalyticsEvent
import uuid
from utils.exceptions import CustomException
from ..models.request  import SubmitEventRequest
from ..models.response import SubmitEventResponse
import os
import json

router = APIRouter()
base_url = "/api/analytics/landing"

ACCESS_TOKEN_EXPIRE_MINUTES = int(os.environ.get("JWT_ACCESS_TOKEN_EXPIRE_MINUTES"))

@router.post(base_url + '/submit-meta-event', status_code=201, response_model=SubmitEventResponse)
def register(submittedEvent: SubmitEventRequest, x_auth_token: str = Header(None), db: Session = Depends(get_db)):
    try:
        event_id = str(uuid.uuid4())
        new_event = AnalyticsEvent(
            analytics_event_public_id = event_id,
            analytics_event_data = json.dumps(submittedEvent.event_data),
            analytics_event_name = submittedEvent.event_name,
            analytics_event_session_id = submittedEvent.event_data['session_id']
        )
        db.add(new_event)
        db.commit()
        return {"message": "Event submitted successfully!"}
    except Exception as e:
        raise CustomException ("Failed to submit event", str(e), status_code=500, error_type="server_error")         

    # landing page event can be just a simple visit to the landing page

    # watching video event
    
    # time spent on specific page

    # scrolling down the page

    # but also a sign up event

    # about click



