from fastapi import APIRouter
from .routes import create_analytics_session, submit_event, submit_questionnaire, submit_pre_signup

router = APIRouter()

# Include routes from all the files in this directory
router.include_router(create_analytics_session.router)
router.include_router(submit_event.router)
router.include_router(submit_questionnaire.router)
router.include_router(submit_pre_signup.router)
