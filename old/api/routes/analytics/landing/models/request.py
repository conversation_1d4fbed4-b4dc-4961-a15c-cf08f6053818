from pydantic import BaseModel
from typing import List, Optional


class SubmitEventRequest(BaseModel):
    event_name: str
    event_data: dict

class CreateSessionResponse(BaseModel):
    session_id: str
    access_token: str


class CreateSessionRequest(BaseModel):
    entry_point: str
    user_agent: str
    consent: bool

class SubmitQuestionnaireRequest(BaseModel):
    data: List[dict] | dict

class SubmitPresignupRequest(BaseModel):
    email: str
    language: str
    source: str
    user_type: str
