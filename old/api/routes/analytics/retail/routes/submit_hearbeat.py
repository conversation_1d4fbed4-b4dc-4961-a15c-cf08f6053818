

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from db.database import get_db
from utils.exceptions import CustomException
from datetime import datetime, timedelta
from db.models import UserActivity, Account, ChildAccount
from fastapi import Request
from ..models.request import SubmitHeartbeatRequest

router = APIRouter()
base_url = "/api/analytics/retail"

HEARBEAT_INTERVAL = 2  # minutes
HEARBEAT_INTERVAL_SECONDS = 30  # secobnds



@router.post(base_url + '/submit-heartbeat', status_code=200)
def record_heartbeat(request: Request, new_hearbeat: SubmitHeartbeatRequest, db: Session = Depends(get_db)):
    parent_uuid = None
    child_account_public_id = None

    try:
        parent_uuid = request.state.parent_uuid
    except:
        pass
    try: 
        child_account_public_id = request.state.decoded_child_token['child_account_public_id']
    except:
        pass
    # Determine the user type and ID based on middleware processing
    if child_account_public_id:
        account_type = 'child'
        # Adjust based on your token structure

        account_public_id = child_account_public_id
    elif parent_uuid:
        account_type = 'parent'
        # Assuming this is directly usable as the account ID
        account_public_id = parent_uuid
    else:
        raise CustomException("Not authorized!", "Token validation failed",
                              status_code=401, error_type="permission_error")

    if account_type == 'parent':
        relevant_account = db.query(Account).filter_by(
            account_cognito_id=account_public_id).first()
    elif account_type == 'child':
        relevant_account = db.query(ChildAccount).filter_by(
            child_account_public_id=account_public_id).first()

    if relevant_account:
        # Check for existing activity within the last x minutes
        time_delta = datetime.utcnow() - timedelta(seconds=HEARBEAT_INTERVAL_SECONDS)
        # time_delta = datetime.utcnow() - timedelta(minutes=HEARBEAT_INTERVAL)
        existing_activity = db.query(UserActivity).filter(
            UserActivity.timestamp >= time_delta,
            (UserActivity.account_id == relevant_account.account_id if account_type ==
             'parent' else UserActivity.child_account_id == relevant_account.child_account_id)
        ).first()

        if not existing_activity:
            # No recent activity, let's record this heartbeat
            new_activity = UserActivity(
                account_id=relevant_account.account_id if account_type == 'parent' else None,
                child_account_id=relevant_account.child_account_id if account_type == 'child' else None,
                activity_type='heartbeat',
                current_path=new_hearbeat.current_path,
                timestamp=datetime.utcnow()
            )
            db.add(new_activity)
            db.commit()
            return {"message": "Heartbeat recorded successfully"}
        else:
            # Recent activity exists, no need to record
            return {"message": "Recent activity already recorded"}

    else:
        raise CustomException("Not authorized!", "Account not found",
                              status_code=401, error_type="permission_error")


