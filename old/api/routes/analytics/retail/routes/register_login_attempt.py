

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from db.database import get_db
from utils.exceptions import CustomException
from db.models import Account,  LoginAttempt
from ..models.request import LoginAttemptRequest, UpdateLoginAttemptRequest
from ..models.response import LoginAttemptResponse
import uuid
import os 
from loguru import logger


router = APIRouter()
base_url = "/api/analytics/retail"

import boto3

# Initialize AWS SES client
ses_client = boto3.client(
    'ses',
    region_name='eu-central-1',  # replace with your AWS region
    aws_access_key_id=os.environ['AWS_ACCESS_KEY'],
    aws_secret_access_key=os.environ['AWS_SECRET_KEY']
)

relevant_email = '<EMAIL>'
if os.environ.get('ENVIRONMENT') == 'prod':
    relevant_email = '<EMAIL>'
elif os.environ.get('ENVIRONMENT') == 'test':
    relevant_email = '<EMAIL>'

def send_email(user_email, error_message):
    if not error_message:
        error_message = "No error message provided"
    try:
        # print("message", message)
        response = ses_client.send_email(
            Source=relevant_email,  # replace with your verified SES email
            Destination={'ToAddresses': ["<EMAIL>"]},
            Message={
                'Body': {
                    'Text': {
                        'Data': "Login failed for user " + user_email + " with error message: " + error_message,
                        'Charset': 'UTF-8'
                    },
                    'Html': {
                        'Data': "Login failed for user " + user_email + " with error message: " + error_message,
                        'Charset': 'UTF-8'
                    }
                },
                'Subject': {
                    'Data':  "Login issues",
                    'Charset': 'UTF-8'
                }
            }
        )
    except Exception as e:
        logger.error(f"Error sending help email: {str(e)}")
        raise CustomException("Error", str(e), status_code=400, error_type="error")
    

@router.post(base_url + '/register-login-attempt', status_code=200, response_model=LoginAttemptResponse)
def register_login_attempt(login_attempt: LoginAttemptRequest, db: Session = Depends(get_db)):

    # Check if the email exists in the database
    relevant_account = db.query(Account).filter_by(
        account_email=login_attempt.email).first()

    new_entry = None
    public_id =str(uuid.uuid4())

    if  relevant_account:
        new_entry = LoginAttempt(
            login_attempt_public_id=public_id,
            login_attempt_account_id=relevant_account.account_id,
            login_attempt_status='pending',
            login_attempt_email=login_attempt.email)
    else:
        new_entry = LoginAttempt(
            login_attempt_public_id=public_id,
            login_attempt_email=login_attempt.email,
            login_attempt_status='pending')
    db.add(new_entry)
    db.commit()

    return {"login_attempt_public_id": public_id}


@router.post(base_url + '/update-login-attempt', status_code=200)
def register_login_attempt(updated_login_attempt: UpdateLoginAttemptRequest, db: Session = Depends(get_db)):

    relevant_attempt = db.query(LoginAttempt).filter_by(
        login_attempt_public_id=updated_login_attempt.login_attempt_public_id).first()
    
    if not relevant_attempt:
        new_entry = LoginAttempt(
            login_attempt_email=updated_login_attempt.email,
            login_attempt_status=updated_login_attempt.status,
            login_attempt_identified=False)
        if updated_login_attempt.error_message:
            new_entry.login_attempt_error_message = updated_login_attempt.error_message
        db.add(new_entry)
    else:
        relevant_attempt.login_attempt_status = updated_login_attempt.status
        if updated_login_attempt.error_message:
            relevant_attempt.login_attempt_error_message = updated_login_attempt.error_message
        relevant_attempt.login_attempt_identified = True

    db.commit()
    if updated_login_attempt.status == 'failed':
        send_email(updated_login_attempt.email, updated_login_attempt.error_message)

    return {"message": 'OK'}
