from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON>pen<PERSON>, <PERSON>er, Request, HTTPException
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import TaskInvocation
from api.task_scheduling.utils.schedule_utils import is_within_time_window
import os
from datetime import datetime, timedelta
from api.task_scheduling.tasks.activity_summary.send_weekly_reports import send_weekly_reports
from loguru import logger

router = APIRouter()
base_url = "/api/task-scheduling"

# Get secret token from environment variable
SECRET_TOKEN = os.getenv('TASK_SCHEDULER_SECRET_TOKEN')
if not SECRET_TOKEN:
    raise ValueError("TASK_SCHEDULER_SECRET_TOKEN environment variable is not set")

def verify_scheduler_token(x_auth_token: str):
    """Simple token verification"""
    try:
        if x_auth_token != SECRET_TOKEN:
            raise HTTPException(
                status_code=401,
                detail="Invalid authentication token"
            )
        return True
    except Exception:
        raise HTTPException(
            status_code=401,
            detail="Invalid authentication token"
        )

async def check_recent_invocation(db: Session, task_type: str, window_minutes: int = 5) -> bool:
    """
    Check if there's been a recent successful invocation of this task type
    Returns True if there has been a recent invocation
    """
    window_start = datetime.utcnow() - timedelta(minutes=window_minutes)
    
    recent_invocation = db.query(TaskInvocation).filter(
        TaskInvocation.task_type == task_type,
        TaskInvocation.created_at >= window_start,
        TaskInvocation.task_status.in_(['success', 'in_progress'])
    ).first()
    
    return recent_invocation is not None

@router.post(base_url + '/trigger-activity-summary', status_code=200)
async def trigger_activity_summary(
    request: Request,
    db: Session = Depends(get_db),
    x_auth_token: str = Header(None), 
):
    verify_scheduler_token(x_auth_token)
    
    task_type = "weekly_activity_summary"
    
    if not is_within_time_window(weekday=6, hour=17, minute=30, window_minutes=120):
        logger.warning("Attempt to trigger activity summary outside scheduled window")
        raise HTTPException(
            status_code=400,
            detail="Task triggered outside its scheduled window"
        )
    
    # Check for recent invocations
    if await check_recent_invocation(db, task_type):
        logger.warning("Activity summary already triggered recently")
        raise HTTPException(
            status_code=400,
            detail="Task was already triggered recently"
        )
    
    # Create new task invocation record
    task_invocation = TaskInvocation(
        task_type=task_type,
        task_status="in_progress"
    )
    db.add(task_invocation)
    db.commit()
    
    try:
        logger.info("Sending weekly reports")
        await send_weekly_reports()
        
        # Update task status to success
        task_invocation.task_status = "success"
        db.commit()
        
        return {"status": "success"}
    except Exception as e:
        # Update task status to failed
        task_invocation.task_status = "failed"
        db.commit()
        
        logger.error(f"Failed to send weekly reports: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to send weekly reports"
        )
