from datetime import datetime
from pytz import timezone

def is_within_time_window(weekday: int, hour: int, minute: int, window_minutes: int = 3) -> bool:
    """
    Generic function to check if current time is within the acceptable window
    
    Args:
        weekday: Target weekday (0-6 for Monday-Sunday)
        hour: Target hour (0-23)
        minute: Target minute (0-59)
        window_minutes: Minutes before/after target time that are acceptable
    
    Returns:
        bool: True if current time is within the acceptable window
    """
    lux_tz = timezone('Europe/Luxembourg')
    current_time = datetime.now(lux_tz)
    
    # Check if it's the right weekday
    if current_time.weekday() != weekday:
        return False
    
    # Convert times to minutes since midnight for easier comparison
    current_minutes = current_time.hour * 60 + current_time.minute
    target_minutes = hour * 60 + minute
    
    # Check if current time is within window
    return abs(current_minutes - target_minutes) <= window_minutes