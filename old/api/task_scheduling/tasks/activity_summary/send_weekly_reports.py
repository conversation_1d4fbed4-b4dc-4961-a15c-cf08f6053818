from datetime import datetime, timedelta
from db.database import get_db
from db.models import ChildAccount, Account
from .child_email_content import generate_child_email_content
from .parent_email_content import generate_parent_email_content
from .compute_statistics import compute_weekly_statistics
from .send_activity_summary_email import send_email
from loguru import logger
import os

TEST_EMAIL = "<EMAIL>"

async def send_weekly_reports():
    worker_id = os.environ.get('WORKER_ID', 'unknown')
    logger.info(
        f"Starting weekly report generation at {datetime.now()} on worker {worker_id}")
    db = next(get_db())

    try:


        # # Calculate the date range for the current week
        end_date = datetime.now()
        start_date = end_date - timedelta(days=end_date.weekday())

        # Compute statistics for all students
        individual_stats, leaderboard_stats = compute_weekly_statistics(
            db, start_date, end_date)

        if not individual_stats:
            logger.info("No activity data found for this week")
            return

        # Get all relevant account IDs
        relevant_children_accounts = list(individual_stats.keys())

        # Query the relevant parent accounts using a join
        relevant_parent_accounts = (
            db.query(
                Account.account_id.label('parent_id'),
                ChildAccount.child_account_id.label('child_id'),
                Account.account_email.label('parent_email'),
                Account.account_language.label('parent_language'),
                ChildAccount.child_account_email.label('child_email'),
                ChildAccount.child_account_name.label('child_name')
            )
            .join(ChildAccount, Account.account_id == ChildAccount.child_account_parent_id)
            .filter(ChildAccount.child_account_id.in_(relevant_children_accounts))
            .all()
        )

        # Process each student's statistics and send emails
        for child_id, stats in individual_stats.items():
            try:
                # Find the parent account info for this child
                parent_info = next(
                    (entry for entry in relevant_parent_accounts if entry.child_id == child_id),
                    None
                )

                if not parent_info:
                    logger.warning(f"No parent found for child ID {child_id}")

                # Get child name and email directly from stats
                child_name = stats['name']
                child_email = stats['email']

               

                # Generate and send parent's email
                if parent_info and parent_info.parent_email:
                    parent_email_content = generate_parent_email_content(
                        child_name=child_name,
                        individual_stats=stats,
                        leaderboard_stats=leaderboard_stats,
                        language=parent_info.parent_language or 'lu'
                    )

                    await send_email(
                        # recipient=TEST_EMAIL,
                        recipient=parent_info.parent_email,
                        subject=parent_email_content["subject"],
                        html_content=parent_email_content["body"]
                    )
                    logger.info(
                        f"Sent parent report to {parent_info.parent_email}")

                # Generate and send child's email if they have an email address that is different from the parent
                parent_email = parent_info.parent_email if parent_info else None
                if child_email and (not parent_email or child_email != parent_email):
                    child_email_content = generate_child_email_content(
                        child_name=child_name,
                        individual_stats=stats,
                        leaderboard_stats=leaderboard_stats
                    )

                    await send_email(
                        # recipient=TEST_EMAIL,
                        recipient=child_email,
                        subject=child_email_content["subject"],
                        html_content=child_email_content["body"]
                    )
                    logger.info(f"Sent student report to {child_email}")
            except Exception as e:
                logger.error(
                    f"Error processing reports for child {child_id}: {str(e)}")
                continue

    except Exception as e:
        logger.error(f"Error in weekly report generation: {str(e)}")
    finally:
        db.close()
