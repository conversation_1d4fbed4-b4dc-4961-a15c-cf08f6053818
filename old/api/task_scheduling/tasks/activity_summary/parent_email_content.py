from typing import Dict, Any
from datetime import datetime

# Achievement messages for different languages
ACHIEVEMENT_MESSAGES = {
    "en": {
        "platform_achievements": {
            "title": "🏆 Platform-Wide Achievements!",
            "most_exercises": "✨ Your child completed the most exercises across all students on LuxEdu this week!",
            "top_3_exercises": "🌟 Your child ranks in the top 3 most active students across LuxEdu!",
            "top_5_exercises": "⭐ Your child ranks in the top 5 most active students across LuxEdu!",
            "highest_accuracy": "🎯 Your child achieved the highest accuracy among all students on LuxEdu this week!",
            "top_3_accuracy": "🌟 Your child ranks in the top 3 most accurate students across LuxEdu!",
            "top_5_accuracy": "⭐ Your child ranks in the top 5 most accurate students across LuxEdu!"
        },
        "chapter_achievements": {
            "title": "📚 Chapter Excellence",
            "rank_1": "🏆 First place in chapter <i>{chapter_title}</i> among students in the same grade!",
            "top_3": "🌟 Top 3 in chapter <i>{chapter_title}</i> among students in the same grade!",
            "top_5": "⭐ Top 5 in chapter <i>{chapter_title}</i> among students in the same grade!"
        },
        "lesson_achievements": {
            "title": "📖 Lesson Mastery",
            "rank_1": "🏆 First place in lesson <i>{lesson_title}</i> among students in the same grade!",
            "top_3": "🌟 Top 3 in lesson <i>{lesson_title}</i> among students in the same grade!",
            "top_5": "⭐ Top 5 in lesson <i>{lesson_title}</i> among students in the same grade!"
        }
    },
    "lu": {  # Luxembourgish
        "platform_achievements": {
            "title": "🏆 Aussergewéinlech Leeschtungen op LuxEdu!",
            "most_exercises": "✨ Déi meeschten Exercicer vun allen Schüler op LuxEdu gemaach!",
            "top_3_exercises": "🌟 Ënnert den Top 3 aktivste Schüler op LuxEdu!",
            "top_5_exercises": "⭐ Ënnert den Top 5 aktivste Schüler op LuxEdu!",
            "highest_accuracy": "🎯 Beschten % korrekt Äntwerten vun allen Schüler op LuxEdu!",
            "top_3_accuracy": "🌟 Ënnert den Top 3 Schüler mat % korrekt Äntwerten op LuxEdu!",
            "top_5_accuracy": "⭐ Ënnert den Top 5 Schüler mat % korrekt Äntwerten op LuxEdu!"
        },
        "chapter_achievements": {
            "title": "📚 Kapitel-Excellenz",
            "rank_1": "🏆 Éischte Plaz am Kapitel <i>{chapter_title}</i> ënnert den Schüler am Joergang!",
            "top_3": "🌟 Top 3 am Kapitel <i>{chapter_title}</i> ënnert den Schüler am Joergang!",
            "top_5": "⭐ Top 5 am Kapitel <i>{chapter_title}</i> ënnert den Schüler am Joergang!"
        },
        "lesson_achievements": {
            "title": "📖 Lektioun-Errungenschaften",
            "rank_1": "🏆 Éischte Plaz an der Lektioun <i>{lesson_title}</i> ënnert den Schüler am Joergang!",
            "top_3": "🌟 Top 3 an der Lektioun <i>{lesson_title}</i> ënnert den Schüler am Joergang!",
            "top_5": "⭐ Top 5 an der Lektioun <i>{lesson_title}</i> ënnert den Schüler am Joergang!"
        }
    },
    "de": {  # German
        "platform_achievements": {
            "title": "🏆 Herausragende Leistungen auf LuxEdu!",
            "most_exercises": "✨ Die meisten Übungen unter allen Schülern auf LuxEdu absolviert!",
            "top_3_exercises": "🌟 Unter den Top 3 aktivsten Schülern auf LuxEdu unter den Schülern im Jahrgang!",
            "top_5_exercises": "⭐ Unter den Top 5 aktivsten Schülern auf LuxEdu unter den Schülern im Jahrgang!",
            "highest_accuracy": "🎯 Bester %-Satz richtiger Antworten unter allen Schülern auf LuxEdu!",
            "top_3_accuracy": "🌟 Unter den Top 3 Schüler mit %-Satz richtiger Antworten auf LuxEdu unter den Schülern im Jahrgang!",
            "top_5_accuracy": "⭐ Unter den Top 5 Schüler mit %-Satz richtiger Antworten auf LuxEdu unter den Schülern im Jahrgang!"
        },
        "chapter_achievements": {
            "title": "📚 Kapitel-Exzellenz",
            "rank_1": "🏆 Erster Platz im Kapitel <i>{chapter_title}</i> unter den Schülern im Jahrgang!",
            "top_3": "🌟 Top 3 im Kapitel <i>{chapter_title}</i> unter den Schülern im Jahrgang!",
            "top_5": "⭐ Top 5 im Kapitel <i>{chapter_title}</i> unter den Schülern im Jahrgang!"
        },
        "lesson_achievements": {
            "title": "📖 Lektionen-Meisterschaft",
            "rank_1": "🏆 Erster Platz in der Lektion <i>{lesson_title}</i> unter den Schülern im Jahrgang!",
            "top_3": "🌟 Top 3 in der Lektion <i>{lesson_title}</i> unter den Schülern im Jahrgang!",
            "top_5": "⭐ Top 5 in der Lektion <i>{lesson_title}</i> unter den Schülern im Jahrgang!"
        }
    },
    "fr": {  # French
        "platform_achievements": {
            "title": "🏆 Performances Exceptionnelles",
            "most_exercises": "✨ Votre enfant a réalisé le plus d'exercices cette semaine parmi tous les élèves sur LuxEdu!",
            "top_3_exercises": "🌟 Dans le top 3 des élèves les plus actifs sur LuxEdu!",
            "top_5_exercises": "⭐ Dans le top 5 des élèves les plus actifs sur LuxEdu!",
            "highest_accuracy": "🎯 Meilleur taux de réussite cette semaine parmi tous les élèves sur LuxEdu!",
            "top_3_accuracy": "🌟 Dans le top 3 des meilleurs taux de réussite sur LuxEdu!",
            "top_5_accuracy": "⭐ Dans le top 5 des meilleurs taux de réussite sur LuxEdu!"
        },
        "chapter_achievements": {
            "title": "📚 Excellence par Chapitre",
            "rank_1": "🏆 Premier place dans le chapitre <i>{chapter_title}</i> parmi les élèves dans son groupe!",
            "top_3": "🌟 Top 3 dans le chapitre <i>{chapter_title}</i> parmi les élèves dans son groupe!",
            "top_5": "⭐ Top 5 dans le chapitre <i>{chapter_title}</i> parmi les élèves dans son groupe!"
        },
        "lesson_achievements": {
            "title": "📖 Réussite par Leçon",
            "rank_1": "🏆 Premier en <i>{lesson_title}</i> parmi les élèves dans son groupe!",
            "top_3": "🌟 Top 3 dans la leçon <i>{lesson_title}</i> parmi les élèves dans son groupe!",
            "top_5": "⭐ Top 5 dans la leçon <i>{lesson_title}</i> parmi les élèves dans son groupe!"
        }
    }
}

EMAIL_CONTENT = {
    "en": {
        "correct": "correct",
        "subject": "🎒 {child_name}'s Weekly Learning Report ⭐",
        "greeting": "Dear Parent,",
        "intro": "We're excited to share {child_name}'s learning achievements this week!",
        "weekly_summary_title": "📊 Weekly Overview",
        "exercises_completed": "Exercises",
        "correct_answers": "Correct Answers",
        "success_rate": "Success Rate",
        "progress_message": {
            "90_plus": "Your child is demonstrating exceptional understanding and consistency! 🌟",
            "80_plus": "Your child is showing strong grasp of the material! ⭐",
            "70_plus": "Your child is making steady progress! 💪",
            "other": "Your child is building foundational knowledge but may benefit from extra practice! 📚"
        },
        "learning_progress_title": "📚 Learning Progress",
        "exercises": "Completed",
        "accuracy": "Success Rate",
        "closing": "Keep encouraging {child_name}'s learning journey!",
        "signature": "Best regards,\nYour LuxEdu Team",
        "contact_info": "If you have any questions or feedback, please don't hesitate to contact <NAME_EMAIL>",
        "unsubscribe_notice": "To unsubscribe from these weekly reports, simply reply to this email."
    },
    "lu": {
        "correct": "Korrekt",
        "subject": "🎒 {child_name} Léier-Progrès ⭐",
        "greeting": "Léif Elteren,",
        "intro": "Mir freeën eis, dem {child_name} seng Efforten vun dëser Woch mat Iech ze deelen!",
        "weekly_summary_title": "📊 Wocheniwwersiicht",
        "exercises_completed": "Exercicer",
        "correct_answers": "Richteg Äntwerten",
        "success_rate": "Erfollegsquot",
        "progress_message": {
            "90_plus": "Äert Kand weist een staarkt Verständnis vun den Themen! 🌟",
            "80_plus": "Äert Kand huet d'Material gudd am Grëff! ⭐",
            "70_plus": "Äert Kand schafft un sengem Progrès! 💪",
            "other": "Äert Kand baut Grondwëssen op, mee kéint eventuell nach vun extra-Übung profitéieren! 📚"
        },
        "learning_progress_title": "📚 Léierfortschrëtt",
        "exercises": "Ofgeschloss",
        "accuracy": "Erfollegsquot",
        "closing": "Gitt dem {child_name} weider déi néideg Ënnerstëtzung op sengem Léierwee!",
        "signature": "Mat beschten Gréiss,\nÄr LuxEdu Equipe",
        "contact_info": "Wann Dir Froen oder Feedback hutt, schreift eis gä<NAME_EMAIL>",
        "unsubscribe_notice": "Wann Dir dës wöchentlech Rapporten net méi wëllt kréien, äntwert einfach op dës E-Mail."
    },
    "de": {
        "correct": "Richtig",
        "subject": "🎒 {child_name}s Lernfortschritt ⭐",
        "greeting": "Liebe Eltern,",
        "intro": "Wir freuen uns, die Lernerfolge von {child_name} diese Woche mit Ihnen zu teilen!",
        "weekly_summary_title": "📊 Wochenübersicht",
        "exercises_completed": "Übungen",
        "correct_answers": "Richtige Antworten",
        "success_rate": "Erfolgsquote",
        "progress_message": {
            "90_plus": "Ihr Kind zeigt ein starkes Verständnis der Themen! 🌟",
            "80_plus": "Ihr Kind beherrscht den Stoff gut! ⭐",
            "70_plus": "Ihr Kind macht stetige Fortschritte! 💪",
            "other": "Ihr Kind baut Grundkenntnisse auf, könnte aber von zusätzlicher Übung profitieren! 📚"
        },
        "learning_progress_title": "📚 Lernfortschritt",
        "exercises": "Abgeschlossen",
        "accuracy": "Erfolgsquote",
        "closing": "Unterstützen Sie {child_name} weiterhin auf diesem Lernweg!",
        "signature": "Mit besten Grüßen,\nIhr LuxEdu Team",
        "contact_info": "Bei Fragen oder Anregungen können Sie uns <NAME_EMAIL> kontaktieren",
        "unsubscribe_notice": "Um diese wöchentlichen Berichte abzubestellen, antworten Sie einfach auf diese E-Mail."
    },
    "fr": {
        "correct": "Correct",
        "subject": "🎒 Progrès de {child_name} ⭐",
        "greeting": "Chers parents,",
        "intro": "Voici les progrès de {child_name} cette semaine !",
        "weekly_summary_title": "📊 Résumé Hebdomadaire",
        "exercises_completed": "Exercices",
        "correct_answers": "Réponses correctes",
        "success_rate": "Taux de réussite",
        "progress_message": {
            "90_plus": "Votre enfant maîtrise parfaitement les sujets ! 🌟",
            "80_plus": "Votre enfant maîtrise bien la matière ! ⭐",
            "70_plus": "Votre enfant progresse régulièrement ! 💪",
            "other": "Votre enfant acquiert les bases, des exercices supplémentaires pourraient être bénéfiques ! 📚"
        },
        "learning_progress_title": "📚 Progression",
        "exercises": "Complétés",
        "accuracy": "Précision",
        "closing": "Continuez à encourager {child_name} dans son apprentissage !",
        "signature": "Cordialement,\nL'équipe LuxEdu",
        "contact_info": "Si vous avez des questions ou des commentaires, n'hésitez pas à nous <NAME_EMAIL>",
        "unsubscribe_notice": "Pour vous désabonner de ces rapports hebdomadaires, répondez simplement à cet e-mail."
    }
}


def get_progress_message(language: str, accuracy: float, number_exercises: int) -> str:
    """Get progress message based on accuracy level and language"""
    messages = EMAIL_CONTENT.get(language, EMAIL_CONTENT["en"])[
        "progress_message"]
    
    if number_exercises < 7:
        return messages["other"]

    if accuracy >= 90:
        return messages["90_plus"]
    elif accuracy >= 80:
        return messages["80_plus"]
    elif accuracy >= 70:
        return messages["70_plus"]
    return messages["other"]


def get_achievement_message(language: str, achievement_type: str, rank: int, title: str = None) -> str:
    """Get achievement message based on type, rank, and language"""
    messages = ACHIEVEMENT_MESSAGES.get(language, ACHIEVEMENT_MESSAGES["en"])

    if achievement_type == "exercises":
        if rank == 1:
            return messages["platform_achievements"]["most_exercises"]
        elif rank <= 3:
            return messages["platform_achievements"]["top_3_exercises"]
        elif rank <= 5:
            return messages["platform_achievements"]["top_5_exercises"]
    elif achievement_type == "accuracy":
        if rank == 1:
            return messages["platform_achievements"]["highest_accuracy"]
        elif rank <= 3:
            return messages["platform_achievements"]["top_3_accuracy"]
        elif rank <= 5:
            return messages["platform_achievements"]["top_5_accuracy"]
    elif achievement_type == "chapter":
        if rank == 1:
            return messages["chapter_achievements"]["rank_1"].format(chapter_title=title)
        elif rank <= 3:
            return messages["chapter_achievements"]["top_3"].format(chapter_title=title)
        elif rank <= 5:
            return messages["chapter_achievements"]["top_5"].format(chapter_title=title)
    elif achievement_type == "lesson":
        if rank == 1:
            return messages["lesson_achievements"]["rank_1"].format(lesson_title=title)
        elif rank <= 3:
            return messages["lesson_achievements"]["top_3"].format(lesson_title=title)
        elif rank <= 5:
            return messages["lesson_achievements"]["top_5"].format(lesson_title=title)
    return ""


def generate_parent_email_content(
    child_name: str,
    individual_stats: Dict[str, Any],
    leaderboard_stats: Dict[str, Any],
    language: str = "en"
) -> Dict[str, str]:
    """Generate parent email content following child email layout"""
    content = EMAIL_CONTENT.get(language, EMAIL_CONTENT["en"])
    messages = ACHIEVEMENT_MESSAGES.get(language, ACHIEVEMENT_MESSAGES["en"])

    # Extract statistics
    total_exercises = individual_stats["total_exercises"]
    total_correct = individual_stats["total_correct"]
    overall_accuracy = individual_stats["accuracy"]

    # Document head with styles - Fixed CSS string
    head_section = """
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body, table, td, div, p, a { 
                -webkit-text-size-adjust: 100%; 
                -ms-text-size-adjust: 100%; 
            }
            table, td { 
                mso-table-lspace: 0pt; 
                mso-table-rspace: 0pt; 
            }
            img { 
                -ms-interpolation-mode: bicubic; 
            }

            @media screen and (max-width: 600px) {
                .container { 
                    width: 100% !important; 
                    padding: 10px !important; 
                }
                .content { 
                    width: 100% !important; 
                    padding: 15px !important; 
                }
                .stats-cell { 
                    display: block !important; 
                    width: 100% !important; 
                    margin-bottom: 15px !important; 
                }
                .chapter-header { 
                    display: block !important; 
                    width: 100% !important; 
                    text-align: left !important; 
                    margin-bottom: 5px !important; 
                }
                .progress-container { 
                    margin-top: 10px !important; 
                }
                h1 { 
                    font-size: 24px !important; 
                }
                h2 { 
                    font-size: 20px !important; 
                }
                h3 { 
                    font-size: 18px !important; 
                }
            }

            @media screen and (max-width: 600px) {
                .progress-bar-cell { 
                    width: 65% !important; 
                }
                .accuracy-cell { 
                    width: 35% !important; 
                    font-size: 13px !important; 
                }
            }
        </style>
    """

    # Divider element
    divider = """
        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
            <tr>
                <td style="height: 1px; background-color: #E5E7EB;"></td>
            </tr>
        </table>
    """

    # Build achievements section - following hierarchical approach
    achievements_section = ""
    achievements_rows = []

    # Check platform achievements first (highest priority)
    has_platform_achievements = False
    for i, entry in enumerate(leaderboard_stats["most_exercises"]):
        if entry["child_id"] == individual_stats["child_id"]:
            achievements_rows.append(
                f'<tr><td style="color: #4B5563; padding: 8px 0;">{get_achievement_message(language, "exercises", i + 1)}</td></tr>'
            )
            has_platform_achievements = True

    for i, entry in enumerate(leaderboard_stats["highest_accuracy"]):
        if entry["child_id"] == individual_stats["child_id"]:
            achievements_rows.append(
                f'<tr><td style="color: #4B5563; padding: 8px 0;">{get_achievement_message(language, "accuracy", i + 1)}</td></tr>'
            )
            has_platform_achievements = True

    # If no platform achievements, check chapter achievements
    # if not has_platform_achievements:
    #     has_chapter_achievements = False
    #     for chapter_id, champions in leaderboard_stats.get("chapter_champions", {}).items():
    #         for i, entry in enumerate(champions):
    #             if entry["child_id"] == individual_stats["child_id"]:
    #                 achievements_rows.append(
    #                     f'<tr><td style="color: #4B5563; padding: 8px 0;">{get_achievement_message(language, "chapter", i + 1, entry["chapter_title"])}</td></tr>'
    #                 )
    #                 has_chapter_achievements = True
    #                 break  # Only show the first chapter achievement
    #         if has_chapter_achievements:
    #             break

    #     # If no chapter achievements, check lesson achievements
    #     if not has_chapter_achievements:
    #         for lesson_id, champions in leaderboard_stats.get("lesson_champions", {}).items():
    #             for i, entry in enumerate(champions):
    #                 if entry["child_id"] == individual_stats["child_id"]:
    #                     achievements_rows.append(
    #                         f'<tr><td style="color: #4B5563; padding: 8px 0;">{get_achievement_message(language, "lesson", i + 1, entry["lesson_title"])}</td></tr>'
    #                     )
    #                     break  # Only show the first lesson achievement
    #             if achievements_rows:  # If we found a lesson achievement, stop looking
    #                 break

    # Generate achievements section HTML if there are any achievements
    if achievements_rows:
        achievements_section = f"""
            <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin: 30px 0;">
                <tr>
                    <td style="color: #1F2937;">
                        <h2 style="margin-top: 0; font-size: 22px;">{messages['platform_achievements']['title']}</h2>
                    </td>
                </tr>
                <tr>
                    <td>
                        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
                            {''.join(achievements_rows)}
                        </table>
                    </td>
                </tr>
            </table>
            {divider}
        """

    # Generate chapter progress section
    chapter_progress = []
    for chapter_data in individual_stats["chapters"].values():
        chapter_progress.append(f"""
            <tr>
                <td style="padding-top: 20px;">
                    <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
                        <tr>
                            <td style="color: #1F2937;">
                                <h3 style="margin: 0; font-size: 18px;">{chapter_data["title"]}</h3>
                            </td>
                            <td align="right" style="color: #6B7280; font-size: 14px;">
                                {chapter_data["exercises_attempted"]} {content["exercises"]}
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="padding-top: 8px;">
                                <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
                                    <tr>
                                        <td class="progress-bar-cell" width="80%" style="background-color: #e8fcf1; height: 10px; border-radius: 5px;">
                                            <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="{chapter_data["accuracy"]}%">
                                                <tr>
                                                    <td style="background-color: #19e07e; height: 10px; border-radius: 5px;"></td>
                                                </tr>
                                            </table>
                                        </td>
                                        <td class="accuracy-cell" width="20%" align="right" style="color: #1F2937; font-weight: bold; padding-left: 10px;">
                                            {chapter_data["accuracy"]}% {content["correct"]}
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        """)

    # Combine all sections into the final email body
    email_body = f"""
    <!DOCTYPE html>
    <html>
    <head>
        {head_section}
    </head>
    <body style="margin: 0; padding: 0; background-color: #e8fcf1; font-family: Arial, sans-serif;">
        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
            <tr>
                <td align="center" class="container" style="padding: 20px;">
                    <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="600" class="content" 
                        style="background-color: #FFFFFF; border-radius: 10px; border: 1px solid #E5E7EB;">
                        <tr>
                            <td style="padding: 30px;">
                                <!-- Header -->
                                <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
                                    <tr>
                                        <td style="color: #1F2937;">
                                            <h1 style="margin-top: 0; font-size: 24px;">{content["greeting"]}</h1>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="font-size: 16px; color: #4B5563; padding-top: 10px;">
                                            {content["intro"].format(child_name=child_name)}
                                        </td>
                                    </tr>
                                </table>

                                <!-- Weekly Summary -->
                                <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin: 30px 0;">
                                    <tr>
                                        <td style="color: #1F2937;">
                                            <h2 style="margin-top: 0; font-size: 22px;">{content["weekly_summary_title"]}</h2>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin: 15px 0;">
                                                <tr>
                                                    <td class="stats-cell" width="33%" align="center" style="padding: 10px;">
                                                        <div style="font-size: 24px; font-weight: bold; color: #19e07e;">{total_exercises}</div>
                                                        <div style="color: #6B7280; font-size: 14px;">✨ {content["exercises_completed"]}</div>
                                                    </td>
                                                    <td class="stats-cell" width="33%" align="center" style="padding: 10px;">
                                                        <div style="font-size: 24px; font-weight: bold; color: #19e07e;">{total_correct}</div>
                                                        <div style="color: #6B7280; font-size: 14px;">🎯 {content["correct_answers"]}</div>
                                                    </td>
                                                    <td class="stats-cell" width="33%" align="center" style="padding: 10px;">
                                                        <div style="font-size: 24px; font-weight: bold; color: #19e07e;">{overall_accuracy}%</div>
                                                        <div style="color: #6B7280; font-size: 14px;">📈 {content["success_rate"]}</div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="color: #4B5563; text-align: center; padding-top: 15px;">
                                            {get_progress_message(language, overall_accuracy, total_exercises)}
                                        </td>
                                    </tr>
                                </table>

                                {divider}
                                {achievements_section}

                                <!-- Learning Progress -->
                                <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin: 30px 0;">
                                    <tr>
                                        <td style="color: #1F2937;">
                                            <h2 style="margin-top: 0; font-size: 22px;">{content["learning_progress_title"]}</h2>
                                        </td>
                                    </tr>
                                    {''.join(chapter_progress)}
                                </table>

                                {divider}

                                <!-- Closing Message -->
                                <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin: 30px 0;">
                                    <tr>
                                        <td style="color: #4B5563; font-size: 16px;">
                                            {content["closing"].format(child_name=child_name)}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="color: #6B7280; font-size: 14px; padding-top: 20px;">
                                            {content["contact_info"]}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="color: #6B7280; font-size: 14px; padding-top: 10px;">
                                            {content["unsubscribe_notice"]}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="color: #6B7280; font-size: 14px; padding-top: 30px; white-space: pre-line;">
                                            {content["signature"]}
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
    </html>
    """

    return {
        "subject": content["subject"].format(child_name=child_name),
        "body": email_body
    }
