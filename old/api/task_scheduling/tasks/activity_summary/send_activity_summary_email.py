
import os
import boto3
from loguru import logger

ses_client = boto3.client(
    'ses',
    region_name='eu-central-1',  # replace with your AWS region
    aws_access_key_id=os.environ['AWS_ACCESS_KEY'],
    aws_secret_access_key=os.environ['AWS_SECRET_KEY']
)

async def send_email(recipient, subject, html_content):
    try:
        sender_email = '<EMAIL>'
        if os.environ.get('ENVIRONMENT') == 'prod':
            sender_email = '<EMAIL>'
        if os.environ.get('ENVIRONMENT') == 'test':
            sender_email = '<EMAIL>'
        response = ses_client.send_email(
            Destination={'ToAddresses': [recipient]},
            Message={
                'Body': {
                    'Html': {'Charset': 'UTF-8', 'Data': html_content},
                },
                'Subject': {'Charset': 'UTF-8', 'Data': subject}
            },
            Source=sender_email
        )
        logger.info(f"Email sent to {recipient}")
        return True
    except Exception as e:
        logger.error(f"Error sending email to {recipient}: {e}")
        return False


