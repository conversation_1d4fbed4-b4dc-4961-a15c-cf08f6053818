from sqlalchemy import func, distinct, and_
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any
from db.models import ChildAccount, ChildAccountExerciseAssociation, Exercise, Lesson, Chapter
from sqlalchemy.orm import Session

# Constants for statistical thresholds
MINIMUM_EXERCISES_FOR_ACCURACY = 8  # Minimum exercises needed to be considered for accuracy rankings
MINIMUM_EXERCISES_FOR_CHAPTER = 5   # Minimum exercises needed to be considered a chapter champion
MINIMUM_EXERCISES_FOR_LESSON = 4    # Minimum exercises needed to be considered a lesson champion
DEFAULT_TOP_N = 5                   # Default number of top performers to return

def get_exercise_data(db: Session, start_date: datetime, end_date: datetime) -> List[Tuple]:
    """Fetches all relevant exercise data for the given time period."""
    # Get active accounts
    active_account_ids = (
        db.query(ChildAccountExerciseAssociation.relevant_child_account_id)
        .filter(
            ChildAccountExerciseAssociation.created_at >= start_date,
            ChildAccountExerciseAssociation.created_at <= end_date
        )
        .distinct()
        .all()
    )
    
    if not active_account_ids:
        return []
        
    active_ids = [id[0] for id in active_account_ids]
    
    # Fetch exercise data
    return (
        db.query(
            ChildAccountExerciseAssociation.relevant_exercise_id,
            ChildAccountExerciseAssociation.association_correctly_answered,
            ChildAccountExerciseAssociation.created_at,
            Exercise.exercise_id,
            Exercise.exercise_answer_type,
            Lesson.lesson_id,
            Lesson.lesson_title,
            Chapter.chapter_id,
            Chapter.chapter_title,
            ChildAccount.child_account_id.label('child_id'),
            ChildAccount.child_account_name.label('child_name'),
            ChildAccount.child_account_email.label('child_email')
        )
        .join(Exercise, 
              ChildAccountExerciseAssociation.relevant_exercise_id == Exercise.exercise_id)
        .join(Lesson, 
              Exercise.relevant_lesson_id == Lesson.lesson_id)
        .join(Chapter, 
              Lesson.relevant_chapter_id == Chapter.chapter_id)
        .join(ChildAccount,
              ChildAccountExerciseAssociation.relevant_child_account_id == ChildAccount.child_account_id)
        .filter(
            ChildAccountExerciseAssociation.relevant_child_account_id.in_(active_ids),
            ChildAccountExerciseAssociation.created_at >= start_date,
            ChildAccountExerciseAssociation.created_at <= end_date
        )
        .all()
    )

def compute_individual_statistics(exercise_data: List[Tuple]) -> Dict[int, Dict[str, Any]]:
    """Computes detailed statistics for each individual student."""
    stats_by_account = {}
    
    for (_, correctly_answered, created_at,
         exercise_id, exercise_type,
         lesson_id, lesson_title,
         chapter_id, chapter_title,
         child_id, child_name, child_email) in exercise_data:
        
        # Initialize student stats if needed
        if child_id not in stats_by_account:
            stats_by_account[child_id] = {
                "child_id": child_id,
                "name": child_name,
                "email": child_email,
                "total_exercises": 0,
                "total_correct": 0,
                "chapters": {}
            }
        
        stats = stats_by_account[child_id]
        
        # Update overall statistics
        stats["total_exercises"] += 1
        if correctly_answered:
            stats["total_correct"] += 1
            
        # Update chapter statistics
        if chapter_id not in stats["chapters"]:
            stats["chapters"][chapter_id] = {
                "title": chapter_title,
                "exercises_attempted": 0,
                "correct_answers": 0,
                "lessons": {}
            }
            
        chapter_stats = stats["chapters"][chapter_id]
        chapter_stats["exercises_attempted"] += 1
        if correctly_answered:
            chapter_stats["correct_answers"] += 1
            
        # Update lesson statistics
        if lesson_id not in chapter_stats["lessons"]:
            chapter_stats["lessons"][lesson_id] = {
                "title": lesson_title,
                "exercises_attempted": 0,
                "correct_answers": 0
            }
            
        lesson_stats = chapter_stats["lessons"][lesson_id]
        lesson_stats["exercises_attempted"] += 1
        if correctly_answered:
            lesson_stats["correct_answers"] += 1

    # Calculate accuracy percentages
    for stats in stats_by_account.values():
        # Overall accuracy
        if stats["total_exercises"] > 0:
            stats["accuracy"] = int(
                (stats["total_correct"] / stats["total_exercises"]) * 100 )
            
        # Chapter and lesson accuracy
        for chapter in stats["chapters"].values():
            if chapter["exercises_attempted"] > 0:
                chapter["accuracy"] = int(
                    (chapter["correct_answers"] / chapter["exercises_attempted"]) * 100
                )
                
            for lesson in chapter["lessons"].values():
                if lesson["exercises_attempted"] > 0:
                    lesson["accuracy"] = int(
                        (lesson["correct_answers"] / lesson["exercises_attempted"]) * 100
                    )
    
    return stats_by_account

def compute_leaderboard_statistics(stats_by_account: Dict[int, Dict[str, Any]], top_n: int = DEFAULT_TOP_N) -> Dict[str, Any]:
    """Computes leaderboard statistics based on individual statistics."""
    achievements = {
        "most_exercises": [],
        "highest_accuracy": [],
        "chapter_champions": {},
        "lesson_champions": {}
    }

    # Most exercises attempted
    sorted_by_exercises = sorted(
        stats_by_account.items(),
        key=lambda x: x[1]["total_exercises"],
        reverse=True
    )[:top_n]
    
    achievements["most_exercises"] = [
        {
            "child_id": child_id,
            "student_name": stats["name"],
            "exercises": stats["total_exercises"]
        }
        for child_id, stats in sorted_by_exercises
    ]

    # Highest accuracy (minimum exercises threshold)
    accuracy_candidates = [
        (child_id, stats) for child_id, stats in stats_by_account.items()
        if stats["total_exercises"] >= MINIMUM_EXERCISES_FOR_ACCURACY
    ]
    
    sorted_by_accuracy = sorted(
        accuracy_candidates,
        key=lambda x: x[1]["accuracy"],
        reverse=True
    )[:top_n]
    
    achievements["highest_accuracy"] = [
        {
            "child_id": child_id,
            "student_name": stats["name"],
            "accuracy": stats["accuracy"],
            "exercises": stats["total_exercises"]
        }
        for child_id, stats in sorted_by_accuracy
    ]

    # Chapter champions
    for child_id, student_stats in stats_by_account.items():
        for chapter_id, chapter in student_stats["chapters"].items():
            if chapter["exercises_attempted"] >= MINIMUM_EXERCISES_FOR_CHAPTER:  # Using constant
                if chapter_id not in achievements["chapter_champions"]:
                    achievements["chapter_champions"][chapter_id] = []
                    
                achievements["chapter_champions"][chapter_id].append({
                    "child_id": child_id,
                    "student_name": student_stats["name"],
                    "chapter_title": chapter["title"],
                    "accuracy": chapter["accuracy"],
                    "exercises": chapter["exercises_attempted"]
                })

    # Lesson champions
    for child_id, student_stats in stats_by_account.items():
        for chapter_id, chapter in student_stats["chapters"].items():
            for lesson_id, lesson in chapter["lessons"].items():
                if lesson["exercises_attempted"] >= MINIMUM_EXERCISES_FOR_LESSON:  # Using constant
                    if lesson_id not in achievements["lesson_champions"]:
                        achievements["lesson_champions"][lesson_id] = []
                        
                    achievements["lesson_champions"][lesson_id].append({
                        "child_id": child_id,
                        "student_name": student_stats["name"],
                        "lesson_title": lesson["title"],
                        "chapter_title": chapter["title"],
                        "accuracy": lesson["accuracy"],
                        "exercises": lesson["exercises_attempted"]
                    })

    # Sort and limit chapter champions
    for chapter_id in achievements["chapter_champions"]:
        achievements["chapter_champions"][chapter_id].sort(
            key=lambda x: (x["accuracy"], x["exercises"]),
            reverse=True
        )
        achievements["chapter_champions"][chapter_id] = achievements["chapter_champions"][chapter_id][:top_n]

    # Sort and limit lesson champions
    for lesson_id in achievements["lesson_champions"]:
        achievements["lesson_champions"][lesson_id].sort(
            key=lambda x: (x["accuracy"], x["exercises"]),
            reverse=True
        )
        achievements["lesson_champions"][lesson_id] = achievements["lesson_champions"][lesson_id][:top_n]

    return achievements

def compute_weekly_statistics(db: Session, start_date: datetime, end_date: datetime, top_n: int = DEFAULT_TOP_N):
    """Main function to compute both individual and leaderboard statistics."""
    try:
        # Get exercise data
        exercise_data = get_exercise_data(db, start_date, end_date)
        
        # Check if exercise_data is valid
        if not exercise_data or not isinstance(exercise_data, list):
            print("No exercise data found or invalid data format")
            return {}, {}
            
        # Compute individual statistics
        individual_stats = compute_individual_statistics(exercise_data)
        if not individual_stats:
            print("No individual statistics computed")
            return {}, {}
        
        # Compute leaderboard statistics
        leaderboard_stats = compute_leaderboard_statistics(individual_stats, top_n)
        
        return individual_stats, leaderboard_stats
        
    except Exception as e:
        print(f"Error in compute_weekly_statistics: {str(e)}")
        return {}, {}