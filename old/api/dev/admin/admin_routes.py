from botocore.exceptions import ClientError
from botocore.config import Config
import boto3
from fastapi import APIRouter, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPException
from sqlalchemy.orm import Session
from api.dev.schemas.api_response_schemas import BaseResponseSchema, CreateLessonDraftResponseSchema
from api.dev.schemas.admin_schemas import SaveLessonDraftSchema
from db.database import get_db
from db.models import  Exercise, Chapter
from loguru import logger
import inspect
import uuid
import json
from pydantic import BaseModel

router = APIRouter()
base_url = "/api/admin"


@router.get(base_url + '/get-chapter-content', status_code=200)
def get_chapter_content(chapter_public_id: str, db: Session = Depends(get_db), x_auth_token: str = Header(None)):
    try:
        relevant_chapter = db.query(Chapter).filter_by(
            chapter_public_id=chapter_public_id).first()
        if relevant_chapter:
            lessons = relevant_chapter.relevant_lessons

            return {"chapter_title": relevant_chapter.chapter_title, 
                    "lessons": [{"lesson_title": lesson.lesson_title, "lesson_public_id": lesson.lesson_public_id} for lesson in lessons if lesson.is_published == True],
                    "lesson_drafts": [{"lesson_draft_title": lesson_draft.lesson_draft_title, "lesson_draft_public_id": lesson_draft.lesson_draft_public_id} for lesson_draft in relevant_chapter.lesson_drafts if lesson_draft.is_published == False]
                    }
        else:
            raise HTTPException(
            status_code=500, detail="Something went wrong")

    except Exception as e:
        logger.exception({"api_route": base_url + '/get-chapter-content',
                          "description": f"Something went wrong"})
        raise HTTPException(
            status_code=500, detail="Something went wrong")


@router.get(base_url + '/get-content-structure', status_code=200)
def get_content_structure(db: Session = Depends(get_db), x_auth_token: str = Header(None)):
    try:
        subjects = db.query(Subject).all()

#  WE get the course structure adding the chapters into the relevant courses
        chapters = []
        output = []
        for subject in subjects:
            chapters = [{"chapter_title": chapter.chapter,
                         "chapter_public_id": chapter.chapter_public_id} for chapter in chapters]
            chapters = subject.chapters
            output.append({"subject_title": subject.subject_title,
                          "subject_public_id": subject.subject_public_id,
                           "chapters": chapters})

        return {"content_structure": output}

    except Exception as e:
        logger.exception({"api_route": base_url + '/get-content-structure',
                          "description": f"Something went wrong"})
        print(e)
        raise HTTPException(
            status_code=500, detail="Something went wrong")

class UnpublishLessonSchema(BaseModel):
    lesson_public_id: str

# @router.post(base_url + '/unpublish-lesson', status_code=200)
# def unpublish_lesson(lesson: UnpublishLessonSchema, db: Session = Depends(get_db), x_auth_token: str = Header(None)):
#     try:
#         relevant_lesson = db.query(Lesson).filter_by(
#             lesson_public_id=lesson.lesson_public_id).first()
#         relevant_lesson_draft = relevant_lesson.lesson_draft[0]

#         if relevant_lesson:
#             relevant_lesson.is_published = False
#             relevant_lesson_draft.is_published = False

#             db.commit()
#             return {"message": "Successfully unpublished lesson!"}
#         else:
#             raise HTTPException(
#             status_code=500, detail="Something went wrong")

#     except Exception as e:
#         logger.exception({"api_route": base_url + '/unpublish-lesson',
#                           "description": f"Something went wrong"})
#         print(e)
#         raise HTTPException(
#             status_code=500, detail="Something went wrong")

# class PublishLessonSchema(BaseModel):
#     lesson_draft_public_id: str
# @router.post(base_url + '/publish-lesson-draft', status_code=201)
# def publish_lesson_draft(lesson_draft:PublishLessonSchema, db: Session = Depends(get_db), x_auth_token: str = Header(None)):

#     relevant_lesson_draft = db.query(LessonDraft).filter_by(
#         lesson_draft_public_id=lesson_draft.lesson_draft_public_id).first()
#     relevant_chapter = relevant_lesson_draft.relevant_chapter
    
#     if relevant_lesson_draft.previously_published:
#         relevant_lesson = relevant_lesson_draft.lesson
#         relevant_lesson.lesson_title = relevant_lesson_draft.lesson_draft_title
#         relevant_lesson.lesson_description = relevant_lesson_draft.lesson_draft_description
#         relevant_lesson.lesson_video_id = relevant_lesson_draft.lesson_draft_video_id
#         relevant_lesson.exercises = relevant_lesson_draft.exercises
#         relevant_lesson.is_published = True
#         relevant_lesson_draft.is_published = True
#         db.commit()
#         return {'message': "Successfully published lesson draft!", "lesson_public_id": relevant_lesson.lesson_public_id}
#     else:
#         lesson_public_id = str(uuid.uuid4())
#         new_lesson = Lesson(lesson_public_id=lesson_public_id,
#                             lesson_title=relevant_lesson_draft.lesson_draft_title,
#                             lesson_description=relevant_lesson_draft.lesson_draft_description,
#                             # lesson_image_id=relevant_lesson_draft.lesson_draft_image_id,
#                             lesson_video_id=relevant_lesson_draft.lesson_draft_video_id,
#                             is_published=True
#                             )
#         db.add(new_lesson)
#         db.flush()
#         new_lesson.relevant_chapter = relevant_chapter
#         relevant_lesson_draft.lesson = new_lesson
#         # new_lesson.exercises = relevant_lesson_draft.exercises

#         relevant_lesson_draft.is_published = True
#         relevant_lesson_draft.previously_published = True
#         db.commit()

#     return {'message': "Successfully published lesson draft!", "lesson_public_id": lesson_public_id}




# class CreateLessonDraftSchema(BaseModel):
#     chapter_public_id: str

# @router.post(base_url + '/create-lesson-draft', status_code=201, )
# def create_lesson_draft(created_draft: CreateLessonDraftSchema, db: Session = Depends(get_db), x_auth_token: str = Header(None)):

#     # try:
#     # TO DO: Make sure it is my uuid
#     relevant_chapter = db.query(Chapter).filter_by(chapter_public_id=created_draft.chapter_public_id).first()

#     new_public_id = str(uuid.uuid4())
#     new_draft = LessonDraft(lesson_draft_public_id=new_public_id,
#                             lesson_draft_title="New draft",
#                             lesson_draft_description="",
#                             lesson_draft_image_id="",
#                             lesson_draft_video_id="",
#                             relevant_chapter_id=relevant_chapter.chapter_id
#                             )
#     db.add(new_draft)
#     db.commit()

#     return {'message': "Successfully created lesson draft!", "lesson_draft_public_id": new_public_id}

#     # except Exception as e:
#     #     logger.exception({"api_route": base_url + '/get-story-urls',
#     #                       "description": f"Something went wrong", "inspect": inspect.stack()[0][3]})
#     #     print(e)
#     #     db.rollback()
#     #     raise HTTPException(
#     #         status_code=500, detail="Something went wrong!")


# @router.post(base_url + '/save-lesson-draft', status_code=200, response_model=BaseResponseSchema)
# def save_lesson_draft(saved_draft: SaveLessonDraftSchema, db: Session = Depends(get_db), x_auth_token: str = Header(None)):

#     # try:
#     # TO DO: Make sure it is my uuid

#     relevant_draft = db.query(LessonDraft).filter_by(
#         lesson_draft_public_id=saved_draft.lesson_draft_public_id).first()
#     # Update Lesson Draft
#     relevant_draft.lesson_draft_title = saved_draft.lesson_draft_title
#     relevant_draft.lesson_draft_description = saved_draft.lesson_draft_description
#     relevant_draft.lesson_draft_image_id = saved_draft.lesson_draft_image_id
#     relevant_draft.lesson_draft_video_id = saved_draft.lesson_draft_video_id

#     # Update Exercises
#     exercise_ids = [exercise["exercise_public_id"]
#                     for exercise in saved_draft.lesson_draft_exercises]
#     exercises_in_db = db.query(Exercise).filter(
#         Exercise.exercise_public_id.in_(exercise_ids)).all()

#     existing_public_ids = [
#         entry.exercise_public_id for entry in exercises_in_db]
#     existing_exercises = [
#         exercise for exercise in saved_draft.lesson_draft_exercises if exercise["exercise_public_id"] in existing_public_ids]
#     new_excercises = [
#         exercise for exercise in saved_draft.lesson_draft_exercises if exercise["exercise_public_id"] not in existing_public_ids]

#     removed_exercises = [exercise for exercise in exercises_in_db if exercise.exercise_public_id not in exercise_ids]

#     # Probably not the most erfficient way to do this
#     for exercise in removed_exercises:
#         db.delete(exercise)

#     for exercise in existing_exercises:
#         relevant_exercise = [
#             entry for entry in exercises_in_db if entry.exercise_public_id == exercise["exercise_public_id"]][0]
#         relevant_exercise.exercise_prompt = exercise["prompt"]
#         relevant_exercise.exercise_solution = json.dumps(exercise["solution"])
#         relevant_exercise.exercise_solution_type = exercise["solution"]["solution_type"]
#         relevant_exercise.exercise_steps = exercise["steps"]
#         relevant_exercise.exercise_video_id = exercise["video_id"]

#     for exercise in new_excercises:
#         new_excercise = Exercise(exercise_prompt=exercise["prompt"],
#                                  exercise_solution=json.dumps(
#                                      exercise["solution"]),
#                                  exercise_solution_type=exercise["solution"]["solution_type"],
#                                  exercise_steps=exercise["steps"],
#                                  exercise_video_id=exercise["video_id"],
#                                  relevant_lesson_draft_id=relevant_draft.lesson_draft_id,
#                                  exercise_public_id=exercise["exercise_public_id"])

#         db.add(new_excercise)

#     db.commit()

#     return {'message': "Successfully saved lesson draft!"}

#     # except Exception as e:
#     #     logger.exception({"api_route": base_url + '/get-story-urls',
#     #                       "description": f"Something went wrong", "inspect": inspect.stack()[0][3]})
#     #     print(e)
#     #     db.rollback()
#     #     raise HTTPException(
#     #         status_code=500, detail="Something went wrong!")


# # @router.post(base_url + '/save-lesson-draft', status_code=200, response_model=BaseResponseSchema)
# # def save_lesson_draft(saved_draft: SaveLessonDraftSchema, db: Session = Depends(get_db), x_auth_token: str = Header(None)):


# @router.get(base_url + '/get-lesson-drafts', status_code=200)
# def get_lesson_drafts(db: Session = Depends(get_db), x_auth_token: str = Header(None)):

#     # try:
#     # TO DO: Make sure it is my uuid

#     drafts = db.query(LessonDraft).all()

#     db.commit()

#     return {'lesson_drafts': [{"lesson_draft_title": draft.lesson_draft_title, "lesson_draft_description": draft.lesson_draft_description, "lesson_draft_public_id": draft.lesson_draft_public_id} for draft in drafts]}

#     # except Exception as e:
#     #     logger.exception({"api_route": base_url + '/get-story-urls',
#     #                       "description": f"Something went wrong", "inspect": inspect.stack()[0][3]})
#     #     print(e)
#     #     db.rollback()
#     #     raise HTTPException(
#     #         status_code=500, detail="Something went wrong!")


# @router.get(base_url + '/get-lesson-draft', status_code=200)
# def get_lesson_drafts(lesson_draft_public_id: str, db: Session = Depends(get_db), x_auth_token: str = Header(None)):

#     # try:
#     # TO DO: Make sure it is my uuid

#     relevant_draft = db.query(LessonDraft).filter_by(
#         lesson_draft_public_id=lesson_draft_public_id).first()

#     if relevant_draft:
#         relevant_exercises = relevant_draft.relevant_exercises

#         # In the student app need to filter the solution out
#         exercises = [{
#             "prompt": exercise.exercise_prompt,
#             "steps": exercise.exercise_steps,
#             "solution": json.loads(exercise.exercise_solution),
#             "video_id": exercise.exercise_video_id,
#             "exercise_public_id": exercise.exercise_public_id} for exercise in relevant_exercises]

#         return {'lesson_draft': {"title": relevant_draft.lesson_draft_title, "description": relevant_draft.lesson_draft_description, "public_id": relevant_draft.lesson_draft_public_id, "image_id": relevant_draft.lesson_draft_image_id, "video_id": relevant_draft.lesson_draft_video_id, "exercises": exercises}}

#     else:
#         raise HTTPException(
#             status_code=404, detail="Lesson draft not found!")
#     # except Exception as e:
#     #     logger.exception({"api_route": base_url + '/get-story-urls',
#     #                       "description": f"Something went wrong", "inspect": inspect.stack()[0][3]})
#     #     print(e)
#     #     db.rollback()
#     #     raise HTTPException(
#     #         status_code=500, detail="Something went wrong!")


# @router.post(base_url + '/decode-token', status_code=200)
# def decode_token(token: str, db: Session = Depends(get_db), x_auth_token: str = Header(None)):
#     try:
#         decoded_token = decode_access_token(token)
#         return {"decoded_token": decoded_token}
#     except Exception as e:
#         print(e)
#         raise HTTPException(
#             status_code=500, detail="Something went wrong!")