from typing import List, Optional
from pydantic import BaseModel
import json
from fastapi import APIRouter, Depen<PERSON>, Header, HTTPException, Body, Request
from sqlalchemy.orm import Session
from sqlalchemy.sql.expression import bindparam


import random

from api.dev.schemas.api_response_schemas import GetCategoriesResponseSchema, GetAuthorProfileResponseSchema
from db.database import get_db
from db.models import Account, Chapter,  Lesson, Subscription, Year, Exercise, Student, Assignment, SchoolClass, assignment_student_connection, Task
from api.utils.auth_utils import get_uuid
from api.utils.media_utils import create_signed_video_url
from loguru import logger
import inspect
import logging
import os
import asyncio
import uuid

from api.dev.schemas.retail.account_schemas import AccountCreateSchema
from api.dev.schemas.api_response_schemas import BaseResponseSchema, GetCoursesResponseScheme, GetCourseResponseScheme, GetChapterResponseScheme, GetLessonResponseScheme, GetSignedVideoResponseScheme

from botocore.exceptions import ClientError
import boto3
from botocore.config import Config

import requests
from fastapi.responses import RedirectResponse

router = APIRouter()
base_url = "/api/student"

# class GetAssignmentsResponseSchema(BaseResponseSchema):
#     assignments: List[Assignment] = []

# route that gets all the assignments for a student


@router.get(base_url + "/assignments")
def get_assignments(student_public_id: str,  db: Session = Depends(get_db)):
    try:
        student = db.query(Student).first()
        # student = db.query(Student).filter(Student.student_public_id == student_public_id).first()
        if student is None:
            raise HTTPException(status_code=401, detail="Invalid auth token")

        relevant_assignments = student.relevant_assignments

        return {"assignments": [
            {
                "assignment_public_id": entry.assignment_public_id,
                "assignment_teacher_id": entry.assignment_teacher_id,
                "assignment_title": entry.assignment_title,
                "assignment_due_date": entry.assignment_due_date,
            } for entry in relevant_assignments if entry.assignment_assigned is True
        ]}

    except Exception as e:
        logger.error(f"Error in get_assignments: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# route that gets a specific assignment for a student


@router.get(base_url + "/get-assignment")
def get_assignment(student_public_id: str, assignment_public_id: str, db: Session = Depends(get_db)):
    # try:
        student = db.query(Student).first()
        # student = db.query(Student).filter(Student.student_public_id == student_public_id).first()
        if student is None:
            raise HTTPException(status_code=401, detail="Invalid auth token")

        relevant_assignment = db.query(Assignment).filter(
            Assignment.assignment_public_id == assignment_public_id).first()
        
        if relevant_assignment is None:
            raise HTTPException(status_code=404, detail="Assignment not found")

        if relevant_assignment.assignment_assigned is False:
            raise HTTPException(
                status_code=401, detail="Assignment not assigned to student")

        # get all the tasks for the assignment

        relevant_tasks = db.query(Task).filter(
            Task.relevant_assignment_id == relevant_assignment.assignment_id,
            Task.relevant_student_id == student.student_id
            ).all()

        # Get relevant exercise and send oout
        relevant_exercises = db.query(Exercise).filter(Exercise.exercise_id.in_(
            [task.relevant_exercise_id for task in relevant_tasks])).all()

        # then match the relevant exercises with the relevant tasks
        tasks = []
        for task in relevant_tasks:
            for exercise in relevant_exercises:
                if task.relevant_exercise_id == exercise.exercise_id:
                    tasks.append({
                        "task_public_id": task.task_public_id,
                        "exercise_public_id": exercise.exercise_public_id,
                        "exercise_prompt": exercise.exercise_prompt,
                        "exercise_steps": exercise.exercise_steps,
                        "exercise_video_id": exercise.exercise_video_id,
                        "exercise_solution": json.loads(exercise.exercise_solution),
                        "task_completed": task.task_completed,
                        "task_correct": task.task_correct,
                    })

        for index, entry in enumerate(tasks):
            tasks[index]["video_url"] = create_signed_video_url(
                entry["exercise_video_id"], 'exercise')

            entry.pop('exercise_video_id')

            if entry["exercise_solution"]["solution_type"] == "multi":
                tasks[index]["exercise_solution"]["multi"] = random.sample(
                    entry["exercise_solution"]["multi"], len(entry["exercise_solution"]["multi"]))

            if entry["exercise_solution"]["solution_type"] == "true_false":
                tasks[index]["exercise_solution"]["true_false"].pop(
                    "answer")

        return {
            "tasks":tasks
        }

    # except Exception as e:
    #     print(e)
    #     db.rollback()
    #     logger.error(f"Error in get_assignments: {e}")
    #     raise HTTPException(status_code=500, detail="Internal server error")


class SubmitTaskSchema(BaseModel):
    task_public_id: str
    student_public_id: str
    answer: str

@router.post(base_url + "/submit-task")
def submit_task(submitted_task: SubmitTaskSchema, db: Session = Depends(get_db)):
    # try:
        # The answer will either be a simple string in case of a simple answer
        # In case of a true/false answer, it will be a string
        
        # relevant_student = db.query(Student).filter_by(student_public_id=submitted_task.student_public_id).first()
        relevant_student = db.query(Student).first()
        relevant_task = db.query(Task).filter_by(
            task_public_id=submitted_task.task_public_id,
            relevant_student_id=relevant_student.student_id
            ).first()

        if  relevant_task.task_completed:
            raise HTTPException(status_code=401, detail="Task already completed")
        
        relevant_exercise = db.query(Exercise).filter_by(exercise_id=relevant_task.relevant_exercise_id).first()

        if relevant_exercise.exercise_solution_type == "multi":
            if submitted_task.answer == relevant_exercise.exercise_correct_answer_id:
                relevant_task.task_correct = True
                relevant_task.task_completed = True
                db.commit()
                return {
                    "message": "Task submitted successfully!",
                    "correctly_answered": "true",
                    "correct_answer": relevant_exercise.exercise_correct_answer_id
                }
            else:
                relevant_task.task_correct = True
                relevant_task.task_completed = True
                relevant_task.submitted_answer = submitted_task.answer
                db.commit()
                return {
                    "message": "Exercise submitted successfully!",
                    "correctly_answered": "false",
                    "correct_answer": relevant_exercise.exercise_correct_answer_id
                }
        else:
            json_solution = json.loads(relevant_exercise.exercise_solution)
            correct_answer = json_solution[relevant_exercise.exercise_solution_type]['answer']
            if submitted_task.answer == correct_answer:
                relevant_task.task_correct = True
                relevant_task.task_completed = True
                db.commit()
                return {
                    "message": "Exercise submitted successfully!",
                    "correctly_answered": "true",
                    "correct_answer": correct_answer
                }

            else:
                relevant_task.task_correct = True
                relevant_task.task_completed = True
                relevant_task.submitted_answer = submitted_task.answer
                db.commit()
            return {
                "message": "Exercise submitted successfully!",
                "correctly_answered": "false",
                "correct_answer": correct_answer
            }

    # except Exception as e:
    #     print(e)
    #     raise HTTPException(status_code=500, detail="Internal Server Error")

