# from typing import List, Optional
# from pydantic import BaseModel
# import json
# from fastapi import APIRouter, Depen<PERSON>, Header, HTTPException, Body, Request
# from sqlalchemy.orm import Session
# from sqlalchemy.sql.expression import bindparam

# from api.dev.schemas.api_response_schemas import GetCategoriesResponseSchema, GetAuthorProfileResponseSchema
# from db.database import get_db
# from db.models import Account, Course, Chapter,  Lesson, Subscription, Year, Exercise, Student, Assignment, SchoolClass, assignment_student_connection, Task, Teacher
# from api.utils.cloudflare_utils import create_signed_video_url
# from loguru import logger
# import inspect
# import logging
# import os
# import asyncio
# import uuid

# from api.dev.schemas.retail.account_schemas import AccountCreateSchema
# from api.dev.schemas.api_response_schemas import BaseResponseSchema, GetCoursesResponseScheme, GetCourseResponseScheme, GetChapterResponseScheme, GetLessonResponseScheme, GetSignedVideoResponseScheme

# from botocore.exceptions import ClientError
# import boto3
# from botocore.config import Config

# import requests
# from fastapi.responses import RedirectResponse


# router = APIRouter()
# base_url = "/api/teacher"

# DOMAIN_URL = "http://localhost:3000/subscription"


# class CreateAssignmentSchema(BaseModel):
#     assigned_exercises: List[str]
#     assigned_students: List[str]


# @router.post(base_url + '/create-assignment', status_code=201)
# def create_assignment(new_assignment: CreateAssignmentSchema, x_auth_token: str = Header(None),  db: Session = Depends(get_db)):
#     try:
#         # account_cognito_id = get_uuid(x_auth_token)
#         account_cognito_id = "yc717QTO6aZiUdLCRqegXIHoU483"

#         if account_cognito_id:
#             # teacher_account = db.query(Account).filter(Account.account_cognito_id == account_cognito_id).first()

#             teacher_account = db.query(Account).first()
#             if teacher_account:
#                 assigned_exercises_public_ids = new_assignment.assigned_exercises
#                 relevant_exercises = db.query(Exercise).filter(
#                     Exercise.exercise_public_id.in_(assigned_exercises_public_ids)).all()

#                 assigned_students_public_ids = new_assignment.assigned_students
#                 relevant_students = db.query(Student).filter(
#                     Student.student_public_id.in_(assigned_students_public_ids)).all()

#                 new_assignment = Assignment(assignment_public_id=str(
#                     uuid.uuid4()), assignment_teacher_id=teacher_account.account_id)
#                 db.add(new_assignment)
#                 db.flush()

#                 # For each of the students, create a new task for each of the exercises
#                 new_tasks = []
#                 new_tasks_public_ids = []
#                 for student in relevant_students:
#                     # For each of the exercises, create a new task
#                     # This seems very inefficient, but I can't figure out how to do it differently for now
#                     for exercise in relevant_exercises:
#                         public_id = str(uuid.uuid4())
#                         new_tasks_public_ids.append(public_id)
#                         new_tasks.append(Task(
#                             task_public_id=str(uuid.uuid4()),
#                             relevant_student=student.student_id,
#                             relevant_assignment_id=new_assignment.assignment_id,
#                             relevant_exercise_id=exercise.exercise_id,
#                             relevant_exercise_public_id=exercise.exercise_public_id,
#                             task_completed=False, task_correct=False))

#                 db.bulk_save_objects(new_tasks)

#                 new_tasks = db.query(Task).filter(
#                     Task.task_public_id.in_(new_tasks_public_ids)).all()

#                 new_assignment.relevant_students = relevant_students
#                 db.commit()

#                 return {
#                     "assignment_public_id": new_assignment.assignment_public_id,
#                     "assigned_exercises": assigned_exercises_public_ids,
#                     "assigned_students": assigned_students_public_ids
#                 }
#             else:
#                 raise HTTPException(status_code=401, detail="Unauthorized")
#         else:
#             raise HTTPException(status_code=401, detail="Unauthorized")

#     except Exception as e:
#         logger.error(e)
#         raise HTTPException(status_code=500, detail="Internal Server Error")

# # Route that gets an assignment based on the xauth token


# @router.get(base_url + '/get-assignment', status_code=200)
# def get_assignment(assignment_public_id, x_auth_token: str = Header(None),  db: Session = Depends(get_db)):
#     try:
#         # account_cognito_id = get_uuid(x_auth_token)
#         account_cognito_id = "yc717QTO6aZiUdLCRqegXIHoU483"
#         if account_cognito_id:
#             teacher_account = db.query(Account).filter(
#                 Account.account_cognito_id == account_cognito_id).first()
#             if teacher_account:
#                 relevant_assignment = db.query(Assignment).filter_by(
#                     assignment_public_id=assignment_public_id).first()
#                 relevant_tasks = db.query(Task).filter_by(
#                     relevant_assignment_id=relevant_assignment.assignment_id).all()

#                 relevant_exercises = db.query(Exercise).filter(Exercise.exercise_id.in_(
#                     [task.relevant_exercise_id for task in relevant_tasks])).all()

#                 assigned_students = relevant_assignment.relevant_students

#                 exercises = [{"exercise_public_id": exercise.exercise_public_id,
#                               "exercise_prompt": exercise.exercise_prompt} for exercise in relevant_exercises]
#                 relevant_school_class = relevant_assignment.relevant_school_class

#                 # get all the students in the classe
#                 # Iterate over students and assigned exercises and return list of exercises as well as students

#                 # for eahc of the students get their performance

#                 return {
#                     "assignment":
#                         {
#                             "assignment_public_id": relevant_assignment.assignment_public_id,
#                             "assignment_title": relevant_assignment.assignment_title,
#                             "exercises": exercises,
#                             "all_students": [{"student_public_id": student.student_public_id, "student_alias": student.student_alias} for student in relevant_school_class.relevant_students],
#                             "assigned_students": [{"student_public_id": student.student_public_id,
#                                                    "student_alias": student.student_alias} for student in assigned_students]
#                         }
#                 }

#             else:
#                 raise HTTPException(status_code=401, detail="Unauthorized")
#         else:
#             raise HTTPException(status_code=401, detail="Unauthorized")

#     except Exception as e:
#         logger.error(e)
#         raise HTTPException(status_code=500, detail="Internal Server Error")


# # get-relevant-exercises route
# # Route that gets all exercises for a given assignment's year



# # route that get sthe teacher overview
# @router.get(base_url + '/get-teacher-overview', status_code=200)
# def get_teacher_overview(x_auth_token: str = Header(None),  db: Session = Depends(get_db)):
#     try:
#         # account_cognito_id = get_uuid(x_auth_token)
#         account_cognito_id = "yc717QTO6aZiUdLCRqegXIHoU483"
#         if account_cognito_id:
#             relevant_account = db.query(Account).first()
#             # teacher_account = db.query(Account).filter(
#             #     Account.account_cognito_id == account_cognito_id).first()
#             if relevant_account:
#                 relevant_teacher = db.query(Teacher).first()

#                 relevant_school_classes = relevant_teacher.relevant_school_classes.all()

#                 relevant_assignments = db.query(Assignment).filter(
#                     Assignment.relevant_school_class_id.in_([school_class.school_class_id for school_class in relevant_school_classes])).filter_by(assignment_assigned=True).all()
                
#                 overview = []
#                 for school_class in relevant_school_classes:
#                     overview.append({"school_class_public_id": school_class.school_class_public_id,
#                                      "school_class_title": school_class.school_class_title,
#                                     #  "school_class_year": school_class.relevant_year,
#                                      "active_assignments": [{"assignment_public_id": assignment.assignment_public_id,
#                                                       "assignment_title": assignment.assignment_title,
#                                                       "assignment_due_date": assignment.assignment_due_date
#                                                       } for assignment in relevant_assignments if assignment.relevant_school_class_id == school_class.school_class_id]})

#                 return {
#                     "overview": overview
#                 }
            
#     except Exception as e:
#         logger.error(e)
#         raise HTTPException(status_code=500, detail="Internal Server Error")                                        


# @router.get(base_url + '/get-course-structure', status_code=200)
# def get_course_structure(assignment_public_id: str, x_auth_token: str = Header(None),  db: Session = Depends(get_db)):
#     try:
#         relevant_assignment = db.query(Assignment).filter_by(
#             assignment_public_id=assignment_public_id).first()
#         relevant_school_class = relevant_assignment.relevant_school_class

#         relevant_course = relevant_school_class.relevant_course
#         relevant_chapters = relevant_course.chapters

#         course_structure = [{"chapter_title": chapter.chapter_title,
#                     "chapter_public_id": chapter.chapter_public_id,
#                     "lessons": [{
#                     "lesson_title": lesson.lesson_title,
#                     "lesson_public_id": lesson.lesson_public_id,
#                     "lesson_description": lesson.lesson_description} for lesson in chapter.relevant_lessons]
#                     } for chapter in relevant_chapters]

#         return {
#             "course_structure": course_structure
#         }

#     except Exception as e:
#         logger.error(e)
#         raise HTTPException(status_code=500, detail="Internal Server Error")


# @router.get(base_url + '/get-relevant-exercises', status_code=200)
# def get_relevant_exercises(lesson_public_id: str, x_auth_token: str = Header(None),  db: Session = Depends(get_db)):
#     try:
#         # account_cognito_id = get_uuid(x_auth_token)
#         account_cognito_id = "yc717QTO6aZiUdLCRqegXIHoU483"
#         if account_cognito_id:
#             teacher_account = db.query(Account).first()
#             # teacher_account = db.query(Account).filter(
#             #     Account.account_cognito_id == account_cognito_id).first()
#             if teacher_account:
#                 relevant_lesson = db.query(Lesson).filter_by(lesson_public_id=lesson_public_id).first()
#                 relevant_exercises = relevant_lesson.relevant_exercises
                
#                 return {
#                     "relevant_exercises": [{"exercise_public_id": exercise.exercise_public_id,
#                                    "exercise_prompt": exercise.exercise_prompt} for exercise in relevant_exercises]
#                 }

#             else:
#                 raise HTTPException(status_code=401, detail="Unauthorized")
#         else:
#             raise HTTPException(status_code=401, detail="Unauthorized")

#     except Exception as e:
#         logger.error(e)
#         raise HTTPException(status_code=500, detail="Internal Server Error")


# # Route that gets all assignments for a teacher
# @router.get(base_url + '/get-assignments', status_code=200)
# def get_assignments(x_auth_token: str = Header(None),  db: Session = Depends(get_db)):
#     try:
#         # account_cognito_id = get_uuid(x_auth_token)
#         account_cognito_id = "yc717QTO6aZiUdLCRqegXIHoU483"
#         if account_cognito_id:
#             teacher_account = db.query(Account).first()
#             # teacher_account = db.query(Account).filter(Account.account_cognito_id == account_cognito_id).first()
#             if teacher_account:
#                 relevant_assignments = db.query(Assignment).filter_by(
#                     assignment_teacher_id=teacher_account.account_id).all()
#                 assignments = [{
#                     "assignment_public_id": assignment.assignment_public_id,
#                     "assignment_title": assignment.assignment_title,
#                     "assignment_assigned": assignment.assignment_assigned,
#                     "assignment_due_date": assignment.assignment_due_date,
#                     "assigned": assignment.assignment_assigned,
#                     "created_at": assignment.created_at
#                 } for assignment in relevant_assignments]
#                 return {
#                     "assignments": assignments
#                 }
#             else:
#                 raise HTTPException(status_code=401, detail="Unauthorized")
#         else:
#             raise HTTPException(status_code=401, detail="Unauthorized")

#     except Exception as e:
#         raise HTTPException(status_code=500, detail="Internal Server Error")


# # route that deletes an assignment
# @router.delete(base_url + '/delete-assignment', status_code=200)
# def delete_assignment(assignment_public_id: str, x_auth_token: str = Header(None),  db: Session = Depends(get_db)):
#     try:
#         # account_cognito_id = get_uuid(x_auth_token)
#         account_cognito_id = "yc717QTO6aZiUdLCRqegXIHoU483"
#         if account_cognito_id:
#             # teacher_account = db.query(Account).filter(Account.account_cognito_id == account_cognito_id).first()
#             teacher_account = db.query(Account).first()
#             if teacher_account:
#                 relevant_assignment = db.query(Assignment).filter_by(
#                     assignment_public_id=assignment_public_id).first()
#                 if relevant_assignment.assignment_teacher_id == teacher_account.account_id:

#                     db.query(Task).filter_by(relevant_assignment_id=relevant_assignment.assignment_id).delete(
#                         synchronize_session=False)

#                     db.delete(relevant_assignment)
#                     db.commit()
#                     return {
#                         "assignment_public_id": relevant_assignment.assignment_public_id
#                     }
#                 else:
#                     raise HTTPException(status_code=401, detail="Unauthorized")
#             else:
#                 raise HTTPException(status_code=401, detail="Unauthorized")
#         else:
#             raise HTTPException(status_code=401, detail="Unauthorized")

#     except Exception as e:
#         raise HTTPException(status_code=500, detail="Internal Server Error")

# # Route that adds exercises to an assignment based on a list of exercise public ids provided in the request body


# class UpdateAssignmentSchema(BaseModel):
#     assignment_public_id: str
#     assigned_school_class_public_id: str
#     assigned_exercises: List[str]
#     assigned_students: List[str]

# # route that updates an assignment


# @router.put(base_url + '/update-assignment', status_code=200)
# def update_assignment(updated_assignment: UpdateAssignmentSchema, x_auth_token: str = Header(None),  db: Session = Depends(get_db)):
#     try:
#         # account_cognito_id = get_uuid(x_auth_token)
#         account_cognito_id = "yc717QTO6aZiUdLCRqegXIHoU483"
#         if account_cognito_id:
#             teacher_account = db.query(Account).filter(
#                 Account.account_cognito_id == account_cognito_id).first()
#             teacher_account = db.query(Account).first()
#             if teacher_account:
#                 relevant_assignment = db.query(Assignment).filter_by(
#                     assignment_public_id=updated_assignment.assignment_public_id).first()
#                 if relevant_assignment.assignment_teacher_id == teacher_account.account_id:

#                     # Delete all assigned exercises that are assigned to this assignment and are not in the updated list
#                     current_tasks = db.query(Task).filter_by(
#                         relevant_assignment_id=relevant_assignment.assignment_id).all()
#                     actual_current_exercise_ids = [
#                         entry.relevant_exercise_id for entry in current_tasks]

#                     actual_current_exercises = db.query(Exercise).filter(
#                         Exercise.exercise_id.in_(actual_current_exercise_ids)).all()

#                     actual_current_exercises_public_ids = [
#                         entry.exercise_public_id for entry in actual_current_exercises]

#                     # This is the list of the public ids of the all exercises in the assignment
#                     assigned_exercises_public_ids = updated_assignment.assigned_exercises

#                     # Filter out the tasks that which have been removed
#                     tasks_to_delete = [
#                         entry for entry in current_tasks if entry.relevant_exercise_public_id not in assigned_exercises_public_ids]

#                     # Delete the tasks that have been removed
#                     for task in tasks_to_delete:
#                         db.delete(task)
#                     # query = db.query(Task).filter(Task.task_id.in_([entry.task_id for entry in tasks_to_delete])).all()
#                     # db.delete(query)

#                         # db.query(AssignedExercise).filter(AssignedExercise.assigned_exercise_public_id.in_(exercises_to_delete)).delete(synchronize_session=False)

#                     # Establish which exercises are new and add their public ids to a list
#                     exercises_to_add_public_ids = [
#                         entry for entry in assigned_exercises_public_ids if entry not in actual_current_exercises_public_ids]
#                     # Find the actual relevant exercises that need to be added to the new tasks
#                     added_exercises = db.query(Exercise).filter(
#                         Exercise.exercise_public_id.in_(exercises_to_add_public_ids)).all()

#                     new_tasks = []
#                     for exercise in added_exercises:
#                         new_tasks.append(Task(task_public_id=str(uuid.uuid4()), relevant_exercise_id=exercise.exercise_id,
#                                               relevant_exercise_public_id=exercise.exercise_public_id, relevant_assignment_id=relevant_assignment.assignment_id,
#                                               task_completed=False, task_correct=False))
#                     db.bulk_save_objects(new_tasks)

#                     assigned_students = db.query(Student).filter(
#                         Student.student_public_id.in_(updated_assignment.assigned_students)).all()
#                     relevant_assignment.relevant_students = assigned_students
#                     relevant_school_class = db.query(SchoolClass).filter_by(
#                         school_class_public_id=updated_assignment.assigned_school_class_public_id).first()

#                     relevant_assignment.relevant_school_class = relevant_school_class
#                     # Get all students that are assigned to this assignment
#                     db.commit()

#                     current_tasks = db.query(Task).filter_by(
#                         relevant_assignment_id=relevant_assignment.assignment_id).all()

#                     return {
#                         "assignment_public_id": relevant_assignment.assignment_public_id,
#                         "assigned_exercises": [task.relevant_exercise_public_id for task in current_tasks],
#                         "assigned_students": [student.student_public_id for student in assigned_students]
#                     }
#                 else:
#                     raise HTTPException(status_code=401, detail="Unauthorized")
#             else:
#                 raise HTTPException(status_code=401, detail="Unauthorized")
#         else:
#             raise HTTPException(status_code=401, detail="Unauthorized")

#     except Exception as e:
#         db.rollback()
#         raise HTTPException(status_code=500, detail="Internal Server Error")
