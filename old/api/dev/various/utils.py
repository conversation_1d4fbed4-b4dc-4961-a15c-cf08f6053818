AWS_ACCESS_KEY = os.environ.get("AWS_ACCESS_KEY")
AWS_SECRET_KEY = os.environ.get("AWS_SECRET_KEY")
AWS_S3_REGION = os.environ.get("AWS_S3_REGION")


def generate_presigned_url(bucket_name, object_key, expiry=3600):

    client = boto3.client("s3", region_name=AWS_S3_REGION,
                          aws_access_key_id=AWS_ACCESS_KEY,
                          aws_secret_access_key=AWS_SECRET_KEY,
                          endpoint_url=f'https://s3.{AWS_S3_REGION}.amazonaws.com',
                          config=Config(s3={'addressing_style': 'virtual'}))
    try:
        response = client.generate_presigned_url('get_object',
                                                 Params={
                                                     'Bucket': bucket_name, 'Key': object_key},
                                                 ExpiresIn=expiry)
        return response
    except ClientError as e:
        print(e)

