


@router.get(base_url + '/get-signed-video', status_code=200, response_model=GetSignedVideoResponseScheme)
def get_signed_video(video_id: str, x_auth_token: str = Header(None)):
    return {"signed_url": create_signed_video_url(video_id)}

@router.get(base_url + '/get-signed-image', status_code=200, response_model=GetSignedVideoResponseScheme)
def get_signed_video(image_id: str, x_auth_token: str = Header(None)):
    return {"signed_url": create_signed_image_url(image_id)}