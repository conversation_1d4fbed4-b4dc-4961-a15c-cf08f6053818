from typing import Optional, List, Dict
from pydantic import BaseModel

############## General ##############

class BaseResponseSchema(BaseModel):
    message: str

class GetCoursesResponseScheme(BaseModel):
    courses: List[Dict]

class GetCourseResponseScheme(BaseModel):
    course: Dict

class GetChapterResponseScheme(BaseModel):
    chapter: Dict

class GetLessonResponseScheme(BaseModel):
    lesson: Dict
    course_overview: Dict
    subscription_status: str


class GetSignedVideoResponseScheme(BaseModel):
    signed_url: str


class CreateLessonDraftResponseSchema(BaseModel):
    message: str
    lesson_draft_public_id: str


# OLD BELOW

class GetCategoriesResponseSchema(BaseModel):
    categories: List[Dict]


class GetAuthorProfileResponseSchema(BaseModel):
    author_account_handle: str
    author_public_id: str
    author_description: str
    is_following: str
    author_follower_count: str
    stories: List[Dict]

class GetFollowedAccountsResponseSchema(BaseModel):
    followed_accounts: List[Dict]
    has_more: bool

class GetFollowedContentResponseSchema(BaseModel):
    followed_content: List[Dict]
    has_more: bool

class GetFavoritedStoriesResponseSchema(BaseModel):
    favorited_stories: List[Dict]
    has_more: bool

############## Drafts ##############

class CreateDraftResponseSchema(BaseModel):
    message: str
    draft_public_id: str


class GetDraftResponseSchmema(BaseModel):
    draft_public_id: str
    draft_title: str
    draft_subtitle: Optional[str]
    draft_summary: Optional[str]
    draft_editor: Optional[str]
    draft_value: Optional[str]


class GetDraftsResponseSchmema(BaseModel):
    drafts: List[Dict]


class PublishDraftResponseSchema(BaseModel):
    message: str
    story_public_id: str
    story_url: str


############## Stories ##############

class GetStoriesResponseSchmema(BaseModel):
    stories: List[Dict]


class GetStoryByTagResponseSchmema(BaseModel):
    stories: List[Dict]


class GetStoryResponseSchmema(BaseModel):
    story: Dict

class GetStoryMetaDataResponseSchema(BaseModel):
    requestor_is_following_author: str
    requestor_has_liked: str
    requestor_has_favorited: str

class GetStoriesByCategoryResponse(BaseModel):
    recent_stories: Optional[List[Dict]]
    most_liked_stories: Optional[List[Dict]]
    most_favorited_stories: Optional[List[Dict]]
    has_more: bool

############## Authoring ##############

class GetAuthoringOverviewResponseSchema(BaseModel):
    drafts: List[Dict]
    stories: List[Dict]


############## Account ##############

class GetAccountPrivateResponseSchema(BaseModel):
    account: Dict

class GetFollowingsResponseSchmema(BaseModel):
    followings: List[Dict]

############## Subscriptions ##############


class GetSubscriptionBundleResponseSchmema(BaseModel):
    public_id: str
    name: str
    description: str
    type: str
    price: str
    included_stories: List[Dict]


class GetSubscriptionBundleByAuthorResponseSchmema(BaseModel):
    subscription_bundles: List[Dict]

############## Auth ##############



############## Next.js ##############

class GetBuildStoriesResponseSchema(BaseModel):
    story_urls: List[str]

class GetBuildAuthorsResponseSchema(BaseModel):
    account_handles: List[str]