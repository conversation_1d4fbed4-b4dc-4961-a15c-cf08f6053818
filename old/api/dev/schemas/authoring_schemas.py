from pydantic import BaseModel
from typing import List, Optional

class CreateDraftSchema(BaseModel):
    draft_title: str
    draft_subtitle: str
    draft_value: str

class PublishDraftSchema(BaseModel):
    draft_public_id: str
    story_categories: List[str]
    story_summary: str

class SaveDraftSchema(BaseModel):
    draft_public_id: str
    draft_value: str

class DeleteDraftSchema(BaseModel):
    draft_public_id: str

class UnpublishStorySchema(BaseModel):
    story_public_id: str

class SaveDraftTitleSchema(BaseModel):
    draft_public_id: str
    draft_title: str
    draft_subtitle: str

class EditStorySchema(BaseModel):
    story_public_id: str
    story_summary: str
    story_categories: List[str]