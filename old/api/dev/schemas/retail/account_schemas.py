from pydantic import BaseModel
from typing import List, Optional

class AccountCreateSchema(BaseModel):
    email: str
    # password: str
    # The public ID will alreayd be created in Cognito and known by the time the account creation takes place on the server

# class AccountUpdateSchema(BaseModel):
#     email: Optional[str]
#     account_handle: Optional[str]
#     account_description: Optional[str]

# class AccountPrivateInformationSchema(BaseModel):
#     account_public_id: str
#     email: str
#     account_handle: str
#     # subscription_type: str
