from pydantic import BaseModel
from typing import List, Optional

class CreateSubscriptionBundleSchema(BaseModel):
    name: str
    description: str
    subscription_type_public_id: str
    price: str
    included_stories: List[str]

class UpdateSubscriptionBundleSchema(CreateSubscriptionBundleSchema):
    subscription_bundle_public_id: str
    description: str
    subscription_type_public_id: str
    price: str
    included_stories: List[str]

class DeleteSubscriptionBundleSchema(BaseModel): 
    subscription_bundle_public_id: str


