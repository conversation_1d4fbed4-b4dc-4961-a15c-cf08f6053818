from typing import Optional
from sqlalchemy.orm import Session
from db.models import Account,  Trial
from datetime import datetime

def check_trial_status(relevant_account: Account,  db: Session, account_type='parent') -> Optional[dict] :
    if account_type == 'parent':
        relevant_trial = db.query(Trial).filter_by(
        trial_account_id=relevant_account.account_id).first()

    else:
        relevant_trial = db.query(Trial).filter_by(
        trial_child_account_id=relevant_account.child_account_id).first()
        
    # Initialize trial_status as None, to be defined based on trial conditions
    trial_status = None

    # Check if a relevant trial record exists
    if relevant_trial:
        # If the trial status is already marked as expired, set the trial_status variable accordingly
        if relevant_trial.trial_status == 'expired':
            trial_status = 'expired'
            trial_days_remaining = 0
        else:
            # Check if the trial's end date is past the current date, indicating expiration
            if relevant_trial.trial_end_date < datetime.utcnow():
                relevant_trial.trial_status = "expired"
                db.commit()
                trial_status = 'expired'
                trial_days_remaining = 0
            else:
                # If the trial is not expired, set it as active
                trial_status = 'active'
                trial_remaining = relevant_trial.trial_end_date - datetime.utcnow()
                trial_days_remaining = trial_remaining.days

    # If there's a trial status determined, include it in the response
    if trial_status:
        return {
            "trial_status": trial_status,
            "days_remaining": trial_days_remaining
        }
    return None
