from db.models import Child<PERSON><PERSON>, ChildEventType
import uuid
import json

from db.models import Child<PERSON>vent, ChildEventType, Course, Chapter, Lesson, Exercise, ChildAccount, ChildAccountExerciseAssociation, ChildAccountLessonAssociation
from sqlalchemy.orm import Session
from sqlalchemy import desc, func, and_, distinct, text
from datetime import datetime, timedelta


def create_event(account_type: str, relevant_account, event_type: str, event_data: dict, db):
    if account_type == 'child':
        relevant_event_type = db.query(ChildEventType).filter_by(
            child_event_type_name=event_type).first()

        registered_event = ChildEvent(
            child_event_public_id=str(uuid.uuid4()),
            child_event_type_id=relevant_event_type.child_event_type_id,
            relevant_child_account_id=relevant_account.child_account_id,
            child_event_data=json.dumps(event_data))

        return registered_event


def get_recent_statistics(db: Session, relevant_child_account: ChildAccount):
    # Calculate the date 7 days ago from the current date
    seven_days_ago = datetime.now() - timedelta(days=7)
    current_date = datetime.now()

    # Round up the current day to the next day
    rounded_current_date = current_date.replace(
        hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
    # Calculate the date 7 days ago and round down to the current day
    seven_days_ago = current_date - timedelta(days=7)
    rounded_seven_days_ago = seven_days_ago.replace(
        hour=0, minute=0, second=0, microsecond=0)

    # Query child account lesson associations within the last 7 days
    child_account_lesson_associations = db.query(ChildAccountLessonAssociation).filter_by(relevant_child_account_id=relevant_child_account.child_account_id).filter(
        ChildAccountLessonAssociation.created_at >= rounded_seven_days_ago).filter(ChildAccountLessonAssociation.created_at < rounded_current_date).order_by(desc(ChildAccountLessonAssociation.created_at)).all()

    # Calculate the count of completed lessons within the specified time frame
    completed_lessons_count = len(child_account_lesson_associations)

    # Use a window function to get the latest exercise attempts in a single query
    latest_exercise_subquery = db.query(
        ChildAccountExerciseAssociation,
        func.row_number().over(
            partition_by=ChildAccountExerciseAssociation.relevant_exercise_id,
            order_by=desc(ChildAccountExerciseAssociation.created_at)
        ).label('rn')
    ).filter(
        and_(
            ChildAccountExerciseAssociation.relevant_child_account_id == relevant_child_account.child_account_id,
            ChildAccountExerciseAssociation.created_at >= rounded_seven_days_ago,
            ChildAccountExerciseAssociation.created_at < rounded_current_date
        )
    ).subquery()

    # Get only the latest attempts
    relevant_exercise_associations = db.query(ChildAccountExerciseAssociation).from_self().filter(
        and_(
            ChildAccountExerciseAssociation.relevant_child_account_id == relevant_child_account.child_account_id,
            ChildAccountExerciseAssociation.created_at >= rounded_seven_days_ago,
            ChildAccountExerciseAssociation.created_at < rounded_current_date
        )
    ).with_entities(
        ChildAccountExerciseAssociation,
        func.row_number().over(
            partition_by=ChildAccountExerciseAssociation.relevant_exercise_id,
            order_by=desc(ChildAccountExerciseAssociation.created_at)
        ).label('rn')
    ).from_self().filter(text('rn = 1')).all()

    # Calculate the count of completed exercises
    completed_exercises_count = len(relevant_exercise_associations)

    # Calculate the share of correct exercises
    if completed_exercises_count > 0:
        correct_exercises_share = len(
            [entry[0] for entry in relevant_exercise_associations if entry[0].association_correctly_answered == True]) / completed_exercises_count
    else:
        correct_exercises_share = 0

    return completed_lessons_count, completed_exercises_count, correct_exercises_share


def get_chapter_statistics(db: Session, relevant_child_account: ChildAccount, relevant_course: Course):
    # Get chapters for the course
    relevant_chapters = db.query(Chapter).filter(
        Chapter.relevant_course_id == relevant_course.course_id
    ).order_by(Chapter.chapter_ordering).all()

    # Add a subquery to get latest exercise attempts
    latest_exercise_attempts = db.query(
        ChildAccountExerciseAssociation.relevant_exercise_id,
        func.max(ChildAccountExerciseAssociation.created_at).label('max_created_at')
    ).filter(
        ChildAccountExerciseAssociation.relevant_child_account_id == relevant_child_account.child_account_id
    ).group_by(ChildAccountExerciseAssociation.relevant_exercise_id).subquery()

    # Modify the main query to use this subquery
    stats_query = db.query(
        Chapter,
        Lesson,
        Exercise,
        ChildAccountLessonAssociation,
        ChildAccountExerciseAssociation
    ).outerjoin(
        Lesson, Lesson.relevant_chapter_id == Chapter.chapter_id
    ).outerjoin(
        Exercise, Exercise.relevant_lesson_id == Lesson.lesson_id
    ).outerjoin(
        ChildAccountLessonAssociation,
        and_(
            ChildAccountLessonAssociation.relevant_lesson_id == Lesson.lesson_id,
            ChildAccountLessonAssociation.relevant_child_account_id == relevant_child_account.child_account_id
        )
    ).outerjoin(
        latest_exercise_attempts, latest_exercise_attempts.c.relevant_exercise_id == Exercise.exercise_id
    ).outerjoin(
        ChildAccountExerciseAssociation,
        and_(
            ChildAccountExerciseAssociation.relevant_exercise_id == Exercise.exercise_id,
            ChildAccountExerciseAssociation.relevant_child_account_id == relevant_child_account.child_account_id,
            ChildAccountExerciseAssociation.created_at == latest_exercise_attempts.c.max_created_at
        )
    ).filter(
        Chapter.relevant_course_id == relevant_course.course_id
    ).order_by(
        Chapter.chapter_ordering,
        Lesson.lesson_ordering,
        ChildAccountExerciseAssociation.created_at.desc()
    ).all()

    # Process results and build statistics
    stats_by_chapter = []
    
    for chapter in relevant_chapters:
        chapter_data = {
            "chapter_public_id": chapter.chapter_public_id,
            "chapter_title": chapter.chapter_title,
            "chapter_icon_id": chapter.chapter_icon_id,
            "chapter_total_lesson_count": 0,
            "chapter_completed_lessons_count": 0,
            "chapter_completed_lesson_share": 0,
            "chapter_total_exercise_count": 0,
            "chapter_completed_exercises_count": 0,
            "chapter_completed_exercises_share": 0,
            "chapter_correct_exercises_count": 0,
            "chapter_correct_exercises_share": 0,
            "by_lesson": []
        }

        # Filter data for this chapter
        chapter_entries = [entry for entry in stats_query if entry[0].chapter_id == chapter.chapter_id]
        
        # Process lessons
        lessons_dict = {}
        for _, lesson, exercise, lesson_assoc, exercise_assoc in chapter_entries:
            if lesson and lesson.lesson_id not in lessons_dict:
                lessons_dict[lesson.lesson_id] = {
                    "lesson_public_id": lesson.lesson_public_id,
                    "lesson_title": lesson.lesson_title,
                    "lesson_total_exercise_count": 0,
                    "completed_exercises_count": 0,
                    "completed_exercises_share": 0,
                    "lesson_correct_exercises_count": 0,
                    "lesson_correct_exercises_share": 0
                }
                chapter_data["chapter_total_lesson_count"] += 1
                if lesson_assoc:
                    chapter_data["chapter_completed_lessons_count"] += 1

            if exercise:
                lessons_dict[lesson.lesson_id]["lesson_total_exercise_count"] += 1
                chapter_data["chapter_total_exercise_count"] += 1
                
                if exercise_assoc:
                    lessons_dict[lesson.lesson_id]["completed_exercises_count"] += 1
                    chapter_data["chapter_completed_exercises_count"] += 1
                    if exercise_assoc.association_correctly_answered:
                        lessons_dict[lesson.lesson_id]["lesson_correct_exercises_count"] += 1
                        chapter_data["chapter_correct_exercises_count"] += 1

        # Calculate shares and add lessons to chapter data
        for lesson_data in lessons_dict.values():
            if lesson_data["lesson_total_exercise_count"] > 0:
                lesson_data["completed_exercises_share"] = round(
                    lesson_data["completed_exercises_count"] / lesson_data["lesson_total_exercise_count"], 2)
            if lesson_data["completed_exercises_count"] > 0:
                lesson_data["lesson_correct_exercises_share"] = round(
                    lesson_data["lesson_correct_exercises_count"] / lesson_data["completed_exercises_count"], 2)
            chapter_data["by_lesson"].append(lesson_data)

        # Calculate chapter shares
        if chapter_data["chapter_total_lesson_count"] > 0:
            chapter_data["chapter_completed_lesson_share"] = round(
                chapter_data["chapter_completed_lessons_count"] / chapter_data["chapter_total_lesson_count"], 2)
        if chapter_data["chapter_total_exercise_count"] > 0:
            chapter_data["chapter_completed_exercises_share"] = round(
                chapter_data["chapter_completed_exercises_count"] / chapter_data["chapter_total_exercise_count"], 2)
        if chapter_data["chapter_completed_exercises_count"] > 0:
            chapter_data["chapter_correct_exercises_share"] = round(
                chapter_data["chapter_correct_exercises_count"] / chapter_data["chapter_completed_exercises_count"], 2)

        stats_by_chapter.append(chapter_data)

    return stats_by_chapter
