from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
import os
from dotenv import load_dotenv
from sqlalchemy.pool import NullPool

load_dotenv()

POSTGRES_DATABASE_URL = os.environ.get('DATABASE_URL')

ENVIRONMENT = os.environ.get('ENVIRONMENT')
if ENVIRONMENT == 'dev':
    engine = create_engine(POSTGRES_DATABASE_URL, pool_pre_ping=True)
else:
    engine = create_engine(POSTGRES_DATABASE_URL, poolclass=NullPool, pool_pre_ping=True)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

SqlAlchemyBase = declarative_base()

# def get_db():
#     db = SessionLocal()
#     try:
#       yield db
#     finally:
#       db.close()

def get_db():
    with SessionLocal() as db: 
        try:
            yield db
        except Exception as err:
            # log your error
            # print(err) # for dev/testing
            db.rollback()
        finally:
            db.close()