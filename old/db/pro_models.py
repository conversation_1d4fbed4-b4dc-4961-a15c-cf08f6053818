



# Teacher stuff
# assignment_assigned_exercise_connection = Table('assignment_assigned_exercise_connection', SqlAlchemyBase.metadata,
#                                                 Column('assigned_exercise_id', <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>(
#                                                     'assigned_exercise.assigned_exercise_id')),
#                                                 Column(
#                                                     'assignment_id', <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>('assignment.assignment_id'))
#                                                 )


# assignment_student_connection = Table('assignment_student_connection', SqlAlchemyBase.metadata,
#                                       Column('assignment_id', <PERSON><PERSON>ger, <PERSON><PERSON><PERSON>(
#                                           'assignment.assignment_id')),
#                                       Column(
#                                           'student_id', Inte<PERSON>, <PERSON><PERSON><PERSON>('student.student_id'))
#                                       )

# student_class_connection = Table('student_class_connection', SqlAlchemyBase.metadata,
#                                  Column('student_id', Inte<PERSON>, <PERSON><PERSON>ey(
#                                      'student.student_id')),
#                                  Column(
#                                      'school_class_id', Integer, <PERSON><PERSON><PERSON>('school_class.school_class_id'))
#                                  )
# teacher_class_connection = Table('teacher_class_connection', SqlAlchemyBase.metadata,
#                                  Column('teacher_id', Integer,
#                                         <PERSON><PERSON><PERSON>('teacher.teacher_id')),
#                                  Column('school_class_id', <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>(
#                                         'school_class.school_class_id'))
#                                  )


# class Teacher(SqlAlchemyBase):
#     __tablename__ = 'teacher'
#     teacher_id = Column(Integer, primary_key=True)
#     teacher_public_id = Column(Text, nullable=False, unique=True)
#     teacher_email = Column(Text)
#     teacher_firebase_id = Column(Text)

#     relevant_school_classes = relationship('SchoolClass', secondary=teacher_class_connection,
#                                            back_populates='relevant_teachers',    lazy='dynamic')

#     created_at = Column(DateTime, index=True, default=datetime.utcnow)


# class Student(SqlAlchemyBase):
#     __tablename__ = 'student'
#     student_id = Column(Integer, primary_key=True)
#     student_public_id = Column(Text, nullable=False, unique=True)
#     student_alias = Column(Text, unique=True)
#     student_year = Column(Integer, ForeignKey('year.year_id'))

#     relevant_assignments = relationship(
#         'Assignment', secondary=assignment_student_connection,
#         back_populates='relevant_students',
#         lazy='dynamic'
#     )

#     relevant_school_classes = relationship('SchoolClass', secondary=student_class_connection,
#                                            back_populates='relevant_students',    lazy='dynamic')

#     relevant_year = relationship("Year", back_populates="relevant_students")

#     created_at = Column(DateTime, index=True, default=datetime.utcnow)


# # Need a connection between tasks and students, ie each task is directly linked to one student


# # An assessment is made up of tasks, which each link to an exercise
# # This is a connection class between the assessment and the exercises
# class Task(SqlAlchemyBase):
#     __tablename__ = 'task'

#     task_id = Column(Integer, primary_key=True)
#     task_public_id = Column(Text, nullable=False, unique=True)

#     relevant_assignment_id = Column(
#         Integer, ForeignKey('assignment.assignment_id'))

#     relevant_exercise_id = Column(Integer, ForeignKey('exercise.exercise_id'))
#     relevant_exercise_public_id = Column(
#         Text, ForeignKey('exercise.exercise_public_id'))
#     relevant_student_id = Column(Integer, ForeignKey('student.student_id'))

#     task_completed = Column(Boolean, default=False)
#     task_correct = Column(Boolean, default=False)
#     submitted_answer = Column(Text)

#     created_at = Column(DateTime, index=True, default=datetime.utcnow)


# class Assignment(SqlAlchemyBase):
#     __tablename__ = 'assignment'
#     assignment_id = Column(Integer, primary_key=True)
#     assignment_public_id = Column(Text, nullable=False, unique=True)
#     assignment_teacher_id = Column(Integer, ForeignKey('teacher.teacher_id'))
#     assignment_title = Column(Text)
#     assignment_assigned = Column(Boolean, default=False)
#     assignment_due_date = Column(DateTime, index=True)

#     created_at = Column(DateTime, index=True, default=datetime.utcnow)

#     relevant_school_class_id = Column(
#         Integer, ForeignKey('school_class.school_class_id'))

#     relevant_school_class = relationship(
#         "SchoolClass", back_populates="relevant_assignments")

#     relevant_students = relationship(
#         'Student', secondary=assignment_student_connection,
#         back_populates='relevant_assignments',
#         lazy='dynamic'
#     )


# # Class here works more as a class for a single subject rather than one class as such
# class SchoolClass(SqlAlchemyBase):
#     __tablename__ = 'school_class'
#     school_class_id = Column(Integer, primary_key=True)
#     school_class_public_id = Column(Text, nullable=False, unique=True)
#     school_class_title = Column(Text)

#     school_class_year_id = Column(Integer, ForeignKey('year.year_id'))
#     school_class_course_id = Column(Integer, ForeignKey('course.course_id'))

#     relevant_year = relationship(
#         "Year", back_populates="relevant_school_classes")
#     relevant_course = relationship(
#         "Course", back_populates="relevant_school_classes")

#     relevant_teachers = relationship('Teacher', secondary=teacher_class_connection,
#                                      back_populates='relevant_school_classes', lazy='dynamic')

#     relevant_students = relationship('Student', secondary=student_class_connection,
#                                      back_populates='relevant_school_classes',    lazy='dynamic')

#     relevant_assignments = relationship(
#         "Assignment", back_populates="relevant_school_class")

#     created_at = Column(DateTime, index=True, default=datetime.utcnow)



