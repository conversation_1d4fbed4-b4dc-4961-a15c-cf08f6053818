from sqlalchemy import Inte<PERSON>, Text, Float, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON><PERSON>, Index
from sqlalchemy.sql.sqltypes import Boolean, DateTime
from db.database import SqlAlchemyBase
from sqlalchemy.orm import relationship
from datetime import datetime, timedelta
from sqlalchemy.dialects.postgresql import JSONB
import uuid
import random
import string

########## Connection tables ##########

# Good explanation of why we need to set the ondelete='CASCADE' option
#  for the foreign keys and then the passive_deletes=True option for the relationships here https://stackoverflow.com/questions/5033547/sqlalchemy-cascade-delete

########## Classes ##########


class Year(SqlAlchemyBase):
    __tablename__ = 'year'
    year_id = Column(Integer, primary_key=True)
    year_name = Column(Text, nullable=False)
    year_public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)

    is_classique = Column(Boolean, default=False)
    is_general = Column(Boolean, default=False)

    relevant_courses = relationship("Course", back_populates="relevant_year")



    # relevant_school_classes = relationship(
    #     "SchoolClass", back_populates="relevant_year")

    relevant_child_accounts = relationship(
        "ChildAccount", back_populates="relevant_year")
    # relevant_students = relationship("Student", back_populates="relevant_year")
    relevant_subscriptions = relationship(
        "Subscription", back_populates="relevant_year")


class Course(SqlAlchemyBase):
    __tablename__ = 'course'
    course_id = Column(Integer, primary_key=True)
    course_public_id = Column(Text, nullable=False,
                              unique=True, default=lambda: str(uuid.uuid4()))
    course_title = Column(Text, nullable=False)
    course_description = Column(Text)
    relevant_year_id = Column(Integer, ForeignKey(
        'year.year_id', ondelete='CASCADE'))
    created_at = Column(DateTime, index=True, default=datetime.utcnow)
    is_published = Column(Boolean, default=False)

    # relevant_school_classes = relationship(
    #     'SchoolClass', back_populates='relevant_course')
    relevant_year = relationship(
        "Year", back_populates="relevant_courses", passive_deletes=True)
    chapters = relationship("Chapter", back_populates="relevant_course")

    __table_args__ = (
        Index('idx_course_year', relevant_year_id),
        Index('idx_course_year_published', relevant_year_id, is_published),
        Index('idx_course_published', is_published),
    )


class Chapter(SqlAlchemyBase):
    __tablename__ = 'chapter'
    chapter_id = Column(Integer, primary_key=True)
    chapter_public_id = Column(
        Text, nullable=False, unique=True, default=lambda: str(uuid.uuid4()))
    chapter_title = Column(Text, nullable=False)
    chapter_description = Column(Text)
    relevant_course_id = Column(Integer, ForeignKey(
        'course.course_id', ondelete='CASCADE'))
    created_at = Column(DateTime, index=True, default=datetime.utcnow)
    is_published = Column(Boolean, default=False)
    is_free = Column(Boolean, default=False)

    # Makes manual sorting easier....
    chapter_ordering = Column(Integer)
    chapter_icon_id = Column(Text)
    # lesson_drafts = relationship(
    #     "LessonDraft", back_populates="relevant_chapter")
    relevant_course = relationship(
        "Course", back_populates="chapters", passive_deletes=True)
    relevant_lessons = relationship(
        "Lesson", back_populates="relevant_chapter")

    __table_args__ = (
        Index('idx_chapter_course', relevant_course_id),
        Index('idx_chapter_course_published', relevant_course_id, is_published),
        Index('idx_chapter_course_ordering', relevant_course_id, chapter_ordering),
        Index('idx_chapter_published_free', is_published, is_free),
    )


class Lesson(SqlAlchemyBase):
    __tablename__ = 'lesson'
    lesson_id = Column(Integer, primary_key=True)
    lesson_public_id = Column(Text, nullable=False,
                              unique=True, default=lambda: str(uuid.uuid4()))
    lesson_title = Column(Text, nullable=False)
    lesson_description = Column(Text)
    lesson_image_id = Column(Text)
    lesson_video_id = Column(Text)
    lesson_url = Column(Text)
    # Makes manual sorting easier....
    lesson_ordering = Column(Integer)

    is_published = Column(Boolean, default=False)
    is_free = Column(Boolean, default=False)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)

    relevant_chapter_id = Column(Integer, ForeignKey(
        'chapter.chapter_id', ondelete='CASCADE'))

    # lesson_draft = relationship("LessonDraft", back_populates="lesson")
    relevant_chapter = relationship(
        "Chapter", back_populates="relevant_lessons", passive_deletes=True)

    relevant_exercises = relationship(
        "Exercise", back_populates="relevant_lesson")

    relevant_lesson_associations = relationship(
        "ChildAccountLessonAssociation", back_populates="relevant_lesson")

    __table_args__ = (
        Index('idx_lesson_chapter', relevant_chapter_id),
        Index('idx_lesson_chapter_published', relevant_chapter_id, is_published),
        Index('idx_lesson_chapter_ordering', relevant_chapter_id, lesson_ordering),
        Index('idx_lesson_published_free', is_published, is_free),
    )



class Exercise(SqlAlchemyBase):
    __tablename__ = 'exercise'
    exercise_id = Column(Integer, primary_key=True)
    exercise_public_id = Column(
        Text, nullable=False, unique=True, default=lambda: str(uuid.uuid4()))
    exercise_answer_type = Column(Text, nullable=False)
    # The answer types are stored as strings and can be one of the following:
    # input, true_false, mc_simple, mc_multiple

    exercise_prompt_text = Column(Text)
    exercise_info_text = Column(Text)

    exercise_prompt_image_public_id = Column(
        Text, nullable=True)
    exercise_prompt_video_public_id = Column(Text)
    # We store the public id of the media assets
    # Once they are actually requested we need to fetch the corresponding Cloudfare ids from the media table and generate signed urls that we can then return in the response

    exercise_answer_options = Column(JSONB)

    # This is a list (that we need to parse with json.loads)
    # Entrues are of the form {'option_public_id': 'some_public_id', 'option_text': 'some_text', 'option_image_public_id': 'some_public_id', 'option_video_public_id': 'some_public_id'}

    exercise_correct_answer = Column(JSONB)
    # This wil be a list in the case of mc_simple and mc_multi (with a single option_public_id for mc_simple and multiple option_public_ids for mc_multi)

    exercise_solution_video_public_id = Column(Text)

    exercise_difficulty = Column(Text)

    created_at = Column(DateTime, index=True, default=datetime.utcnow)

    relevant_lesson_id = Column(Integer, ForeignKey(
        'lesson.lesson_id', ondelete='CASCADE'))
    # relevant_lesson_draft_id = Column(
    #     Integer, ForeignKey('lesson_draft.lesson_draft_id'))

    # relevant_lesson_draft = relationship(
    #     "LessonDraft", back_populates="relevant_exercises")

    relevant_lesson = relationship(
        "Lesson", back_populates="relevant_exercises", passive_deletes=True)

    relevant_exercise_associations = relationship(
        "ChildAccountExerciseAssociation", back_populates="relevant_exercise", primaryjoin="Exercise.exercise_id==ChildAccountExerciseAssociation.relevant_exercise_id")
    # We need to specify the primary join because we use two foreign keys to the same table (exercise_id and exercise_public_id)

    __table_args__ = (
        Index('idx_exercise_lesson_id', relevant_lesson_id),
        Index('idx_exercise_difficulty', exercise_difficulty),
        Index('idx_exercise_created', created_at),
        Index('idx_exercise_lesson_difficulty', relevant_lesson_id, exercise_difficulty),
    )


# Login events
# Logout events
# # Opened lesson
# Started video
# Completed video
# # Viewed lesson
# # Started exercises
# # Completed exercise

# Child events for active interactions
# Child activty for passive interactions


class ChildActivityType(SqlAlchemyBase):
    __tablename__ = 'child_activity_type'
    child_activity_type_id = Column(Integer, primary_key=True)
    child_activity_type_name = Column(Text)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)

    relevant_child_activities = relationship(
        "ChildActivity", back_populates="relevant_child_activity_type")

class ChildActivity(SqlAlchemyBase):
    __tablename__ = 'child_activity'
    child_activity_id = Column(Integer, primary_key=True)
    child_activity_public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)

    child_activity_data = Column(JSONB)
    child_activity_date = Column(Text)
    child_activity_hour = Column(Text)
    child_activity_block = Column(Text)
    child_activity_name = Column(Text)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)




    child_activity_type_id = Column(Integer, ForeignKey(
        'child_activity_type.child_activity_type_id', ondelete='CASCADE'))

    relevant_child_account_id = Column(Integer, ForeignKey(
        'child_account.child_account_id', ondelete='CASCADE'))
    relevant_child_activity_type = relationship(
        "ChildActivityType", back_populates="relevant_child_activities", passive_deletes=True)
    
    __table_args__ = (
        Index('idx_child_activity_account_date', relevant_child_account_id, created_at),
        Index('idx_child_activity_account_type', relevant_child_account_id, child_activity_type_id),
        Index('idx_child_activity_date_hour', child_activity_date, child_activity_hour),
        Index('idx_child_activity_type', child_activity_type_id),
    )



class ChildEventType(SqlAlchemyBase):
    __tablename__ = 'child_event_type'
    child_event_type_id = Column(Integer, primary_key=True)
    child_event_type_name = Column(Text)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)

    relevant_child_events = relationship(
        "ChildEvent", back_populates="relevant_event_type")


class ChildEvent(SqlAlchemyBase):
    __tablename__ = 'child_event'
    child_event_id = Column(Integer, primary_key=True)
    child_event_public_id = Column(
        Text, nullable=False, unique=True, default=lambda: str(uuid.uuid4()))
    child_event_data = Column(JSONB)
    child_event_reference_id = Column(Text)

    child_event_type_id = Column(Integer, ForeignKey(
        'child_event_type.child_event_type_id', ondelete='CASCADE'))
    relevant_child_account_id = Column(Integer, ForeignKey(
        'child_account.child_account_id', ondelete='CASCADE'))

    relevant_event_type = relationship(
        "ChildEventType", back_populates="relevant_child_events", passive_deletes=True)
    relevant_child_account = relationship(
        "ChildAccount", back_populates="relevant_child_events", passive_deletes=True)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)


class ChildAccountLessonAssociation(SqlAlchemyBase):
    __tablename__ = 'child_account_lesson_association'
    association_id = Column(Integer, primary_key=True)
    association_public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)

    lesson_public_id = Column(Text, unique=True, nullable=False)
    completed_video = Column(Boolean, default=False)

    relevant_child_account_id = Column(
        Integer, ForeignKey('child_account.child_account_id', ondelete='CASCADE'))

    relevant_lesson_id = Column(Integer, ForeignKey('lesson.lesson_id'))

    relevant_child_account = relationship(
        "ChildAccount", back_populates="relevant_lesson_associations", passive_deletes=True)

    relevant_lesson = relationship(
        "Lesson", back_populates="relevant_lesson_associations")

    created_at = Column(DateTime, index=True, default=datetime.utcnow)

# Need to track which exercises have been completed by which students


class ChildAccountExerciseAssociation(SqlAlchemyBase):
    __tablename__ = 'child_account_exercise_association'
    association_id = Column(Integer, primary_key=True)
    association_public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)

    # # We need to include the public id as well as the private id because when we check if an exercise has been previously answered,
    # # the post request from the frontend will have the public_id for the exercise, if we then go and search for the association we would have to do an extra query looking up the private id
    # relevant_exercise_public_id = Column(Text,  ForeignKey('exercise.exercise_public_id'))
    association_given_answer = Column(Text)
    association_correctly_answered = Column(Boolean)

    relevant_child_account_id = Column(
        Integer, ForeignKey('child_account.child_account_id', ondelete='CASCADE'))

    relevant_exercise_id = Column(Integer, ForeignKey('exercise.exercise_id'))
    relevant_lesson_id = Column(Integer, ForeignKey('lesson.lesson_id'))
    relevant_chapter_id = Column(Integer, ForeignKey('chapter.chapter_id'))

    relevant_child_account = relationship(
        "ChildAccount", back_populates="relevant_exercise_associations", passive_deletes=True)

    # We need to specify the primary join because we use two foreign keys to the same table (exercise_id and exercise_public_id)
    relevant_exercise = relationship(
        "Exercise", back_populates="relevant_exercise_associations",  primaryjoin="Exercise.exercise_id==ChildAccountExerciseAssociation.relevant_exercise_id")

    created_at = Column(DateTime, index=True, default=datetime.utcnow)

    __table_args__ = (
        Index('idx_association_account_exercise', relevant_child_account_id, relevant_exercise_id),
        Index('idx_association_account_lesson', relevant_child_account_id, relevant_lesson_id),
        Index('idx_association_account_date', relevant_child_account_id, created_at),
        Index('idx_association_account_correct', relevant_child_account_id, association_correctly_answered),
        Index('idx_exercise_account_created', 
              relevant_child_account_id, created_at),
        Index('idx_exercise_account_exercise_created',
              relevant_child_account_id, relevant_exercise_id, created_at),
    )

class AccountType(SqlAlchemyBase):
    __tablename__ = 'account_type'
    account_type_id = Column(Integer, primary_key=True)
    account_type_name = Column(Text)
    account_type_description = Column(Text)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)

    relevant_accounts = relationship(
        "Account", back_populates="relevant_account_type")


class Account(SqlAlchemyBase):
    __tablename__ = 'account'
    account_id = Column(Integer, primary_key=True)
    account_email = Column(Text, unique=True, nullable=False)
    account_cognito_id = Column(Text, unique=True, nullable=False)
    account_public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)
    account_stripe_id = Column(Text, unique=True, nullable=False)
    account_referral_code = Column(Text)
    # This is the language that the user has selected
    account_language = Column(Text, default="en")
    pin_hash = Column(Text)
    
    show_intro = Column(Boolean, default=True)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)
    is_auth_migrated = Column(Boolean, default=False)
    is_verified = Column(Boolean, default=False)
    account_verification_code = Column(Text)

    account_type_id = Column(Integer, ForeignKey(
        'account_type.account_type_id', ondelete='CASCADE'))

    relevant_subscription_associations = relationship(
        "AccountSubscriptionAssociation", back_populates="relevant_account", passive_deletes=True)

    relevant_account_type = relationship(
        "AccountType", back_populates="relevant_accounts")

    # relevant_consent_associations = relationship("UserConsentAssociation", back_populates="relevant_account")

    children_accounts = relationship(
        "ChildAccount", back_populates="parent_account")
  # One account can have multiple subscriptions

# email: formData.email,
#                     language,
#                     system_options: formData.systemOptions,
#                     year_options: formData.yearOptions,
#                     marketing_consent: formData.marketingConsent,
#                     user_agent: navigator.userAgent,
#                     marketing_consent_text: languageData.marketingConsentText,
#                     privacy_policy_version: PRIVACY_POLICY_VERSION,


class CookiesConsent(SqlAlchemyBase):
    __tablename__ = 'cookies_consent'
    cookies_consent_id = Column(Integer, primary_key=True)
    cookies_consent_relevant_account_id = Column(Integer, ForeignKey(
        'account.account_id', ondelete='CASCADE'))
    cookies_consent_account_email = Column(Text)
    cookies_consent_public_id = Column(
        Text, default=lambda: str(uuid.uuid4()), unique=True, nullable=False)
    cookies_consent_choices = Column(Text)
    cookies_consent_user_agent = Column(Text)
    cookies_consent_ip_address = Column(Text)

    created_at = Column(DateTime, index=True, default=datetime.utcnow)


class MarketingConsent(SqlAlchemyBase):
    __tablename__ = 'marketing_consent'
    marketing_consent_id = Column(Integer, primary_key=True)
    marketing_consent_email = Column(Text, nullable=False)

    marketing_consent_public_id = Column(
        Text, default=lambda: str(uuid.uuid4()), unique=True, nullable=False)
    marketing_consent_relevant_account_id = Column(Integer, ForeignKey(
        'account.account_id', ondelete='CASCADE'))
    marketing_consent_user_agent = Column(Text)
    marketing_consent_privacy_policy_version = Column(Text)
    marketing_consent_text = Column(Text)
    marketing_consent_ip_address = Column(Text)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)


class ChildAccount(SqlAlchemyBase):
    __tablename__ = 'child_account'
    child_account_id = Column(Integer, primary_key=True)
    child_account_public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)
    child_account_name = Column(Text, nullable=False)
    child_account_parent_id = Column(Integer, ForeignKey(
        'account.account_id', ondelete='CASCADE'))
    child_account_year_id = Column(Integer, ForeignKey('year.year_id'))
    child_account_login_alias = Column(Text, unique=True)
    child_account_pin = Column(Text)
    child_account_email = Column(Text, unique=True)
    child_account_language = Column(Text)
    child_account_verification_code = Column(Text)
    child_account_register_source = Column(Text)

    show_intro = Column(Boolean, default=True)

    is_verified = Column(Boolean, default=False)

    # child_account_referral_code = Column(Text)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)

    parent_account = relationship(
        "Account", back_populates="children_accounts", passive_deletes=True)

    relevant_exercise_associations = relationship(
        "ChildAccountExerciseAssociation", back_populates="relevant_child_account", cascade="all, delete-orphan")

    relevant_lesson_associations = relationship(
        "ChildAccountLessonAssociation", back_populates="relevant_child_account", cascade="all, delete-orphan")

    relevant_year = relationship(
        "Year", back_populates="relevant_child_accounts")

    relevant_child_events = relationship(
        "ChildEvent", back_populates="relevant_child_account", cascade="all, delete-orphan")


class ParentChildAssignment(SqlAlchemyBase):
    __tablename__ = 'parent_assignment'
    parent_child_assignment_id = Column(Integer, primary_key=True)
    parent_email = Column(Text)
    relevant_child_account_id = Column(Integer, ForeignKey(
        'child_account.child_account_id', ondelete='CASCADE'))
    verification_code = Column(Text)
    language = Column(Text)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)


class PriceVersion(SqlAlchemyBase):
    __tablename__ = 'price_version'
    price_version_id = Column(Integer, primary_key=True)
    name = Column(Text)
    version = Column(Integer, nullable=False)
    is_current = Column(Boolean, default=False)
    is_active = Column(Boolean, default=False)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)

class SubscriptionOption(SqlAlchemyBase):
    __tablename__ = 'subscription_option'

    subscription_option_id = Column(Integer, primary_key=True)

    public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)

    price_version_id = Column(Integer, ForeignKey('price_version.price_version_id'), nullable=False)

    year_id = Column(Integer, ForeignKey('year.year_id'), nullable=False)

    stripe_monthly_id = Column(Text)
    stripe_yearly_id = Column(Text)

    monthly_amount = Column(Float)
    yearly_amount = Column(Float)

    created_at = Column(DateTime, index=True, default=datetime.utcnow)

class PriceEligibility(SqlAlchemyBase):
    __tablename__ = 'price_eligibility'
    price_eligibility_id = Column(Integer, primary_key=True)
    public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)
    
    account_id = Column(Integer, ForeignKey('account.account_id'))
    price_version_id = Column(Integer, ForeignKey('price_version.price_version_id'))
    created_at = Column(DateTime, index=True, default=datetime.utcnow)


# This table links accounts to specific subscriptions

class ActiveSubscription(SqlAlchemyBase):
    __tablename__ = 'active_subscription'
    active_subscription_id = Column(Integer, primary_key=True)
    
    public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)
    
    subscription_option_id = Column(Integer, ForeignKey('subscription_option.subscription_option_id'))
    subscription_type = Column(Text) # monthly or yearly
    account_id = Column(Integer, ForeignKey('account.account_id'))
    
    status = Column(Text)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)
    

class Subscription(SqlAlchemyBase):
    __tablename__ = 'subscription'
    subscription_id = Column(Integer, primary_key=True)
    subscription_public_id = Column(
        Text, nullable=False, unique=True, default=lambda: str(uuid.uuid4()))
    subscription_year_id = Column(Integer, ForeignKey('year.year_id'))
    subscription_currency = Column(Text)
    subscription_price = Column(Float)
    subscription_stripe_id = Column(Text)
    subscription_type = Column(Text)  # monthly or yearly
    created_at = Column(DateTime, index=True, default=datetime.utcnow)

    relevant_year = relationship(
        "Year", back_populates="relevant_subscriptions")
    relevant_subscription_associations = relationship(
        "AccountSubscriptionAssociation", back_populates="relevant_subscription")

# This table links accounts to specific subscriptions


class AccountSubscriptionAssociation(SqlAlchemyBase):
    __tablename__ = 'account_subscription_association'
    association_id = Column(Integer, primary_key=True)
    association_public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)
    association_subscription_id = Column(
        Integer, ForeignKey('subscription.subscription_id'))
    association_account_id = Column(Integer, ForeignKey('account.account_id'))
    created_at = Column(DateTime, index=True, default=datetime.utcnow)
    association_subscription_status = Column(Text)

    relevant_account = relationship(
        "Account", back_populates="relevant_subscription_associations")

    relevant_subscription = relationship(
        "Subscription", back_populates="relevant_subscription_associations")


# Media table
# Media is used for images and videos


# class Media(SqlAlchemyBase):
#     __tablename__ = 'media'
#     media_id = Column(Integer, primary_key=True)
#     # Not sure if we need a private id for media but we can add it for now
#     media_private_id = Column(Text, default=lambda: str(uuid.uuid4()))
#     # The public id is the id that cloudflare uses to identify the media
#     # We use it as the default public id because it will either way be visible to the user
#     # Hence the media table as such is not necessarily needed, the public id is also stored in the exercises directly
#     # But we can potentially use this to help with manipulating and managing images further downb the line
#     media_public_id = Column(Text, unique=True)
#     media_type = Column(Text)
#     media_name = Column(Text)
#     created_at = Column(DateTime, index=True, default=datetime.utcnow)

class SignUp(SqlAlchemyBase):
    __tablename__ = 'sign_up'
    sign_up_id = Column(Integer, primary_key=True)
    sign_up_account_id = Column(Integer, ForeignKey('account.account_id'))
    sign_up_email = Column(Text, nullable=False)
    sign_up_language = Column(Text, nullable=False)
    signup_source = Column(Text)
    signup_year_options = Column(Text)
    signup_system_options = Column(Text)
    signup_searchparams = Column(Text)

    created_at = Column(DateTime, index=True, default=datetime.utcnow)


class AnalyticsEvent(SqlAlchemyBase):
    __tablename__ = 'analytics_event'
    analytics_event_id = Column(Integer, primary_key=True)
    analytics_event_public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)
    analytics_event_data = Column(Text)
    analytics_event_name = Column(Text)
    analytics_event_session_id = Column(Text)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)


class BlogPost(SqlAlchemyBase):
    __tablename__ = 'blog_post'
    blog_post_id = Column(Integer, primary_key=True)
    blog_post_public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)
    blog_post_title = Column(Text, nullable=False)
    blog_post_language = Column(Text, nullable=False)
    blog_post_summary = Column(Text, nullable=False)
    blog_post_content = Column(Text, nullable=False)
    blog_post_slug = Column(Text, unique=True, nullable=False)
    blog_post_image_name = Column(Text)
    blog_post_ordering = Column(Integer)
    blog_post_reading_time = Column(Integer)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)
    last_updated = Column(DateTime, index=True, default=datetime.utcnow)


class Trial(SqlAlchemyBase):
    __tablename__ = 'trial'
    trial_id = Column(Integer, primary_key=True)
    trial_public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)
    trial_account_id = Column(Integer, ForeignKey('account.account_id'))
    trial_child_account_id = Column(
        Integer, ForeignKey('child_account.child_account_id', ondelete='SET NULL'), nullable=True)
    trial_start_date = Column(DateTime, index=True, default=datetime.utcnow)
    trial_end_date = Column(DateTime, index=True, default=datetime.utcnow)
    trial_status = Column(Text)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)


class UserActivity(SqlAlchemyBase):
    __tablename__ = 'user_activity'
    id = Column(Integer, primary_key=True)
    account_id = Column(Integer, ForeignKey(
        'account.account_id'), index=True, nullable=True)
    child_account_id = Column(Integer, ForeignKey(
        'child_account.child_account_id', ondelete='CASCADE'), index=True, nullable=True)
    # Keeping it flexible for different activity types
    activity_type = Column(Text, default='heartbeat')
    current_path = Column(Text)
    timestamp = Column(DateTime, index=True, default=datetime.utcnow)


class Questionnaire(SqlAlchemyBase):
    __tablename__ = 'questionnaire'
    questionnaire_id = Column(Integer, primary_key=True)
    questionnaire_public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)
    questionnaire_data = Column(JSONB)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)


class PreSignUp(SqlAlchemyBase):
    __tablename__ = 'pre_sign_up'
    pre_sign_up_id = Column(Integer, primary_key=True)
    pre_sign_up_email = Column(Text, nullable=False)
    pre_sign_up_language = Column(Text, nullable=False)
    pre_sign_up_source = Column(Text, nullable=False)
    pre_sign_up_questionnaire = Column(JSONB)
    pre_sign_up_user_type = Column(Text, default='parent')
    created_at = Column(DateTime, index=True, default=datetime.utcnow)


class LoginAttempt(SqlAlchemyBase):
    __tablename__ = 'login_attempt'
    login_attempt_id = Column(Integer, primary_key=True)
    login_attempt_public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)
    login_attempt_identified = Column(Boolean, default=False)
    login_attempt_email = Column(Text, nullable=False)
    login_attempt_account_id = Column(
        Integer, ForeignKey('account.account_id'))
    login_attempt_status = Column(Text)
    login_attempt_error_message = Column(Text)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)


class LoginEmail(SqlAlchemyBase):
    __tablename__ = 'login_email'
    login_email_id = Column(Integer, primary_key=True)
    login_email_public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)
    email = Column(Text, nullable=False)
    account_id = Column(Integer, ForeignKey('account.account_id'))
    secret_code = Column(Text)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)

class Toast(SqlAlchemyBase):
    __tablename__ = 'toast'
    toast_id = Column(Integer, primary_key=True)
    toast_public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)
    content = Column(Text)
    priority = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    account_type = Column(Text, default='parent')
    temporary_dismissable = Column(Boolean, default=True)
    permanently_dismissable = Column(Boolean, default=False)
    hide_from_subscribers = Column(Boolean, default=True)
    show_to_subscribers_only = Column(Boolean, default=False)
    type = Column(Text, default='info')
    created_at = Column(DateTime, index=True, default=datetime.utcnow)

class DismissedToast(SqlAlchemyBase):
    __tablename__ = 'dismissed_toast'
    dismissed_toast_id = Column(Integer, primary_key=True)
    account_id = Column(Integer, ForeignKey('account.account_id'))
    child_account_id = Column(Integer, ForeignKey('child_account.child_account_id', ondelete='CASCADE'))
    toast_id = Column(Integer, ForeignKey('toast.toast_id'))
    created_at = Column(DateTime, index=True, default=datetime.utcnow)


# class ConsentType(SqlAlchemyBase):
#     __tablename__ = 'consent_type'
#     consent_type_id = Column(Integer, primary_key=True)
#     consent_type_name = Column(Text)
#     consent_type_description = Column(Text)
#     created_at = Column(DateTime, index=True, default=datetime.utcnow)

#     relevant_consent_associations = relationship("UserConsentAssociation", back_populates="relevant_consent_type")

# # Association table
# class UserConsentAssociation(SqlAlchemyBase):
#     __tablename__ = 'user_consent_association'
#     association_id = Column(Integer, primary_key=True)
#     association_public_id = Column(Text)
#     account_id = Column(Integer, ForeignKey('account.account_id'))
#     consent_type_id = Column(Integer, ForeignKey('consent_type.consent_type_id'))
#     created_at = Column(DateTime, index=True, default=datetime.utcnow)

#     relevant_account = relationship("Account", back_populates="relevant_consent_associations")
#     relevant_consent_type = relationship("ConsentType", back_populates="relevant_consent_associations")

# class ReferralAssociation(SqlAlchemyBase):
#     __tablename__ = 'referral_association'
#     association_id = Column(Integer, primary_key=True)
#     association_public_id = Column(Text)

#     referrer_account_id = Column(Integer, ForeignKey('account.account_id'))
#     referred_account_id = Column(Integer, ForeignKey('account.account_id'))

#     created_at = Column(DateTime, index=True, default=datetime.utcnow)


# class LessonDraft(SqlAlchemyBase):
#     __tablename__ = 'lesson_draft'
#     lesson_draft_id = Column(Integer, primary_key=True)
#     lesson_draft_title = Column(Text)
#     lesson_draft_description = Column(Text)
#     lesson_draft_image_id = Column(Text)
#     lesson_draft_video_id = Column(Text)
#     lesson_draft_public_id = Column(Text)
#     relevant_lesson_id = Column(Integer, ForeignKey('lesson.lesson_id'))
#     relevant_chapter_id = Column(Integer, ForeignKey('chapter.chapter_id'))
#     is_published = Column(Boolean, default=False)
#     previously_published = Column(Boolean, default=False)
#     created_at = Column(DateTime, index=True, default=datetime.utcnow)

#     relevant_chapter = relationship("Chapter", back_populates="lesson_drafts")
#     relevant_exercises = relationship(
#         "Exercise", back_populates="relevant_lesson_draft")
#     lesson = relationship("Lesson", back_populates="lesson_draft")


# class Referral(SqlAlchemyBase):
#     __tablename__ = 'referral'
#     referral_id = Column(Integer, primary_key=True)
#     referral_public_id = Column(Text, nullable=False, unique=True)
#     referral_code = Column(Text, nullable=False, unique=True)
#     relevant_account_id = Column(Integer, ForeignKey('account.account_id'))
#     created_at = Column(DateTime, index=True, default=datetime.utcnow)


class SummaryDownload(SqlAlchemyBase):
    __tablename__ = 'summary_download'
    summary_download_id = Column(Integer, primary_key=True)
    account_id = Column(
        Integer, ForeignKey('account.account_id'))
    child_account_id = Column(
        Integer, ForeignKey('child_account.child_account_id', ondelete='SET NULL'))
    downloaded_file = Column(Text)
    email = Column(Text)
    created_at = Column(
        DateTime, index=True, default=datetime.utcnow)


class YearSummaryDownload(SqlAlchemyBase):
    __tablename__ = 'year_summary_download'
    year_summary_download_id = Column(Integer, primary_key=True)
    account_id = Column(Integer, ForeignKey('account.account_id'))
    child_account_id = Column(Integer, ForeignKey('child_account.child_account_id', ondelete='SET NULL'))
    downloaded_file = Column(Text)
    email = Column(Text)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)


class DiscountCode(SqlAlchemyBase):
    __tablename__ = 'discount_code'
    discount_code_id = Column(Integer, primary_key=True)
    stripe_id = Column(Text)
    public_code = Column(Text)
    one_time = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, index=True, default=datetime.utcnow)

class DiscountCodeUsage(SqlAlchemyBase):
    __tablename__ = 'discount_code_usage'
    discount_code_usage_id = Column(Integer, primary_key=True)
    discount_code_id = Column(Integer, ForeignKey('discount_code.discount_code_id'))
    account_id = Column(Integer, ForeignKey('account.account_id'))
    created_at = Column(DateTime, index=True, default=datetime.utcnow)


# class MailingList(SqlAlchemyBase):
#     __tablename__ = 'mailing_list'
#     mailing_list_id = Column(Integer, primary_key=True)
#     list_name = Column(Text)
#     mailerlite_id = Column(Text)
#     is_active = Column(Boolean, default=True)
#     created_at = Column(DateTime, index=True, default=datetime.utcnow)


class TaskInvocation(SqlAlchemyBase):
    __tablename__ = 'task_invocation'
    task_invocation_id = Column(Integer, primary_key=True)
    task_invocation_public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)
    task_type = Column(Text, nullable=False)  # e.g., 'weekly_activity_summary'
    task_status = Column(Text, nullable=False)  # 'success', 'failed', 'in_progress'
    created_at = Column(DateTime, index=True, default=datetime.utcnow)

    __table_args__ = (
        Index('idx_task_type_created', task_type, created_at),
    )
class AccountAuthSecurity(SqlAlchemyBase):
    __tablename__ = 'account_auth_security'
    id = Column(Integer, primary_key=True)
    account_id = Column(Integer, ForeignKey('account.account_id', ondelete='CASCADE'), unique=True)
    failed_attempts = Column(Integer, default=0)
    last_failed_attempt = Column(DateTime)
    locked_until = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    __table_args__ = (
        Index('idx_auth_security_account', account_id),
        Index('idx_auth_security_locked', locked_until),
    )

# We want a short more user-friendly reset code
def generate_parent_pin_reset_code():
    return ''.join(random.choices(string.ascii_letters + string.digits, k=9))

class PinResetRequest(SqlAlchemyBase):
    __tablename__ = 'pin_reset_request'
    id = Column(Integer, primary_key=True)
    account_id = Column(Integer, ForeignKey('account.account_id', ondelete='CASCADE'))
    request_token = Column(Text, unique=True, default=lambda: generate_parent_pin_reset_code())
    expires_at = Column(DateTime, default=lambda: datetime.utcnow() + timedelta(minutes=30))
    used = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        Index('idx_pinreset_account', account_id),
        Index('idx_pinreset_token', request_token),
    )
