# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Running the Application
```bash
# Development mode (with auto-reload)
python run.py

# Or directly with uvicorn for development
uvicorn main:app --reload --host 127.0.0.1 --port 8000
```

### Testing
```bash
# Run all tests
pytest

# Run specific test file
pytest tests/api/v1/app/student/test_get_dashboard.py

# Run tests with verbose output
pytest -v

# Run tests with logging
pytest --log-cli-level=INFO
```

### Code Quality
```bash
# Linting (check for flake8 in requirements.txt)
flake8 .

# Code formatting
autopep8 --in-place --recursive .

# Import sorting
isort .
```

## Architecture Overview

### Core Structure
This is a FastAPI backend for an educational platform with the following key components:

- **API Versioning**: All endpoints under `/api/v1/` with clear separation between child/parent/student authentication and app functionality
- **Database**: PostgreSQL with SQLAlchemy ORM, synchronous sessions
- **Authentication**: JWT-based with separate auth flows for parents and children
- **Configuration**: Pydantic Settings with environment variable support

### Key Directories

#### `/api/v1/`
- **`auth/`**: Authentication endpoints for child and parent accounts
- **`app/`**: Main application functionality (student dashboard, parent management, subscriptions)
- **`landing/`**: Public landing page endpoints
- **`other/`**: Miscellaneous endpoints (contact forms)

#### `/db/`
- **`models/`**: SQLAlchemy models organized by domain (auth, content, subscription, etc.)
- **`database.py`**: Database configuration and session management

#### `/core/`
- **`config/`**: Application settings and logging configuration
- **`exception_handling/`**: Custom exceptions and global exception handlers

#### `/dependencies/`
- **`auth_dependencies/`**: Authentication middleware and dependency injection
- **`email_dependency.py`**: Email service dependencies

#### `/services/`
- **`mailer/`**: Email service with multi-language template support
- **`email_list/`**: Email list management (MailerLite integration)

### Authentication Architecture

**Dual Authentication System:**
- **Parent Authentication**: Full JWT tokens with extended expiration (7 days)
- **Child Authentication**: Simplified PIN-based system with parent assignment flow
- **Session Validation**: Both systems support session validation endpoints

**Key Auth Dependencies:**
- `AuthDependency`: General authentication validation
- `SubscriptionDependency`: Validates active subscription status
- Headers: Uses `x-auth-token` header for authentication

### Database Patterns

**Models Organization:**
- Separate files for different domains (auth.py, content.py, subscription.py, etc.)
- Association tables in `/associations/` directory
- Base model with common fields (id, created_at, updated_at)

**Session Management:**
- Synchronous SQLAlchemy sessions via `get_db()` dependency
- Automatic rollback on exceptions
- Connection pooling configured differently for dev vs production

### Service Layer Pattern

**Structure:**
- Each route has a corresponding service in `/services/` directory
- Services handle business logic and database operations
- Routes focus on request/response handling and validation

**Example Pattern:**
```
/routes/get_dashboard.py -> /services/get_dashboard_service.py
```

### Error Handling

**Custom Exceptions:**
- `ServiceError`: General service-level errors
- `NotFoundError`: Resource not found errors
- Global exception handlers in `core/exception_handling/`

### Configuration Management

**Environment-based Configuration:**
- Pydantic Settings with `.env` file support
- Separate configs for development, test, and production
- Extensive third-party service integration (Stripe, AWS, Cloudflare, MailerLite)

### Testing Infrastructure

**Test Setup:**
- Pytest with custom fixtures in `tests/_infra/fixtures.py`
- Database testing with testcontainers (Docker-based)
- Separate test configuration and logging
- Request context simulation for testing authenticated endpoints

**Test Organization:**
- Tests mirror the API structure in `/tests/api/v1/`
- Fixtures for common test data and authentication
- Mock data loaders for consistent test scenarios

### Key Integrations

- **Stripe**: Payment processing and subscription management
- **AWS SES**: Email delivery
- **Cloudflare R2**: File storage and CDN
- **MailerLite**: Email marketing and user segmentation
- **Sentry**: Error tracking (production only)
- **Bunny CDN**: Video content delivery

### Development Notes

- **Logging**: Structured logging with Loguru, custom formatting for development vs production
- **CORS**: Permissive CORS settings for development, restrictive for production
- **Documentation**: FastAPI auto-docs disabled in production (`docs_url=None`)
- **Environment Variables**: Extensive configuration via environment variables with sensible defaults