from pathlib import Path
from fastapi import Depends
from loguru import logger

from core.config.settings import settings
from services.mailer.loader import TemplateLoader
from services.mailer.mailer_service import <PERSON><PERSON>ailer, NullMailer, EmailService
from services.mailer.auth_mailer_service import AuthMailerService

# Determine the base path of the project for templates
# Assumes this file is at project_edu_backend/dependencies/email_dependency.py
# and templates are at project_edu_backend/services/mailer/templates
PROJECT_ROOT = Path(__file__).resolve().parent.parent
TEMPLATES_BASE_DIR = PROJECT_ROOT / "services" / "mailer" / "templates"

def get_email_from() -> str:
    """Provides the email from address based on environment."""
    if isinstance(settings.EMAIL_FROM, dict):
        return settings.EMAIL_FROM.get(settings.ENVIRONMENT, settings.EMAIL_FROM.get('dev', ''))
    else:
        return settings.EMAIL_FROM

def get_email_service() -> EmailService:
    """Provides the underlying email service (SES or Null) based on environment."""
    template_loader = TemplateLoader(templates_base_dir=str(TEMPLATES_BASE_DIR))
    
    # Use SESMailer in all environments now - dev will use real email service
    # but MailerLite remains production-only (configured separately)
    email_from = get_email_from()
    if settings.AWS_ACCESS_KEY and settings.AWS_SECRET_KEY and settings.AWS_SES_REGION and email_from:
        logger.info("Using SESMailer with AWS SES for email delivery.")
        return SESMailer(template_loader=template_loader)
    else:
        logger.warning("AWS SES not fully configured. Using NullMailer (emails will be logged only).")
        return NullMailer(template_loader=template_loader)

def get_auth_mailer_service(
    email_service: EmailService = Depends(get_email_service)
) -> AuthMailerService:
    """Provides the AuthMailerService, which uses an underlying EmailService."""
    return AuthMailerService(email_service=email_service)