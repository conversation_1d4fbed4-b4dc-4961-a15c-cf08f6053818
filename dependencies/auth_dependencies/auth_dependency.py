from .base_auth_dependency import BaseAuthDependency, AuthDependencyResponse
from fastapi import Depends, <PERSON><PERSON>
from sqlalchemy.orm import Session as SyncSession
from db.database import get_db
from typing import Optional


class AuthDependency(BaseAuthDependency):
    def __call__(
        self,
        x_auth_token: Optional[str] = Header(None, alias="x-auth-token"),
        db: SyncSession = Depends(get_db)
    ) -> Optional[AuthDependencyResponse]:
        return self.validate_auth(x_auth_token, db)