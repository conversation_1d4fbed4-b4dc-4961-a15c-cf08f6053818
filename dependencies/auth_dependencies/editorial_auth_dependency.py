from fastapi import Depends, <PERSON><PERSON>, HTTPException
from typing import Optional
from sqlalchemy.orm import Session
from sqlalchemy import select
from db.database import get_db
from db.models import EditorAccount, EditorRole
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError, AuthorizationError
from api.v1.common.schemas import AppErrorCode
from pydantic import BaseModel
import jwt
from datetime import datetime, timedelta, UTC
from core.config import settings

class EditorAuthResponse(BaseModel):
    editor_id: int
    editor_public_id: str
    email: str
    role: EditorRole
    editor: EditorAccount
    
    model_config = {
        "arbitrary_types_allowed": True
    }

class EditorAuthDependency:
    def __init__(self, required_role: Optional[EditorRole] = None):
        self.required_role = required_role
    
    def __call__(
        self,
        x_editor_token: Optional[str] = Header(None, alias="x-editor-token"),
        db: Session = Depends(get_db)
    ) -> EditorAuthResponse:
        if not x_editor_token:
            raise AuthenticationError(
                message="Authentication required",
                error_code=AppErrorCode.AUTHENTICATION_REQUIRED
            )
        
        try:
            # Decode JWT token
            payload = jwt.decode(
                x_editor_token,
                settings.SECRET_KEY,
                algorithms=[settings.JWT_ALGORITHM]
            )
            editor_id = payload.get("editor_id")
            
            if not editor_id:
                raise AuthenticationError(
                    message="Invalid token",
                    error_code=AppErrorCode.INVALID_TOKEN
                )
            
            # Get editor from database
            stmt = select(EditorAccount).where(
                EditorAccount.id == editor_id,
                EditorAccount.is_active == True
            )
            editor = db.execute(stmt).scalar_one_or_none()
            
            if not editor:
                raise AuthenticationError(
                    message="Editor not found or inactive",
                    error_code=AppErrorCode.USER_NOT_FOUND
                )
            
            # Check role requirement
            if self.required_role and editor.role != self.required_role:
                raise AuthorizationError(
                    message=f"Requires {self.required_role.value} role",
                    error_code=AppErrorCode.PERMISSION_DENIED
                )
            
            return EditorAuthResponse(
                editor_id=editor.id,
                editor_public_id=editor.public_id,
                email=editor.email,
                role=editor.role,
                editor=editor
            )
            
        except jwt.ExpiredSignatureError:
            raise AuthenticationError(
                message="Token expired",
                error_code=AppErrorCode.TOKEN_EXPIRED
            )
        except jwt.InvalidTokenError:
            raise AuthenticationError(
                message="Invalid token",
                error_code=AppErrorCode.INVALID_TOKEN
            )

# Convenience dependencies
RequireEditor = EditorAuthDependency()
RequireAdmin = EditorAuthDependency(required_role=EditorRole.ADMIN)