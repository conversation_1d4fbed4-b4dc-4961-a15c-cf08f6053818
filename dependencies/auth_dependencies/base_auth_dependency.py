from fastapi import Depends, <PERSON><PERSON>
from typing import Optional, <PERSON>
from sqlalchemy.orm import Session as SyncSession
from sqlalchemy import select
from db.database import get_db
from core.exception_handling.exceptions.custom_exceptions import (
    AuthenticationError,
    AuthorizationError,
    NotFoundError,
    ServiceError
)
from api.v1.common.schemas import App<PERSON>rrorCode
from api.v1.app.auth.utils import decode_jwt_token
from db.models import Account, ChildAccount
from pydantic import BaseModel
import enum

class UserType(str, enum.Enum):
    parent = "parent"
    child = "child"


class AuthDependencyResponse(BaseModel):
    access_token: str
    account_id: int
    account_public_id: str
    user_type: UserType
    account: Union[Account, ChildAccount]

    model_config = {
        "arbitrary_types_allowed": True
    }

class BaseAuthDependency:
    def __init__(self, required: bool = True):
        self.required = required

    def validate_auth(
        self,
        x_auth_token: Optional[str] = Header(None, alias="x-auth-token"),
        db: SyncSession = Depends(get_db) 
    ) -> Optional[AuthDependencyResponse]:
        # Inject the Request object into your dependency function.
        # After successfully authenticating and fetching the user, 
        # set request.state.user_public_id and request.state.user_type.
        # Return the user object as usual.

        # If not required and no token present, return None
        if not self.required and not x_auth_token:
            return None
        # If required but no token present, raise 401
        if self.required and not x_auth_token:
            raise AuthenticationError(
                message="Authentication required",
                log_message="Authentication required",
                error_code=AppErrorCode.AUTHENTICATION_REQUIRED
            )

        # Decode JWT token
        payload = decode_jwt_token(x_auth_token)
        if not payload:
            if self.required:
                raise AuthorizationError(
                    message="Invalid authentication token",
                    log_message="Invalid authentication token",
                    error_code=AppErrorCode.INVALID_TOKEN
                )
            return None

        # Validate the session using the session token from JWT payload

        # Get the relevant account from the database
        user_type = payload.get("user_type")
        account_public_id_from_token = payload.get("account_public_id") 

        if not account_public_id_from_token: 
            if self.required:
                raise AuthorizationError(
                    message="Invalid token payload: missing account_public_id",
                    log_message="Token payload missing account_public_id",
                    error_code=AppErrorCode.INVALID_TOKEN_PAYLOAD
                )
            return None


        if user_type == UserType.parent.value: 
            stmt = select(Account).where(Account.public_id == account_public_id_from_token)
        elif user_type == UserType.child.value: 
            stmt = select(ChildAccount).where(
                ChildAccount.public_id == account_public_id_from_token)
        else: 
            if self.required:
                raise AuthorizationError(
                    message="Invalid user type in token",
                    log_message=f"Invalid user_type '{user_type}' in token",
                    error_code=AppErrorCode.INVALID_USER_TYPE
                )
            return None
            
        result = db.execute(stmt)
        relevant_account = result.scalar_one_or_none()

        if not relevant_account:
            if self.required:
                raise NotFoundError(
                    message="Account not found",
                    log_message=f"Account not found for public_id {account_public_id_from_token} and type {user_type}",
                    entity_name="Account", 
                    identifier=account_public_id_from_token,
                    error_code=AppErrorCode.USER_NOT_FOUND  
                )
            return None
            
        try:
            return AuthDependencyResponse(
                access_token=x_auth_token,
                account_id=relevant_account.id,
                account_public_id=relevant_account.public_id,
                user_type=UserType(user_type),  # Convert string back to UserType enum
                account=relevant_account
            )
        except (AuthenticationError, AuthorizationError, NotFoundError) as e:
            if self.required:
                raise e
            return None
        except Exception as e:
            if self.required:
                raise ServiceError(
                    message="Error processing authentication data.",
                    log_message=f"Failed to create AuthDependencyResponse: {str(e)}",
                    error_code=AppErrorCode.SERVICE_ERROR
                )
            return None