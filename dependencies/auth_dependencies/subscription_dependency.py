from fastapi import Depends, Header
from typing import List, Dict, Union, Set, Optional
from sqlalchemy.orm import Session as SyncSession
from sqlalchemy.orm import selectinload
from db.database import get_db
from sqlalchemy import select
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError, ServiceError
from db.models import (
    Account, ActiveSubscription, SubscriptionStatusType,
    ActiveSubscriptionPlanLink, SubscriptionOption, PlanTypeEnum,
    Subject, Year, PlanSelectedSubject, Trial
)
from .base_auth_dependency import BaseAuthDependency, UserType, AuthDependencyResponse
from datetime import datetime, timedelta, UTC
from core.config.settings import settings
from loguru import logger
from api.v1.common.schemas import AppErrorCode


class SubscriptionInfo(AuthDependencyResponse):
    is_subscribed: bool
    trial_active: bool
    subscribed_subjects: List[Dict[str, Union[str, Dict[str, str]]]]
    actual_subscription_type: Optional[str] = None  # "SC" or "YF" from database

    model_config = {
        "arbitrary_types_allowed": True
    }


class SubscriptionDependency(BaseAuthDependency):
    def __init__(self):
        super().__init__(required=True)

    def _add_subject_to_list(
        self,
        subject: Subject,
        processed_subject_ids: Set[int],
        subscribed_subjects_list: List[Dict[str, Union[str, Dict[str, str]]]]
    ):
        if subject and subject.id not in processed_subject_ids:
            year_details = {"public_id": "", "name": "N/A"}
            if subject.year:
                year_details = {"public_id": subject.year.public_id, "name": subject.year.name}
            
            subscribed_subjects_list.append({
                "public_id": subject.public_id,
                "name": subject.name,
                "year": year_details
            })
            processed_subject_ids.add(subject.id)

    def __call__(
        self,
        x_auth_token: str = Header(None, alias="x-auth-token"),
        db: SyncSession = Depends(get_db)
    ) -> SubscriptionInfo:
        auth_data = self.validate_auth(x_auth_token, db)

        if not auth_data:
            logger.error("Auth data missing in SubscriptionDependency despite being required.")
            raise AuthenticationError(
                message="Authentication failed.",
                log_message="Auth data not found in SubscriptionDependency.",
                error_code=AppErrorCode.AUTHENTICATION_REQUIRED
            )

        parent_id_for_subscription_query: int
        
        if auth_data.user_type == UserType.parent:
            parent_id_for_subscription_query = auth_data.account_id
        elif auth_data.user_type == UserType.child:
            child_account_orm = auth_data.account
            if not child_account_orm.parent_account_id:
                logger.error(f"""Child account {child_account_orm.public_id} 
                missing parent_account_id for subscription check.""")
                raise ServiceError(
                    message="Child account data incomplete for subscription check.", 
                    error_code=AppErrorCode.SERVICE_ERROR
                )
            parent_id_for_subscription_query = child_account_orm.parent_account_id
        else:
            logger.error(f"""Invalid user type '{auth_data.user_type}' for subscription check.""")
            raise AuthenticationError(
                message="Invalid user type for subscription check.", 
                error_code=AppErrorCode.INVALID_USER_TYPE
            )

        stmt_active_subs = (
            select(ActiveSubscription)
            .options(
                selectinload(ActiveSubscription.plan_links).options(
                    selectinload(ActiveSubscriptionPlanLink.subscription_option).options(
                        selectinload(SubscriptionOption.year).options(
                            selectinload(Year.subjects).selectinload(Subject.year) 
                            # Eager load subjects of the year, and their year
                        )
                    ),
                    selectinload(ActiveSubscriptionPlanLink.selected_subjects).options(
                        selectinload(PlanSelectedSubject.subject).selectinload(Subject.year) 
                        # Eager load chosen subjects and their year
                    )
                )
            )
            .where(
                ActiveSubscription.parent_account_id == parent_id_for_subscription_query,
                ActiveSubscription.status.in_([SubscriptionStatusType.ACTIVE, SubscriptionStatusType.TRIALING, SubscriptionStatusType.PAUSED])
            )
        )
        active_parent_subscriptions = db.execute(stmt_active_subs).scalars().unique().all()

        subscribed_subjects_list: List[Dict[str, Union[str, Dict[str, str]]]] = []
        processed_subject_ids: Set[int] = set()
        actual_subscription_type: Optional[str] = None
        
        is_subscribed_paid = any(sub.status == SubscriptionStatusType.ACTIVE for sub in active_parent_subscriptions)

        for active_sub in active_parent_subscriptions:
            # Skip processing subjects from paused subscriptions
            if active_sub.status == SubscriptionStatusType.PAUSED:
                logger.info(f"Skipping paused subscription {active_sub.public_id} for access calculation")
                continue
                
            for plan_link in active_sub.plan_links:
                # The new model ActiveSubscriptionPlanLink has chosen_plan_type directly.
                # It also links to SubscriptionOption which has the year.
                
                if not plan_link.subscription_option:
                    logger.warning(f"SubscriptionOption missing for plan_link {plan_link.public_id}. Skipping.")
                    continue

                plan_type = plan_link.chosen_plan_type  # Use the chosen_plan_type from the link
                
                # Capture the actual subscription type (prioritize YF over SC if both exist)
                if active_sub.status == SubscriptionStatusType.ACTIVE:
                    if plan_type == PlanTypeEnum.YF:
                        actual_subscription_type = "YF"
                    elif plan_type == PlanTypeEnum.SC and actual_subscription_type != "YF":
                        actual_subscription_type = "SC"

                if plan_type == PlanTypeEnum.SC:
                    for selected_subject_link in plan_link.selected_subjects:
                        if selected_subject_link.subject:
                            self._add_subject_to_list(
                                selected_subject_link.subject,
                                processed_subject_ids,
                                subscribed_subjects_list
                            )
                
                elif plan_type == PlanTypeEnum.YF:
                    # Year comes from the linked SubscriptionOption
                    year_for_yf_plan = plan_link.subscription_option.year
                    if year_for_yf_plan and year_for_yf_plan.subjects:
                        for subject_in_year in year_for_yf_plan.subjects:
                            if subject_in_year and subject_in_year.is_active:  # Check if subject itself is active
                                self._add_subject_to_list(
                                    subject_in_year,
                                    processed_subject_ids,
                                    subscribed_subjects_list
                                )
                    else:
                        logger.warning(f"""Year or subjects missing for YF plan_link
                        {plan_link.public_id} (SubscriptionOption: {plan_link.subscription_option.public_id})""")


        trial_active = False
        trial_stmt = select(Trial).where(
            Trial.account_id == parent_id_for_subscription_query,
            Trial.status == 'active',
            Trial.end_date > datetime.now(UTC)
        )
        db_trial = db.execute(trial_stmt).scalar_one_or_none()

        if db_trial:
            trial_active = True
        elif not is_subscribed_paid:
            parent_account_trial_fallback_stmt = select(Account).where(Account.id == parent_id_for_subscription_query)
            parent_account_trial_fallback = db.execute(parent_account_trial_fallback_stmt).scalar_one_or_none()

            if not parent_account_trial_fallback:
                logger.error(f"""Parent account
                 {parent_id_for_subscription_query} 
                 not found for trial fallback check.""")
            elif (
                parent_account_trial_fallback.created_at 
                + timedelta(days=settings.TRIAL_DURATION_DAYS) 
                > datetime.now(UTC)
            ):
                trial_active = True
        
        if trial_active and not subscribed_subjects_list:
            logger.info(f"""Trial active for parent account ID
             {parent_id_for_subscription_query}, 
             populating all subjects for trial access.""")
            all_subjects_stmt_trial = select(Subject).options(
                selectinload(Subject.year)
            ).where(
                Subject.is_active
            )
            all_db_subjects_trial = db.execute(all_subjects_stmt_trial).scalars().unique().all()
            for subject_trial in all_db_subjects_trial:
                self._add_subject_to_list(
                    subject_trial,
                    processed_subject_ids,
                    subscribed_subjects_list
                )

        return SubscriptionInfo(
            access_token=auth_data.access_token,
            account_id=auth_data.account_id,
            account_public_id=auth_data.account_public_id,
            user_type=auth_data.user_type,
            account=auth_data.account,
            is_subscribed=is_subscribed_paid or trial_active,
            trial_active=trial_active,
            subscribed_subjects=subscribed_subjects_list,
            actual_subscription_type=actual_subscription_type
        )