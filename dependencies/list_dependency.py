from loguru import logger

from core.config.settings import settings
from services.email_list.service import MailerLiteService, NullListService, ListService 

def get_list_service() -> ListService:
    """Provides the list management service (MailerLite or Null) based on environment."""
    if settings.ENVIRONMENT.lower() == "dev":
        logger.info("Using NullListService for development (list_dependency).")
        return NullListService()
    else:
        logger.info("Using MailerLiteService (list_dependency).")
        return MailerLiteService()