from botocore.config import Config
import boto3
from botocore.exceptions import Client<PERSON>rror
from hashlib import sha256
from jwcrypto import jwt, jwk  # pip install jwcrypto
from typing import Literal
from datetime import datetime, timezone
import base64
import os
# CLOUDFLARE_JWK = os.environ.get("CLOUDFLARE_JWK")
# CLOUDFLARE_ACCOUNT_ID = os.environ.get("CLOUDFLARE_ACCOUNT_ID")
# CLOUDFLARE_CUSTOMER_ID = os.environ.get("CLOUDFLARE_CUSTOMER_ID")


# Base64-encoded JWT you get from Cloudflare Stream API (https://developers.cloudflare.com/stream/viewing-videos/securing-your-stream#step-1-call-the-streamkey-endpoint-once-to-obtain-a-key)
# b64_jwk = CLOUDFLARE_JWK
# jwk_key = jwk.JWK.from_json(base64.b64decode(b64_jwk))


# def get_video_token(video_id: str, lifetime: int = 3600) -> str:
#     header = {"kid": jwk_key["kid"], "alg": "RS256"}
#     payload = {
#         "sub": video_id,
#         "kid": jwk_key["kid"],
#         "exp": int(datetime.now().timestamp() + lifetime),
#     }
#     token = jwt.JWT(header=header, claims=payload)
#     token.make_signed_token(jwk_key)
#     return token.serialize()


# def create_signed_video_url(video_id: str) -> str:
#     token = get_video_token(video_id)

#     return f"https://customer-{CLOUDFLARE_CUSTOMER_ID}.cloudflarestream.com/{token}/iframe"


BUNNY_EXERCISES_TOKEN_SECURITY_KEY = os.environ.get(
    "BUNNY_EXERCISES_TOKEN_SECURITY_KEY")
BUNNY_EXERCISES_VIDEO_LIBRARY_ID = os.environ.get(
    "BUNNY_EXERCISES_VIDEO_LIBRARY_ID")

BUNNY_LECTURES_TOKEN_SECURITY_KEY = os.environ.get(
    "BUNNY_LECTURES_TOKEN_SECURITY_KEY")
BUNNY_LECTURES_VIDEO_LIBRARY_ID = os.environ.get(
    "BUNNY_LECTURES_VIDEO_LIBRARY_ID")


def create_signed_video_url(video_id: str, video_type: Literal["exercise", "lecture"]) -> str:
    if video_id:
        lifetime = 3600
        expiration = str(int(datetime.now(timezone.utc).timestamp()+ lifetime))

        if video_type == "exercise":
            token_security_key = BUNNY_EXERCISES_TOKEN_SECURITY_KEY
            video_library_id = BUNNY_EXERCISES_VIDEO_LIBRARY_ID
        else:
            token_security_key = BUNNY_LECTURES_TOKEN_SECURITY_KEY
            video_library_id = BUNNY_LECTURES_VIDEO_LIBRARY_ID

        # Create a new hash object
        hash = sha256()

        # print("token_security_key", token_security_key)
        # print("video_id", video_id)
        # print("expiration", expiration)

        # Update the hash with the concatenated data
        data_to_hash = token_security_key.encode(
            'utf-8') + video_id.encode('utf-8') + expiration.encode('utf-8')
        hash.update(data_to_hash)

        # Get the hexadecimal digest of the hash
        token = hash.hexdigest()

        return f"https://iframe.mediadelivery.net/embed/{video_library_id}/{video_id}?token={token}&expires={expiration}&autoplay=false&loop=false&muted=false&preload=true&responsive=true"
    else:
        return ""


CLOUDFLARE_R2_ENDPOINT = os.environ.get("CLOUDFLARE_R2_ENDPOINT")
CLOUDFLARE_R2_ACCESS_KEY_ID = os.environ.get("CLOUDFLARE_R2_ACCESS_KEY_ID")
CLOUDFLARE_R2_SECRET_ACCESS_KEY = os.environ.get(
    "CLOUDFLARE_R2_SECRET_ACCESS_KEY")
CLOUDFLARE_R2_REGION_NAME = os.environ.get("CLOUDFLARE_R2_REGION_NAME")

CLOUDFLARE_R2_IMAGE_BUCKET = os.environ.get("CLOUDFLARE_R2_IMAGE_BUCKET")


def create_signed_image_url(image_id: str, expiry=1800):

    bucket_name = CLOUDFLARE_R2_IMAGE_BUCKET

    client = boto3.client(
        "s3",
        region_name="auto",
        endpoint_url=CLOUDFLARE_R2_ENDPOINT,
        aws_access_key_id=CLOUDFLARE_R2_ACCESS_KEY_ID,
        aws_secret_access_key=CLOUDFLARE_R2_SECRET_ACCESS_KEY,
    )

    try:
        response = client.generate_presigned_url('get_object',  Params={
                                                 'Bucket': bucket_name, 'Key': f"exercise_images/{image_id}.png"}, ExpiresIn=expiry)
        return response
    except ClientError as e:
        print(e)

    except Exception as e:
        print(e)
        return ""
