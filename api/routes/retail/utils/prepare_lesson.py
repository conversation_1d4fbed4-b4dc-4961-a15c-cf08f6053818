from sqlalchemy.orm import Session
from api.utils.media_utils import create_signed_video_url
from db.models import Account, Lesson, Subscription, Exercise, ChildAccount, ChildAccountExerciseAssociation, ChildAccountLessonAssociation,  ActiveSubscription, SubscriptionOption
from .prepare_exercises import prepare_exercises
from datetime import datetime
from api.utils.trial_utils import check_trial_status
from sqlalchemy import case

TEMPORARY_FREE_ACCESS = False  # Set this to False to revert to normal behavior


def prepare_lesson(lesson_public_id: str, user_type: str, relevant_acount: ChildAccount | Account, db: Session, pagination_size: int):
    relevant_lesson = db.query(Lesson).filter_by(
        lesson_public_id=lesson_public_id).first()

    # Checking whether or not the parent account has a subscription to this year
    # To do so we need to first map the current lesson to a year and the child account to a parent account
    relevant_chapter = relevant_lesson.relevant_chapter
    relevant_course = relevant_chapter.relevant_course
    relevant_year = relevant_course.relevant_year

    # More metadata
    course_overview = {
        "course_title": relevant_course.course_title,
        "course_public_id": relevant_course.course_public_id,
        "chapter_title": relevant_chapter.chapter_title,
        "chapter_public_id": relevant_chapter.chapter_public_id,
        "lesson_title": relevant_lesson.lesson_title,
        "lesson_public_id": relevant_lesson.lesson_public_id
    }


    if user_type == "parent":
        parent_account = relevant_acount

    elif user_type == "child":
        # Finding the relevant account and lesson
        relevant_child_account = relevant_acount
        parent_account = relevant_child_account.parent_account

    # Uncomment two lines below to re-enable the previous pricing model
    # relevant_subscription_association = None
    # if not relevant_lesson.is_free:
    # Check if the parent account has an active subscription for the relevant year
    has_active_subscription = False
    if user_type == "child" and parent_account != None:
        relevant_subscription = db.query(ActiveSubscription).filter_by(account_id=parent_account.account_id, status='active').join(
            SubscriptionOption, ActiveSubscription.subscription_option_id == SubscriptionOption.subscription_option_id).filter(SubscriptionOption.year_id == relevant_year.year_id).first()
        if relevant_subscription:
            has_active_subscription = True

    if user_type == "parent":
        relevant_subscription = db.query(ActiveSubscription).filter_by(account_id=parent_account.account_id, status='active').join(
            SubscriptionOption, ActiveSubscription.subscription_option_id == SubscriptionOption.subscription_option_id).filter(SubscriptionOption.year_id == relevant_year.year_id).first()
        if relevant_subscription:
            has_active_subscription = True

    # if True:

    ######### This switches to the free initial lesson model, if re-enabled, also need to change again on the frontend to display the free chapters and lessons and to disable the trial message #####
    # if relevant_subscription_association != None or relevant_lesson.is_free:
    ###################
    trial_status = None
    if user_type != 'free':
        trial_status = 'expired'
        if user_type == "parent":
            relevant_trial = check_trial_status(parent_account, db)
        elif user_type == "child":
            relevant_trial = check_trial_status(
                relevant_child_account, db, 'child')
        if relevant_trial:
            trial_status = relevant_trial["trial_status"]
    else:
        trial_status = 'free'

    # Modify the condition in the main if statement
    if user_type != 'free' and (TEMPORARY_FREE_ACCESS or (has_active_subscription) or trial_status == 'active'):
        # Fetch the exercises associated with this lesson and prepare them for consumption

        difficulty_order = case(
            (Exercise.exercise_difficulty == 'low', 1),
            (Exercise.exercise_difficulty == 'medium', 2),
            (Exercise.exercise_difficulty == 'hard', 3)
        )

        relevant_exercises = db.query(Exercise).filter_by(
            relevant_lesson_id=relevant_lesson.lesson_id).order_by(
            difficulty_order, Exercise.exercise_id).limit(pagination_size).all()

        randomize = True
        exercise_list = prepare_exercises(
            relevant_exercises, randomize, db, private_id=True)

        # TODO: rework this a bit need a clearer distinction between the video watched and completed lessons
        has_completed_video = False
        if user_type == "child":
            # Preparing metadata
            relevant_lesson_association = db.query(ChildAccountLessonAssociation).filter_by(
                relevant_child_account_id=relevant_child_account.child_account_id, relevant_lesson_id=relevant_lesson.lesson_id).first()

            if relevant_lesson_association != None:
                has_completed_video = True

            # Go through the ChildAccountExercziseAssociation and figure out which exercises the account has already completed
            relevant_child_exercise_associations = db.query(ChildAccountExerciseAssociation).filter_by(
                relevant_child_account_id=relevant_child_account.child_account_id).filter_by(relevant_lesson_id=relevant_lesson.lesson_id).all()

            completed_exercises_ids = [
                entry.relevant_exercise_id for entry in relevant_child_exercise_associations]
            completed_exercises = db.query(Exercise).filter(
                Exercise.exercise_id.in_(completed_exercises_ids)).all()
            # completed_exercises_public_ids = [
            #     entry.exercise_public_id for entry in completed_exercises]
            # We then iterate over the exercise_list that we prepared earlier and match the public ids
            # We set the field according to whether or not the account has previously completed the exercise
            for entry in exercise_list:
                entry["exercise_status"] = 'uncomplete'
                if entry["exercise_id"] in completed_exercises_ids:
                    relevant_exercise_association = [
                        association for association in relevant_child_exercise_associations if association.relevant_exercise_id == entry["exercise_id"]][0]
                    if relevant_exercise_association.association_correctly_answered:
                        entry["exercise_status"] = 'correct'
                    else:
                        entry["exercise_status"] = 'incorrect'

            # new_event = create_event('child', relevant_child_account, 'lesson_viewed', {
            #                          "lesson_public_id": relevant_lesson.lesson_public_id}, db)
            # db.add(new_event)
            # db.commit()

        elif user_type == "parent":
            # If we are dealing with a parent account, there is no need to get all the metadata
            for entry in exercise_list:
                entry["exercise_status"] = 'uncomplete'

        # Remove the private id
        for entry in exercise_list:
            entry.pop("exercise_id")

        return {
            "course_overview": course_overview,
            # "subscription_status": "active" if relevant_subscription_association else "free",
            "subscription_status": "active",
            "lesson": {
                "lesson_title": relevant_lesson.lesson_title,
                "lesson_description": relevant_lesson.lesson_description,
                "lesson_public_id": relevant_lesson.lesson_public_id,
                "lesson_video_url": create_signed_video_url(relevant_lesson.lesson_video_id, 'lecture') if relevant_lesson.lesson_video_id else None
            },
            "has_completed_video": has_completed_video,
            'trial': relevant_trial if relevant_trial != None else None,
            "exercises": exercise_list
        }

    elif user_type == 'free':
        print("Free user")
        relevant_exercises = db.query(Exercise).filter_by(
            relevant_lesson_id=relevant_lesson.lesson_id).limit(2).all()
        randomize = True
        exercise_list = prepare_exercises(
            relevant_exercises, randomize, db, private_id=True)

        return {
            "course_overview": course_overview,
            "subscription_status": "free",
            "subscription_info": {
                "year_name": relevant_year.year_name,
                "year_public_id": relevant_year.year_public_id,
                "course_title": relevant_course.course_title,
                "course_public_id": relevant_course.course_public_id,
                "lesson_exercise_count": len(relevant_lesson.relevant_exercises),
            },
            "lesson": {
                "lesson_title": relevant_lesson.lesson_title,
                "lesson_description": relevant_lesson.lesson_description,
                "lesson_public_id": relevant_lesson.lesson_public_id,
                "lesson_video_url": create_signed_video_url(relevant_lesson.lesson_video_id, 'lecture') if relevant_lesson.lesson_video_id else None
            },
            "exercises": exercise_list
        }
    else:
        relevant_exercises = db.query(Exercise).filter_by(
            relevant_lesson_id=relevant_lesson.lesson_id).limit(2).all()
        randomize = True
        exercise_list = prepare_exercises(
            relevant_exercises, randomize, db, private_id=True)

        return {
            "course_overview": course_overview,
            "subscription_info": {

                "year_name": relevant_year.year_name,
                "year_public_id": relevant_year.year_public_id,
                "course_title": relevant_course.course_title,
                "course_public_id": relevant_course.course_public_id,
                "lesson_exercise_count": len(relevant_lesson.relevant_exercises),

            },
            "lesson": {
                "lesson_title": relevant_lesson.lesson_title,
                "lesson_description": relevant_lesson.lesson_description,
                "lesson_public_id": relevant_lesson.lesson_public_id,
                "lesson_video_url": create_signed_video_url(relevant_lesson.lesson_video_id, 'lecture') if relevant_lesson.lesson_video_id else None
            },
            "exercises": exercise_list,

            'trial': relevant_trial if relevant_trial != None else None,
            "subscription_status": "invalid"}
