import json
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import  ChildAccount, ChildActivityType, ChildActivity
from datetime import datetime, timedelta

ACTIVITIES_BUFFER_INTERVAL = 2  # Minutes
CALENDAR_DAYS = 180

def generate_activity_calendar(child_acount: ChildAccount, db: Session):
    # Filter the events for the last x months, order in ascending order for example:

    relevant_activity_types = db.query(ChildActivityType).all()
    relevant_activity_types = {
        activity_type.child_activity_type_id: activity_type.child_activity_type_name for activity_type in relevant_activity_types}
    relevant_activitys = db.query(ChildActivity).filter_by(relevant_child_account_id=child_acount.child_account_id).filter(
        ChildActivity.created_at > datetime.now() - timedelta(days=180)).order_by(ChildActivity.created_at).all()
    if not relevant_activitys:
        return []

    relevant_activitys = [{
        "date": activity.child_activity_date,
        "activity_name": relevant_activity_types[activity.child_activity_type_id],
        "activity_day": activity.child_activity_date,
        "activity_hour": activity.child_activity_hour,
        "activity_block": activity.child_activity_block,
        "activity_data": json.loads(activity.child_activity_data)} for activity in relevant_activitys]

    dates = []
    current_weekday = datetime.now().weekday()
    if current_weekday != 6:
        for i in range(6 - current_weekday):
            dates.append(datetime.now() + timedelta(days=i + 1))
    dates.reverse()
    dates = [*dates, *[datetime.now() - timedelta(days=i) for i in range(CALENDAR_DAYS)]]
    dates = [date.date().strftime("%Y-%m-%d") for date in dates]
    # dates = [datetime.now() - timedelta(days=i) for i in range(CALENDAR_DAYS)]
    # Find the weekday of the current date and add entries for the rest of the current week if needed

    finished_calendar = []

    for date in dates:
        # Check if there is a corresponding entry in the relevant_activitys list
        if date in [activity['date'] for activity in relevant_activitys]:
            relevant_activitys_for_date = [
                activity for activity in relevant_activitys if activity['date'] == date]

        # Then for each date we need to group the activitys by type
        # Need
            # 1. Idle time
            idle = [
                activity for activity in relevant_activitys_for_date if activity['activity_name'] == 'idle']
            idle_minutes = len(idle) * ACTIVITIES_BUFFER_INTERVAL
        # 2. Active time
            active = [
                activity for activity in relevant_activitys_for_date if activity['activity_name'] == 'active']
            active_minutes = len(active) * ACTIVITIES_BUFFER_INTERVAL

        # 3. Watching lesson
            watching_lesson = [
                activity for activity in relevant_activitys_for_date if activity['activity_name'] == 'watching_lesson']
            watching_lesson_minutes = len(
                watching_lesson) * ACTIVITIES_BUFFER_INTERVAL

        # 4. Doing exercises
            exercise = [
                activity for activity in relevant_activitys_for_date if activity['activity_name'] == 'exercises_open']
            exercise_minutes = len(exercise) * ACTIVITIES_BUFFER_INTERVAL

            finished_calendar.append({
                "date": date,
                "idle": idle_minutes,
                "active": active_minutes,
                "watching_lesson": watching_lesson_minutes,
                "exercise": exercise_minutes,
                "total": idle_minutes + active_minutes + watching_lesson_minutes + exercise_minutes
            })
        else :
            finished_calendar.append({
                "date": date,
                "idle": 0,
                "active": 0,
                "watching_lesson": 0,
                "exercise": 0,
                "total": 0
            })

    return finished_calendar
