from sqlalchemy.orm import Session
from db.models import Account, Lesson,  Exercise, ChildAccount, ChildAccountExerciseAssociation, ActiveSubscription, SubscriptionOption
from .prepare_exercises import prepare_exercises
from sqlalchemy import case

TEMPORARY_FREE_ACCESS = False  # Set this to False to revert to normal behavior


def prepare_paginated_exercises(lesson_public_id: str, user_type:str, page: int, relevant_acount: ChildAccount | Account, db: Session, pagination_size:int):
        items_per_page = pagination_size
        # Page count starts at 0
        offset = page  * items_per_page

        relevant_lesson = db.query(Lesson).filter_by(
            lesson_public_id=lesson_public_id).first()

        # Checking whether or not the parent account has a subscription to this year
        # To do so we need to first map the current lesson to a year and the child account to a parent account
        relevant_chapter = relevant_lesson.relevant_chapter
        relevant_course = relevant_chapter.relevant_course
        relevant_year = relevant_course.relevant_year
     
        if user_type == "parent":
            parent_account = relevant_acount

        elif user_type == "child":
            # Finding the relevant account and lesson
            relevant_child_account = relevant_acount
            parent_account = relevant_child_account.parent_account

        has_active_subscription = False
        
        if not relevant_lesson.is_free and parent_account != None:
            # Check if the parent account has an active subscription for the relevant year

            has_active_subscription = False
            relevant_subscription = db.query(ActiveSubscription).filter_by(account_id=parent_account.account_id, status='active').join(SubscriptionOption, ActiveSubscription.subscription_option_id == SubscriptionOption.subscription_option_id).filter(SubscriptionOption.year_id == relevant_year.year_id).first()
            if relevant_subscription:
                has_active_subscription = True


        if has_active_subscription or relevant_lesson.is_free or TEMPORARY_FREE_ACCESS:

            # Fetch the exercises associated with this lesson and prepare them for consumption
            # We are fetching one more than we need to check if there are more exercises
            # relevant_exercises = db.query(Exercise).filter_by(relevant_lesson_id=relevant_lesson.lesson_id).limit(items_per_page + 1).offset(offset).all()

            difficulty_order = case(
                (Exercise.exercise_difficulty == 'low', 1),
                (Exercise.exercise_difficulty == 'medium', 2),
                (Exercise.exercise_difficulty == 'hard', 3)
            )

            relevant_exercises = db.query(Exercise).filter_by(
            relevant_lesson_id=relevant_lesson.lesson_id).order_by(
            difficulty_order, Exercise.exercise_id).limit(items_per_page + 1).offset(offset).all()



            # Check if there are more exercises
            has_more = len(relevant_exercises) > items_per_page

            # If we have more exercises, remove the extra one
            if has_more:
                relevant_exercises = relevant_exercises[:-1]

            randomize_answer_options = True
            exercise_list = prepare_exercises(relevant_exercises, randomize_answer_options, db, private_id=True)

            if user_type == "child":
                # Fetch all relevant exercise associations in a single JOIN query
                relevant_child_exercise_associations = (
                    db.query(ChildAccountExerciseAssociation)
                    .filter(
                        ChildAccountExerciseAssociation.relevant_child_account_id == relevant_child_account.child_account_id,
                        ChildAccountExerciseAssociation.relevant_lesson_id == relevant_lesson.lesson_id,
                        ChildAccountExerciseAssociation.relevant_exercise_id.in_([ex["exercise_id"] for ex in exercise_list])
                    )
                    .all()
                )
                
                # Create a lookup dictionary for O(1) access
                exercise_status_map: dict[int, bool] = {
                    assoc.relevant_exercise_id: assoc.association_correctly_answered 
                    for assoc in relevant_child_exercise_associations
                }

                # Update exercise statuses using the lookup dictionary
                for entry in exercise_list:
                    if entry["exercise_id"] in exercise_status_map:
                        entry["exercise_status"] = 'correct' if exercise_status_map[entry["exercise_id"]] else 'incorrect'
                    else:
                        entry["exercise_status"] = 'uncomplete'

            else: 
                for entry in exercise_list:
                    entry["exercise_status"] = 'uncomplete'

            for entry in exercise_list:
                entry.pop("exercise_id")
            return {
                "subscription_status": "active" if has_active_subscription else "free",
                "has_more": has_more,
                "exercises": exercise_list
            }
        else:
            return {"subscription_status": "invalid", "has_more": False, "exercises": []}
