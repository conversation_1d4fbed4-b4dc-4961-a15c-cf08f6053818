from typing import Dict, Any
from datetime import datetime


def format_achievement_message(achievement_type: str, rank: int, total: int) -> str:
    """Generate achievement messages in Luxembourgish based on rank."""
    if rank == 1:
        return f"🏆 Du bass #{1} an {achievement_type}!"
    elif rank <= 3:
        return f"🌟 Du bass ënner den Top 3 an {achievement_type}!"
    elif rank <= 5:
        return f"⭐ Du bass ënner den Top 5 an {achievement_type}!"
    elif rank <= 10:
        return f"👏 Du bass ënner den Top 10 an {achievement_type}!"
    return f"Weider esou!"


def get_improvement_message(accuracy: float, number_exercises: int) -> str:
    """Generate encouraging improvement messages in Luxembourgish based on accuracy."""
    if number_exercises < 10:
        return "All Effort hëlleft dir beim <PERSON>! Weider esou, da gëss du ëmmer besser! 🌱"
    
    if accuracy >= 90:
        return "Du bass super ënnerwee! Maach weider esou! 🌟"
    elif accuracy >= 80:
        return "Super Aarbecht! Du bass um richtege Wee! 💪"
    elif accuracy >= 70:
        return "Du méchs gutt Fortschrëtter! Du bass um gudde Wee! 📈"
    else:
        return "All Effort hëlleft dir beim <PERSON>! <PERSON><PERSON> es<PERSON>, da gëss du ëmmer besser! 🌱"
    

def generate_child_email_content(
    child_name: str,
    individual_stats: Dict[str, Any],
    leaderboard_stats: Dict[str, Any]
) -> Dict[str, str]:
    """
    Generate personalized email content based on student statistics and achievements.
    Returns both subject and body of the email.
    """
    # Extract relevant statistics
    total_exercises = individual_stats["total_exercises"]
    total_correct = individual_stats["total_correct"]
    overall_accuracy = individual_stats["accuracy"]

    # Build achievements section with proper hierarchy
    platform_achievements = []
    
    # Platform-wide achievements (most exercises and highest accuracy)
    for i, entry in enumerate(leaderboard_stats.get("most_exercises", [])):
        if entry["child_id"] == individual_stats["child_id"]:
            platform_achievements.append(format_achievement_message(
                "<i>Gemaachten Exercicer</i>", i + 1, len(leaderboard_stats["most_exercises"])))

    for i, entry in enumerate(leaderboard_stats.get("highest_accuracy", [])):
        if entry["child_id"] == individual_stats["child_id"]:
            platform_achievements.append(format_achievement_message(
                "<i>% korrekt Exercicer</i>", i + 1, len(leaderboard_stats["highest_accuracy"])))

    # Chapter achievements (requires 5+ exercises)
    chapter_achievements = []
    for chapter_id, chapter_data in individual_stats["chapters"].items():
        if chapter_id in leaderboard_stats.get("chapter_champions", {}) and chapter_data["exercises_attempted"] >= 5:
            for i, entry in enumerate(leaderboard_stats["chapter_champions"][chapter_id]):
                if entry["child_id"] == individual_stats["child_id"]:
                    chapter_title = f"dem Kapitel <i>{chapter_data['title']}</i>"
                    chapter_achievements.append(
                        format_achievement_message(chapter_title, i + 1, len(leaderboard_stats["chapter_champions"][chapter_id]))
                    )

    # Lesson achievements (requires 4+ exercises)
    lesson_achievements = []
    for chapter_data in individual_stats["chapters"].values():
        for lesson_id, lesson_data in chapter_data.get("lessons", {}).items():
            if lesson_id in leaderboard_stats.get("lesson_champions", {}) and lesson_data["exercises_attempted"] >= 4:
                for i, entry in enumerate(leaderboard_stats["lesson_champions"][lesson_id]):
                    if entry["child_id"] == individual_stats["child_id"]:
                        lesson_title = f"an der Lektioun <i>{lesson_data['title']}</i>"
                        lesson_achievements.append(
                            format_achievement_message(lesson_title, i + 1, len(leaderboard_stats["lesson_champions"][lesson_id]))
                        )

    # Email subject
    email_subject = f"🎒 Deng Fortschrëtter dës Woch!"

    # Document head with styles
    head_section = """
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style type="text/css">
            /* Reset styles for email clients */
            body, table, td, div, p, a { -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }
            table, td { mso-table-lspace: 0pt; mso-table-rspace: 0pt; }
            img { -ms-interpolation-mode: bicubic; }

            /* Responsive breakpoints */
            @media screen and (max-width: 600px) {
                .container {
                    width: 100% !important;
                    padding: 10px !important;
                }
                .content {
                    width: 100% !important;
                    padding: 15px !important;
                }
                .stats-cell {
                    display: block !important;
                    width: 100% !important;
                    margin-bottom: 15px !important;
                }
                .chapter-header {
                    display: block !important;
                    width: 100% !important;
                    text-align: left !important;
                    margin-bottom: 5px !important;
                }
                .progress-container {
                    margin-top: 10px !important;
                }
                h1 { font-size: 24px !important; }
                h2 { font-size: 20px !important; }
                h3 { font-size: 18px !important; }
            }

            @media screen and (max-width: 600px) {
                .progress-bar-cell {
                    width: 65% !important;
                }
                .accuracy-cell {
                    width: 35% !important;
                    font-size: 13px !important;
                }
            }
        </style>
    """

    # Opening HTML structure
    html_open = f"""
    <!DOCTYPE html>
    <html>
    <head>
        {head_section}
    </head>
    <body style="margin: 0; padding: 0; background-color: #e8fcf1; font-family: Arial, sans-serif;">
        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
            <tr>
                <td align="center" class="container" style="padding: 20px;">
                    <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="600" class="content" 
                        style="background-color: #FFFFFF; border-radius: 10px; border: 1px solid #E5E7EB;">
                        <tr>
                            <td style="padding: 30px;">
    """

    # Header section
    header_section = f"""
        <!-- Header -->
        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
            <tr>
                <td style="color: #1F2937;">
                    <h1 style="margin-top: 0; font-size: 24px;">Hallo {child_name} 👋</h1>
                </td>
            </tr>
            <tr>
                <td style="font-size: 16px; color: #4B5563; padding-top: 10px;">
                    An eng weider spannend Léierwoch as em! Zäit fir deng Leeschtungen a Fortschrëtter ze feieren.
                </td>
            </tr>
        </table>
    """

    # Weekly summary section
    weekly_summary_section = f"""
        <!-- Weekly Summary -->
        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin: 30px 0;">
            <tr>
                <td style="color: #1F2937;">
                    <h2 style="margin-top: 0; font-size: 22px;">📊 Däin Wocheniwwerbléck</h2>
                </td>
            </tr>
            <tr>
                <td>
                    <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin: 15px 0;">
                        <tr>
                            <td class="stats-cell" width="33%" align="center" style="padding: 10px;">
                                <div style="font-size: 24px; font-weight: bold; color: #19e07e;">{total_exercises}</div>
                                <div style="color: #6B7280; font-size: 14px;">✨ Exercicer</div>
                            </td>
                            <td class="stats-cell" width="33%" align="center" style="padding: 10px;">
                                <div style="font-size: 24px; font-weight: bold; color: #19e07e;">{total_correct}</div>
                                <div style="color: #6B7280; font-size: 14px;">🎯 Richteg</div>
                            </td>
                            <td class="stats-cell" width="33%" align="center" style="padding: 10px;">
                                <div style="font-size: 24px; font-weight: bold; color: #19e07e;">{overall_accuracy}%</div>
                                <div style="color: #6B7280; font-size: 14px;">📈 % Korrekt</div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="color: #4B5563; text-align: center; padding-top: 15px;">
                    {get_improvement_message(overall_accuracy, total_exercises)}
                </td>
            </tr>
        </table>
    """

    # Divider element
    divider = """
        <!-- Divider -->
        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
            <tr>
                <td style="height: 1px; background-color: #E5E7EB;"></td>
            </tr>
        </table>
    """

    # Achievements section
    achievements_section = ""
    if platform_achievements:
        # Platform-wide achievements
        achievements_section = f"""
            <!-- Overall Platform Achievements -->
            <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin: 30px 0;">
                <tr>
                    <td style="color: #1F2937;">
                        <h2 style="margin-top: 0; font-size: 22px;">🏆 Top Schüler op LuxEdu!</h2>
                    </td>
                </tr>
                <tr>
                    <td>
                        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
                            {''.join(f'''
                            <tr>
                                <td style="color: #4B5563; padding: 8px 0;">✨ {achievement}</td>
                            </tr>
                            ''' for achievement in platform_achievements)}
                        </table>
                    </td>
                </tr>
            </table>
        """
    # elif chapter_achievements:
    #     # Chapter-specific achievements
    #     achievements_section = f"""
    #         <!-- Chapter Achievements -->
    #         <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin: 30px 0;">
    #             <tr>
    #                 <td style="color: #1F2937;">
    #                     <h2 style="margin-top: 0; font-size: 22px;">📚 Top Schüler an de Kapitelen!</h2>
    #                 </td>
    #             </tr>
    #             <tr>
    #                 <td>
    #                     <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
    #                         {''.join(f'''
    #                         <tr>
    #                             <td style="color: #4B5563; padding: 8px 0;">{achievement}</td>
    #                         </tr>
    #                         ''' for achievement in chapter_achievements)}
    #                     </table>
    #                 </td>
    #             </tr>
    #         </table>
    #     """
    # elif lesson_achievements:
    #     # Lesson-specific achievements
    #     achievements_section = f"""
    #         <!-- Lesson Achievements -->
    #         <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin: 30px 0;">
    #             <tr>
    #                 <td style="color: #1F2937;">
    #                     <h2 style="margin-top: 0; font-size: 22px;">📖 Top Schüler an de Léieren!</h2>
    #                 </td>
    #             </tr>
    #             <tr>
    #                 <td>
    #                     <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
    #                         {''.join(f'''
    #                         <tr>
    #                             <td style="color: #4B5563; padding: 8px 0;">{achievement}</td>
    #                         </tr>
    #                         ''' for achievement in lesson_achievements)}
    #                     </table>
    #                 </td>
    #             </tr>
    #         </table>
    #     """

    # Lesson progress section header
    lesson_progress_header = """
        <!-- Lesson Progress -->
        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin: 30px 0;">
            <tr>
                <td style="color: #1F2937;">
                    <h2 style="margin-top: 0; font-size: 22px;">📚 Léier-Fortschrëtt</h2>
                </td>
            </tr>
    """

    # Generate chapter and lesson content
    chapter_lesson_content = []
    for chapter_data in individual_stats["chapters"].values():
        # Chapter header
        chapter_header = f"""
            <!-- Chapter Header -->
            <tr>
                <td style="padding-top: 25px; padding-bottom: 10px;">
                    <h3 style="margin: 0; font-size: 18px; color: #1F2937; font-weight: 600;">{chapter_data["title"]}</h3>
                </td>
            </tr>
        """
        
        # Generate lesson rows for this chapter
        lesson_rows = []
        for lesson_id, lesson_data in chapter_data["lessons"].items():
            lesson_row = f"""
            <tr>
                <td style="padding-top: 12px;">
                    <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
                        <tr>
                            <td class="lesson-header">
                                <div style="font-size: 15px; color: #4B5563;">{lesson_data["title"]}</div>
                            </td>
                            <td class="lesson-header" align="right" style="color: #6B7280; font-size: 14px;">
                                {lesson_data["exercises_attempted"]} {"Exercice" if lesson_data["exercises_attempted"] == 1 else "Exercicer"} ofgeschloss
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" class="progress-container" style="padding-top: 6px;">
                                <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
                                    <tr>
                                        <td class="progress-bar-cell" width="80%" style="background-color: #e8fcf1; height: 8px; border-radius: 4px;">
                                            <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="{lesson_data["accuracy"]}%">
                                                <tr>
                                                    <td style="background-color: #19e07e; height: 8px; border-radius: 4px;"></td>
                                                </tr>
                                            </table>
                                        </td>
                                        <td class="accuracy-cell" width="20%" align="right" style="color: #1F2937; font-weight: bold; padding-left: 10px; white-space: nowrap;">
                                            {lesson_data["accuracy"]}% korrekt
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            """
            lesson_rows.append(lesson_row)
        
        # Combine chapter header with its lesson rows
        chapter_content = chapter_header + ''.join(lesson_rows)
        chapter_lesson_content.append(chapter_content)

    # Lesson progress closing
    lesson_progress_closing = """
        </table>
    """

    # Combine all parts of the lesson progress section
    lesson_progress_section = (
        lesson_progress_header +
        ''.join(chapter_lesson_content) +
        lesson_progress_closing
    )

    # Footer section
    footer_section = """
        <!-- Keep Going Section -->
        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin: 30px 0;">
            <tr>
                <td style="color: #1F2937;">
                    <h2 style="margin-top: 0; font-size: 22px;">🎯 Weider esou!</h2>
                </td>
            </tr>
            <tr>
                <td style="color: #4B5563; font-size: 16px;">
                    Denk drun: all Exercice deens du mëss hëlleft dir an der nächster Prüfung! 
                    Mir si gespaant op deng Fortschrëtter nächst Woch!
                </td>
            </tr>
        </table>

        <!-- Contact & Unsubscribe -->
        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin: 20px 0;">
            <tr>
                <td style="color: #6B7280; font-size: 14px; padding-bottom: 10px;">
                    Hues du Froen? Schreif <NAME_EMAIL>
                </td>
            </tr>
            <tr>
                <td style="color: #6B7280; font-size: 14px;">
                    Wanns du dës wöchentlech Rapporten net méi wëlls kréien, äntwert einfach op dës E-Mail.
                </td>
            </tr>
        </table>

        <!-- Signature -->
        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin-top: 30px;">
            <tr>
                <td style="color: #6B7280; font-size: 14px;">
                    Bravo an weider esou!<br>
                    Däin LuxEdu Team
                </td>
            </tr>
        </table>
    """

    # Closing HTML structure
    html_close = """
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
    </html>
    """

    # Combine all sections into the final email
    email_body = f"""
    {html_open}
    {header_section}
    {weekly_summary_section}
    {divider}
    {achievements_section if platform_achievements or chapter_achievements or lesson_achievements else ""}
    {divider if platform_achievements or chapter_achievements or lesson_achievements else ""}
    {lesson_progress_section}
    {divider}
    {footer_section}
    {html_close}
    """

    return {
        "subject": email_subject,
        "body": email_body
    }
