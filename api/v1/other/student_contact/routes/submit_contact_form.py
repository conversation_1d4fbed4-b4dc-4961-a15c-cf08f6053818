from api.v1.other.contact.schemas.request import StudentContactFormRequest
from api.v1.other.contact.schemas.response import StudentContactFormResponse
from fastapi import APIRouter, Depends, Request
# from sqlalchemy.orm import Session # Removed
# from db.database import get_db # Removed
# from db.models import Account # Removed
# from loguru import logger # Removed, service handles logging for its operations
# from fastapi import BackgroundTasks # Removed
from dependencies.auth_dependencies.auth_dependency import AuthDependency
from dependencies.auth_dependencies.base_auth_dependency import AuthDependencyResponse
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError, BadRequestError, ServiceError # BadRequestError kept if needed for other checks
from api.v1.common.schemas import AppErrorCode
# from core.config.settings import settings # Removed
from services.student_contact_service import StudentContactService


router = APIRouter()

@router.post('/submit-contact-form', response_model=StudentContactFormResponse)
def submit_student_contact_form(
    contact_form: StudentContactFormRequest,
    request: Request,
    auth: AuthDependencyResponse = Depends(AuthDependency()),
    service: StudentContactService = Depends()
):
    account_public_id = auth.account_public_id
    if account_public_id:
        try:
            # Pydantic model StudentContactFormRequest already validates email, name, message.
            # No need for: if not contact_form.email...

            service.process_student_contact_form(contact_form)
            return {"message": "Success"}

        except ServiceError:
            # Re-raise service errors directly as they are already structured
            raise
        except Exception as e:
            # Log the exception if a logger is available and configured for the route level, otherwise the service log should suffice.
            # For now, relying on service-level logging for errors within service.process_student_contact_form.
            # This generic catch is for unexpected errors in the route itself or unhandled by the service.
            raise ServiceError(
                message="Error processing student contact form", 
                log_message=f"Unexpected error in student contact form submission route: {str(e)}",
                error_code=AppErrorCode.SERVICE_ERROR,
                original_exception=e
            )
    else:
        raise AuthenticationError(
            message="Not authorized!",
            log_message="No account_public_id in token for student contact form",
            error_code=AppErrorCode.AUTHENTICATION_REQUIRED
        )
