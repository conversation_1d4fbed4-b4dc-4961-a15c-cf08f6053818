import boto3
from ..schemas.request import HelpFormRequest
from ..schemas.response import HelpFormResponse
from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session
from db.database import get_db
from db.models import Account
from loguru import logger
from fastapi import BackgroundTasks
from dependencies.auth_dependencies.auth_dependency import AuthDependency
from dependencies.auth_dependencies.base_auth_dependency import AuthDependencyResponse
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError, BadRequestError, ServiceError
from api.v1.common.schemas import AppErrorCode
from sqlalchemy import select
from core.config.settings import settings

router = APIRouter()
base_url = "/api/contact"

# Initialize AWS SES client
# Initialize AWS SES client with settings
ses_client = boto3.client(
    'ses',
    region_name=settings.AWS_SES_REGION,
    aws_access_key_id=settings.AWS_ACCESS_KEY,
    aws_secret_access_key=settings.AWS_SECRET_KEY
)

# Get the appropriate email based on environment
relevant_email = settings.CONTACT_FORM_EMAILS.get(
    settings.ENVIRONMENT, 
    settings.CONTACT_FORM_EMAILS['dev']  # Default to dev email if environment not found
)

def send_help_email_task(message):
    try:
        # Ignore in flake8 
        # flake8: noqa
        response = ses_client.send_email(
            Source=relevant_email,
            Destination={'ToAddresses': [settings.ADMIN_EMAIL]},
            Message={
                'Body': {
                    'Text': {
                        'Data': "New help form message: \n" + message,
                        'Charset': 'UTF-8'
                    },
                    'Html': {
                        'Data': "New help form message: \n" + message,
                        'Charset': 'UTF-8'
                    }
                },
                'Subject': {
                    'Data':  "New help form message",
                    'Charset': 'UTF-8'
                }
            }
        )
    except Exception as e:
        logger.error(f"Error sending help email: {str(e)}")
        raise ServiceError(
            message="Error sending email",
            log_message=f"Error sending help form email: {str(e)}",
            error_code=AppErrorCode.EXTERNAL_SERVICE_FAILURE,
            original_exception=e
        )



@router.post('/submit-help-form', status_code=200, response_model=HelpFormResponse)
def submit_help_form(
    contact_form: HelpFormRequest,
    request: Request,
    db: Session = Depends(get_db),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    auth: AuthDependencyResponse = Depends(AuthDependency())
):


    parent_public_id = auth.account_public_id
    if parent_public_id:
        stmt = select(Account).where(Account.public_id == parent_public_id)
        relevant_account = db.execute(stmt).scalar_one_or_none()

        if relevant_account:
            email = relevant_account.email
            try:
                if not email:
                    raise BadRequestError(
                        message="No email found",
                        log_message=f"Account {parent_public_id} has no email address",
                        error_code=AppErrorCode.BAD_REQUEST
                    )
                message = f"Email: {email} \nMessage: {contact_form.message}"
                background_tasks.add_task(send_help_email_task, message)
                return {"message": "Success"}

            except ServiceError:
                # Re-raise service errors directly
                raise
            except Exception as e:
                raise ServiceError(
                    message="Error processing help form",
                    log_message=f"Unexpected error in help form submission: {str(e)}",
                    error_code=AppErrorCode.SERVICE_ERROR,
                    original_exception=e
                )
        else:
            raise AuthenticationError(
                message="Not authorized!",
                log_message=f"No parent account found for cognito ID {parent_public_id}",
                error_code=AppErrorCode.AUTHENTICATION_REQUIRED
            )
    else:
        raise AuthenticationError(
            message="Not authorized!",
            log_message="No parent_account_public_id in token",
            error_code=AppErrorCode.AUTHENTICATION_REQUIRED
        )
