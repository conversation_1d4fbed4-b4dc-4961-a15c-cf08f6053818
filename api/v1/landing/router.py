from fastapi import APIRouter
from .routes import get_posts, get_post, get_slugs, get_chapter_summary, get_year_summary, get_recent_signups

router = APIRouter()

# Include routes from all the files in this directory
router.include_router(get_posts.router)
router.include_router(get_post.router)
router.include_router(get_slugs.router)
router.include_router(get_chapter_summary.router)
router.include_router(get_year_summary.router)
router.include_router(get_recent_signups.router)
