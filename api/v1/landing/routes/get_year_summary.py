from ..models.response import GetChapterSummaryResponse
from fastapi import APIRouter, Depends
from db.database import get_db
from sqlalchemy.orm import Session
from core.exception_handling.exceptions.custom_exceptions import ServiceError
from api.v1.common.schemas import AppErrorCode
from db.models import Account, YearSummaryDownload, ChildAccount
import boto3
from botocore.exceptions import ClientError
import os
from .utils.email_sender import send_email
from sqlalchemy import select

router = APIRouter()
base_url = "/api/summaries"

CLOUDFLARE_R2_ENDPOINT = os.environ.get("CLOUDFLARE_R2_ENDPOINT")
CLOUDFLARE_R2_ACCESS_KEY_ID = os.environ.get("CLOUDFLARE_R2_ACCESS_KEY_ID")
CLOUDFLARE_R2_SECRET_ACCESS_KEY = os.environ.get(
    "CLOUDFLARE_R2_SECRET_ACCESS_KEY")
CLOUDFLARE_R2_REGION_NAME = os.environ.get("CLOUDFLARE_R2_REGION_NAME")

CLOUDFLARE_R2_APP_BUCKET = os.environ.get("CLOUDFLARE_R2_APP_BUCKET")

BUCKET_BASE_PATH = "summary_materials/years/"


def create_presigned_url(year_id: str, expiry=86400):
    bucket_name = CLOUDFLARE_R2_APP_BUCKET

    client = boto3.client(
        "s3",
        region_name="auto",
        endpoint_url=CLOUDFLARE_R2_ENDPOINT,
        aws_access_key_id=CLOUDFLARE_R2_ACCESS_KEY_ID,
        aws_secret_access_key=CLOUDFLARE_R2_SECRET_ACCESS_KEY,
    )

    year_id_mapping = {
        "year_05": "5e",
        "year_06": "6e",
        "year_07": "7e"
    }
    file_name = "LuxEdu Fiche de Révision - " + year_id_mapping.get(year_id)


    try:
        response = client.generate_presigned_url('get_object', Params={
                                                 'Bucket': bucket_name, 
                                                 'Key': BUCKET_BASE_PATH + file_name + ".pdf"}, 
                                                 ExpiresIn=expiry)
        return response

    except ClientError as e:
        print(e)

    except Exception as e:
        print(e)
        return ""


@router.get(base_url + "/get-year-summary", response_model=GetChapterSummaryResponse)
def get_year_summary(year_id: str, email: str, language: str, db: Session = Depends(get_db)):
    # Check if the account exists

    stmt = select(Account).where(Account.email == email)
    account = db.execute(stmt).scalar_one_or_none()

    stmt = select(ChildAccount).where(ChildAccount.email == email)
    child_account = db.execute(stmt).scalar_one_or_none()
    
    new_download = YearSummaryDownload(
        account_id=account.id if account else None,
        child_account_id=child_account.id if child_account else None,
        downloaded_file=year_id,
        email=email
    )
    db.add(new_download)
    db.commit()

    presigned_url = create_presigned_url(year_id=year_id)
    if not presigned_url:
        raise ServiceError(
            message="Failed to generate year summary download link",
            log_message=f"Failed to create presigned URL for year summary {year_id}",
            error_code=AppErrorCode.EXTERNAL_SERVICE_FAILURE
        )

    try:
        send_email(email, presigned_url, language)
    except Exception as e:
        # Log error but continue - email is non-critical
        print(f"Failed to send email to {email}: {e}")

    return GetChapterSummaryResponse(
        message="Year summary generated successfully",
        download_url=presigned_url
    )
