from ..models.response import GetRecentSignupsResponse
from fastapi import APIRouter, Depends, Request
from db.database import get_db
from sqlalchemy.orm import Session
from datetime import datetime, timedelta, UTC
from core.exception_handling.exceptions.custom_exceptions import PermissionDeniedError
from api.v1.common.schemas import AppErrorCode
from urllib.parse import urlparse
from itertools import product
import json
from loguru import logger
from db.models import SignUp
from sqlalchemy import select

router = APIRouter()
base_url = "/api/landing"

RELEVANT_YEARS = ["7", "6", "5", "4"]
RELEVANT_SYSTEMS = ["general", "classique"]
ALLOWED_DOMAINS = [
    "luxedu.lu"
]


def is_allowed_domain(url: str) -> bool:
    if not url:
        return False

    try:
        parsed = urlparse(url)
        domain = parsed.netloc or parsed.path  # fallback to path if netloc is empty

        # Remove 'www.' if present for consistent comparison
        domain = domain.replace('www.', '')

        # Check if any allowed domain matches
        return any(allowed_domain.replace('www.', '') in domain
                   for allowed_domain in ALLOWED_DOMAINS)
    except Exception:
        return False


@router.get(base_url + "/get-recent-signups", response_model=GetRecentSignupsResponse)
def get_recent_signups(
    request: Request,
    db: Session = Depends(get_db)
):
    # Check both referer and origin headers
    referer = request.headers.get("referer")
    origin = request.headers.get("origin")

    # If neither referer nor origin is present
    if not referer and not origin:
        raise PermissionDeniedError(
            message="Direct API calls not allowed",
            log_message="Request to get-recent-signups has no referer or origin header",
            error_code=AppErrorCode.PERMISSION_DENIED
        )

    # Check if either referer or origin is from allowed domain
    if not (is_allowed_domain(referer) or is_allowed_domain(origin)):
        raise PermissionDeniedError(
            message="Access denied",
            log_message=f"Unauthorized domain attempted to access signups API. Referer: {referer}, Origin: {origin}",
            error_code=AppErrorCode.PERMISSION_DENIED
        )

    # Calculate date 7 days ago
    seven_days_ago = datetime.now(UTC) - timedelta(days=7)

    # Query signups from last 7 days, limiting to 15 results
    stmt = select(SignUp).filter(
        SignUp.created_at >= seven_days_ago
    ).order_by(SignUp.created_at.desc()).limit(12)
    recent_signups = db.execute(stmt).all()

    processed_signups = []
    for signup in recent_signups:
        try:
            # logger.info(f"Processing signup with raw data - 
            # year_options: {signup.signup_year_options}, 
            # system_options: {signup.signup_system_options}")
            
            # Parse JSON strings into Python lists
            try:
                year_options = json.loads(signup.signup_year_options) if signup.signup_year_options else []
                system_options = json.loads(signup.signup_system_options) if signup.signup_system_options else []
            except json.JSONDecodeError as e:
                logger.error(f"JSON parsing error for signup {signup.sign_up_id}: {str(e)}")
                continue
            
            # logger.info(f"Parsed JSON options - years: {year_options}, systems: {system_options}")

            # Check if any of the selected years are in our relevant years
            relevant_years = [
                year for year in year_options if year in RELEVANT_YEARS]
            # Check if any of the selected systems are in our relevant systems
            relevant_systems = [
                system for system in system_options if system in RELEVANT_SYSTEMS]
            
            # logger.info(f"Filtered relevant options - years: {relevant_years}, systems: {relevant_systems}")

            # Only include signup if it has relevant years and systems
            if relevant_years and relevant_systems:
                # Generate all possible combinations of years and systems
                combinations = list(product(relevant_years, relevant_systems))
                # logger.info(f"Generated combinations: {combinations}")
                
                # Add an entry for each combination
                for year, system in combinations:
                    entry = {
                        "year": year,
                        "system": system,
                        "created_at": signup.created_at.strftime('%Y-%m-%d')
                    }
                    # logger.info(f"Adding entry: {entry}")
                    processed_signups.append(entry)

        except Exception as e:
            logger.error(f"Error processing signup {signup.sign_up_id}: {str(e)}", exc_info=True)
            continue

    return GetRecentSignupsResponse(signups=processed_signups)
