from ..models.response import GetSlugsResponse
from fastapi import APIRouter, Depends
from db.database import get_db
from sqlalchemy.orm import Session
from core.exception_handling.exceptions.custom_exceptions import ServiceError
from api.v1.common.schemas import AppErrorCode

from db.models import BlogPost

router = APIRouter()
base_url = "/api/blog"


@router.get("/get-slugs", response_model=GetSlugsResponse)
def get_slugs(language: str, db: Session = Depends(get_db),):
    try:

        relevant_posts = db.query(BlogPost).filter_by(blog_post_language=language).all()

        slugs = []
        for post in relevant_posts:
            slugs.append(post.blog_post_slug)

        return {"slugs": slugs}

    except Exception as e:
        raise ServiceError(
            message="Failed to retrieve blog post slugs",
            log_message=f"Error when fetching blog post slugs for language '{language}': {str(e)}",
            error_code=AppErrorCode.SERVICE_ERROR,
            original_exception=e
        )
