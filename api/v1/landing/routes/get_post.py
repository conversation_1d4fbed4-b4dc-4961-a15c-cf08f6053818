from ..models.response import GetPostResponse
from fastapi import APIRouter, Depends
from db.database import get_db
from sqlalchemy.orm import Session
from sqlalchemy import select
from db.models import BlogPost
from core.exception_handling.exceptions.custom_exceptions import NotFoundError
from api.v1.common.schemas import AppErrorCode

router = APIRouter()
base_url = "/api/blog"

IMAGE_BASE_URL = "https://pub-e2b70868e474414392f797cc1f284b26.r2.dev/blog%2Fimages%2F"


@router.get(base_url + "/get-post", response_model=GetPostResponse)
def get_post(slug: str, db: Session = Depends(get_db)):
    stmt = select(BlogPost).where(BlogPost.blog_post_slug == slug)
    relevant_post = db.execute(stmt).scalar_one_or_none()

    if not relevant_post:
        raise NotFoundError(
            message="Post not found",
            log_message=f"Blog post with slug '{slug}' was not found",
            error_code=AppErrorCode.RESOURCE_NOT_FOUND,
            entity_name="BlogPost",
            identifier=slug
        )

    return GetPostResponse(
        image_url=f"{IMAGE_BASE_URL + relevant_post.blog_post_image_name}" 
        if relevant_post.blog_post_image_name else None,
        public_id=relevant_post.blog_post_public_id,
        title=relevant_post.blog_post_title,
        summary=relevant_post.blog_post_summary,
        content=relevant_post.blog_post_content,
        language=relevant_post.blog_post_language,
        slug=relevant_post.blog_post_slug,
        reading_time=relevant_post.blog_post_reading_time,
        created_at=relevant_post.created_at.strftime('%Y-%m-%d %H:%M:%S'),
        last_updated=relevant_post.last_updated.strftime('%Y-%m-%d %H:%M:%S')
    )
