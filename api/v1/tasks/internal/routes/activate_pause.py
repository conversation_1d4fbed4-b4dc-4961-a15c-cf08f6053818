from fastapi import APIRouter, Depends, Body, HTTPException, status, Request
from sqlalchemy.orm import Session as SyncSession
from sqlalchemy import select
from pydantic import BaseModel
from db.database import get_db
from db.models import SubscriptionPause
from db.models.subscription import SubscriptionPauseStatus
from core.config.settings import settings
from api.v1.app.parent.subscription.services.activate_pause_service import activate_pause
from loguru import logger
from qstash import Receiver

router = APIRouter()

class ActivatePauseRequest(BaseModel):
    """Request model for QStash pause activation callback"""
    active_subscription_id: int
    subscription_pause_public_id: str
    action: str
    # Additional fields for pre-pause tasks
    actual_pause_time: str = None  # ISO format datetime for when actual pause should happen
    is_pre_pause_task: bool = False

async def verify_qstash_signature(request: Request):
    """Verify QStash signature for security using QStash library"""
    if not settings.QSTASH_CURRENT_SIGNING_KEY:
        logger.warning("QStash signature verification skipped (no key configured).")
        return
    
    try:
        # Use QStash library's built-in signature verification
        receiver = Receiver(
            current_signing_key=settings.QSTASH_CURRENT_SIGNING_KEY,
            next_signing_key=settings.QSTASH_NEXT_SIGNING_KEY
        )
        
        signature = request.headers.get("Upstash-Signature")
        body_bytes = await request.body()
        
        # Verify the signature
        receiver.verify(
            signature=signature,
            body=body_bytes.decode()  # QStash library expects string body
        )
        
        logger.debug("QStash signature verified successfully.")
        
    except Exception as e:
        logger.error(f"QStash signature verification failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, 
            detail="Invalid signature"
        )

@router.post("/activate-pause")
async def activate_pause_endpoint(
    request: Request,
    payload: ActivatePauseRequest = Body(...),
    db: SyncSession = Depends(get_db),
    _: None = Depends(verify_qstash_signature)
):
    """
    Internal endpoint called by QStash to activate scheduled pauses.
    This endpoint is idempotent - multiple calls with same data are safe.
    """
    logger.info(
        f"Received QStash activation request for subscription_id={payload.active_subscription_id}, "
        f"pause_public_id={payload.subscription_pause_public_id}, action={payload.action}"
    )
    
    if payload.action not in ["activate_pause", "schedule_pause"]:
        logger.error(f"Invalid action received: {payload.action}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid action"
        )
    
    # Check if already processed (idempotency check)
    pause = db.execute(
        select(SubscriptionPause)
        .where(
            SubscriptionPause.public_id == payload.subscription_pause_public_id,
            SubscriptionPause.active_subscription_id == payload.active_subscription_id
        )
    ).scalar_one_or_none()
    
    if not pause:
        logger.error(f"Pause record not found: {payload.subscription_pause_public_id}")
        # Return success to prevent QStash retries for non-existent pause
        return {"status": "success", "message": "Pause record not found"}
    
    if pause.status == SubscriptionPauseStatus.PAUSED:
        logger.info(f"Pause {payload.subscription_pause_public_id} already activated")
        # Idempotent - return success
        return {"status": "success", "message": "Pause already activated"}
    
    try:
        if payload.action == "activate_pause":
            # Regular pause activation
            success = activate_pause(
                db=db,
                active_subscription_id=payload.active_subscription_id,
                subscription_pause_public_id=payload.subscription_pause_public_id
            )
            
            if success:
                logger.info(f"Successfully activated pause {payload.subscription_pause_public_id}")
                return {"status": "success", "message": "Pause activated successfully"}
            else:
                logger.error(f"Failed to activate pause {payload.subscription_pause_public_id}")
                # Return 500 to trigger QStash retry
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to activate pause"
                )
                
        elif payload.action == "schedule_pause":
            # Pre-pause task: schedule the actual pause
            from api.v1.app.parent.subscription.services.pause_subscription_service import schedule_pause_activation
            from datetime import datetime, UTC
            from dateutil import parser as date_parser
            
            if not payload.actual_pause_time:
                logger.error("actual_pause_time is required for schedule_pause action")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="actual_pause_time is required for schedule_pause action"
                )
            
            # Parse the actual pause time
            actual_pause_datetime = date_parser.parse(payload.actual_pause_time)
            
            logger.info(
                f"Pre-pause task triggered. Scheduling actual pause for {actual_pause_datetime}"
            )
            
            # Schedule the actual pause (should be ≤6 days away now)
            qstash_message_id = schedule_pause_activation(
                active_subscription_id=payload.active_subscription_id,
                subscription_pause_id=payload.subscription_pause_public_id,
                activation_time=actual_pause_datetime
            )
            
            if qstash_message_id:
                logger.info(
                    f"Successfully scheduled actual pause for {actual_pause_datetime}. "
                    f"QStash message ID: {qstash_message_id}"
                )
                return {
                    "status": "success", 
                    "message": f"Actual pause scheduled for {actual_pause_datetime}",
                    "qstash_message_id": qstash_message_id
                }
            else:
                logger.error(f"Failed to schedule actual pause for {actual_pause_datetime}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to schedule actual pause"
                )
            
    except Exception as e:
        logger.error(
            f"Error activating pause {payload.subscription_pause_public_id}: {str(e)}",
            exc_info=True
        )
        # Return 500 to trigger QStash retry
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )