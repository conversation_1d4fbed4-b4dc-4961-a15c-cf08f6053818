# Editorial API Contract

This document describes the API contract for the Editorial system, including all available endpoints, request/response structures, and authentication requirements.

## Overview

The Editorial API provides comprehensive endpoints for managing the editorial workflow, with 37 total endpoints organized into three main sections:

- **Authentication** (`/api/v1/editorial/auth`): Login, refresh, logout, and session validation functionality (4 endpoints)
- **Editor** (`/api/v1/editorial/editor`): Current editor info, assigned learning nodes, draft management and editing workflows for editors (12 endpoints)
- **Admin** (`/api/v1/editorial/admin`): Administrative functions including publishing, editor management, metrics, content hierarchy, and cleanup operations (21 endpoints)

## Authentication

All endpoints except login and refresh require authentication via the `x-editor-token` header containing a valid JW<PERSON> token.

### Token Structure
```json
{
  "editor_id": 123,
  "email": "<EMAIL>",
  "role": "EDITOR|ADMIN",
  "scopes": ["SUBJECT:math", "GRADE:5"],
  "exp": 1234567890
}
```

## API Endpoints

### Authentication Endpoints

#### POST `/api/v1/editorial/auth/login`
Authenticate an editor or admin user.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

**Response (200 OK):**
```json
{
  "access_token": "eyJ...",
  "refresh_token": "eyJ...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "editor": {
    "id": 123,
    "email": "<EMAIL>",
    "full_name": "John Doe",
    "role": "EDITOR",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z"
  },
  "scopes": [
    {
      "scope_type": "SUBJECT",
      "scope_value": "math"
    },
    {
      "scope_type": "GRADE",
      "scope_value": "5"
    }
  ]
}
```

#### POST `/api/v1/editorial/auth/refresh`
Refresh an access token using a refresh token.

**Request Body:**
```json
{
  "refresh_token": "eyJ..."
}
```

**Response (200 OK):**
```json
{
  "access_token": "eyJ...",
  "token_type": "Bearer",
  "expires_in": 3600
}
```

#### POST `/api/v1/editorial/auth/logout`
Logout the current editor (client-side token invalidation).

**Headers Required:** `x-editor-token`

**Response (200 OK):**
```json
{
  "message": "Successfully logged out",
  "success": true
}
```

#### POST `/api/v1/editorial/auth/validate-session`
Validate the current editor session and retrieve user information.

**Headers Required:** `x-editor-token`

**Response (200 OK):**
```json
{
  "is_valid": true,
  "editor": {
    "public_id": "ed-123",
    "email": "<EMAIL>",
    "role": "EDITOR",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z"
  },
  "scopes": [
    {
      "subject_id": "math-5",
      "subject_name": "Mathematics Grade 5",
      "chapter_id": null,
      "chapter_title": null,
      "learning_node_id": null,
      "learning_node_title": null
    }
  ]
}
```

### Editor Endpoints

#### GET `/api/v1/editorial/editor/me`
Get information about the currently authenticated editor.

**Headers Required:** `x-editor-token`

**Response (200 OK):**
```json
{
  "id": 123,
  "public_id": "ed-123",
  "email": "<EMAIL>",
  "role": "EDITOR",
  "is_active": true,
  "created_at": "2024-01-01T00:00:00Z",
  "scopes": [
    {
      "id": 1,
      "type": "SUBJECT",
      "name": "Mathematics Grade 5",
      "public_id": "math-5",
      "subject_name": "Mathematics Grade 5",
      "subject_id": "math-5",
      "chapter_name": null,
      "chapter_id": null
    },
    {
      "id": 2,
      "type": "CHAPTER",
      "name": "Fractions",
      "public_id": "ch-123",
      "subject_name": "Mathematics Grade 5",
      "subject_id": "math-5",
      "chapter_name": "Fractions",
      "chapter_id": "ch-123"
    }
  ]
}
```

#### GET `/api/v1/editorial/editor/learning-nodes`
Get all learning nodes assigned to the current editor based on their scopes.

**Headers Required:** `x-editor-token`

**Note:** Each learning node now includes draft count breakdown:
- `draft_count`: Total number of draft exercises (NEW + IN_REVIEW)
- `new_drafts`: Number of NEW draft exercises (newly created, awaiting review)
- `in_review_drafts`: Number of IN_REVIEW draft exercises (currently being reviewed)

**Response (200 OK):**
```json
{
  "total_nodes": 25,
  "subjects": [
    {
      "id": "math-5",
      "name": "Mathematics Grade 5",
      "order": 1,
      "icon": "calculator",
      "chapters": [
        {
          "id": "ch-123",
          "title": "Fractions",
          "order": 1,
          "learning_nodes": [
            {
              "id": "ln-456",
              "title": "Introduction to Fractions",
              "order": 1,
              "exercises_count": 10,
              "draft_count": 2,
              "new_drafts": 1,
              "in_review_drafts": 1
            },
            {
              "id": "ln-457",
              "title": "Adding Fractions",
              "order": 2,
              "exercises_count": 15,
              "draft_count": 0,
              "new_drafts": 0,
              "in_review_drafts": 0
            }
          ]
        },
        {
          "id": "ch-124",
          "title": "Decimals",
          "order": 2,
          "learning_nodes": [
            {
              "id": "ln-458",
              "title": "Introduction to Decimals",
              "order": 1,
              "exercises_count": 12,
              "draft_count": 3,
              "new_drafts": 2,
              "in_review_drafts": 1
            }
          ]
        }
      ]
    }
  ]
}
```

#### GET `/api/v1/editorial/editor/drafts`
Get a paginated list of draft exercises available to the current editor.

**Headers Required:** `x-editor-token`

**Query Parameters:**
- `status` (optional): Filter by draft status (NEW, IN_REVIEW, ACCEPTED_BY_EDITOR, REJECTED_BY_EDITOR, REJECTED_BY_ADMIN, PUBLISHED)
- `subject_id` (optional): Filter by subject public ID
- `chapter_id` (optional): Filter by chapter public ID
- `page` (optional, default: 1): Page number
- `page_size` (optional, default: 50, max: 100): Items per page

**Response (200 OK):**
```json
{
  "drafts": [
    {
      "id": 1,
      "exercise_type": "MULTIPLE_CHOICE",
      "status": "NEW",
      "subject": {
        "id": "math-5",
        "name": "Mathematics Grade 5"
      },
      "chapter": {
        "id": "ch-123",
        "name": "Fractions"
      },
      "assigned_editor": null,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 50,
    "total_items": 100,
    "total_pages": 2
  }
}
```

#### GET `/api/v1/editorial/editor/drafts/{draft_id}`
Get detailed information about a specific draft.

**Headers Required:** `x-editor-token`

**Path Parameters:**
- `draft_id` (integer): The ID of the draft

**Response (200 OK):**
```json
{
  "draft": {
    "id": 1,
    "exercise_type": "MULTIPLE_CHOICE",
    "status": "IN_REVIEW",
    "data_json": {
      "question": "What is 2 + 2?",
      "options": ["3", "4", "5", "6"],
      "correct_answer": 1
    },
    "solution_json": {
      "explanation": "2 + 2 equals 4",
      "steps": ["Add 2 to 2", "The result is 4"]
    },
    "subject": {
      "id": "math-5",
      "name": "Mathematics Grade 5"
    },
    "chapter": {
      "id": "ch-123",
      "name": "Basic Arithmetic"
    },
    "assigned_editor": {
      "id": 123,
      "email": "<EMAIL>",
      "full_name": "John Doe"
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-02T00:00:00Z",
    "assigned_at": "2024-01-02T00:00:00Z"
  },
  "media": [
    {
      "id": 1,
      "media_type": "IMAGE",
      "file_name": "diagram.png",
      "file_size": 12345,
      "mime_type": "image/png",
      "url": "https://cdn.example.com/media/diagram.png",
      "metadata": {
        "width": 800,
        "height": 600
      },
      "created_at": "2024-01-02T00:00:00Z"
    }
  ],
  "audit_history": [
    {
      "id": 1,
      "action": "CLAIMED",
      "editor": {
        "id": 123,
        "email": "<EMAIL>",
        "full_name": "John Doe"
      },
      "timestamp": "2024-01-02T00:00:00Z",
      "details": null
    }
  ]
}
```

#### POST `/api/v1/editorial/editor/drafts/{draft_id}/claim`
Claim a draft for editing.

**Headers Required:** `x-editor-token`

**Path Parameters:**
- `draft_id` (integer): The ID of the draft to claim

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Draft successfully claimed",
  "draft": {
    "id": 1,
    "status": "IN_REVIEW",
    "assigned_editor": {
      "id": 123,
      "email": "<EMAIL>",
      "full_name": "John Doe"
    },
    "assigned_at": "2024-01-02T10:00:00Z"
  }
}
```

#### PATCH `/api/v1/editorial/editor/drafts/{draft_id}`
Update draft exercise data and/or solution.

**Headers Required:** `x-editor-token`

**Path Parameters:**
- `draft_id` (integer): The ID of the draft to update

**Request Body:**
```json
{
  "data_json": {
    "question": "What is 3 + 3?",
    "options": ["5", "6", "7", "8"],
    "correct_answer": 1
  },
  "solution_json": {
    "explanation": "3 + 3 equals 6",
    "steps": ["Add 3 to 3", "The result is 6"]
  }
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Draft successfully updated",
  "draft": {
    "id": 1,
    "status": "IN_REVIEW",
    "data_json": {
      "question": "What is 3 + 3?",
      "options": ["5", "6", "7", "8"],
      "correct_answer": 1
    },
    "solution_json": {
      "explanation": "3 + 3 equals 6",
      "steps": ["Add 3 to 3", "The result is 6"]
    },
    "updated_at": "2024-01-02T11:00:00Z"
  }
}
```

#### POST `/api/v1/editorial/editor/drafts/{draft_id}/accept`
Submit a draft for admin review.

**Headers Required:** `x-editor-token`

**Path Parameters:**
- `draft_id` (integer): The ID of the draft to accept

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Draft submitted for review",
  "draft": {
    "id": 1,
    "status": "IN_REVIEW",
    "submitted_at": "2024-01-02T12:00:00Z"
  }
}
```

#### POST `/api/v1/editorial/editor/drafts/{draft_id}/release`
Release a draft assignment.

**Headers Required:** `x-editor-token`

**Path Parameters:**
- `draft_id` (integer): The ID of the draft to release

**Request Body (optional):**
```json
{
  "reason": "Unable to complete due to lack of subject expertise"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Draft assignment released",
  "draft": {
    "id": 1,
    "status": "NEW",
    "assigned_editor": null,
    "released_at": "2024-01-02T13:00:00Z"
  }
}
```

#### POST `/api/v1/editorial/editor/drafts/{draft_id}/reject`
Reject a draft due to quality issues.

**Headers Required:** `x-editor-token`

**Path Parameters:**
- `draft_id` (integer): The ID of the draft to reject

**Requirements:**
- For NEW drafts: Any editor with scope can reject without claiming
- For IN_REVIEW drafts: Only the assigned editor can reject

**Request Body:**
```json
{
  "rejection_reason": "The content is not aligned with the learning objectives",
  "rejection_type": "quality"  // optional: quality, irrelevant, duplicate, other
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Draft rejected successfully. Status: REJECTED_BY_EDITOR",
  "draft": {
    "id": 1,
    "status": "REJECTED_BY_EDITOR",
    "assigned_editor": null,
    "reject_reason": "The content is not aligned with the learning objectives",
    "data_json": {
      "metadata": {
        "editor_rejection": {
          "reason": "The content is not aligned with the learning objectives",
          "type": "quality",
          "rejected_by": "<EMAIL>",
          "rejected_at": "2024-01-02T13:00:00Z"
        }
      }
    }
  }
}
```

**Note:** Rejected drafts are unassigned from the editor and flagged for admin review. Admins can then decide whether to delete the draft permanently.

### Media Management Endpoints

#### GET `/api/v1/editorial/editor/drafts/{draft_id}/media`
Get all media files associated with a draft.

**Headers Required:** `x-editor-token`

**Path Parameters:**
- `draft_id` (integer): The ID of the draft

**Response (200 OK):**
```json
{
  "media": [
    {
      "id": 1,
      "media_type": "IMAGE",
      "file_name": "diagram.png",
      "file_size": 12345,
      "mime_type": "image/png",
      "url": "https://cdn.example.com/media/diagram.png?token=...",
      "metadata": {
        "width": 800,
        "height": 600
      },
      "created_at": "2024-01-02T00:00:00Z"
    },
    {
      "id": 2,
      "media_type": "AUDIO",
      "file_name": "pronunciation.mp3",
      "file_size": 54321,
      "mime_type": "audio/mpeg",
      "url": "https://cdn.example.com/media/pronunciation.mp3?token=...",
      "metadata": {
        "duration": 3.5
      },
      "created_at": "2024-01-02T00:00:00Z"
    }
  ]
}
```

#### POST `/api/v1/editorial/editor/drafts/{draft_id}/media`
Upload a media file for a draft.

**Headers Required:** `x-editor-token`

**Path Parameters:**
- `draft_id` (integer): The ID of the draft

**Request Body (multipart/form-data):**
- `file`: The media file to upload (max 10MB for images, 20MB for audio)
- `media_type`: "IMAGE" or "AUDIO"
- `metadata` (optional): JSON string with additional metadata

**Response (201 Created):**
```json
{
  "id": 3,
  "media_type": "IMAGE",
  "file_name": "example.jpg",
  "file_size": 23456,
  "mime_type": "image/jpeg",
  "url": "https://cdn.example.com/media/example.jpg",
  "metadata": {
    "width": 1024,
    "height": 768
  },
  "created_at": "2024-01-02T14:00:00Z"
}
```

#### DELETE `/api/v1/editorial/editor/drafts/{draft_id}/media/{media_id}`
Delete a media file from a draft.

**Headers Required:** `x-editor-token`

**Path Parameters:**
- `draft_id` (integer): The ID of the draft
- `media_id` (integer): The ID of the media file to delete

**Response (204 No Content)**

### Admin Endpoints

#### POST `/api/v1/editorial/admin/drafts/{draft_public_id}/publish`
Publish a draft to production.

**Headers Required:** `x-editor-token` (Admin role required)

**Path Parameters:**
- `draft_public_id` (string): The public ID of the draft to publish

**Request Body (optional):**
```json
{
  "publish_notes": "Reviewed and approved - good quality exercise",
  "batch_id": "batch-2024-01"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "exercise_id": "ex-12345",
  "published_at": "2024-01-03T10:00:00Z",
  "message": "Draft successfully published to production"
}
```

#### POST `/api/v1/editorial/admin/drafts/{draft_id}/reject`
Reject a draft with different behaviors based on current status.

**Headers Required:** `x-editor-token` (Admin role required)

**Path Parameters:**
- `draft_id` (integer): The ID of the draft to reject

**Behavior by Status:**
- **ACCEPTED_BY_EDITOR**: Normal rejection - sends back to assigned editor for revision
- **REJECTED_BY_EDITOR**: Admin override - reassigns to original editor (if active) and requests revision

**Request Body:**
```json
{
  "rejection_reason": "The question is ambiguous and needs clarification",
  "suggested_changes": [
    "Clarify what type of triangle is being referred to",
    "Add a diagram to illustrate the problem",
    "Review the solution steps for accuracy"
  ]
}
```

**Response (200 OK) - Normal Rejection:**
```json
{
  "success": true,
  "message": "Draft rejected by admin. Status: REJECTED_BY_ADMIN",
  "draft": {
    "id": 1,
    "status": "REJECTED_BY_ADMIN",
    "rejection_reason": "The question is ambiguous and needs clarification",
    "assigned_editor": {
      "id": 5,
      "email": "<EMAIL>"
    }
  }
}
```

**Response (200 OK) - Override Editor Rejection:**
```json
{
  "success": true,
  "message": "Draft rejected by admin and reassigned to editor. Status: REJECTED_BY_ADMIN",
  "draft": {
    "id": 1,
    "status": "REJECTED_BY_ADMIN",
    "rejection_reason": "Admin review: This has potential, please revise. (Previously rejected by editor)",
    "assigned_editor": {
      "id": 5,
      "email": "<EMAIL>"
    }
  }
}
```

#### POST `/api/v1/editorial/admin/drafts/bulk-publish`
Publish multiple drafts in a single batch.

**Headers Required:** `x-editor-token` (Admin role required)

**Request Body:**
```json
{
  "draft_ids": [1, 2, 3, 4, 5],
  "batch_name": "Math Grade 5 - Chapter 3",
  "batch_notes": "Reviewed set of exercises for fractions chapter"
}
```

**Response (200 OK):**
```json
{
  "batch_id": "batch-2024-01-03-001",
  "total_drafts": 5,
  "published": 4,
  "failed": 1,
  "results": [
    {
      "draft_id": 1,
      "success": true,
      "exercise_id": "ex-12345",
      "published_at": "2024-01-03T12:00:00Z"
    },
    {
      "draft_id": 2,
      "success": true,
      "exercise_id": "ex-12346",
      "published_at": "2024-01-03T12:00:01Z"
    },
    {
      "draft_id": 3,
      "success": false,
      "error": "Draft is not in ACCEPTED_BY_EDITOR status"
    }
  ]
}
```

### Editor Management Endpoints

#### GET `/api/v1/editorial/admin/editors`
Get a paginated list of editor accounts.

**Headers Required:** `x-editor-token` (Admin role required)

**Query Parameters:**
- `page` (optional, default: 1): Page number
- `limit` (optional, default: 20): Items per page
- `is_active` (optional): Filter by active status (true/false)
- `role` (optional): Filter by role (EDITOR/ADMIN)

**Response (200 OK):**
```json
{
  "editors": [
    {
      "id": 123,
      "email": "<EMAIL>",
      "full_name": "John Doe",
      "role": "EDITOR",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00Z",
      "last_login_at": "2024-01-03T09:00:00Z",
      "scopes": [
        {
          "id": 1,
          "scope_type": "SUBJECT",
          "scope_value": "math"
        },
        {
          "id": 2,
          "scope_type": "GRADE",
          "scope_value": "5"
        }
      ],
      "stats": {
        "total_drafts_claimed": 45,
        "total_drafts_accepted": 38,
        "total_drafts_rejected": 5,
        "average_completion_hours": 2.5
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 35,
    "pages": 2
  }
}
```

#### POST `/api/v1/editorial/admin/editors`
Create a new editor account.

**Headers Required:** `x-editor-token` (Admin role required)

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "full_name": "Jane Smith",
  "role": "EDITOR",
  "is_active": true
}
```

**Response (201 Created):**
```json
{
  "id": 124,
  "email": "<EMAIL>",
  "full_name": "Jane Smith",
  "role": "EDITOR",
  "is_active": true,
  "created_at": "2024-01-03T13:00:00Z",
  "temporary_password": "TempPass123!",
  "message": "Editor account created. Temporary password must be changed on first login."
}
```

#### PATCH `/api/v1/editorial/admin/editors/{editor_id}`
Update an editor account.

**Headers Required:** `x-editor-token` (Admin role required)

**Path Parameters:**
- `editor_id` (integer): The ID of the editor to update

**Request Body:**
```json
{
  "full_name": "Jane Smith-Johnson",
  "is_active": false,
  "role": "ADMIN"
}
```

**Response (200 OK):**
```json
{
  "id": 124,
  "email": "<EMAIL>",
  "full_name": "Jane Smith-Johnson",
  "role": "ADMIN",
  "is_active": false,
  "updated_at": "2024-01-03T14:00:00Z"
}
```

#### POST `/api/v1/editorial/admin/editors/{editor_id}/scopes`
Add a scope to an editor.

**Headers Required:** `x-editor-token` (Admin role required)

**Path Parameters:**
- `editor_id` (integer): The ID of the editor

**Request Body:**
```json
{
  "scope_type": "SUBJECT",
  "scope_value": "science"
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "message": "Scope added successfully",
  "scope": {
    "id": 3,
    "editor_id": 124,
    "type": "SUBJECT",
    "name": "Science Grade 5",
    "public_id": "science-5"
  }
}
```

#### DELETE `/api/v1/editorial/admin/editors/{editor_id}/scopes/{scope_id}`
Remove a scope from an editor.

**Headers Required:** `x-editor-token` (Admin role required)

**Path Parameters:**
- `editor_id` (integer): The ID of the editor
- `scope_id` (integer): The ID of the scope to remove

**Response (204 No Content)**

### Audit & Metrics Endpoints

#### POST `/api/v1/editorial/admin/audit/query`
Query audit logs with filtering.

**Headers Required:** `x-editor-token` (Admin role required)

**Request Body:**
```json
{
  "draft_id": 1,
  "editor_id": 123,
  "action": "UPDATED",
  "start_date": "2024-01-01T00:00:00Z",
  "end_date": "2024-01-31T23:59:59Z",
  "page": 1,
  "limit": 50
}
```

**Response (200 OK):**
```json
{
  "audit_logs": [
    {
      "id": 100,
      "draft_id": 1,
      "editor_id": 123,
      "editor_email": "<EMAIL>",
      "action": "UPDATED",
      "old_status": null,
      "new_status": null,
      "details": {
        "fields_updated": ["data_json", "solution_json"]
      },
      "created_at": "2024-01-02T11:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 125,
    "pages": 3
  }
}
```

#### GET `/api/v1/editorial/admin/audit/drafts/{draft_id}`
Get complete audit history for a draft.

**Headers Required:** `x-editor-token` (Admin role required)

**Path Parameters:**
- `draft_id` (integer): The ID of the draft

**Response (200 OK):**
```json
{
  "draft_id": 1,
  "audit_logs": [
    {
      "id": 1,
      "action": "CREATED",
      "editor_id": null,
      "editor_email": "system",
      "old_status": null,
      "new_status": null,
      "details": {
        "source": "content_migration"
      },
      "created_at": "2024-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "draft_id": 1,
      "action": "CLAIMED",
      "editor_id": 123,
      "editor_email": "<EMAIL>",
      "old_status": null,
      "new_status": null,
      "details": {},
      "created_at": "2024-01-02T10:00:00Z"
    },
    {
      "id": 3,
      "draft_id": 1,
      "action": "UPDATED",
      "editor_id": 123,
      "editor_email": "<EMAIL>",
      "old_status": null,
      "new_status": null,
      "details": {
        "fields_updated": ["data_json"]
      },
      "created_at": "2024-01-02T11:00:00Z"
    }
  ]
}
```

#### POST `/api/v1/editorial/admin/metrics/overview`
Get comprehensive editorial workflow metrics.

**Headers Required:** `x-editor-token` (Admin role required)

**Request Body:**
```json
{
  "start_date": "2024-01-01T00:00:00Z",
  "end_date": "2024-01-31T23:59:59Z",
  "group_by": "day"
}
```

**Response (200 OK):**
```json
{
  "period": {
    "start": "2024-01-01T00:00:00Z",
    "end": "2024-01-31T23:59:59Z"
  },
  "draft_metrics": {
    "total_created": 150,
    "total_claimed": 140,
    "total_accepted": 120,
    "total_rejected": 15,
    "total_published": 105,
    "average_time_to_claim_hours": 2.5,
    "average_time_to_complete_hours": 4.8,
    "average_time_to_publish_hours": 8.2
  },
  "editor_metrics": {
    "active_editors": 12,
    "total_assignments": 140,
    "average_drafts_per_editor": 11.7,
    "top_performers": [
      {
        "editor_id": 123,
        "full_name": "John Doe",
        "drafts_completed": 25,
        "acceptance_rate": 0.92
      }
    ]
  },
  "publishing_metrics": {
    "total_published": 105,
    "by_subject": {
      "math": 45,
      "science": 30,
      "english": 30
    },
    "by_exercise_type": {
      "MULTIPLE_CHOICE": 60,
      "FILL_IN_BLANK": 25,
      "TRUE_FALSE": 20
    }
  }
}
```

#### GET `/api/v1/editorial/admin/metrics/editor/{editor_id}/performance`
Get performance metrics for a specific editor.

**Headers Required:** `x-editor-token` (Admin role required)

**Path Parameters:**
- `editor_id` (integer): The ID of the editor

**Response (200 OK):**
```json
{
  "editor": {
    "id": 123,
    "email": "<EMAIL>",
    "full_name": "John Doe"
  },
  "performance": {
    "total_drafts_claimed": 45,
    "total_drafts_completed": 40,
    "total_accepted": 38,
    "total_rejected": 2,
    "acceptance_rate": 0.95,
    "average_completion_hours": 3.2,
    "fastest_completion_hours": 1.5,
    "slowest_completion_hours": 8.0,
    "current_assignments": 2,
    "last_activity": "2024-01-03T16:00:00Z"
  },
  "by_subject": {
    "math": {
      "claimed": 25,
      "completed": 23,
      "accepted": 22
    },
    "science": {
      "claimed": 20,
      "completed": 17,
      "accepted": 16
    }
  },
  "recent_activity": [
    {
      "draft_id": 50,
      "action": "ACCEPTED_BY_EDITOR",
      "timestamp": "2024-01-03T16:00:00Z"
    }
  ]
}
```

### Content Hierarchy Endpoints

#### GET `/api/v1/editorial/admin/content/subjects`
Get all available subjects for scope assignment.

**Headers Required:** `x-editor-token` (Admin role required)

**Response (200 OK):**
```json
{
  "subjects": [
    {
      "id": "math-5",
      "name": "Mathematics Grade 5",
      "public_id": "math-5",
      "order": 1,
      "icon": "calculator",
      "total_chapters": 12,
      "total_learning_nodes": 156
    },
    {
      "id": "science-5",
      "name": "Science Grade 5", 
      "public_id": "science-5",
      "order": 2,
      "icon": "flask",
      "total_chapters": 10,
      "total_learning_nodes": 134
    }
  ]
}
```

#### GET `/api/v1/editorial/admin/content/subjects/{subject_id}/chapters`
Get all chapters within a specific subject.

**Headers Required:** `x-editor-token` (Admin role required)

**Path Parameters:**
- `subject_id` (string): The public ID of the subject

**Response (200 OK):**
```json
{
  "subject": {
    "id": "math-5",
    "name": "Mathematics Grade 5"
  },
  "chapters": [
    {
      "id": "ch-123",
      "title": "Fractions",
      "public_id": "ch-123",
      "order": 1,
      "total_learning_nodes": 15
    },
    {
      "id": "ch-124",
      "title": "Decimals",
      "public_id": "ch-124",
      "order": 2,
      "total_learning_nodes": 12
    }
  ]
}
```

#### GET `/api/v1/editorial/admin/content/chapters/{chapter_id}/learning-nodes`
Get all learning nodes within a specific chapter.

**Headers Required:** `x-editor-token` (Admin role required)

**Path Parameters:**
- `chapter_id` (string): The public ID of the chapter

**Response (200 OK):**
```json
{
  "chapter": {
    "id": "ch-123",
    "title": "Fractions",
    "subject_name": "Mathematics Grade 5"
  },
  "learning_nodes": [
    {
      "id": "ln-456",
      "title": "Introduction to Fractions",
      "public_id": "ln-456",
      "order": 1
    },
    {
      "id": "ln-457",
      "title": "Adding Fractions",
      "public_id": "ln-457",
      "order": 2
    }
  ]
}
```

### Cleanup Endpoints

#### GET `/api/v1/editorial/admin/cleanup/report`
Generate a cleanup report (dry run).

**Headers Required:** `x-editor-token` (Admin role required)

**Response (200 OK):**
```json
{
  "report_generated_at": "2024-01-03T17:00:00Z",
  "media_cleanup": {
    "retention_days": 30,
    "files_to_delete": 125,
    "total_size_mb": 450.5,
    "oldest_file_date": "2023-11-15T00:00:00Z"
  },
  "draft_cleanup": {
    "stale_days": 90,
    "drafts_to_delete": 45,
    "by_status": {
      "NEW": 30,
      "REJECTED_BY_ADMIN": 15
    }
  },
  "assignment_cleanup": {
    "abandoned_days": 7,
    "assignments_to_release": 8,
    "affected_editors": [
      {
        "editor_id": 125,
        "email": "<EMAIL>",
        "assignments_to_release": 3
      }
    ]
  }
}
```

#### POST `/api/v1/editorial/admin/cleanup/all`
Run all cleanup tasks.

**Headers Required:** `x-editor-token` (Admin role required)

**Query Parameters:**
- `media_retention_days` (optional, default: 30): Days to retain media files
- `draft_stale_days` (optional, default: 90): Days before considering drafts stale
- `assignment_abandoned_days` (optional, default: 7): Days before releasing abandoned assignments

**Response (200 OK):**
```json
{
  "cleanup_completed_at": "2024-01-03T18:00:00Z",
  "media_cleanup": {
    "files_deleted": 125,
    "size_freed_mb": 450.5,
    "errors": []
  },
  "draft_cleanup": {
    "drafts_deleted": 45,
    "errors": []
  },
  "assignment_cleanup": {
    "assignments_released": 8,
    "errors": []
  },
  "total_execution_time_seconds": 15.3
}
```

## Error Responses

All endpoints may return the following error responses:

### 400 Bad Request
```json
{
  "detail": "Validation error: field 'email' is required"
}
```

### 401 Unauthorized
```json
{
  "detail": "Invalid or expired authentication token"
}
```

### 403 Forbidden
```json
{
  "detail": "Insufficient permissions for this operation"
}
```

### 404 Not Found
```json
{
  "detail": "Draft with ID 123 not found"
}
```

### 409 Conflict
```json
{
  "detail": "Email address already registered"
}
```

### 500 Internal Server Error
```json
{
  "detail": "An unexpected error occurred"
}
```

## Data Types

### Ordering Fields
- **Subjects**: Ordered alphabetically by name (no explicit order field in database)
- **Chapters**: Have an `ordering` field (integer) for custom display order
- **Learning Nodes**: Have an `ordering` field (integer) for custom display order within chapters

### Draft Status
- `NEW`: Newly created, available for claiming by editors
- `IN_REVIEW`: Being worked on by an assigned editor
- `ACCEPTED_BY_EDITOR`: Accepted by editor, ready for admin review
- `REJECTED_BY_EDITOR`: Rejected by editor due to quality issues, flagged for admin review
- `REJECTED_BY_ADMIN`: Rejected by admin, returned to editor for revision
- `PUBLISHED`: Published to production

### Editor Roles
- `EDITOR`: Standard editor with limited permissions
- `ADMIN`: Administrator with full system access

### Scope Types
- `SUBJECT`: Access limited to specific subjects
- `GRADE`: Access limited to specific grade levels
- `CHAPTER`: Access limited to specific chapters

### Exercise Types
- `MULTIPLE_CHOICE`: Multiple choice questions
- `FILL_IN_BLANK`: Fill in the blank exercises
- `TRUE_FALSE`: True/false questions
- `MATCHING`: Matching exercises
- `SHORT_ANSWER`: Short answer questions
- `ESSAY`: Essay questions

### Media Types
- `IMAGE`: Image files (JPEG, PNG, GIF, WebP)
- `AUDIO`: Audio files (MP3, WAV, OGG)

## Rate Limits

- Authentication endpoints: 5 requests per minute per IP
- Media upload: 10 requests per minute per user
- Bulk operations: 1 request per minute per user
- All other endpoints: 60 requests per minute per user

## File Size Limits

- Images: Maximum 10MB per file
- Audio: Maximum 20MB per file
- JSON data fields: Maximum 100KB per field