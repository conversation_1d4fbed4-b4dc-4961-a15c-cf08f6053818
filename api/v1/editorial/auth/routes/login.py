from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from db.database import get_db
from api.v1.editorial.auth.schemas.request import EditorLoginRequest
from api.v1.editorial.auth.schemas.response import EditorLoginResponse
from api.v1.editorial.auth.services.login_service import LoginService
from core.exception_handling.exceptions.custom_exceptions import (
    AuthenticationError, ServiceError
)
from loguru import logger

router = APIRouter()

@router.post(
    "/login",
    response_model=EditorLoginResponse,
    status_code=status.HTTP_200_OK,
    summary="Editor Login",
    description="Authenticate an editor or admin and receive access tokens"
)
async def login(
    request: EditorLoginRequest,
    db: Session = Depends(get_db)
):
    """
    Login endpoint for editors and admins.
    
    Returns JWT access and refresh tokens along with editor information and scopes.
    """
    try:
        response = await LoginService.login_editor(
            db=db,
            email=request.email,
            password=request.password
        )
        return response
        
    except AuthenticationError as e:
        logger.warning(f"Authentication failed: {e.message}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred during login"
        )