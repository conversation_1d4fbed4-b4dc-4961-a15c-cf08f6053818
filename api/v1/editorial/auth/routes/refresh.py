from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from db.database import get_db
from api.v1.editorial.auth.schemas.request import EditorRefreshTokenRequest
from api.v1.editorial.auth.schemas.response import EditorRefreshResponse
from api.v1.editorial.auth.utils.jwt_utils import (
    decode_editor_token, create_editor_access_token, verify_token_type
)
from db.models import EditorAccount
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError
from api.v1.common.schemas import AppErrorCode
from loguru import logger

router = APIRouter()

@router.post(
    "/refresh",
    response_model=EditorRefreshResponse,
    status_code=status.HTTP_200_OK,
    summary="Refresh Access Token",
    description="Use a refresh token to obtain a new access token"
)
async def refresh_token(
    request: EditorRefreshTokenRequest,
    db: Session = Depends(get_db)
):
    """
    Refresh access token using a valid refresh token.
    
    Returns a new JWT access token.
    """
    try:
        # Verify it's a refresh token
        if not verify_token_type(request.refresh_token, "editor_refresh"):
            raise AuthenticationError(
                message="Invalid refresh token",
                error_code=AppErrorCode.INVALID_TOKEN
            )
        
        # Decode the refresh token
        payload = decode_editor_token(request.refresh_token)
        if not payload:
            raise AuthenticationError(
                message="Invalid or expired refresh token",
                error_code=AppErrorCode.TOKEN_EXPIRED
            )
        
        editor_id = payload.get("editor_id")
        if not editor_id:
            raise AuthenticationError(
                message="Invalid token payload",
                error_code=AppErrorCode.INVALID_TOKEN
            )
        
        # Get editor from database
        editor = db.get(EditorAccount, editor_id)
        if not editor or not editor.is_active:
            raise AuthenticationError(
                message="Editor not found or inactive",
                error_code=AppErrorCode.USER_NOT_FOUND
            )
        
        # Create new access token
        access_token = create_editor_access_token(
            editor_id=editor.id,
            email=editor.email,
            role=editor.role.value
        )
        
        logger.info(f"Access token refreshed for editor: {editor.email}")
        
        return EditorRefreshResponse(
            access_token=access_token,
            token_type="Bearer",
            expires_in=28800  # 8 hours in seconds
        )
        
    except AuthenticationError as e:
        logger.warning(f"Token refresh failed: {e.message}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Token refresh error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred during token refresh"
        )