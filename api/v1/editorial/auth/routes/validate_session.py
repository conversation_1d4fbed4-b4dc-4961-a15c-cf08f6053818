from fastapi import APIRouter, Depends, status, HTTPException
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireEditor, EditorAuthResponse
)
from api.v1.editorial.auth.schemas.response import ValidateSessionResponse
from api.v1.editorial.auth.services.validate_session_service import ValidateSessionService
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError
from loguru import logger

router = APIRouter()

@router.post(
    "/validate-session",
    response_model=ValidateSessionResponse,
    status_code=status.HTTP_200_OK,
    summary="Validate Editor Session",
    description="Validate the current editor session and return user information"
)
async def validate_session(
    auth: EditorAuthResponse = Depends(RequireEditor),
    db: Session = Depends(get_db)
):
    """
    Validate the current editor's session.
    
    This endpoint is used by the frontend to check if the current session
    is still valid and to retrieve the user's current information including
    role and scopes.
    
    Returns:
        ValidateSessionResponse: Editor information with role and scopes
    """
    try:
        response = await ValidateSessionService.validate_session(
            db=db,
            auth=auth
        )
        return response
        
    except AuthenticationError as e:
        logger.warning(f"Session validation failed: {e.message}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Session validation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred during session validation"
        )