from fastapi import APIRouter
from .login import router as login_router
from .refresh import router as refresh_router
from .logout import router as logout_router
from .validate_session import router as validate_session_router

# Create main auth router
auth_router = APIRouter(prefix="/auth", tags=["Editorial Auth"])

# Include sub-routers
auth_router.include_router(login_router)
auth_router.include_router(refresh_router)
auth_router.include_router(logout_router)
auth_router.include_router(validate_session_router)