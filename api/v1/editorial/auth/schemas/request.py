from pydantic import BaseModel, EmailStr, Field, field_validator
from typing import Optional

class EditorLoginRequest(BaseModel):
    email: EmailStr = Field(..., description="Editor email address")
    password: str = Field(..., min_length=1, description="Editor password")
    
    @field_validator('email')
    @classmethod
    def normalize_email(cls, v: str) -> str:
        return v.lower().strip()

class EditorRefreshTokenRequest(BaseModel):
    refresh_token: str = Field(..., description="JWT refresh token")

class EditorChangePasswordRequest(BaseModel):
    current_password: str = Field(..., min_length=1, description="Current password")
    new_password: str = Field(..., min_length=8, description="New password")
    
    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, v: str, values) -> str:
        # Import here to avoid circular imports
        from ..utils.password_utils import validate_password_strength
        
        # Check if new password is different from current
        if 'current_password' in values and v == values['current_password']:
            raise ValueError("New password must be different from current password")
        
        # Validate password strength
        is_valid, error_msg = validate_password_strength(v)
        if not is_valid:
            raise ValueError(error_msg)
        
        return v

class EditorResetPasswordRequest(BaseModel):
    email: EmailStr = Field(..., description="Editor email address")
    
    @field_validator('email')
    @classmethod
    def normalize_email(cls, v: str) -> str:
        return v.lower().strip()

class EditorSetNewPasswordRequest(BaseModel):
    reset_token: str = Field(..., description="Password reset token")
    new_password: str = Field(..., min_length=8, description="New password")
    
    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, v: str) -> str:
        from ..utils.password_utils import validate_password_strength
        
        is_valid, error_msg = validate_password_strength(v)
        if not is_valid:
            raise ValueError(error_msg)
        
        return v