from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, List
from db.models import EditorRole

class EditorInfoResponse(BaseModel):
    public_id: str
    email: str
    role: EditorRole
    is_active: bool
    created_at: datetime
    
    model_config = {
        "from_attributes": True
    }

class EditorScopeResponse(BaseModel):
    subject_id: Optional[str] = Field(None, description="Subject public ID if scoped to subject")
    subject_name: Optional[str] = Field(None, description="Subject name")
    chapter_id: Optional[str] = Field(None, description="Chapter public ID if scoped to chapter")
    chapter_title: Optional[str] = Field(None, description="Chapter title")
    learning_node_id: Optional[str] = Field(None, description="Learning node public ID if scoped to node")
    learning_node_title: Optional[str] = Field(None, description="Learning node title")
    
    model_config = {
        "from_attributes": True
    }

class EditorLoginResponse(BaseModel):
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field(default="Bearer", description="Token type")
    expires_in: int = Field(..., description="Access token expiration time in seconds")
    editor: EditorInfoResponse = Field(..., description="Editor information")
    scopes: List[EditorScopeResponse] = Field(default_factory=list, description="Editor's assigned scopes")

class EditorRefreshResponse(BaseModel):
    access_token: str = Field(..., description="New JWT access token")
    token_type: str = Field(default="Bearer", description="Token type")
    expires_in: int = Field(..., description="Access token expiration time in seconds")

class EditorLogoutResponse(BaseModel):
    message: str = Field(default="Successfully logged out", description="Logout confirmation message")

class PasswordChangeResponse(BaseModel):
    message: str = Field(default="Password changed successfully", description="Confirmation message")

class PasswordResetResponse(BaseModel):
    message: str = Field(default="Password reset email sent", description="Confirmation message")
    
class SetNewPasswordResponse(BaseModel):
    message: str = Field(default="Password reset successfully", description="Confirmation message")

class ValidateSessionResponse(BaseModel):
    is_valid: bool = Field(default=True, description="Whether the session is valid")
    editor: EditorInfoResponse = Field(..., description="Editor information")
    scopes: List[EditorScopeResponse] = Field(default_factory=list, description="Editor's assigned scopes")