import jwt
from datetime import datetime, timedelta, UTC
from typing import Dict, Any, Optional
from core.config import settings

def create_editor_access_token(
    editor_id: int,
    email: str,
    role: str,
    expires_delta: Optional[timedelta] = None
) -> str:
    """Create JWT access token for editor"""
    to_encode = {
        "editor_id": editor_id,
        "email": email,
        "role": role,
        "type": "editor_access"
    }
    
    if expires_delta:
        expire = datetime.now(UTC) + expires_delta
    else:
        # Default to 8 hours for editor sessions
        expire = datetime.now(UTC) + timedelta(hours=8)
    
    to_encode.update({"exp": expire, "iat": datetime.now(UTC)})
    
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.SECRET_KEY, 
        algorithm=settings.JWT_ALGORITHM
    )
    return encoded_jwt

def create_editor_refresh_token(
    editor_id: int,
    expires_delta: Optional[timedelta] = None
) -> str:
    """Create JWT refresh token for editor"""
    to_encode = {
        "editor_id": editor_id,
        "type": "editor_refresh"
    }
    
    if expires_delta:
        expire = datetime.now(UTC) + expires_delta
    else:
        # Default to 30 days for refresh tokens
        expire = datetime.now(UTC) + timedelta(days=30)
    
    to_encode.update({"exp": expire, "iat": datetime.now(UTC)})
    
    encoded_jwt = jwt.encode(
        to_encode,
        settings.SECRET_KEY,
        algorithm=settings.JWT_ALGORITHM
    )
    return encoded_jwt

def decode_editor_token(token: str) -> Optional[Dict[str, Any]]:
    """Decode and validate editor JWT token"""
    try:
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        
        # Verify it's an editor token
        if payload.get("type") not in ["editor_access", "editor_refresh"]:
            return None
            
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

def verify_token_type(token: str, expected_type: str) -> bool:
    """Verify token is of expected type"""
    payload = decode_editor_token(token)
    if not payload:
        return False
    return payload.get("type") == expected_type