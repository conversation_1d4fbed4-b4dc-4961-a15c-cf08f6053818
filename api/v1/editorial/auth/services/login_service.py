from sqlalchemy.orm import Session
from sqlalchemy import select
from typing import Optional, List
from datetime import datetime, UTC, timedelta

from db.models import EditorAccount, EditorScope
from api.v1.editorial.auth.schemas.request import EditorLoginRequest
from api.v1.editorial.auth.schemas.response import (
    EditorLoginResponse, EditorInfoResponse, EditorScopeResponse
)
from api.v1.editorial.auth.utils.password_utils import verify_password
from api.v1.editorial.auth.utils.jwt_utils import (
    create_editor_access_token, create_editor_refresh_token
)
from core.exception_handling.exceptions.custom_exceptions import (
    AuthenticationError, NotFoundError
)
from api.v1.common.schemas import AppErrorCode
from loguru import logger

class LoginService:
    @staticmethod
    async def login_editor(
        db: Session,
        email: str,
        password: str
    ) -> EditorLoginResponse:
        """Authenticate editor and return tokens"""
        # Find editor by email
        stmt = select(EditorAccount).where(
            EditorAccount.email == email.lower()
        )
        editor = db.execute(stmt).scalar_one_or_none()
        
        if not editor:
            logger.warning(f"Login attempt for non-existent email: {email}")
            raise AuthenticationError(
                message="Invalid email or password",
                error_code=AppErrorCode.INVALID_CREDENTIALS
            )
        
        # Check if account is active
        if not editor.is_active:
            logger.warning(f"Login attempt for inactive account: {email}")
            raise AuthenticationError(
                message="Account is inactive",
                error_code=AppErrorCode.ACCOUNT_LOCKED
            )
        
        # Verify password
        if not verify_password(password, editor.pwd_hash):
            logger.warning(f"Failed login attempt for email: {email}")
            raise AuthenticationError(
                message="Invalid email or password",
                error_code=AppErrorCode.INVALID_CREDENTIALS
            )
        
        # Create tokens
        access_token = create_editor_access_token(
            editor_id=editor.id,
            email=editor.email,
            role=editor.role.value
        )
        
        refresh_token = create_editor_refresh_token(
            editor_id=editor.id
        )
        
        # Update last login
        editor.updated_at = datetime.now(UTC)
        db.commit()
        
        # Get editor scopes
        scopes = LoginService._get_editor_scopes(db, editor)
        
        logger.info(f"Successful login for editor: {editor.email}")
        
        return EditorLoginResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="Bearer",
            expires_in=28800,  # 8 hours in seconds
            editor=EditorInfoResponse(
                public_id=editor.public_id,
                email=editor.email,
                role=editor.role,
                is_active=editor.is_active,
                created_at=editor.created_at
            ),
            scopes=scopes
        )
    
    @staticmethod
    def _get_editor_scopes(db: Session, editor: EditorAccount) -> List[EditorScopeResponse]:
        """Get all scopes for an editor"""
        scopes = []
        
        for scope in editor.scopes:
            scope_response = EditorScopeResponse()
            
            if scope.learning_node:
                scope_response.learning_node_id = scope.learning_node.public_id
                scope_response.learning_node_title = scope.learning_node.title
                
                if scope.learning_node.chapter:
                    scope_response.chapter_id = scope.learning_node.chapter.public_id
                    scope_response.chapter_title = scope.learning_node.chapter.title
                    
                    if scope.learning_node.chapter.subject:
                        scope_response.subject_id = scope.learning_node.chapter.subject.public_id
                        scope_response.subject_name = scope.learning_node.chapter.subject.name
            
            elif scope.chapter:
                scope_response.chapter_id = scope.chapter.public_id
                scope_response.chapter_title = scope.chapter.title
                
                if scope.chapter.subject:
                    scope_response.subject_id = scope.chapter.subject.public_id
                    scope_response.subject_name = scope.chapter.subject.name
            
            elif scope.subject:
                scope_response.subject_id = scope.subject.public_id
                scope_response.subject_name = scope.subject.name
            
            scopes.append(scope_response)
        
        return scopes