from sqlalchemy.orm import Session
from sqlalchemy import select
from typing import List

from db.models import EditorAccount, EditorScope
from api.v1.editorial.auth.schemas.response import (
    ValidateSessionResponse, EditorInfoResponse, EditorScopeResponse
)
from dependencies.auth_dependencies.editorial_auth_dependency import EditorAuthResponse
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError
from api.v1.common.schemas import AppErrorCode
from loguru import logger

class ValidateSessionService:
    @staticmethod
    async def validate_session(
        db: Session,
        auth: EditorAuthResponse
    ) -> ValidateSessionResponse:
        """Validate editor session and return current information"""
        # Get fresh editor data from database
        stmt = select(EditorAccount).where(
            EditorAccount.id == auth.editor_id
        )
        editor = db.execute(stmt).scalar_one_or_none()
        
        if not editor:
            logger.warning(f"Session validation failed - editor not found: {auth.editor_id}")
            raise AuthenticationError(
                message="Session is no longer valid",
                error_code=AppErrorCode.INVALID_TOKEN
            )
        
        if not editor.is_active:
            logger.warning(f"Session validation failed - editor inactive: {editor.email}")
            raise AuthenticationError(
                message="Account is inactive",
                error_code=AppErrorCode.ACCOUNT_LOCKED
            )
        
        # Get editor scopes
        scopes = ValidateSessionService._get_editor_scopes(db, editor)
        
        logger.info(f"Session validated for editor: {editor.email}")
        
        return ValidateSessionResponse(
            is_valid=True,
            editor=EditorInfoResponse(
                public_id=editor.public_id,
                email=editor.email,
                role=editor.role,
                is_active=editor.is_active,
                created_at=editor.created_at
            ),
            scopes=scopes
        )
    
    @staticmethod
    def _get_editor_scopes(db: Session, editor: EditorAccount) -> List[EditorScopeResponse]:
        """Get all active scopes for an editor"""
        scopes = []
        
        # All scopes are considered active (no is_active field in model)
        for scope in editor.scopes:
            scope_response = EditorScopeResponse()
            
            if scope.learning_node:
                scope_response.learning_node_id = scope.learning_node.public_id
                scope_response.learning_node_title = scope.learning_node.title
                
                if scope.learning_node.chapter:
                    scope_response.chapter_id = scope.learning_node.chapter.public_id
                    scope_response.chapter_title = scope.learning_node.chapter.title
                    
                    if scope.learning_node.chapter.subject:
                        scope_response.subject_id = scope.learning_node.chapter.subject.public_id
                        scope_response.subject_name = scope.learning_node.chapter.subject.name
            
            elif scope.chapter:
                scope_response.chapter_id = scope.chapter.public_id
                scope_response.chapter_title = scope.chapter.title
                
                if scope.chapter.subject:
                    scope_response.subject_id = scope.chapter.subject.public_id
                    scope_response.subject_name = scope.chapter.subject.name
            
            elif scope.subject:
                scope_response.subject_id = scope.subject.public_id
                scope_response.subject_name = scope.subject.name
            
            scopes.append(scope_response)
        
        return scopes