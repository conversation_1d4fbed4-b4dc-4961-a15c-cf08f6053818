from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireAdmin, EditorAuthResponse
)
from api.v1.editorial.admin.schemas.request import MetricsQueryRequest
from api.v1.editorial.admin.schemas.response import (
    MetricsOverviewResponse,
    EditorPerformanceResponse,
    DraftStatusMetricsResponse
)
from services.draft_management.metrics_service import MetricsService
from loguru import logger
from typing import List

router = APIRouter()

@router.post(
    "/metrics/overview",
    response_model=MetricsOverviewResponse,
    status_code=status.HTTP_200_OK,
    summary="Get Metrics Overview",
    description="Get comprehensive editorial workflow metrics"
)
async def get_metrics_overview(
    request: MetricsQueryRequest,
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """
    Get comprehensive overview of editorial workflow metrics.
    
    Includes:
    - Draft counts by status
    - Editor activity metrics
    - Publishing statistics
    - Average processing times
    """
    try:
        metrics = await MetricsService.get_overview_metrics(
            db=db,
            date_from=request.date_from,
            date_to=request.date_to,
            subject_id=request.subject_id
        )
        
        return MetricsOverviewResponse(
            period={
                "from": request.date_from.isoformat() if request.date_from else "all_time",
                "to": request.date_to.isoformat() if request.date_to else "current",
                "subject": request.subject_id or "all_subjects"
            },
            draft_metrics=metrics["draft_metrics"],
            editor_metrics=metrics["editor_metrics"],
            publishing_metrics=metrics["publishing_metrics"],
            processing_times=metrics["processing_times"]
        )
        
    except Exception as e:
        logger.error(f"Error getting metrics overview: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving metrics"
        )


@router.get(
    "/metrics/editor/{editor_id}/performance",
    response_model=EditorPerformanceResponse,
    status_code=status.HTTP_200_OK,
    summary="Get Editor Performance",
    description="Get performance metrics for a specific editor"
)
async def get_editor_performance(
    editor_id: int,
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """
    Get performance metrics for a specific editor.
    
    Includes:
    - Drafts reviewed, accepted, rejected
    - Average review time
    - Acceptance rate
    - Current assignments
    """
    try:
        # Create request with editor filter
        request = MetricsQueryRequest(editor_id=editor_id)
        
        performance = await MetricsService.get_editor_performance(
            db=db,
            editor_id=editor_id,
            date_from=request.date_from,
            date_to=request.date_to
        )
        
        return EditorPerformanceResponse(
            editor_id=editor_id,
            editor_name=performance["editor_name"],
            period={
                "from": request.date_from.isoformat() if request.date_from else "all_time",
                "to": request.date_to.isoformat() if request.date_to else "current"
            },
            drafts_reviewed=performance["drafts_reviewed"],
            drafts_accepted=performance["drafts_accepted"],
            drafts_rejected=performance["drafts_rejected"],
            average_review_time_hours=performance["average_review_time_hours"],
            acceptance_rate=performance["acceptance_rate"],
            current_assignments=performance["current_assignments"]
        )
        
    except Exception as e:
        logger.error(f"Error getting editor performance: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving editor performance metrics"
        )


@router.post(
    "/metrics/draft-status",
    response_model=DraftStatusMetricsResponse,
    status_code=status.HTTP_200_OK,
    summary="Get Draft Status Metrics",
    description="Get distribution of drafts by various dimensions"
)
async def get_draft_status_metrics(
    request: MetricsQueryRequest,
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """
    Get distribution of drafts by status, subject, editor, and age.
    
    Useful for identifying bottlenecks and resource allocation.
    """
    try:
        metrics = await MetricsService.get_draft_status_metrics(
            db=db,
            subject_id=request.subject_id,
            editor_id=request.editor_id
        )
        
        return DraftStatusMetricsResponse(
            total_drafts=metrics["total_drafts"],
            by_status=metrics["by_status"],
            by_subject=metrics["by_subject"],
            by_assigned_editor=metrics["by_assigned_editor"],
            aging_report=metrics["aging_report"]
        )
        
    except Exception as e:
        logger.error(f"Error getting draft status metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving draft status metrics"
        )


@router.get(
    "/metrics/editors/leaderboard",
    response_model=List[EditorPerformanceResponse],
    status_code=status.HTTP_200_OK,
    summary="Get Editor Leaderboard",
    description="Get top performing editors by various metrics"
)
async def get_editor_leaderboard(
    limit: int = 10,
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """
    Get leaderboard of top performing editors.
    
    Ranked by total drafts accepted in the current month.
    """
    try:
        leaderboard = await MetricsService.get_editor_leaderboard(
            db=db,
            limit=limit
        )
        
        results = []
        for editor_data in leaderboard:
            results.append(EditorPerformanceResponse(
                editor_id=editor_data["editor_id"],
                editor_name=editor_data["editor_name"],
                period={
                    "from": "current_month",
                    "to": "current"
                },
                drafts_reviewed=editor_data["drafts_reviewed"],
                drafts_accepted=editor_data["drafts_accepted"],
                drafts_rejected=editor_data["drafts_rejected"],
                average_review_time_hours=editor_data["average_review_time_hours"],
                acceptance_rate=editor_data["acceptance_rate"],
                current_assignments=editor_data["current_assignments"]
            ))
        
        return results
        
    except Exception as e:
        logger.error(f"Error getting editor leaderboard: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving editor leaderboard"
        )