from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireAdmin, EditorAuthResponse
)
from services.draft_management.tasks.cleanup_tasks import DraftCleanupTasks
from loguru import logger
from typing import Optional

router = APIRouter()

@router.get(
    "/cleanup/report",
    status_code=status.HTTP_200_OK,
    summary="Cleanup Report",
    description="Generate a report of items that would be cleaned up without actually deleting anything"
)
async def get_cleanup_report(
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """
    Generate a cleanup report showing what would be deleted.
    
    This is a dry-run operation that doesn't delete anything.
    Useful for reviewing before running actual cleanup.
    """
    try:
        report = await DraftCleanupTasks.generate_cleanup_report(db)
        
        return {
            "success": True,
            "report": report
        }
        
    except Exception as e:
        logger.error(f"Error generating cleanup report: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while generating the cleanup report"
        )


@router.post(
    "/cleanup/media",
    status_code=status.HTTP_200_OK,
    summary="Clean Up Old Media",
    description="Delete draft media files older than the retention period"
)
async def cleanup_old_media(
    retention_days: int = Query(30, ge=1, le=365, description="Days to retain media files"),
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """
    Clean up draft media files older than retention period.
    
    Only deletes media that hasn't been published to production.
    Default retention: 30 days.
    """
    try:
        result = await DraftCleanupTasks.cleanup_old_draft_media(
            db=db,
            retention_days=retention_days
        )
        
        return {
            "success": True,
            "message": f"Cleaned up {result['deleted_media_count']} old media files",
            "details": result
        }
        
    except Exception as e:
        logger.error(f"Error cleaning up media: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while cleaning up media files"
        )


@router.post(
    "/cleanup/drafts",
    status_code=status.HTTP_200_OK,
    summary="Clean Up Stale Drafts",
    description="Delete drafts that haven't been updated in a long time"
)
async def cleanup_stale_drafts(
    stale_days: int = Query(90, ge=30, le=365, description="Days before considering draft stale"),
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """
    Clean up stale drafts.
    
    Deletes drafts in NEW or REJECTED_BY_ADMIN status that haven't
    been updated in the specified number of days.
    Default: 90 days.
    """
    try:
        result = await DraftCleanupTasks.cleanup_stale_drafts(
            db=db,
            stale_days=stale_days
        )
        
        return {
            "success": True,
            "message": f"Cleaned up {result['deleted_draft_count']} stale drafts",
            "details": result
        }
        
    except Exception as e:
        logger.error(f"Error cleaning up drafts: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while cleaning up stale drafts"
        )


@router.post(
    "/cleanup/assignments",
    status_code=status.HTTP_200_OK,
    summary="Release Abandoned Assignments",
    description="Release draft assignments that have been abandoned by editors"
)
async def release_abandoned_assignments(
    abandoned_days: int = Query(7, ge=1, le=30, description="Days before considering assignment abandoned"),
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """
    Release abandoned draft assignments.
    
    Releases drafts in IN_REVIEW status that haven't been updated
    in the specified number of days.
    Default: 7 days.
    """
    try:
        result = await DraftCleanupTasks.release_abandoned_assignments(
            db=db,
            abandoned_days=abandoned_days
        )
        
        return {
            "success": True,
            "message": f"Released {result['released_count']} abandoned assignments",
            "details": result
        }
        
    except Exception as e:
        logger.error(f"Error releasing assignments: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while releasing abandoned assignments"
        )


@router.post(
    "/cleanup/all",
    status_code=status.HTTP_200_OK,
    summary="Run All Cleanup Tasks",
    description="Run all cleanup tasks in sequence"
)
async def run_all_cleanup_tasks(
    media_retention_days: int = Query(30, ge=1, le=365, description="Days to retain media files"),
    stale_draft_days: int = Query(90, ge=30, le=365, description="Days before considering draft stale"),
    abandoned_assignment_days: int = Query(7, ge=1, le=30, description="Days before considering assignment abandoned"),
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """
    Run all cleanup tasks in sequence.
    
    1. Release abandoned assignments
    2. Delete stale drafts
    3. Delete old media files
    
    Use with caution - this performs actual deletions.
    """
    try:
        result = await DraftCleanupTasks.run_all_cleanup_tasks(
            db=db,
            media_retention_days=media_retention_days,
            stale_draft_days=stale_draft_days,
            abandoned_assignment_days=abandoned_assignment_days
        )
        
        # Count total operations
        total_released = result["tasks"].get("abandoned_assignments", {}).get("released_count", 0)
        total_drafts = result["tasks"].get("stale_drafts", {}).get("deleted_draft_count", 0)
        total_media = result["tasks"].get("old_media", {}).get("deleted_media_count", 0)
        
        return {
            "success": True,
            "message": f"Cleanup completed: {total_released} assignments released, {total_drafts} drafts deleted, {total_media} media files deleted",
            "details": result
        }
        
    except Exception as e:
        logger.error(f"Error running cleanup tasks: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while running cleanup tasks"
        )