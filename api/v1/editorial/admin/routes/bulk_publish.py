from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireAdmin, EditorAuthResponse
)
from api.v1.editorial.admin.schemas.request import BulkPublishRequest
from api.v1.editorial.admin.schemas.response import BulkPublishResponse
from services.draft_management.publishing_service import PublishingService
from core.exception_handling.exceptions.custom_exceptions import (
    ValidationError, PermissionDeniedError
)
from loguru import logger
from typing import List, Dict, Any

router = APIRouter()

@router.post(
    "/drafts/bulk-publish",
    response_model=BulkPublishResponse,
    status_code=status.HTTP_200_OK,
    summary="Bulk Publish Drafts",
    description="Publish multiple drafts to production in a single batch"
)
async def bulk_publish_drafts(
    request: BulkPublishRequest,
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """
    Publish multiple drafts in a single batch operation.
    
    Requirements:
    - All drafts must be in ACCEPTED status
    - Admin must have scope for all drafts
    - Creates a batch record for tracking
    - Processes each draft individually, tracking successes and failures
    """
    try:
        # Create batch and process drafts
        batch_result = await PublishingService.bulk_publish(
            db=db,
            draft_ids=request.draft_ids,
            admin=auth.editor,
            batch_name=request.batch_name,
            batch_notes=request.batch_notes
        )
        
        # Commit the transaction
        db.commit()
        
        # Prepare detailed results
        results: List[Dict[str, Any]] = []
        for draft_id, result in batch_result["results"].items():
            if result["success"]:
                results.append({
                    "draft_id": draft_id,
                    "success": True,
                    "exercise_id": result["exercise_id"],
                    "message": "Published successfully"
                })
            else:
                results.append({
                    "draft_id": draft_id,
                    "success": False,
                    "error": result["error"],
                    "message": f"Failed to publish: {result['error']}"
                })
        
        return BulkPublishResponse(
            success=batch_result["successful_count"] > 0,
            message=f"Batch publish completed: {batch_result['successful_count']}/{len(request.draft_ids)} drafts published",
            batch_id=batch_result["batch_id"],
            total_drafts=len(request.draft_ids),
            successful_publishes=batch_result["successful_count"],
            failed_publishes=batch_result["failed_count"],
            results=results
        )
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error in bulk publish: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred during bulk publish operation"
        )