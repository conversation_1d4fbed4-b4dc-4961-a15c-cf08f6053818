from fastapi import APIRouter
from .publish_draft import router as publish_draft_router
from .reject_draft import router as reject_draft_router
from .delete_draft import router as delete_draft_router
from .bulk_publish import router as bulk_publish_router
from .manage_editors import router as manage_editors_router

from .metrics import router as metrics_router
from .cleanup_tasks import router as cleanup_tasks_router
from .content_hierarchy import router as content_hierarchy_router

# Create main admin router
admin_router = APIRouter(prefix="/admin", tags=["Editorial - Admin"])

# Include all sub-routers
admin_router.include_router(publish_draft_router)
admin_router.include_router(reject_draft_router)
admin_router.include_router(delete_draft_router)
admin_router.include_router(bulk_publish_router)
admin_router.include_router(manage_editors_router)

admin_router.include_router(metrics_router)
admin_router.include_router(cleanup_tasks_router)
admin_router.include_router(content_hierarchy_router)