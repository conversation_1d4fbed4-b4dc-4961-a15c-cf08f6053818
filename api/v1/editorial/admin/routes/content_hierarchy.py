from fastapi import APIRouter, Depends, Path, HTTPException, status
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireAdmin, EditorAuthResponse
)
from api.v1.editorial.admin.schemas.response import (
    SubjectListResponse, ChapterListResponse, LearningNodeListResponse
)
from services.editorial.content_hierarchy_service import ContentHierarchyService
from core.exception_handling.exceptions.custom_exceptions import NotFoundError
from loguru import logger

router = APIRouter(prefix="/content", tags=["Editorial - Admin Content"])

@router.get(
    "/subjects",
    response_model=SubjectListResponse,
    status_code=status.HTTP_200_OK,
    summary="Get Available Subjects",
    description="Fetch all subjects available in the system for scope assignment"
)
async def get_available_subjects(
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """Get all subjects with chapter and learning node counts."""
    try:
        subjects = await ContentHierarchyService.get_all_subjects(db)
        return SubjectListResponse(subjects=subjects)
        
    except Exception as e:
        logger.error(f"Error fetching subjects: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching subjects"
        )


@router.get(
    "/subjects/{subject_id}/chapters",
    response_model=ChapterListResponse,
    status_code=status.HTTP_200_OK,
    summary="Get Chapters for Subject",
    description="Fetch chapters within a specific subject"
)
async def get_subject_chapters(
    subject_id: str = Path(..., description="The public ID of the subject"),
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """Get all chapters for a specific subject."""
    try:
        result = await ContentHierarchyService.get_subject_chapters(db, subject_id)
        return result
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error fetching chapters for subject {subject_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching chapters"
        )


@router.get(
    "/chapters/{chapter_id}/learning-nodes",
    response_model=LearningNodeListResponse,
    status_code=status.HTTP_200_OK,
    summary="Get Learning Nodes for Chapter",
    description="Fetch learning nodes within a specific chapter"
)
async def get_chapter_learning_nodes(
    chapter_id: str = Path(..., description="The public ID of the chapter"),
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """Get all learning nodes for a specific chapter."""
    try:
        result = await ContentHierarchyService.get_chapter_learning_nodes(db, chapter_id)
        return result
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error fetching learning nodes for chapter {chapter_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching learning nodes"
        )