from fastapi import APIRouter, Depends, Path, HTTPException, status
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireAdmin, EditorAuthResponse
)
from api.v1.editorial.admin.schemas.request import RejectDraftRequest
from api.v1.editorial.editor.schemas.response import DraftActionResponse
from services.draft_management.draft_service import DraftManagementService
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ValidationError, PermissionDeniedError
)
from loguru import logger

router = APIRouter()

@router.post(
    "/drafts/{draft_id}/reject",
    response_model=DraftActionResponse,
    status_code=status.HTTP_200_OK,
    summary="Reject Draft",
    description="Reject a draft and send it back to the editor"
)
async def reject_draft(
    draft_id: int = Path(..., description="Draft ID to reject"),
    request: RejectDraftRequest = ...,
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """
    Reject a draft with different behaviors based on current status.
    
    For ACCEPTED_BY_EDITOR drafts:
    - Changes status to REJECTED_BY_ADMIN
    - Sends back to the assigned editor for revision
    
    For REJECTED_BY_EDITOR drafts (Admin overriding editor rejection):
    - Changes status to REJECTED_BY_ADMIN
    - Reassigns to the editor who rejected it (if possible)
    - Indicates this is an admin override of editor's rejection
    
    Requirements:
    - Admin must have appropriate scope
    - Rejection reason is required
    """
    try:
        # Reject the draft
        draft = await DraftManagementService.reject_draft_by_admin(
            db=db,
            draft_id=draft_id,
            admin=auth.editor,
            rejection_reason=request.rejection_reason,
            suggested_changes=request.suggested_changes
        )
        
        # Get updated draft details
        from api.v1.editorial.editor.routes.draft_detail import get_draft_detail
        detail_response = await get_draft_detail(draft_id, auth, db)
        
        # Determine the message based on what happened
        if draft.assigned_editor_id:
            message = f"Draft rejected by admin and reassigned to editor. Status: REJECTED_BY_ADMIN"
        else:
            message = f"Draft rejected by admin. Status: REJECTED_BY_ADMIN"
        
        return DraftActionResponse(
            success=True,
            message=message,
            draft=detail_response
        )
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.message
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error rejecting draft {draft_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while rejecting the draft"
        )