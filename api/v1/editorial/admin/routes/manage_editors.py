from fastapi import APIRouter, Depends, Path, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Optional, Dict, Any
from db.database import get_db
from db.models import Subject, Chapter, LearningNode
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireAdmin, EditorAuthResponse
)
from api.v1.editorial.admin.schemas.request import EditorManagementRequest, EditorScopeRequest
from api.v1.editorial.admin.schemas.response import EditorAccountResponse, EditorListResponse
from services.draft_management.editor_management_service import EditorManagementService
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ValidationError, ConflictError
)
from loguru import logger

router = APIRouter()

@router.get(
    "/editors",
    response_model=EditorListResponse,
    status_code=status.HTTP_200_OK,
    summary="List Editor Accounts",
    description="Get a paginated list of editor accounts"
)
async def list_editors(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    role: Optional[str] = Query(None, description="Filter by role (EDITOR/ADMIN)"),
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """List all editor accounts with pagination and filtering."""
    try:
        result = await EditorManagementService.list_editors(
            db=db,
            page=page,
            limit=limit,
            is_active=is_active,
            role=role
        )
        
        return EditorListResponse(
            editors=result["editors"],
            total=result["total"],
            page=page,
            limit=limit,
            total_pages=result["total_pages"]
        )
        
    except Exception as e:
        logger.error(f"Error listing editors: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while listing editors"
        )


@router.get(
    "/editors/{editor_public_id}",
    response_model=EditorAccountResponse,
    status_code=status.HTTP_200_OK,
    summary="Get Editor Account",
    description="Get a specific editor account by public ID"
)
async def get_editor(
    editor_public_id: str = Path(..., description="Editor Public ID"),
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """Get a specific editor account by public ID."""
    try:
        editor = await EditorManagementService.get_editor_by_public_id(
            db=db,
            editor_public_id=editor_public_id
        )

        # Transform scopes to dictionary format expected by EditorAccountResponse
        scopes = []
        for scope in editor.scopes:
            scope_dict = {
                "id": scope.id,
                "editor_id": scope.editor_id,
                "created_at": scope.created_at.isoformat()
            }
            if scope.subject:
                scope_dict["scope_type"] = "SUBJECT"
                scope_dict["scope_value"] = scope.subject.public_id
                scope_dict["name"] = scope.subject.name
            elif scope.chapter:
                scope_dict["scope_type"] = "CHAPTER"
                scope_dict["scope_value"] = scope.chapter.public_id
                scope_dict["name"] = scope.chapter.title
            elif scope.learning_node:
                scope_dict["scope_type"] = "LEARNING_NODE"
                scope_dict["scope_value"] = scope.learning_node.public_id
                scope_dict["name"] = scope.learning_node.title
            scopes.append(scope_dict)

        # Create response data with transformed scopes
        editor_data = {
            "id": editor.id,
            "public_id": editor.public_id,
            "email": editor.email,
            "role": editor.role.value,
            "is_active": editor.is_active,
            "created_at": editor.created_at,
            "last_login_at": None,  # This field doesn't exist in the model
            "total_drafts_reviewed": 0,  # TODO: Calculate actual values
            "total_drafts_accepted": 0,  # TODO: Calculate actual values
            "scopes": scopes
        }

        return EditorAccountResponse.model_validate(editor_data)

    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error getting editor {editor_public_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while getting the editor"
        )


@router.post(
    "/editors",
    response_model=EditorAccountResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create Editor Account",
    description="Create a new editor account"
)
async def create_editor(
    request: EditorManagementRequest,
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """Create a new editor account."""
    try:
        editor = await EditorManagementService.create_editor(
            db=db,
            email=request.email,
            role=request.role,
            created_by=auth.editor,
            password=request.password
        )

        return EditorAccountResponse.model_validate(editor)

    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=e.message
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error creating editor: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while creating the editor"
        )


@router.patch(
    "/editors/{editor_public_id}",
    response_model=EditorAccountResponse,
    status_code=status.HTTP_200_OK,
    summary="Update Editor Account",
    description="Update an existing editor account"
)
async def update_editor(
    editor_public_id: str = Path(..., description="Editor Public ID"),
    request: EditorManagementRequest = ...,
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """Update an existing editor account."""
    try:
        editor = await EditorManagementService.update_editor_by_public_id(
            db=db,
            editor_public_id=editor_public_id,
            updates={
                "email": request.email,
                "is_active": request.is_active,
                "role": request.role
            },
            updated_by=auth.editor
        )

        return EditorAccountResponse.model_validate(editor)

    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.message
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error updating editor {editor_public_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while updating the editor"
        )


@router.post(
    "/editors/{editor_public_id}/scopes",
    response_model=Dict[str, Any],
    status_code=status.HTTP_201_CREATED,
    summary="Add Editor Scope",
    description="Add a new scope to an editor account"
)
async def add_editor_scope(
    editor_public_id: str = Path(..., description="Editor Public ID"),
    request: EditorScopeRequest = ...,
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """Add a new scope to an editor account."""
    try:
        scope = await EditorManagementService.add_editor_scope_by_public_id(
            db=db,
            editor_public_id=editor_public_id,
            scope_type=request.scope_type,
            scope_value=request.scope_value,
            added_by=auth.editor
        )
        
        # Build response with scope information
        scope_info = {
            "id": scope.id,
            "editor_id": scope.editor_id
        }

        # Add scope details based on what was created
        if scope.subject_id:
            subject = db.query(Subject).filter_by(id=scope.subject_id).first()
            scope_info.update({
                "type": "SUBJECT",
                "name": subject.name if subject else "Unknown",
                "public_id": subject.public_id if subject else scope.subject_id
            })
        elif scope.chapter_id:
            chapter = db.query(Chapter).filter_by(id=scope.chapter_id).first()
            scope_info.update({
                "type": "CHAPTER",
                "name": chapter.title if chapter else "Unknown",
                "public_id": chapter.public_id if chapter else scope.chapter_id
            })
        elif scope.learning_node_id:
            node = db.query(LearningNode).filter_by(id=scope.learning_node_id).first()
            scope_info.update({
                "type": "LEARNING_NODE",
                "name": node.title if node else "Unknown",
                "public_id": node.public_id if node else scope.learning_node_id
            })
        
        return {
            "success": True,
            "message": f"Scope added successfully",
            "scope": scope_info
        }
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.message
        )
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=e.message
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error adding scope to editor {editor_public_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while adding the scope"
        )


@router.delete(
    "/editors/{editor_public_id}",
    status_code=status.HTTP_200_OK,
    summary="Delete Editor Account",
    description="Delete an editor account by public ID"
)
async def delete_editor(
    editor_public_id: str = Path(..., description="Editor Public ID"),
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """
    Delete an editor account.

    Business rules:
    - Cannot delete the last admin user
    - Cannot delete the currently logged-in admin
    - Only admins can delete editors
    - Performs soft delete (sets is_active=False)
    """
    try:
        success = await EditorManagementService.delete_editor_by_public_id(
            db=db,
            editor_public_id=editor_public_id,
            deleted_by=auth.editor
        )

        return {
            "success": success,
            "message": f"Editor {editor_public_id} has been successfully deleted",
            "editor_public_id": editor_public_id
        }

    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.message
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error deleting editor {editor_public_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while deleting the editor"
        )


@router.delete(
    "/editors/{editor_public_id}/permanent",
    status_code=status.HTTP_200_OK,
    summary="Permanently Delete Editor Account",
    description="Permanently delete an editor account by public ID (hard delete)"
)
async def permanently_delete_editor(
    editor_public_id: str = Path(..., description="Editor Public ID"),
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """
    Permanently delete an editor account (hard delete).

    Business rules:
    - Cannot delete the last admin user
    - Cannot delete the currently logged-in admin
    - Only admins can delete editors
    - Performs hard delete (removes record from database)
    - WARNING: This is irreversible and may cause foreign key issues
    """
    try:
        success = await EditorManagementService.hard_delete_editor_by_public_id(
            db=db,
            editor_public_id=editor_public_id,
            deleted_by=auth.editor
        )

        return {
            "success": success,
            "message": f"Editor {editor_public_id} has been permanently deleted",
            "editor_public_id": editor_public_id
        }

    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.message
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error permanently deleting editor {editor_public_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while permanently deleting the editor"
        )


@router.delete(
    "/editors/{editor_public_id}/scopes/{scope_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Remove Editor Scope",
    description="Remove a scope from an editor account"
)
async def remove_editor_scope(
    editor_public_id: str = Path(..., description="Editor Public ID"),
    scope_id: int = Path(..., description="Scope ID"),
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """Remove a scope from an editor account."""
    try:
        await EditorManagementService.remove_editor_scope_by_public_id(
            db=db,
            editor_public_id=editor_public_id,
            scope_id=scope_id,
            removed_by=auth.editor
        )

    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error removing scope {scope_id} from editor {editor_public_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while removing the scope"
        )