from fastapi import APIRout<PERSON>, Depends, Path, HTTPException, status
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireAdmin, EditorAuthResponse
)

from services.draft_management.draft_service import DraftManagementService
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ValidationError, PermissionDeniedError
)
from loguru import logger

router = APIRouter()

@router.delete(
    "/drafts/{draft_id}",
    status_code=status.HTTP_200_OK,
    summary="Delete Draft",
    description="Permanently delete a draft exercise"
)
async def delete_draft(
    draft_id: int = Path(..., description="Draft ID to delete"),
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """
    Permanently delete a draft exercise.
    
    This is a hard delete operation that completely removes the draft
    and all associated data from the database.
    
    Requirements:
    - Admin must have appropriate scope
    - Draft cannot be in PUBLISHED status
    - Operation is irreversible
    """
    try:
        # Delete the draft
        success = await DraftManagementService.delete_draft_by_admin(
            db=db,
            draft_id=draft_id,
            admin=auth.editor
        )
        
        return {
            "success": success,
            "message": f"Draft {draft_id} has been permanently deleted",
            "draft_id": draft_id
        }
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.message
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error deleting draft {draft_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while deleting the draft"
        )
