from fastapi import APIRouter, Depends, Path, HTTPException, status
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireAdmin, EditorAuthResponse
)
from api.v1.editorial.admin.schemas.request import PublishDraftRequest
from api.v1.editorial.admin.schemas.response import PublishResultResponse
from services.draft_management.publishing_service import PublishingService
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ValidationError, PermissionDeniedError
)
from loguru import logger

router = APIRouter()

@router.post(
    "/drafts/{draft_public_id}/publish",
    response_model=PublishResultResponse,
    status_code=status.HTTP_200_OK,
    summary="Publish Draft to Production",
    description="Publish an accepted draft to production as a new exercise"
)
async def publish_draft(
    draft_public_id: str = Path(..., description="Draft public ID to publish"),
    request: PublishDraftRequest = PublishDraftRequest(),
    auth: EditorAuthResponse = Depends(RequireAdmin),
    db: Session = Depends(get_db)
):
    """
    Publish a draft to production.

    Requirements:
    - Draft must be in ACCEPTED status
    - Admin must have appropriate scope
    - Creates new exercise in production
    - Copies media files to production paths
    - Updates draft status to PUBLISHED
    """
    try:
        # Look up draft by public ID to get internal ID
        from db.models.draft_exercise import DraftExercise
        draft = db.query(DraftExercise).filter_by(public_id=draft_public_id).first()
        if not draft:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Draft with public ID {draft_public_id} not found"
            )

        # Publish the draft using internal ID
        result = await PublishingService.publish_draft(
            db=db,
            draft_id=draft.id,
            admin=auth.editor,
            publish_notes=request.publish_notes,
            batch_id=request.batch_id
        )

        # Commit the transaction
        db.commit()

        return PublishResultResponse(
            success=True,
            message="Draft published successfully to production",
            draft_id=draft.id,
            exercise_id=result["exercise_id"],
            published_at=result["published_at"],
            batch_id=result.get("batch_id")
        )
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.message
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error publishing draft {draft_public_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while publishing the draft"
        )