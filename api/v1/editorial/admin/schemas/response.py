from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List, Dict, Any
from datetime import datetime



class PublishResultResponse(BaseModel):
    """Response for successful draft publication"""
    model_config = ConfigDict(from_attributes=True)
    
    success: bool
    message: str
    draft_id: int
    exercise_id: int = Field(..., description="New production exercise ID")
    published_at: datetime
    batch_id: Optional[int] = None


class BulkPublishResponse(BaseModel):
    """Response for bulk publishing operation"""
    success: bool
    message: str
    batch_id: int
    total_drafts: int
    successful_publishes: int
    failed_publishes: int
    results: List[Dict[str, Any]] = Field(..., description="Individual publish results")


class EditorAccountResponse(BaseModel):
    """Editor account details"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    public_id: str
    email: str
    role: str
    is_active: bool
    created_at: datetime
    last_login_at: Optional[datetime] = None
    total_drafts_reviewed: int = 0
    total_drafts_accepted: int = 0
    scopes: List[Dict[str, Any]] = Field(default_factory=list)


class EditorListResponse(BaseModel):
    """List of editor accounts"""
    editors: List[EditorAccountResponse]
    total: int
    page: int
    limit: int
    total_pages: int





class MetricsOverviewResponse(BaseModel):
    """Editorial metrics overview"""
    period: Dict[str, Any] = Field(..., description="Time period for metrics")
    draft_metrics: Dict[str, int] = Field(..., description="Draft counts by status")
    editor_metrics: Dict[str, int] = Field(..., description="Active editors and performance")
    publishing_metrics: Dict[str, int] = Field(..., description="Publishing statistics")
    processing_times: Dict[str, float] = Field(..., description="Average processing times")


class EditorPerformanceResponse(BaseModel):
    """Individual editor performance metrics"""
    editor_id: int
    editor_name: str
    period: Dict[str, Any]
    drafts_reviewed: int
    drafts_accepted: int
    drafts_rejected: int
    average_review_time_hours: float
    acceptance_rate: float
    current_assignments: int


class DraftStatusMetricsResponse(BaseModel):
    """Draft status distribution metrics"""
    total_drafts: int
    by_status: Dict[str, int]
    by_subject: Dict[str, int]
    by_assigned_editor: Dict[str, int]
    aging_report: Dict[str, int]


class SubjectSummaryResponse(BaseModel):
    """Subject summary for content hierarchy"""
    id: str = Field(..., description="Subject public ID")
    name: str
    public_id: str
    order: Optional[int] = None
    icon: Optional[str] = None
    total_chapters: int
    total_learning_nodes: int


class SubjectListResponse(BaseModel):
    """List of subjects for scope assignment"""
    subjects: List[SubjectSummaryResponse]


class ChapterSummaryResponse(BaseModel):
    """Chapter summary for content hierarchy"""
    id: str = Field(..., description="Chapter public ID")
    title: str
    public_id: str
    order: Optional[int] = None
    total_learning_nodes: int


class ChapterListResponse(BaseModel):
    """List of chapters for a subject"""
    subject: Dict[str, str] = Field(..., description="Subject info with id and name")
    chapters: List[ChapterSummaryResponse]


class LearningNodeSummaryResponse(BaseModel):
    """Learning node summary for content hierarchy"""
    id: str = Field(..., description="Learning node public ID")
    title: str
    public_id: str
    order: Optional[int] = None


class LearningNodeListResponse(BaseModel):
    """List of learning nodes for a chapter"""
    chapter: Dict[str, str] = Field(..., description="Chapter info with id, title, and subject_name")
    learning_nodes: List[LearningNodeSummaryResponse]