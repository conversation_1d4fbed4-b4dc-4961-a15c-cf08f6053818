from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


class PublishDraftRequest(BaseModel):
    """Request to publish a draft to production"""
    publish_notes: Optional[str] = Field(None, description="Admin notes about the publication")
    batch_id: Optional[int] = Field(None, description="Batch ID for bulk publishing")


class RejectDraftRequest(BaseModel):
    """Request to reject a draft"""
    rejection_reason: str = Field(..., min_length=10, description="Detailed reason for rejection")
    suggested_changes: Optional[List[str]] = Field(None, description="Specific changes requested")



class BulkPublishRequest(BaseModel):
    """Request to publish multiple drafts"""
    draft_ids: List[int] = Field(..., min_items=1, max_items=100, description="Draft IDs to publish")
    batch_name: str = Field(..., min_length=1, max_length=255, description="Name for this batch")
    batch_notes: Optional[str] = Field(None, description="Notes about this batch publication")


class EditorManagementRequest(BaseModel):
    """Request to create or update an editor account"""
    email: str = Field(..., description="Editor email address")
    is_active: bool = Field(True, description="Whether account is active")
    role: str = Field("EDITOR", description="EDITOR or ADMIN")
    password: Optional[str] = Field(None, min_length=8, description="Optional password for new accounts")


class EditorScopeRequest(BaseModel):
    """Request to manage editor scopes"""
    scope_type: str = Field(..., description="SUBJECT, CHAPTER, or LEARNING_NODE")
    scope_value: str = Field(..., description="Scope identifier")
    is_active: bool = Field(True, description="Whether scope is active")


class AuditQueryRequest(BaseModel):
    """Request to query audit logs"""
    draft_id: Optional[int] = Field(None, description="Filter by draft ID")
    editor_id: Optional[int] = Field(None, description="Filter by editor ID")
    action: Optional[str] = Field(None, description="Filter by action type")
    date_from: Optional[datetime] = Field(None, description="Start date for filtering")
    date_to: Optional[datetime] = Field(None, description="End date for filtering")
    page: int = Field(1, ge=1, description="Page number")
    limit: int = Field(20, ge=1, le=100, description="Items per page")


class MetricsQueryRequest(BaseModel):
    """Request to query editorial metrics"""
    editor_id: Optional[int] = Field(None, description="Filter by editor")
    subject_id: Optional[str] = Field(None, description="Filter by subject")
    date_from: Optional[datetime] = Field(None, description="Start date")
    date_to: Optional[datetime] = Field(None, description="End date")
    metric_type: str = Field("overview", description="overview, editor_performance, draft_status")