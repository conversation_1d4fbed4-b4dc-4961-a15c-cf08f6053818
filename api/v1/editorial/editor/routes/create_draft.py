from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireEditor, EditorAuthResponse
)
from api.v1.editorial.editor.schemas.request import DraftCreateRequest
from api.v1.editorial.editor.schemas.response import DraftCreateResponse
from services.draft_management.draft_service import DraftManagementService
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, PermissionDeniedError, ValidationError
)
from loguru import logger

router = APIRouter()

@router.post(
    "/drafts",
    response_model=DraftCreateResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create Draft",
    description="Create a new draft exercise"
)
async def create_draft(
    request: DraftCreateRequest,
    auth: EditorAuthResponse = Depends(RequireEditor),
    db: Session = Depends(get_db)
):
    """
    Create a new draft exercise.
    
    The draft will be automatically assigned to the creating editor
    and set to IN_REVIEW status.
    """
    try:
        # Create the draft
        draft = await DraftManagementService.create_draft(
            db=db,
            editor=auth.editor,
            exercise_type=request.exercise_type,
            difficulty=request.difficulty,
            data_json=request.data_json,
            solution_json=request.solution_json,
            learning_node_ids=request.learning_node_ids
        )
        
        # Create minimal response for draft creation
        from api.v1.editorial.editor.schemas.response import MinimalDraftResponse

        # Get the draft with relationships loaded
        db.refresh(draft)

        # Build minimal response with only essential fields
        draft_response = MinimalDraftResponse(
            id=draft.id,
            public_id=draft.public_id,
            status=draft.status,
            exercise_type=draft.exercise_type,
            difficulty=draft.difficulty,
            data_json=draft.data_json,
            solution_json=draft.solution_json,
            created_at=draft.created_at,
            updated_at=draft.updated_at
        )

        return DraftCreateResponse(
            success=True,
            message="Draft created successfully",
            draft=draft_response
        )
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.message
        )
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=e.message
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error creating draft: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while creating the draft"
        )
