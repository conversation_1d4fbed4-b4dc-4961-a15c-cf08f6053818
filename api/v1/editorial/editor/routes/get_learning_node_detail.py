from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, Path, status
from sqlalchemy.orm import Session, joinedload
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireEditor, EditorAuthResponse
)
from api.v1.editorial.editor.schemas.response import LearningNodeDetailResponse
from db.models import LearningNode, Chapter, Subject, EditorAccount, EditorScope
from sqlalchemy import or_, and_
from loguru import logger

router = APIRouter()

@router.get(
    "/learning-nodes/{learning_node_id}",
    response_model=LearningNodeDetailResponse,
    status_code=status.HTTP_200_OK,
    summary="Get Learning Node Detail",
    description="Get detailed information about a specific learning node assigned to the editor"
)
async def get_learning_node_detail(
    learning_node_id: str = Path(..., description="Learning node public ID"),
    auth: EditorAuthResponse = Depends(RequireEditor),
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific learning node.
    Only returns nodes that the editor has access to based on their scopes.
    """
    logger.info(f"Getting learning node detail for editor: {auth.email}, node: {learning_node_id}")
    
    # Get editor with scopes
    editor = db.query(EditorAccount).filter(
        EditorAccount.id == auth.editor_id
    ).options(
        joinedload(EditorAccount.scopes)
    ).first()
    
    if not editor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Editor not found"
        )
    
    # Build query conditions based on editor scopes
    learning_node_conditions = []
    
    for scope in editor.scopes:
        if scope.learning_node_id:
            # Specific learning node scope
            learning_node_conditions.append(
                LearningNode.id == scope.learning_node_id
            )
        elif scope.chapter_id:
            # All learning nodes in this chapter
            learning_node_conditions.append(
                LearningNode.chapter_id == scope.chapter_id
            )
        elif scope.subject_id:
            # All learning nodes in all chapters of this subject
            learning_node_conditions.append(
                and_(
                    Chapter.subject_id == scope.subject_id,
                    LearningNode.chapter_id == Chapter.id
                )
            )
    
    if not learning_node_conditions:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="No learning node access permissions"
        )
    
    # Query the specific learning node with access check
    query = db.query(LearningNode).join(Chapter).join(Subject).filter(
        LearningNode.public_id == learning_node_id
    )
    
    if len(learning_node_conditions) == 1:
        query = query.filter(learning_node_conditions[0])
    else:
        query = query.filter(or_(*learning_node_conditions))
    
    # Load with relationships
    node = query.options(
        joinedload(LearningNode.chapter).joinedload(Chapter.subject)
    ).first()
    
    if not node:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Learning node not found or access denied"
        )
    
    # Count drafts for this learning node (TODO: implement when draft associations are ready)
    draft_count = 0
    
    # Count published exercises (TODO: implement when exercise associations are ready)
    exercises_count = len(node.exercise_associations) if hasattr(node, 'exercise_associations') else 0
    
    return {
        "id": node.public_id,
        "title": node.title,
        "description": getattr(node, 'description', None),
        "order": node.ordering,
        "exercises_count": exercises_count,
        "draft_count": draft_count,
        "chapter": {
            "id": node.chapter.public_id,
            "title": node.chapter.title,
            "subject": {
                "id": node.chapter.subject.public_id,
                "name": node.chapter.subject.name
            }
        }
    }
