from fastapi import APIRouter
from .upload_media import router as upload_media_router
from .delete_media import router as delete_media_router
from .list_media import router as list_media_router

# Create media router
media_router = APIRouter(tags=["Editorial - Media"])

# Include all media sub-routers
media_router.include_router(upload_media_router)
media_router.include_router(delete_media_router)
media_router.include_router(list_media_router)