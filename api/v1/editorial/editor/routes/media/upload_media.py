from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, Path
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireEditor, EditorAuthResponse
)
from db.models import DraftExercise, DraftExerciseStatus, DraftMediaType
from services.media.draft_media_service import DraftMediaService
from services.draft_management.scope_service import ScopeService
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, PermissionDeniedError, ValidationError
)
from core.config.media_config import (
    MAX_IMAGE_SIZE, MAX_AUDIO_SIZE,
    ALLOWED_IMAGE_TYPES, ALLOWED_AUDIO_TYPES
)
from loguru import logger
from typing import Optional
import json

router = APIRouter()

@router.post(
    "/drafts/{draft_id}/media",
    status_code=status.HTTP_201_CREATED,
    summary="Upload Draft Media",
    description="Upload an image or audio file for a draft exercise"
)
async def upload_draft_media(
    draft_id: int = Path(..., description="Draft ID"),
    file: UploadFile = File(..., description="Media file to upload"),
    media_type: str = Form(..., description="Media type: IMAGE or AUDIO"),
    metadata: Optional[str] = Form(None, description="Optional JSON metadata"),
    auth: EditorAuthResponse = Depends(RequireEditor),
    db: Session = Depends(get_db)
):
    """
    Upload media file for a draft exercise.
    
    Requirements:
    - Draft must be assigned to the current editor
    - Draft must be in IN_REVIEW status
    - File size limits apply (configured per environment)
    - Supported formats: Images (PNG, JPG, WEBP), Audio (MP3, WAV)
    """
    try:
        # Validate media type
        try:
            media_type_enum = DraftMediaType[media_type.upper()]
        except KeyError:
            raise ValidationError(f"Invalid media type: {media_type}. Must be IMAGE or AUDIO")
        
        # Parse metadata if provided
        parsed_metadata = None
        if metadata:
            try:
                parsed_metadata = json.loads(metadata)
            except json.JSONDecodeError:
                raise ValidationError("Invalid JSON metadata")
        
        # Get draft and verify access
        draft = db.query(DraftExercise).filter_by(id=draft_id).first()
        if not draft:
            raise NotFoundError(f"Draft {draft_id} not found")
        
        # Check editor has scope
        if not await ScopeService.editor_has_scope_for_draft(db, auth.editor, draft):
            raise PermissionDeniedError("You don't have access to this draft")
        
        # Verify draft is assigned to this editor
        if draft.assigned_editor_id != auth.editor.id:
            raise PermissionDeniedError("Draft is not assigned to you")
        
        # Verify draft is in correct status
        if draft.status != DraftExerciseStatus.IN_REVIEW:
            raise ValidationError(f"Draft must be in IN_REVIEW status, current: {draft.status}")
        
        # Validate file type and size limits
        
        if media_type_enum == DraftMediaType.IMAGE:
            if file.content_type not in ALLOWED_IMAGE_TYPES:
                raise ValidationError(f"Invalid image type: {file.content_type}")
            if file.size and file.size > MAX_IMAGE_SIZE:
                raise ValidationError(f"Image file too large. Maximum size: {MAX_IMAGE_SIZE // (1024*1024)}MB")
        else:
            if file.content_type not in ALLOWED_AUDIO_TYPES:
                raise ValidationError(f"Invalid audio type: {file.content_type}")
            if file.size and file.size > MAX_AUDIO_SIZE:
                raise ValidationError(f"Audio file too large. Maximum size: {MAX_AUDIO_SIZE // (1024*1024)}MB")
        
        # Initialize media service
        media_service = DraftMediaService()
        
        # Upload the file
        draft_media = media_service.upload_draft_media(
            db=db,
            draft_exercise_id=draft_id,
            file=file.file,
            filename=file.filename,
            media_type=media_type_enum,
            metadata=parsed_metadata
        )
        
        # Commit the transaction
        db.commit()
        
        # Generate URL for the uploaded media
        media_url = media_service.get_draft_media_url(draft_media.storage_path)
        
        return {
            "success": True,
            "message": f"{media_type} uploaded successfully",
            "media": {
                "id": draft_media.id,
                "media_type": draft_media.media_type.value,
                "storage_path": draft_media.storage_path,
                "original_filename": draft_media.original_filename,
                "content_type": draft_media.content_type,
                "url": media_url,
                "created_at": draft_media.created_at.isoformat()
            }
        }
        
    except (NotFoundError, PermissionDeniedError, ValidationError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error uploading media for draft {draft_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while uploading the media"
        )