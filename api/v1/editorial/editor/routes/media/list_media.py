from fastapi import APIRouter, Depends, Path, HTTPException, status
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireEditor, EditorAuthResponse
)
from db.models import DraftMediaFile, DraftExercise
from services.media.draft_media_service import DraftMediaService
from services.draft_management.scope_service import ScopeService
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, PermissionDeniedError
)
from loguru import logger
from typing import List, Dict, Any

router = APIRouter()

@router.get(
    "/drafts/{draft_id}/media",
    response_model=List[Dict[str, Any]],
    status_code=status.HTTP_200_OK,
    summary="List Draft Media",
    description="Get all media files for a draft exercise"
)
async def list_draft_media(
    draft_id: int = Path(..., description="Draft ID"),
    auth: EditorAuthResponse = Depends(RequireEditor),
    db: Session = Depends(get_db)
):
    """
    List all media files associated with a draft exercise.
    
    Requirements:
    - Editor must have scope to access the draft
    - Returns media with presigned URLs for access
    """
    try:
        # Get draft and verify access
        draft = db.query(DraftExercise).filter_by(id=draft_id).first()
        if not draft:
            raise NotFoundError(f"Draft {draft_id} not found")
        
        # Check editor has scope
        if not await ScopeService.editor_has_scope_for_draft(db, auth.editor, draft):
            raise PermissionDeniedError("You don't have access to this draft")
        
        # Get all media for the draft
        media_files = db.query(DraftMediaFile).filter_by(
            draft_exercise_id=draft_id
        ).order_by(DraftMediaFile.created_at.desc()).all()
        
        # Initialize media service for URL generation
        media_service = DraftMediaService()
        
        # Build response
        media_list = []
        for media in media_files:
            media_url = media_service.get_draft_media_url(media.storage_path)
            
            media_list.append({
                "id": media.id,
                "media_type": media.media_type.value,
                "storage_path": media.storage_path,
                "original_filename": media.original_filename,
                "content_type": media.content_type,
                "url": media_url,
                "metadata": media.media_metadata or {},
                "created_at": media.created_at.isoformat(),
                "production_media_id": media.production_media_id,
                "copied_to_production": media.copied_to_production_at is not None
            })
        
        return media_list
        
    except (NotFoundError, PermissionDeniedError) as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN if isinstance(e, PermissionDeniedError) else status.HTTP_404_NOT_FOUND,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error listing media for draft {draft_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while listing media"
        )