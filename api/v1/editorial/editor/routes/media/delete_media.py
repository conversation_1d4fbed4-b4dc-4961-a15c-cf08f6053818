from fastapi import APIRouter, Depends, Path, HTTPException, status
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireEditor, EditorAuthResponse
)
from db.models import DraftMediaFile, DraftExercise, DraftExerciseStatus
from services.media.draft_media_service import DraftMediaService
from services.draft_management.scope_service import ScopeService
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, PermissionDeniedError, ValidationError
)
from loguru import logger

router = APIRouter()

@router.delete(
    "/drafts/{draft_id}/media/{media_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Draft Media",
    description="Delete a media file from a draft exercise"
)
async def delete_draft_media(
    draft_id: int = Path(..., description="Draft ID"),
    media_id: int = Path(..., description="Media ID to delete"),
    auth: EditorAuthResponse = Depends(RequireEditor),
    db: Session = Depends(get_db)
):
    """
    Delete a media file from a draft exercise.
    
    Requirements:
    - Draft must be assigned to the current editor
    - Draft must be in IN_REVIEW status
    - Media must belong to the specified draft
    """
    try:
        # Get draft and verify access
        draft = db.query(DraftExercise).filter_by(id=draft_id).first()
        if not draft:
            raise NotFoundError(f"Draft {draft_id} not found")
        
        # Check editor has scope
        if not await ScopeService.editor_has_scope_for_draft(db, auth.editor, draft):
            raise PermissionDeniedError("You don't have access to this draft")
        
        # Verify draft is assigned to this editor
        if draft.assigned_editor_id != auth.editor.id:
            raise PermissionDeniedError("Draft is not assigned to you")
        
        # Verify draft is in correct status
        if draft.status != DraftExerciseStatus.IN_REVIEW:
            raise ValidationError(f"Draft must be in IN_REVIEW status, current: {draft.status}")
        
        # Get media file
        media = db.query(DraftMediaFile).filter_by(
            id=media_id,
            draft_exercise_id=draft_id
        ).first()
        
        if not media:
            raise NotFoundError(f"Media {media_id} not found for draft {draft_id}")
        
        # Initialize media service and delete
        media_service = DraftMediaService()
        media_service.delete_draft_media(db, media)
        
        # Commit the transaction
        db.commit()
        
        logger.info(f"Editor {auth.editor.id} deleted media {media_id} from draft {draft_id}")
        
    except (NotFoundError, PermissionDeniedError, ValidationError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error deleting media {media_id} from draft {draft_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while deleting the media"
        )