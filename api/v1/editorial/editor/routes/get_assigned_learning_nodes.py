from fastapi import APIRouter, Depends, status
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireEditor, EditorAuthResponse
)
from api.v1.editorial.editor.schemas.response import AssignedLearningNodesResponse
from services.editorial.editor_service import EditorService
from loguru import logger

router = APIRouter()

@router.get(
    "/learning-nodes",
    response_model=AssignedLearningNodesResponse,
    status_code=status.HTTP_200_OK,
    summary="Get Assigned Learning Nodes",
    description="Get all learning nodes assigned to the current editor based on their scopes"
)
async def get_assigned_learning_nodes(
    auth: EditorAuthResponse = Depends(RequireEditor),
    db: Session = Depends(get_db)
):
    """
    Get all learning nodes that the editor has access to based on their scopes.
    Returns nodes grouped by subject and chapter for easy navigation.
    """
    logger.info(f"Getting assigned learning nodes for editor: {auth.email}")
    
    nodes = await EditorService.get_assigned_learning_nodes(
        db=db,
        editor_id=auth.editor_id
    )
    
    return nodes