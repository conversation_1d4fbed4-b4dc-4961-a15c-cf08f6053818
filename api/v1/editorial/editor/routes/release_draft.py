from fastapi import APIRouter, Depends, Path, HTTPException, status
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireEditor, EditorAuthResponse
)
from api.v1.editorial.editor.schemas.request import DraftReleaseRequest
from api.v1.editorial.editor.schemas.response import DraftActionResponse
from services.draft_management.assignment_service import AssignmentService
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, PermissionDeniedError, ValidationError
)
from loguru import logger

router = APIRouter()

@router.post(
    "/drafts/{draft_id}/release",
    response_model=DraftActionResponse,
    status_code=status.HTTP_200_OK,
    summary="Release Draft",
    description="Release a draft assignment to make it available for other editors"
)
async def release_draft(
    draft_id: int = Path(..., description="Draft ID"),
    request: DraftReleaseRequest = DraftReleaseRequest(),
    auth: EditorAuthResponse = Depends(RequireEditor),
    db: Session = Depends(get_db)
):
    """
    Release a draft assignment.
    
    Only the assigned editor or an admin can release a draft.
    Released drafts return to NEW status and can be claimed by other editors.
    """
    try:
        # Release the draft
        draft = await AssignmentService.release_draft(
            db=db,
            draft_id=draft_id,
            editor=auth.editor
        )
        
        # Get updated draft details for response
        from .draft_detail import get_draft_detail
        detail_response = await get_draft_detail(draft_id, auth, db)
        
        return DraftActionResponse(
            success=True,
            message="Draft released successfully and is now available for other editors",
            draft=detail_response
        )
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.message
        )
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=e.message
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error releasing draft: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while releasing the draft"
        )