from fastapi import APIRouter
from .get_drafts import router as get_drafts_router
from .draft_detail import router as draft_detail_router
from .create_draft import router as create_draft_router
from .update_draft import router as update_draft_router
from .claim_draft import router as claim_draft_router
from .accept_draft import router as accept_draft_router
from .release_draft import router as release_draft_router
from .reject_draft import router as reject_draft_router
from .media import media_router
from .get_current_editor import router as get_current_editor_router
from .get_assigned_learning_nodes import router as get_assigned_learning_nodes_router
from .get_learning_node_detail import router as get_learning_node_detail_router
from .get_learning_node_content import router as get_learning_node_content_router

# Create main editor router
editor_router = APIRouter(prefix="/editor", tags=["Editorial - Editor"])

# Include all sub-routers
editor_router.include_router(get_current_editor_router)
editor_router.include_router(get_assigned_learning_nodes_router)
editor_router.include_router(get_learning_node_detail_router)
editor_router.include_router(get_learning_node_content_router)
editor_router.include_router(get_drafts_router)
editor_router.include_router(draft_detail_router)
editor_router.include_router(create_draft_router)
editor_router.include_router(update_draft_router)
editor_router.include_router(claim_draft_router)
editor_router.include_router(accept_draft_router)
editor_router.include_router(release_draft_router)
editor_router.include_router(reject_draft_router)
editor_router.include_router(media_router)