from fastapi import APIRouter, Depends, Path, HTTPException, status
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireEditor, EditorAuthResponse
)
from api.v1.editorial.editor.schemas.response import (
    DraftDetailResponse, MediaFileResponse,
    AssignedEditorResponse, LearningNodeInfo
)
from services.draft_management.draft_service import DraftManagementService
from services.media.draft_media_service import DraftMediaService
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, PermissionDeniedError
)
from loguru import logger

router = APIRouter()

@router.get(
    "/drafts/{draft_id}",
    response_model=DraftDetailResponse,
    status_code=status.HTTP_200_OK,
    summary="Get Draft Detail",
    description="Get detailed information about a specific draft"
)
async def get_draft_detail(
    draft_id: int = Path(..., description="Draft ID"),
    auth: EditorAuthResponse = Depends(RequireEditor),
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a draft.
    
    Includes exercise data, solution, media files, and recent audit history.
    Access is restricted based on editor scope.
    """
    try:
        # Get draft with permission check
        draft = await DraftManagementService.get_draft_detail(
            db=db,
            draft_id=draft_id,
            editor=auth.editor
        )
        

        
        # Transform media files
        media_service = DraftMediaService()
        media_files = []
        for media in draft.media_files:
            media_response = MediaFileResponse(
                type=media.type.value,
                r2_url=media_service.get_draft_media_url(media.storage_path),
                mime_type=media.mime_type
            )
            
            # Add type-specific metadata
            if media.media_metadata:
                if media.type.value == "image":
                    media_response.width = media.media_metadata.get("width")
                    media_response.height = media.media_metadata.get("height")
                else:  # audio
                    media_response.duration = media.media_metadata.get("duration")
            
            media_files.append(media_response)
        

        
        # Transform assigned editor
        assigned_editor = None
        if draft.assigned_editor:
            assigned_editor = AssignedEditorResponse(
                id=draft.assigned_editor.id,
                email=draft.assigned_editor.email,
                role=draft.assigned_editor.role
            )
        
        # Transform learning nodes
        learning_nodes = [
            LearningNodeInfo(
                title=assoc.learning_node.title,
                public_id=assoc.learning_node.public_id
            )
            for assoc in draft.learning_node_associations
        ]
        
        return DraftDetailResponse(
            id=draft.id,
            public_id=draft.public_id,
            status=draft.status,
            exercise_type=draft.exercise_type,
            difficulty=draft.difficulty,
            assigned_editor=assigned_editor,
            data_json=draft.data_json,
            solution_json=draft.solution_json,
            media=media_files,

            reject_reason=draft.reject_reason,
            created_at=draft.created_at,
            updated_at=draft.updated_at,
            published_at=draft.published_at,
            learning_nodes=learning_nodes
        )
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.message
        )
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error fetching draft detail: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching draft details"
        )