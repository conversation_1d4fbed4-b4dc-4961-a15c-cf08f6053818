from fastapi import APIRouter, Depends, status
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireEditor, EditorAuthResponse
)
from api.v1.editorial.editor.schemas.response import CurrentEditorResponse
from services.editorial.editor_service import EditorService
from loguru import logger

router = APIRouter()

@router.get(
    "/me",
    response_model=CurrentEditorResponse,
    status_code=status.HTTP_200_OK,
    summary="Get Current Editor",
    description="Get information about the currently authenticated editor"
)
async def get_current_editor(
    auth: EditorAuthResponse = Depends(RequireEditor),
    db: Session = Depends(get_db)
):
    """
    Get current editor information including role and active scopes.
    """
    logger.info(f"Getting current editor info for: {auth.email}")
    
    editor_info = await EditorService.get_current_editor_info(
        db=db,
        editor_id=auth.editor_id
    )
    
    return editor_info