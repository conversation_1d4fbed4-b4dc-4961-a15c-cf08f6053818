from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from db.models import DraftExerciseStatus, EditorRole

class SubjectInfo(BaseModel):
    name: str
    public_id: str
    
    model_config = {
        "from_attributes": True
    }

class ChapterInfo(BaseModel):
    title: str
    public_id: Optional[str] = None
    
    model_config = {
        "from_attributes": True
    }

class LearningNodeInfo(BaseModel):
    title: str
    public_id: str
    
    model_config = {
        "from_attributes": True
    }

class DraftListItemResponse(BaseModel):
    id: int
    public_id: str
    title: str = Field(..., description="Exercise prompt or title")
    subject: SubjectInfo
    chapter: ChapterInfo
    learning_node: LearningNodeInfo
    status: DraftExerciseStatus
    updated_at: datetime
    assigned_editor: Optional[str] = Field(None, description="Email of assigned editor")
    media_count: int = Field(0, description="Number of attached media files")
    
    model_config = {
        "from_attributes": True
    }

class DraftListResponse(BaseModel):
    drafts: List[DraftListItemResponse]
    total: int = Field(..., description="Total number of drafts matching filters")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Items per page")
    total_pages: int = Field(..., description="Total number of pages")

class MediaFileResponse(BaseModel):
    type: str = Field(..., description="'image' or 'audio'")
    r2_url: str = Field(..., description="Public URL to access the media")
    width: Optional[int] = Field(None, description="Image width in pixels")
    height: Optional[int] = Field(None, description="Image height in pixels")
    duration: Optional[float] = Field(None, description="Audio duration in seconds")
    mime_type: Optional[str] = Field(None, description="MIME type of the file")



class AssignedEditorResponse(BaseModel):
    id: int
    email: str
    role: EditorRole
    
    model_config = {
        "from_attributes": True
    }

class DraftDetailResponse(BaseModel):
    id: int
    public_id: str
    status: DraftExerciseStatus
    exercise_type: str
    difficulty: Optional[str]
    assigned_editor: Optional[AssignedEditorResponse]
    data_json: Dict[str, Any]
    solution_json: Optional[Dict[str, Any]]
    media: List[MediaFileResponse]

    reject_reason: Optional[str] = Field(None, description="Rejection reason if rejected by admin")
    created_at: datetime
    updated_at: datetime
    published_at: Optional[datetime] = None
    learning_nodes: List[LearningNodeInfo]
    
    model_config = {
        "from_attributes": True
    }

class MinimalDraftResponse(BaseModel):
    """Minimal draft response for creation - only essential fields"""
    id: int
    public_id: str
    status: DraftExerciseStatus
    exercise_type: str
    difficulty: Optional[str]
    data_json: Dict[str, Any]
    solution_json: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    }

class DraftActionResponse(BaseModel):
    success: bool = True
    message: str
    draft: DraftDetailResponse

class DraftCreateResponse(BaseModel):
    """Response for draft creation with minimal fields"""
    success: bool = True
    message: str
    draft: MinimalDraftResponse


class EditorScopeInfoResponse(BaseModel):
    """Editor scope information"""
    id: int
    type: str = Field(..., description="SUBJECT, CHAPTER, or LEARNING_NODE")
    name: str = Field(..., description="Name of the scoped item")
    public_id: str = Field(..., description="Public ID of the scoped item")
    subject_name: Optional[str] = Field(None, description="Subject name if applicable")
    subject_id: Optional[str] = Field(None, description="Subject ID if applicable")
    chapter_name: Optional[str] = Field(None, description="Chapter name if applicable") 
    chapter_id: Optional[str] = Field(None, description="Chapter ID if applicable")


class CurrentEditorResponse(BaseModel):
    """Current editor information"""
    model_config = {
        "from_attributes": True
    }
    
    id: int
    public_id: str
    email: str
    role: str
    is_active: bool
    created_at: datetime
    scopes: List[EditorScopeInfoResponse]


class LearningNodeDetails(BaseModel):
    """Learning node information for assigned nodes"""
    id: str = Field(..., description="Learning node public ID")
    title: str
    order: Optional[int] = None
    exercises_count: int = Field(0, description="Number of published exercises")
    draft_count: int = Field(0, description="Total number of draft exercises (NEW + IN_REVIEW)")
    new_drafts: int = Field(0, description="Number of NEW draft exercises")
    in_review_drafts: int = Field(0, description="Number of IN_REVIEW draft exercises")


class ChapterWithNodes(BaseModel):
    """Chapter with its learning nodes"""
    id: str = Field(..., description="Chapter public ID")
    title: str
    order: Optional[int] = None
    learning_nodes: List[LearningNodeDetails]


class SubjectWithChapters(BaseModel):
    """Subject with its chapters and learning nodes"""
    id: str = Field(..., description="Subject public ID")
    name: str
    order: Optional[int] = None
    chapters: List[ChapterWithNodes]


class AssignedLearningNodesResponse(BaseModel):
    """Response for assigned learning nodes"""
    total_nodes: int
    subjects: List[SubjectWithChapters]


class LearningNodeSubjectInfo(BaseModel):
    """Subject information for learning node detail"""
    id: str = Field(..., description="Subject public ID")
    name: str


class LearningNodeChapterInfo(BaseModel):
    """Chapter information for learning node detail"""
    id: str = Field(..., description="Chapter public ID")
    title: str
    subject: LearningNodeSubjectInfo


class LearningNodeDetailResponse(BaseModel):
    """Detailed learning node information"""
    id: str = Field(..., description="Learning node public ID")
    title: str
    description: Optional[str] = None
    order: Optional[int] = None
    exercises_count: int = Field(0, description="Number of published exercises")
    draft_count: int = Field(0, description="Number of draft exercises")
    chapter: LearningNodeChapterInfo


class LearningNodeContentResponse(BaseModel):
    """Learning node content information for editors"""
    id: str = Field(..., description="Learning node public ID")
    title: str
    description: Optional[str] = None
    node_type: str = Field(..., description="Type of learning node (MATH, GRAMMAR, etc.)")
    notes: Optional[str] = Field(None, description="Learning node notes content")
    chapter: LearningNodeChapterInfo