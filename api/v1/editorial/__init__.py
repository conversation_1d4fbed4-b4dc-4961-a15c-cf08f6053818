from fastapi import APIRouter
from .auth.routes import auth_router
from .editor.routes import editor_router
from .admin.routes import admin_router

# Create main editorial router
editorial_router = APIRouter(prefix="/editorial", tags=["Editorial"])

# Include auth, editor, and admin routers
editorial_router.include_router(auth_router)
editorial_router.include_router(editor_router)
editorial_router.include_router(admin_router)