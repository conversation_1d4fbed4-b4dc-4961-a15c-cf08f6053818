from fastapi import APIRouter

# Import student-specific route modules
from .routes import get_learning_node
from .routes import submit_exercise
from .routes import get_progress_overview
from .routes import get_subjects
from .routes import get_dashboard
from .routes import get_subject
from .routes import update_profile

# Create a main router for the student app section
router = APIRouter(prefix="/student")

# Include routers from the route modules
router.include_router(get_learning_node.router, tags=["Student Content"])
router.include_router(submit_exercise.router, tags=["Student Content"])
router.include_router(get_progress_overview.router, tags=["Student Progress"])  
router.include_router(get_subjects.router, tags=["Student Content"])
router.include_router(get_dashboard.router, tags=["Student Dashboard"])
router.include_router(get_subject.router, tags=["Student Content"])
router.include_router(update_profile.router, tags=["Student Profile"])
