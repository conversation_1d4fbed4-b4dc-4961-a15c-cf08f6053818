from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from db.database import get_db
from dependencies.auth_dependencies.base_auth_dependency import AuthDependencyResponse, UserType
from dependencies.auth_dependencies.auth_dependency import AuthDependency
from api.v1.app.student.schemas.request import UpdateStudentProfileRequest
from api.v1.app.student.schemas.response import UpdateStudentProfileResponse
from api.v1.app.student.services.update_student_profile_service import update_student_profile_service
from core.exception_handling.exceptions import AuthenticationError
from api.v1.common.schemas import AppErrorCode

router = APIRouter()
require_auth = AuthDependency(required=True)

@router.put(
    "/profile/update-student-profile",
    response_model=UpdateStudentProfileResponse,
    summary="Update Student Profile",
    description="Updates the profile information for a student account including name, email, and PIN."
)
def update_student_profile_route(
    update_data: UpdateStudentProfileRequest,
    db: Session = Depends(get_db),
    auth_data: AuthDependencyResponse = Depends(require_auth)
):
    if not auth_data or not auth_data.user_type == UserType.child or not auth_data.account_public_id:
        raise AuthenticationError(
            message="Not authorized! Token is invalid, missing, or not for a student account.",
            error_code=AppErrorCode.AUTHENTICATION_REQUIRED
        )

    return update_student_profile_service(
        db=db,
        student_public_id=auth_data.account_public_id,
        update_data=update_data
    )