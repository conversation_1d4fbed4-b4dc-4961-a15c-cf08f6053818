from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from db.database import get_db
from dependencies.auth_dependencies.subscription_dependency import SubscriptionDependency, SubscriptionInfo
from api.v1.app.student.services.get_subjects_service import GetSubjectsService
from api.v1.app.student.schemas.response import GetSubjectsResponse


router = APIRouter()
require_subscription = SubscriptionDependency()

@router.get(
    "/get-subjects",
    response_model=GetSubjectsResponse,
    summary="Get Subjects",
    description="Retrieves the subjects for a specific student account."
)
def get_subjects_route(
    db: Session = Depends(get_db),
    subscription_info: SubscriptionInfo = Depends(require_subscription),
    content_service: GetSubjectsService = Depends(GetSubjectsService)
):
    # Validate that this is a child account (student), not a parent
    if subscription_info.user_type != "child":
        from core.exception_handling.exceptions.custom_exceptions import AuthenticationError
        from api.v1.common.schemas import AppErrorCode
        raise AuthenticationError(
            message="This endpoint is only accessible to student accounts.",
            error_code=AppErrorCode.AUTHENTICATION_REQUIRED,
            log_message=f"Parent account {subscription_info.account_public_id} attempted to access student endpoint"
        )
    
    # SubscriptionDependency handles authentication and user type validation
    return content_service.get_subjects(
        db=db,
        student_account=subscription_info.account,  # Now guaranteed to be ChildAccount
        is_subscribed=subscription_info.is_subscribed,
        trial_active=subscription_info.trial_active,
        subscribed_subjects=subscription_info.subscribed_subjects,
        actual_subscription_type=subscription_info.actual_subscription_type
    )