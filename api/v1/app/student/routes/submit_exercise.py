from fastapi import APIRouter, Depends, status
from sqlalchemy.orm import Session
from typing import Annotated

from core.exception_handling.exceptions.custom_exceptions import AuthorizationError
from db.database import get_db
from dependencies.auth_dependencies.subscription_dependency import SubscriptionDependency, SubscriptionInfo
from dependencies.auth_dependencies.base_auth_dependency import UserType
from ..schemas.request import ExerciseSubmissionRequest
from ..schemas.response import ExerciseSubmissionResponse
from ..services.exercise_submission_service import ExerciseSubmissionService

from api.v1.common.schemas import AppErrorCode

router = APIRouter(
    prefix="/exercises",
    tags=["Student Exercises"]
)

@router.post(
    "/submit-exercise",
    response_model=ExerciseSubmissionResponse,
    summary="Submit an Exercise Answer",
    description="Allows a student to submit an answer for a specific exercise and get immediate feedback."
)
def submit_exercise_answer_route(
    submission_data: ExerciseSubmissionRequest,
    subscription_info: Annotated[SubscriptionInfo, Depends(SubscriptionDependency())],
    db: Session = Depends(get_db),
):
    if not subscription_info.user_type == UserType.child or not subscription_info.account:
        raise AuthorizationError(
            message="Only students can submit exercises",
            error_code=AppErrorCode.AUTHORIZATION_ERROR,
            status_code=status.HTTP_403_FORBIDDEN
        )

    submission_service = ExerciseSubmissionService(db)
    return submission_service.submit_exercise_answer(
        db=db,
        student_account=subscription_info.account,
        submission_data=submission_data,
        is_subscribed=subscription_info.is_subscribed,
        trial_active=subscription_info.trial_active,
        subscribed_subjects=subscription_info.subscribed_subjects,
        actual_subscription_type=subscription_info.actual_subscription_type
    )