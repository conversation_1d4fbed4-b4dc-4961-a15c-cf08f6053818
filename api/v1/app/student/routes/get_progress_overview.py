from fastapi import APIRouter, Depends
from typing import Annotated
from sqlalchemy.orm import Session

from db.database import get_db
from db.models import ChildAccount
from dependencies.auth_dependencies.auth_dependency import AuthDependency
from dependencies.auth_dependencies.base_auth_dependency import AuthDependencyResponse, UserType
from ..schemas.response import ProgressOverviewResponse
from ..services.student_progress_service import StudentProgressService
from core.exception_handling.exceptions.custom_exceptions import AuthorizationError, NotFoundError
from api.v1.common.schemas import AppErrorCode
from loguru import logger

router = APIRouter()

@router.get(
    "/{student_public_id}/progress/detailed",
    response_model=ProgressOverviewResponse,
    summary="Get Detailed Student Progress Overview",
    description="Retrieves a detailed progress overview for the specified student."
)
def get_student_progress_overview_route(
    student_public_id: str,
    auth_data: AuthDependencyResponse = Depends(AuthDependency(required=True)),
    db: Session = Depends(get_db),
    progress_service: StudentProgressService = Depends(StudentProgressService)
):
    
    logger.info(f"Getting progress overview for student {student_public_id} with auth data {auth_data.user_type} {auth_data.account_public_id}")

    # Ensure the authenticated user is the student themselves or a linked parent (not implemented here)
    # For now, only allow student to fetch their own data.

    logger.info(f"Auth data user type: {auth_data.user_type}")
    logger.info(f"Auth data account public id: {auth_data.account_public_id}")
    logger.info(f"Auth data account: {auth_data.account}")
    logger.info(f"Auth data account type: {type(auth_data.account)}")
    logger.info(f"Auth data account id: {auth_data.account.id}")
    logger.info(f"Auth data account public id: {auth_data.account.public_id}")

    if not (
        auth_data.user_type == UserType.child 
        and auth_data.account_public_id == student_public_id
    ):
        # If it's a parent, additional checks for linkage would be needed.
        # For simplicity, this example restricts to student fetching their own.
        raise AuthorizationError(
            message="You are not authorized to view this student's progress.",
            error_code=AppErrorCode.PERMISSION_DENIED
        )

    # The auth_data.account should be the ChildAccount instance if user_type is child
    if not isinstance(auth_data.account, ChildAccount):
        # This case should ideally be handled by AuthDependency or a more specific student auth dependency
        raise NotFoundError(
            message="Student account data not found in authentication token.",
            error_code=AppErrorCode.CHILD_ACCOUNT_NOT_FOUND
        )
        
    student_account: ChildAccount = auth_data.account

    return progress_service.get_progress_overview(db, student_account)