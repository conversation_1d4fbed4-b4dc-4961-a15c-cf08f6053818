from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from db.database import get_db
from dependencies.auth_dependencies.subscription_dependency import SubscriptionDependency, SubscriptionInfo
from ..services.get_learning_node_service import GetLearningNodeService
from ..schemas.response import GetLearningNodeResponse

router = APIRouter()
require_subscription = SubscriptionDependency()

@router.get(
    "/learning-nodes/{learning_node_public_id}",
    response_model=GetLearningNodeResponse,
    summary="Get Learning Node Content",
    description="Retrieves the content for a specific learning node, including its children and associated exercises."
)
def get_learning_node_content_route(
    learning_node_public_id: str,
    db: Session = Depends(get_db),
    subscription_info: SubscriptionInfo = Depends(require_subscription),
    learning_node_service: GetLearningNodeService = Depends(GetLearningNodeService)
):
    # Validate that this is a child account (student), not a parent
    if subscription_info.user_type != "child":
        from core.exception_handling.exceptions.custom_exceptions import AuthenticationError
        from api.v1.common.schemas import AppErrorCode
        raise AuthenticationError(
            message="This endpoint is only accessible to student accounts.",
            error_code=AppErrorCode.AUTHENTICATION_REQUIRED,
            log_message=f"Parent account {subscription_info.account_public_id} attempted to access student endpoint"
        )
    
    # SubscriptionDependency handles authentication and user type validation
    return learning_node_service.get_learning_node(
        db=db,
        student_account=subscription_info.account,  # Now guaranteed to be ChildAccount
        learning_node_public_id=learning_node_public_id,
        is_subscribed=subscription_info.is_subscribed,
        trial_active=subscription_info.trial_active,
        subscribed_subjects=subscription_info.subscribed_subjects,
        actual_subscription_type=subscription_info.actual_subscription_type
    )