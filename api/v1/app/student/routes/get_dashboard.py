from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from db.database import get_db
from dependencies.auth_dependencies.base_auth_dependency import UserType
from dependencies.auth_dependencies.subscription_dependency import SubscriptionDependency, SubscriptionInfo
from api.v1.app.student.schemas.response import GetStudentDashboardResponse
from api.v1.app.student.services.get_dashboard_service import GetDashboardService
from core.exception_handling.exceptions import AuthenticationError
from api.v1.common.schemas import AppErrorCode

router = APIRouter()
require_subscription = SubscriptionDependency()

@router.get(
    "/dashboard/get-dashboard",
    response_model=GetStudentDashboardResponse,
    summary="Get Student Dashboard",
    description="Retrieves the dashboard data for a student account, including profile and subjects."
)
def get_dashboard_route(
    db: Session = Depends(get_db),
    subscription_info: SubscriptionInfo = Depends(require_subscription)
):
    dashboard_service = GetDashboardService(db=db)
    
    if not subscription_info or not subscription_info.user_type == UserType.child or not subscription_info.account_public_id:
        raise AuthenticationError(
            message="Not authorized! Token is invalid, missing, or not for a student account.",
            error_code=AppErrorCode.AUTHENTICATION_REQUIRED
        )

    return dashboard_service.get_dashboard_data(
        db=db,
        student_account=subscription_info.account,
        is_subscribed=subscription_info.is_subscribed,
        trial_active=subscription_info.trial_active,
        subscribed_subjects=subscription_info.subscribed_subjects,
        actual_subscription_type=subscription_info.actual_subscription_type
    )


