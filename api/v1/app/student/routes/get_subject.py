from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from db.database import get_db
from dependencies.auth_dependencies.subscription_dependency import SubscriptionDependency, SubscriptionInfo
from ..services.get_subject_service import GetSubjectService
from ..schemas.response import GetSubjectResponse

router = APIRouter()
require_subscription = SubscriptionDependency()

@router.get(
    "/subjects/{subject_public_id}",
    response_model=GetSubjectResponse,
    summary="Get Subject Details",
    description="Retrieves detailed information for a specific subject, including its chapters and learning notes."
)
def get_subject_route(
    subject_public_id: str,
    db: Session = Depends(get_db),
    subscription_info: SubscriptionInfo = Depends(require_subscription),
    subject_service: GetSubjectService = Depends(GetSubjectService)
):
    # Validate that this is a child account (student), not a parent
    if subscription_info.user_type != "child":
        from core.exception_handling.exceptions.custom_exceptions import AuthenticationError
        from api.v1.common.schemas import AppErrorCode
        raise AuthenticationError(
            message="This endpoint is only accessible to student accounts.",
            error_code=AppErrorCode.AUTHENTICATION_REQUIRED,
            log_message=f"Parent account {subscription_info.account_public_id} attempted to access student endpoint"
        )
    
    # SubscriptionDependency handles authentication and user type validation
    return subject_service.get_subject(
        db=db,
        student_account=subscription_info.account,  # Now guaranteed to be ChildAccount
        subject_public_id=subject_public_id,
        is_subscribed=subscription_info.is_subscribed,
        trial_active=subscription_info.trial_active,
        subscribed_subjects=subscription_info.subscribed_subjects,
        actual_subscription_type=subscription_info.actual_subscription_type
    )
