
from pydantic import BaseModel, Field
from typing import List, Optional, Union, Dict, Any, Literal, Annotated
from db.models.associations.child_account_exercise import ExerciseStatusEnum
from db.models.exercise import DifficultyEnum

# --- Exercise Related Schemas ---
class ExerciseOptionResponse(BaseModel):
    public_id: str
    text: str

class ExerciseImageOptionResponse(BaseModel):
    public_id: str
    image_url: str
    text: Optional[str] = None

class ExerciseResponse(BaseModel):
    public_id: str
    prompt: str
    prompt_image_url: Optional[str] = None
    status: ExerciseStatusEnum = Field(default=ExerciseStatusEnum.NOT_ATTEMPTED)
    type: str
    difficulty: DifficultyEnum

class MCSimpleExerciseResponse(ExerciseResponse):
    type: Literal['mc-simple']
    options: List[Union[ExerciseOptionResponse, ExerciseImageOptionResponse]]

class MCMultiExerciseResponse(ExerciseResponse):
    type: Literal['mc-multi']
    options: List[Union[ExerciseOptionResponse, ExerciseImageOptionResponse]]

class InputExerciseResponse(ExerciseResponse):
    type: Literal['input']

class ClozeExerciseResponse(ExerciseResponse):
    type: Literal['cloze']
    text_parts: List[str]

class DropdownExerciseResponse(ExerciseResponse):
    type: Literal['dropdown']
    template: str
    options: List[List[str]]

class HighlightExerciseResponse(ExerciseResponse):
    type: Literal['highlight']
    parts: List[str]

class MatchingPairItemResponse(BaseModel):
    public_id: str
    text: str

class MatchingPairsExerciseResponse(ExerciseResponse):
    type: Literal['matching-pairs']
    column_a: List[MatchingPairItemResponse]
    column_b: List[MatchingPairItemResponse]

class CategorizeItemResponse(BaseModel):
    public_id: str
    text: str

class CategoryItemResponse(BaseModel):
    public_id: str
    text: str

class CategorizeExerciseResponse(ExerciseResponse):
    type: Literal['categorize']
    prompt: Optional[str] = None
    categories: List[CategoryItemResponse]
    options: List[CategorizeItemResponse]

class ErrorCorrectionExerciseResponse(ExerciseResponse):
    type: Literal['error-correction']
    parts: List[str]

class TrueFalseExerciseResponse(ExerciseResponse):
    type: Literal['true-false']
    # No options field needed - True/False are implicit in frontend


AnyExerciseResponse = Union[
    MCSimpleExerciseResponse,
    MCMultiExerciseResponse,
    InputExerciseResponse,
    ClozeExerciseResponse,
    DropdownExerciseResponse,
    HighlightExerciseResponse,
    MatchingPairsExerciseResponse,
    CategorizeExerciseResponse,
    ErrorCorrectionExerciseResponse,
    TrueFalseExerciseResponse,
]