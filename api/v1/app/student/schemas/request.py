from pydantic import BaseModel, Field, field_validator
from typing import Union, List, Dict, Optional
import re

# This model attempts to match the frontend's exerciseUserAnswerSchema union.
# The frontend sends the direct answer value.
# The structure of `answer` will vary based on the exercise type.
# Validation of the specific answer structure against the exercise type
# will need to happen in the service layer.

class MatchingPairRequest(BaseModel):
    a: str  # ID from column A
    b: str  # ID from column B

class ExerciseSubmissionRequest(BaseModel):
    exercise_public_id: str
    answer: Union[
        str,  # For input, cloze single-blank
        List[str],  # For mc-simple (single optionId), mc-multi (optionIds), cloze multi-blank, dropdown selections
        List[int],  # For error-correction (indices of words user thinks are wrong), highlight (indices of highlighted parts)
        List[MatchingPairRequest],  # For matching-pairs e.g. [{"a": "id1", "b": "id2"}]
        Dict[str, str],  # For categorize e.g. {"optionId1": "categoryId1"}
        bool  # For true-false exercises
    ]

class UpdateStudentProfileRequest(BaseModel):
    student_public_id: str = Field(
        ...,
        description="Public ID of the student account to update"
    )
    name: str = Field(
        ...,
        description="Updated name for the student"
    )
    email: str = Field(
        ...,
        description="Updated email address for the student"
    )
    pin: Optional[str] = Field(
        None,
        description="Updated PIN for the student (4 digits) - optional"
    )

    @field_validator('email')
    @classmethod
    def validate_email(cls, v: str) -> str:
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, v):
            raise ValueError('Invalid email address')
        return v

    @field_validator('pin')
    @classmethod
    def validate_pin(cls, v: Optional[str]) -> Optional[str]:
        if v is not None and not re.match(r'^[0-9]{4}$', v):
            raise ValueError('PIN must be 4 digits')
        return v