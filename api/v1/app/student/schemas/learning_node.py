from enum import Enum
from pydantic import Field # BaseModel might still be needed for other schemas in this file
from typing import List, Optional, Union, Dict, Any, Literal, Annotated
from db.models.associations.child_account_exercise import ExerciseStatusEnum
from .exercise import AnyExerciseResponse


class LearningNodeType(str, Enum):
    MATH = "MATH"
    GRAMMAR = "GRAMMAR"
    VOCABULARY = "VOCABULARY"
    READING = "READING"
    LISTENING = "LISTENING"
    VERB = "VERB"
    SPEAKING = "SPEAKING"
    WRITING = "WRITING"

# # --- Learning Node Content Schemas ---
# class VocabWordResponse(BaseModel):
#     base: str 
#     target: str 
#     gender: Optional[Literal['masculine', 'feminine', 'neuter']] = None
#     example_sentence: Optional[str] = None 
#     explanation: Optional[str] = None
#     pronunciation_url: Optional[str] = None 

# class VocabGroupResponse(BaseModel):
#     id: str
#     title: str
#     words: List[VocabWordResponse]
#     exercises: Optional[List[Annotated[AnyExerciseResponse, Field(discriminator='type')]]] = Field(default_factory=list)

# class VocabContentResponse(BaseModel):
#     type: Literal["VOCABULARY"] = "VOCABULARY"
#     base_language: str
#     target_language: str
#     groups: List[VocabGroupResponse]
#     exercises: Optional[List[Annotated[AnyExerciseResponse, Field(discriminator='type')]]] = Field(default_factory=list)

# class VerbFormResponse(BaseModel):
#     person: str
#     form: str
#     irregular: Optional[bool] = None

# class VerbConjugationResponse(BaseModel):
#     tense_id: str
#     tense_name: str
#     is_compound: bool
#     auxiliary: Optional[str] = None
#     participle: Optional[str] = None
#     forms: List[VerbFormResponse]

# class VerbResponse(BaseModel):
#     infinitive: str
#     translation: str
#     explanation: str
#     example: str
#     verb_family: Literal['er', 'ir', 're']
#     is_irregular: bool
#     is_reflexive: bool
#     conjugations: List[VerbConjugationResponse]

# class VerbTenseResponse(BaseModel):
#     tense_id: str
#     tense_name: str

# class VerbMoodResponse(BaseModel):
#     mood_id: str
#     mood_name: str
#     tenses: List[VerbTenseResponse]

# class VerbContentResponse(BaseModel):
#     type: Literal["VERB"] = "VERB"
#     moods: List[VerbMoodResponse]
#     verbs: List[VerbResponse]
#     exercises: Optional[List[Annotated[AnyExerciseResponse, Field(discriminator='type')]]] = Field(default_factory=list)

# class ReadingExplanationResponse(BaseModel):
#     term: str
#     match_term: str 
#     word_type: Optional[str] = None
#     base_language: Optional[str] = None
#     target_language: Optional[str] = None
#     translation: Optional[str] = None
#     explanation: Optional[str] = None
#     examples: Optional[List[str]] = None
#     audio_url: Optional[str] = None 

# class ReadingContentResponse(BaseModel):
#     type: Literal["READING"] = "READING"
#     text: str
#     explanations: Optional[List[ReadingExplanationResponse]] = None
#     video_url: Optional[str] = None 
#     notes: Optional[str] = None
#     exercises: Optional[List[Annotated[AnyExerciseResponse, Field(discriminator='type')]]] = Field(default_factory=list)


# class ListeningContentResponse(BaseModel):
#     type: Literal["LISTENING"] = "LISTENING"
#     audio_url: Optional[str] = None 
#     transcription: Optional[str] = None
#     exercises: Optional[List[Annotated[AnyExerciseResponse, Field(discriminator='type')]]] = Field(default_factory=list)

# class MathContentResponse(BaseModel):
#     type: Literal["MATH"] = "MATH"
#     video_url: Optional[str] = None 
#     data: Optional[Any] = None
#     exercises: Optional[List[Annotated[AnyExerciseResponse, Field(discriminator='type')]]] = Field(default_factory=list)

# class GrammarContentResponse(BaseModel):
#     type: Literal["GRAMMAR"] = "GRAMMAR"
#     exercises: Optional[List[Annotated[AnyExerciseResponse, Field(discriminator='type')]]] = Field(default_factory=list)


# # Add Speaking and Writing content types to match frontend schema
# class SpeakingContentResponse(BaseModel):
#     type: Literal["SPEAKING"] = "SPEAKING"
#     exercises: Optional[List[Annotated[AnyExerciseResponse, Field(discriminator='type')]]] = Field(default_factory=list)

# class WritingContentResponse(BaseModel):
#     type: Literal["WRITING"] = "WRITING"
#     exercises: Optional[List[Annotated[AnyExerciseResponse, Field(discriminator='type')]]] = Field(default_factory=list)


# --- Learning Node Schemas ---
# class LearningNodeResponse(BaseModel):
#     public_id: str
#     title: str
#     description: Optional[str] = None
#     video_url: Optional[str] = None 

# class MathNodeResponse(LearningNodeResponse):
#     type: Literal["MATH"]
#     content: MathContentResponse
#     child_nodes: Optional[List['AnyLearningNodeResponse']] = Field(default_factory=list)

# class GrammarNodeResponse(LearningNodeResponse):
#     type: Literal["GRAMMAR"]
#     content: GrammarContentResponse
#     child_nodes: Optional[List['AnyLearningNodeResponse']] = Field(default_factory=list)

# class VocabNodeResponse(LearningNodeResponse):
#     type: Literal["VOCABULARY"]
#     content: VocabContentResponse
#     child_nodes: Optional[List['AnyLearningNodeResponse']] = Field(default_factory=list)

# class ReadingNodeResponse(LearningNodeResponse):
#     type: Literal["READING"]
#     content: ReadingContentResponse
#     child_nodes: Optional[List['AnyLearningNodeResponse']] = Field(default_factory=list)

# class ListeningNodeResponse(LearningNodeResponse):
#     type: Literal["LISTENING"]
#     content: ListeningContentResponse
#     child_nodes: Optional[List['AnyLearningNodeResponse']] = Field(default_factory=list)

# class VerbNodeResponse(LearningNodeResponse):
#     type: Literal["VERB"]
#     content: VerbContentResponse
#     child_nodes: Optional[List['AnyLearningNodeResponse']] = Field(default_factory=list)

# # Add Speaking and Writing if they have distinct content structures
# # For now, assuming they might be similar to Grammar or have simple content
# class SpeakingNodeResponse(LearningNodeResponse): 
#     type: Literal["SPEAKING"]
#     content: SpeakingContentResponse 
#     child_nodes: Optional[List['AnyLearningNodeResponse']] = Field(default_factory=list)

# class WritingNodeResponse(LearningNodeResponse): 
#     type: Literal["WRITING"]
#     content: WritingContentResponse 
#     child_nodes: Optional[List['AnyLearningNodeResponse']] = Field(default_factory=list)


# AnyLearningNodeResponse = Union[
#     MathNodeResponse,
#     GrammarNodeResponse,
#     VocabNodeResponse,
#     ReadingNodeResponse,
#     ListeningNodeResponse,
#     VerbNodeResponse,
#     SpeakingNodeResponse,
# #     WritingNodeResponse,
# # ]

# # Call rebuild on individual models that contain the forward reference
# MathNodeResponse.model_rebuild()
# GrammarNodeResponse.model_rebuild()
# VocabNodeResponse.model_rebuild()
# ReadingNodeResponse.model_rebuild()
# ListeningNodeResponse.model_rebuild()
# VerbNodeResponse.model_rebuild()
# SpeakingNodeResponse.model_rebuild()
# WritingNodeResponse.model_rebuild()

# # Also rebuild Content models containing nested Annotated/Union exercises
# MathContentResponse.model_rebuild()
# GrammarContentResponse.model_rebuild()
# VocabContentResponse.model_rebuild()
# ReadingContentResponse.model_rebuild()
# ListeningContentResponse.model_rebuild()
# VerbContentResponse.model_rebuild()
# # Potentially needed for VocabGroupResponse too, if exercises can be deeply nested?
# VocabGroupResponse.model_rebuild()

