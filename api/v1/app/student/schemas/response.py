from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Union, Dict, Any, Literal, Annotated
from .learning_node import LearningNodeType
from .exercise import AnyExerciseResponse # Added import
from api.v1.app.common.subscription_types import SubscriptionStatus, SubscriptionType

# Use string literals for type discrimination instead of enum
# This matches the frontend LearningNodeTypeSchema

# --- Progress Overview Schemas ---
class DailyProgressResponse(BaseModel):
    day: str  
    correct: int 
    total: int 
    percentage: float 

class DetailedSubjectProgressResponse(BaseModel):
    subject: str 
    public_id: str 
    image_url: Optional[str] = None 
    total_exercises: int 
    correct_percentage: float 
    weekly_trend: str 
    daily_progress: List[DailyProgressResponse] 

class ProgressOverviewResponse(BaseModel):
    subjects: List[DetailedSubjectProgressResponse]

# --- Submission Related Schemas ---
class SolutionStepResponse(BaseModel):
    text: Optional[str] = None
    math: Optional[str] = None
    image_url: Optional[str] = None

class ExerciseSubmissionResponse(BaseModel):
    is_correct: bool
    correct_answer: Union[str, List[str], Dict[str, str], List[Dict[str, Any]], bool]
    solution_steps: Optional[List[SolutionStepResponse]] = None
    video_url: Optional[str] = None
    recently_submitted: bool = Field(default=False, description="Indicates if this exercise was submitted in the last 24 hours")

# --- Student Dashboard Schemas ---
class SchoolYear(BaseModel):
    public_id: str
    name: str

class LearningProfile(BaseModel):
    public_id: str
    name: str
    email: Optional[str] = None
    school_year: SchoolYear
    pin: Optional[str] = None

class Subject(BaseModel):
    name: str
    public_id: str
    chapter_count: int
    is_subscribed: bool = False  # Whether this subject is included in their subscription
    image_url: Optional[str] = None

class GetStudentDashboardResponse(BaseModel):
    profile: LearningProfile
    subjects: List[Subject]


class GetSubjectsResponse(BaseModel):
    subjects: List[Subject]
    subscription_status: SubscriptionStatus
    subscription_type: Optional[SubscriptionType] = None
# --- Subject Detail Schemas ---

class LearningNodePreview(BaseModel):
    public_id: str
    title: str
    type: str
    exercise_count: int
    description: Optional[str] = None
    has_video: bool
    video_length: Optional[int] = None

class Chapter(BaseModel):
    public_id: str
    title: str
    description: Optional[str] = None
    ordering: int
    learning_nodes: List[LearningNodePreview] | None


class GetSubjectResponse(BaseModel):
    subject: Subject
    chapters: List[Chapter] | None
    is_subscribed: bool = False  # Whether this subject is included in their subscription
    subscription_status: SubscriptionStatus = "none"  # Default for backward compatibility


# --- Learning Node Schemas ---
class MathNodeContentResponse(BaseModel):
    video_url: Optional[str] = None
    video_duration: Optional[int] = None
    notes: Optional[str] = None
    exercises: List[AnyExerciseResponse] = []

class ReadingExplanationResponse(BaseModel):
    term: str
    match_term: str 
    word_type: Optional[str] = None
    base_language: Optional[str] = None
    target_language: Optional[str] = None
    translation: Optional[str] = None
    explanation: Optional[str] = None
    examples: Optional[List[str]] = None
    audio_url: Optional[str] = None 

class ContentOutlineItem(BaseModel):
    """Represents a section header in markdown content outline."""
    level: int  # 1-6 for H1-H6
    title: str
    order: Optional[int] = None  # Position in the document

class ReadingNodeContentResponse(BaseModel):
    video_url: Optional[str] = None
    video_duration: Optional[int] = None
    markdown_text: Optional[str] = None
    notes: Optional[str] = None
    exercises: List[AnyExerciseResponse] = []
    explanations: Optional[List[ReadingExplanationResponse]] = None
    content_outline: Optional[List[ContentOutlineItem]] = None


class ListeningNodeContentResponse(BaseModel):
    audio_url: Optional[str] = None
    transcription: Optional[str] = None
    exercises: List[AnyExerciseResponse] = []
    notes: Optional[str] = None


# Vocabulary Node Models
class VocabWordResponse(BaseModel):
    base_language: str
    target_language: str
    gender: Optional[Literal['masculine', 'feminine', 'neuter']] = None
    example_sentence: Optional[str] = None
    explanation: Optional[str] = None
    audio_url: Optional[str] = None

class VocabGroupResponse(BaseModel):
    id: str
    title: str
    words: List[VocabWordResponse] = []

class VocabNodeContentResponse(BaseModel):
    base_language: str
    target_language: str
    groups: List[VocabGroupResponse] = []
    exercises: List[AnyExerciseResponse] = []
    notes: Optional[str] = None
    video_url: Optional[str] = None
    video_duration: Optional[int] = None


# Verb Node Models - These match the database model definitions
class VerbFormModel(BaseModel):
    person: str
    form: str
    irregular: Optional[bool] = None

class VerbConjugationModel(BaseModel):
    tense_id: str
    tense_name: str
    is_compound: bool
    auxiliary: Optional[str] = None
    participle: Optional[str] = None
    forms: List[VerbFormModel]

class VerbModel(BaseModel):
    infinitive: str
    translation: str
    explanation: str
    example: str
    verb_family: Literal['er', 'ir', 're']
    is_irregular: bool
    is_reflexive: bool
    conjugations: List[VerbConjugationModel]

class VerbTenseModel(BaseModel):
    tense_id: str
    tense_name: str

class VerbMoodModel(BaseModel):
    mood_id: str
    mood_name: str
    tenses: List[VerbTenseModel]

class VerbContentModel(BaseModel):
    moods: List[VerbMoodModel]
    verbs: List[VerbModel]

# Response models for verb content - These are used in the API response
class VerbFormResponse(BaseModel):
    person: str
    form: str
    irregular: Optional[bool] = None

class VerbConjugationResponse(BaseModel):
    tense_id: str
    tense_name: str
    is_compound: bool
    auxiliary: Optional[str] = None
    participle: Optional[str] = None
    forms: List[VerbFormResponse]

class VerbResponse(BaseModel):
    infinitive: str
    translation: str
    explanation: str
    example: str
    verb_family: Literal['er', 'ir', 're']
    is_irregular: bool
    is_reflexive: bool
    conjugations: List[VerbConjugationResponse]

class VerbTenseResponse(BaseModel):
    tense_id: str
    tense_name: str

class VerbMoodResponse(BaseModel):
    mood_id: str
    mood_name: str
    tenses: List[VerbTenseResponse]


class VerbNodeContentResponse(BaseModel):
    moods: List[VerbMoodResponse]
    verbs: List[VerbResponse]
    exercises: List[AnyExerciseResponse] = []
    notes: Optional[str] = None
    video_url: Optional[str] = None
    video_duration: Optional[int] = None


# Base content response with a node_type discriminator field
class BaseNodeContentResponse(BaseModel):
    node_type: str
    exercises: List[AnyExerciseResponse] = []

# Update all node content response models to inherit from BaseNodeContentResponse
class MathNodeContentResponse(BaseNodeContentResponse):
    node_type: Literal["MATH"] = "MATH"
    video_url: Optional[str] = None
    video_duration: Optional[int] = None
    notes: Optional[str] = None
    exercises: List[AnyExerciseResponse] = []
    content_outline: Optional[List[ContentOutlineItem]] = None

class ReadingNodeContentResponse(BaseNodeContentResponse):
    node_type: Literal["READING"] = "READING"
    video_url: Optional[str] = None
    video_duration: Optional[int] = None
    markdown_text: Optional[str] = None
    notes: Optional[str] = None
    exercises: List[AnyExerciseResponse] = []
    explanations: Optional[List[ReadingExplanationResponse]] = None
    content_outline: Optional[List[ContentOutlineItem]] = None

class ListeningNodeContentResponse(BaseNodeContentResponse):
    node_type: Literal["LISTENING"] = "LISTENING"
    audio_url: Optional[str] = None
    transcription: Optional[str] = None
    exercises: List[AnyExerciseResponse] = []
    notes: Optional[str] = None
    content_outline: Optional[List[ContentOutlineItem]] = None

class VocabNodeContentResponse(BaseNodeContentResponse):
    node_type: Literal["VOCABULARY"] = "VOCABULARY"
    base_language: str
    target_language: str
    groups: List[VocabGroupResponse] = []
    exercises: List[AnyExerciseResponse] = []
    notes: Optional[str] = None
    video_url: Optional[str] = None
    video_duration: Optional[int] = None
    content_outline: Optional[List[ContentOutlineItem]] = None

class VerbNodeContentResponse(BaseNodeContentResponse):
    node_type: Literal["VERB"] = "VERB"
    moods: List[VerbMoodResponse]
    verbs: List[VerbResponse]
    exercises: List[AnyExerciseResponse] = []
    notes: Optional[str] = None
    video_url: Optional[str] = None
    video_duration: Optional[int] = None
    content_outline: Optional[List[ContentOutlineItem]] = None

# Define GrammarNodeContentResponse
class GrammarNodeContentResponse(BaseNodeContentResponse):
    node_type: Literal["GRAMMAR"] = "GRAMMAR"
    video_url: Optional[str] = None
    video_duration: Optional[float] = None
    notes: Optional[str] = None
    exercises: List[AnyExerciseResponse] = []
    content_outline: Optional[List[ContentOutlineItem]] = None


# class GrammarNodeContentResponse(BaseModel):
#     node_type: Literal["GRAMMAR", "SPEAKING", "WRITING"]
#     video_url: Optional[str] = None
#     video_duration: Optional[float] = None
#     notes: Optional[str] = None
#     exercises: List[AnyExerciseResponse] = []


# Define the content models as tagged unions with a discriminator field
NodeContentUnion = Annotated[
    Union[
        MathNodeContentResponse,
        ReadingNodeContentResponse,
        ListeningNodeContentResponse,
        VocabNodeContentResponse,
        VerbNodeContentResponse,
        GrammarNodeContentResponse
    ],
    Field(discriminator='node_type')
]

class GetLearningNodeResponse(BaseModel):
    public_id: str
    title: str
    type: LearningNodeType  # Use the Enum directly
    exercise_count: int
    description: Optional[str] = None
    content: NodeContentUnion
    is_subscribed: bool = False  # Whether this subject is included in their subscription
    total_exercises: int = 0  # Total number of exercises before limiting
    shown_exercises: int = 0  # Number of exercises actually returned
    content_truncated: bool = False  # Whether text content was truncated for preview

class UpdateStudentProfileResponse(BaseModel):
    public_id: str
    name: str
    email: str
    school_year: SchoolYear
    pin: Optional[str] = None