# Request Schemas
from .request import (
    ExerciseSubmissionRequest,
    MatchingPairRequest
)

# General Response Schemas (Progress, Submission, Dashboard, Subject Detail, Basic Learning Node Info)
from .response import (
    DailyProgressResponse,
    DetailedSubjectProgressResponse,
    ProgressOverviewResponse,
    SolutionStepResponse,
    ExerciseSubmissionResponse, # Note: This is a general response for submission status
    SchoolYear,
    LearningProfile,
    Subject,
    GetStudentDashboardResponse,
    GetSubjectsResponse,
    LearningNodePreview,
    Chapter,
    GetSubjectResponse,
    GetLearningNodeResponse # General response for fetching a learning node
)

# Exercise-Specific Response Schemas
from .exercise import (
    ExerciseOptionResponse,
    ExerciseImageOptionResponse,
    ExerciseResponse, # Base for specific exercise types
    MCSimpleExerciseResponse,
    MCMultiExerciseResponse,
    InputExerciseResponse,
    ClozeExerciseResponse,
    DropdownExerciseResponse,
    HighlightExerciseResponse,
    MatchingPairItemResponse,
    MatchingPairsExerciseResponse,
    CategorizeItemResponse,
    CategoryItemResponse,
    CategorizeExerciseResponse,
    ErrorCorrectionExerciseResponse,
    AnyExerciseResponse, # Union of all specific exercise responses
)

# Learning Node Content and Specific Node Type Response Schemas
from .learning_node import (
    LearningNodeType,
    # VocabWordResponse,
    # VocabGroupResponse,
    # VocabContentResponse,
    # VerbFormResponse,
    # VerbConjugationResponse,
    # VerbResponse,
    # VerbTenseResponse,
    # VerbMoodResponse,
    # VerbContentResponse,
    # ReadingExplanationResponse,
    # ReadingContentResponse,
    # ListeningContentResponse,
    # MathContentResponse,
    # GrammarContentResponse,
    # SpeakingContentResponse,
    # WritingContentResponse,
    # LearningNodeResponse, # Base for specific learning node types
    # MathNodeResponse,
    # GrammarNodeResponse,
    # VocabNodeResponse,
    # ReadingNodeResponse,
    # ListeningNodeResponse,
    # VerbNodeResponse,
    # SpeakingNodeResponse,
    # WritingNodeResponse,
    # AnyLearningNodeResponse, # Union of all specific learning node responses
)


__all__ = [
    # Requests
    "ExerciseSubmissionRequest",
    "MatchingPairRequest",

    # General Responses
    "DailyProgressResponse",
    "DetailedSubjectProgressResponse",
    "ProgressOverviewResponse",
    "SolutionStepResponse",
    "ExerciseSubmissionResponse",
    "SchoolYear",
    "LearningProfile",
    "Subject",
    "GetStudentDashboardResponse",
    "GetSubjectsResponse",
    "LearningNodePreview",
    "Chapter",
    "GetSubjectResponse",
    "GetLearningNodeResponse",

    # Exercise-Specific Responses
    "ExerciseOptionResponse",
    "ExerciseImageOptionResponse",
    "ExerciseResponse",
    "MCSimpleExerciseResponse",
    "MCMultiExerciseResponse",
    "InputExerciseResponse",
    "ClozeExerciseResponse",
    "DropdownExerciseResponse",
    "HighlightExerciseResponse",
    "MatchingPairItemResponse",
    "MatchingPairsExerciseResponse",
    "CategorizeItemResponse",
    "CategoryItemResponse",
    "CategorizeExerciseResponse",
    "ErrorCorrectionExerciseResponse",
    "AnyExerciseResponse",

    # Learning Node Content and Specific Node Type Responses
    "LearningNodeType",
    # "VocabWordResponse",
    # "VocabGroupResponse",
    # "VocabContentResponse",
    # "VerbFormResponse",
    # "VerbConjugationResponse",
    # "VerbResponse",
    # "VerbTenseResponse",
    # "VerbMoodResponse",
    # "VerbContentResponse",
    # "ReadingExplanationResponse",
    # "ReadingContentResponse",
    # "ListeningContentResponse",
    # "MathContentResponse",
    # "GrammarContentResponse",
    # "SpeakingContentResponse",
    # "WritingContentResponse",
    # "LearningNodeResponse",
    # "MathNodeResponse",
    # "GrammarNodeResponse",
    # "VocabNodeResponse",
    # "ReadingNodeResponse",
    # "ListeningContentResponse",
    # "VerbNodeResponse",
    # "SpeakingNodeResponse",
    # "WritingNodeResponse",
    # "AnyLearningNodeResponse",
]