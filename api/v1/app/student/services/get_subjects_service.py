from sqlalchemy import select
from sqlalchemy.orm import Session, joinedload
from typing import List, Dict, Optional
from loguru import logger

from db.models.account import ChildAccount
from db.models.content import Subject as SubjectDB
from api.v1.app.student.schemas.response import GetSubjectsResponse, Subject
from api.v1.app.common.subscription_types import SubscriptionContext, determine_subscription_type

class GetSubjectsService:
    def __init__(self):
        pass

    def get_subjects(self, db: Session, student_account: ChildAccount,
                     is_subscribed: bool, trial_active: bool,
                     subscribed_subjects: List[Dict], 
                     actual_subscription_type: Optional[str] = None) -> GetSubjectsResponse:
        if not student_account.year_id:
            logger.warning(f"Student {student_account.public_id} has no year_id assigned")
            return GetSubjectsResponse(
                subjects=[],
                subscription_status="none",
                subscription_type=None
            )

        # Fetch all subjects for the student's year
        stmt = (
            select(SubjectDB)
            .options(joinedload(SubjectDB.chapters))
            .where(SubjectDB.year_id == student_account.year_id)
            .where(SubjectDB.is_active)
            .order_by(SubjectDB.name)
        )
        subjects_db = db.execute(stmt).scalars().all()

        # Create subscription context
        subscription_context = SubscriptionContext.from_subscription_info(
            is_subscribed=is_subscribed,
            trial_active=trial_active,
            subscribed_subjects=subscribed_subjects,
            all_year_subjects=subjects_db,
            actual_subscription_type=actual_subscription_type
        )

        logger.info(
            f"Subscription check for student {student_account.public_id}: "
            f"subscribed={is_subscribed}, trial={trial_active}, "
            f"subscribed_subjects={len(subscription_context.subscribed_subject_ids)}/{len(subjects_db)}"
        )

        # Build subject response items
        subject_items: List[Subject] = []
        for subject_db_item in subjects_db:
            is_subject_subscribed = subscription_context.is_subject_subscribed(subject_db_item.public_id)
            
            subject_items.append(
                Subject(
                    public_id=subject_db_item.public_id,
                    name=subject_db_item.name,
                    image_url=subject_db_item.image_url,
                    chapter_count=len(subject_db_item.chapters),
                    is_subscribed=is_subject_subscribed
                )
            )
            
            if logger.isEnabledFor(20):  # DEBUG level
                logger.debug(
                    f"Subject {subject_db_item.name} ({subject_db_item.public_id}): "
                    f"is_subscribed={is_subject_subscribed}"
                )
        
        # Use the actual subscription type from the context
        subscription_type = subscription_context.subscription_type
        
        return GetSubjectsResponse(
            subjects=subject_items,
            subscription_status=subscription_context.subscription_status,
            subscription_type=subscription_type
        )