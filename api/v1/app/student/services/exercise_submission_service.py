from sqlalchemy.orm import Session
from sqlalchemy import select
from datetime import datetime, UTC, timedelta
from typing import Any, List, Dict, Union, Optional
from loguru import logger
from uuid import uuid4
from fastapi import status

from db.models import ChildAccount, Exercise, ChildAccountExerciseAssociation, ImageFile, VideoFile
from db.models.exercise import (
    ExerciseTypeEnum,
    MCSimpleSolutionAnswer, MCMultiSolutionAnswer, InputSolutionAnswer,
    ClozeSolutionAnswer, DropdownSolutionAnswer, HighlightSolutionAnswer,
    MatchingPairsSolutionAnswer, CategorizeSolutionAnswer, ErrorCorrectionSolutionAnswer,
    ErrorCorrectionText, TrueFalseSolutionAnswer
)
from db.models.associations.child_account_exercise import ExerciseStatusEnum
from ..schemas.request import ExerciseSubmissionRequest, MatchingPairRequest
from ..schemas.response import ExerciseSubmissionResponse, SolutionStepResponse
from core.config.settings import settings
from core.exception_handling.exceptions.custom_exceptions import NotFoundError, ValidationError, ServiceError, AuthorizationError
from api.v1.common.schemas import AppErrorCode
from api.v1.app.common.subscription_types import SubscriptionContext, PREVIEW_EXERCISE_LIMIT
from .media_utils import create_signed_video_url
from .image_service import batch_fetch_images, get_image_url, get_image_url_by_id

class ExerciseSubmissionService:
    def __init__(self, db: Session):
        self.db = db

    def _resolve_video_url(self, video_public_id: Optional[str]) -> Optional[str]:
        if not video_public_id:
            return None

        # Fetch the VideoFile from database using the public_id
        video_file_stmt = select(VideoFile).where(VideoFile.public_id == video_public_id)
        video_file_db = self.db.execute(video_file_stmt).scalar_one_or_none()
        
        if not video_file_db:
            logger.warning(f"VideoFile with public_id '{video_public_id}' not found.")
            return None
            
        if not video_file_db.storage_id or not video_file_db.storage_library_id:
            logger.warning(f"VideoFile '{video_public_id}' missing storage_id or storage_library_id.")
            return None
            
        try:
            return create_signed_video_url(video_file_db.storage_id, video_file_db.storage_library_id)
        except Exception as e:
            logger.error(f"Error creating signed video URL for '{video_public_id}': {e}")
            return None

    def _resolve_image_url(self, image_id: Optional[str]) -> Optional[str]:
        if not image_id:
            return None
        # Use the image service function which properly handles storage_path
        return get_image_url_by_id(self.db, image_id)

    def _check_mc_simple_answer(self, submitted_answer: Any, correct_solution: MCSimpleSolutionAnswer) -> bool:
        # MC Simple expects a list with a single answer from frontend
        if not isinstance(submitted_answer, list):
            return False
        if len(submitted_answer) != 1 or not isinstance(submitted_answer[0], str):
            return False
        if not correct_solution.correct_option_id or len(correct_solution.correct_option_id) != 1:
            return False
        return submitted_answer[0] == correct_solution.correct_option_id[0]

    def _check_mc_multi_answer(self, submitted_answer: Any, correct_solution: MCMultiSolutionAnswer) -> bool:
        if not isinstance(submitted_answer, list):
            return False
        # Ensure all elements in submitted_answer are strings if correctOptionIds are strings
        if not all(isinstance(item, str) for item in submitted_answer):
            return False
        return sorted(list(set(submitted_answer))) == sorted(list(set(correct_solution.correct_option_ids)))

    def _check_input_answer(self, submitted_answer: Any, correct_solution: InputSolutionAnswer) -> bool:
        return (isinstance(submitted_answer, str) 
                and submitted_answer.strip().lower() == correct_solution.correct_answer.strip().lower())

    def _check_cloze_answer(self, submitted_answer: Any, correct_solution: ClozeSolutionAnswer) -> bool:
        if not isinstance(submitted_answer, list) or len(submitted_answer) != len(correct_solution.correct_answers):
            return False
        # Ensure submitted answers are strings for comparison
        if not all(isinstance(sub, str) for sub in submitted_answer):
            return False
        return all(
            sub.strip().lower() == corr.strip().lower() 
            for sub, corr in zip(submitted_answer, correct_solution.correct_answers)
        )

    def _check_dropdown_answer(self, submitted_answer: Any, correct_solution: DropdownSolutionAnswer) -> bool:
        if not isinstance(submitted_answer, list) or len(submitted_answer) != len(correct_solution.correct_selections):
            return False
        # Ensure submitted answers are strings if correctSelections are strings
        if not all(isinstance(sub, str) for sub in submitted_answer):
            return False
        return submitted_answer == correct_solution.correct_selections

    def _check_highlight_answer(self, submitted_answer: Any, correct_solution: HighlightSolutionAnswer) -> bool:
        if not isinstance(submitted_answer, list):
            return False
        try:
            # Ensure submitted answers are convertible to int
            submitted_indices = [int(item) for item in submitted_answer]
        except ValueError:
            return False  # Contains non-integer values
        return sorted(list(set(submitted_indices))) == sorted(list(set(correct_solution.correct_indices)))

    def _check_matching_pairs_answer(
        self, submitted_answer: Any, correct_solution: MatchingPairsSolutionAnswer
    ) -> bool:
        if not isinstance(submitted_answer, list):  # Frontend sends list of objects
            return False

        submitted_pairs_dict = {}
        try:
            for item_obj in submitted_answer:  
                if not isinstance(item_obj, MatchingPairRequest):  
                    if isinstance(item_obj, dict) and 'a' in item_obj and 'b' in item_obj:  
                        submitted_pairs_dict[item_obj['a']] = item_obj['b']
                    else:
                        return False  # Invalid format
                else:
                    submitted_pairs_dict[item_obj.a] = item_obj.b
            print("Submitted pairs dict:", submitted_pairs_dict)
        except Exception as e:  # Catch errors during processing
            logger.error("Error processing submitted answer for matching pairs exercise: {}", e)
            return False

        return submitted_pairs_dict == correct_solution.correct_pairs

    def _check_categorize_answer(self, submitted_answer: Any, correct_solution: CategorizeSolutionAnswer) -> bool:
        if not isinstance(submitted_answer, dict):  # Frontend sends dict {optionId: categoryId}
            return False
        # Ensure keys and values are strings
        if not all(isinstance(k, str) and isinstance(v, str) for k, v in submitted_answer.items()):
            return False
        return submitted_answer == correct_solution.correct_categories

    def _check_error_correction_answer(
        self, submitted_answer: Any, correct_solution: ErrorCorrectionSolutionAnswer, exercise_data: Any
    ) -> bool:
        # Error correction: user selects indices of words they think are wrong
        # submitted_answer should be List[int] (indices of words user selected)
        # correct_solution.corrections contains the actual error indices with their corrections
        if not isinstance(submitted_answer, list):
            return False
        
        # Ensure all submitted answers are integers
        if not all(isinstance(idx, int) for idx in submitted_answer):
            return False
            
        # Extract actual error indices from corrections
        actual_error_indices = [correction.index for correction in correct_solution.corrections]
        
        # Compare user's selected indices with actual error indices
        return sorted(list(set(submitted_answer))) == sorted(list(set(actual_error_indices)))

    def _check_true_false_answer(self, submitted_answer: Any, correct_solution: TrueFalseSolutionAnswer) -> bool:
        return (isinstance(submitted_answer, bool) 
                and submitted_answer == correct_solution.is_true)

    def submit_exercise_answer(
        self, db: Session, student_account: ChildAccount, submission_data: ExerciseSubmissionRequest,
        is_subscribed: bool, trial_active: bool, subscribed_subjects: List[Dict],
        actual_subscription_type: Optional[str] = None
    ) -> ExerciseSubmissionResponse:
        logger.info(
            "Student {} submitting answer for exercise {}.",
            student_account.public_id, 
            submission_data.exercise_public_id
        )

        from sqlalchemy.orm import selectinload
        from db.models.learning_node import LearningNode
        from db.models.content import Chapter
        
        exercise_stmt = (
            select(Exercise)
            .options(selectinload(Exercise.learning_node_associations))
            .where(Exercise.public_id == submission_data.exercise_public_id)
        )
        exercise = db.execute(exercise_stmt).scalar_one_or_none()
        if not exercise:
            logger.warning("Exercise not found for submission: {}", submission_data.exercise_public_id)
            raise NotFoundError(
                message="Exercise not found",
                error_code=AppErrorCode.EXERCISE_NOT_FOUND,
                entity_name="Exercise",
                identifier=submission_data.exercise_public_id
            )

        # Check if user has access to this exercise based on subscription
        if exercise.learning_node_associations:
            # Get the learning node for this exercise
            learning_node_assoc = exercise.learning_node_associations[0]
            if learning_node_assoc.learning_node_id:
                # Fetch the learning node with its chapter and subject
                node_stmt = (
                    select(LearningNode)
                    .options(
                        selectinload(LearningNode.chapter).selectinload(Chapter.subject)
                    )
                    .where(LearningNode.id == learning_node_assoc.learning_node_id)
                )
                learning_node = db.execute(node_stmt).scalar_one_or_none()
                
                if learning_node and learning_node.chapter and learning_node.chapter.subject:
                    # Create subscription context to check access
                    subscription_context = SubscriptionContext.from_subscription_info(
                        is_subscribed=is_subscribed,
                        trial_active=trial_active,
                        subscribed_subjects=subscribed_subjects,
                        actual_subscription_type=actual_subscription_type
                    )
                    
                    subject_public_id = learning_node.chapter.subject.public_id
                    is_subject_subscribed = subscription_context.is_subject_subscribed(subject_public_id)
                    
                    if not is_subject_subscribed:
                        # Check if this exercise is within the preview limit
                        # Get exercise position in the learning node
                        exercise_position = None
                        for idx, assoc in enumerate(learning_node.exercise_associations):
                            if assoc.exercise_id == exercise.id:
                                exercise_position = idx
                                break
                        
                        if exercise_position is not None and exercise_position >= PREVIEW_EXERCISE_LIMIT:
                            logger.warning(
                                f"Student {student_account.public_id} attempting to submit exercise "
                                f"{submission_data.exercise_public_id} beyond preview limit. "
                                f"Position: {exercise_position + 1}, Limit: {PREVIEW_EXERCISE_LIMIT}"
                            )
                            raise AuthorizationError(
                                message="This exercise is not available in preview mode. Please subscribe to access all exercises.",
                                error_code=AppErrorCode.SUBSCRIPTION_REQUIRED,
                                status_code=status.HTTP_403_FORBIDDEN
                            )
        
        if not exercise.solution or not exercise.solution.correct_answer:
            logger.error("Exercise {} has no solution configured.", submission_data.exercise_public_id)
            raise ValidationError(  # Or ServiceError if it's a server config issue
                message="Exercise has no solution configured",
                error_code=AppErrorCode.SERVICE_ERROR
            )

        exercise_solution_model = exercise.solution
        correct_answer_detail = exercise_solution_model.correct_answer

        is_correct = False
        submitted_ans = submission_data.answer

        try:
            if exercise.exercise_type == ExerciseTypeEnum.MC_SIMPLE and isinstance(
                    correct_answer_detail, MCSimpleSolutionAnswer):
                is_correct = self._check_mc_simple_answer(submitted_ans, correct_answer_detail)
            elif exercise.exercise_type == ExerciseTypeEnum.MC_MULTI and isinstance(
                    correct_answer_detail, MCMultiSolutionAnswer):
                is_correct = self._check_mc_multi_answer(submitted_ans, correct_answer_detail)
            elif exercise.exercise_type == ExerciseTypeEnum.INPUT and isinstance(
                    correct_answer_detail, InputSolutionAnswer):
                is_correct = self._check_input_answer(submitted_ans, correct_answer_detail)
            elif exercise.exercise_type == ExerciseTypeEnum.CLOZE and isinstance(
                    correct_answer_detail, ClozeSolutionAnswer):
                is_correct = self._check_cloze_answer(submitted_ans, correct_answer_detail)
            elif exercise.exercise_type == ExerciseTypeEnum.DROPDOWN and isinstance(
                    correct_answer_detail, DropdownSolutionAnswer):
                is_correct = self._check_dropdown_answer(submitted_ans, correct_answer_detail)
            elif exercise.exercise_type == ExerciseTypeEnum.HIGHLIGHT and isinstance(
                    correct_answer_detail, HighlightSolutionAnswer):
                is_correct = self._check_highlight_answer(submitted_ans, correct_answer_detail)
            elif exercise.exercise_type == ExerciseTypeEnum.MATCHING_PAIRS and isinstance(
                    correct_answer_detail, MatchingPairsSolutionAnswer):
                is_correct = self._check_matching_pairs_answer(submitted_ans, correct_answer_detail)
            elif exercise.exercise_type == ExerciseTypeEnum.CATEGORIZE and isinstance(
                    correct_answer_detail, CategorizeSolutionAnswer):
                is_correct = self._check_categorize_answer(submitted_ans, correct_answer_detail)
            elif exercise.exercise_type == ExerciseTypeEnum.ERROR_CORRECTION and isinstance(
                    correct_answer_detail, ErrorCorrectionSolutionAnswer):
                is_correct = self._check_error_correction_answer(submitted_ans, correct_answer_detail, exercise.data)
            elif exercise.exercise_type == ExerciseTypeEnum.TRUE_FALSE and isinstance(
                    correct_answer_detail, TrueFalseSolutionAnswer):
                is_correct = self._check_true_false_answer(submitted_ans, correct_answer_detail)
            else:
                logger.error(
                    f"Answer checking not implemented for exercise type {exercise.exercise_type} "
                    f"or solution type mismatch for exercise {submission_data.exercise_public_id}."
                )
                raise ServiceError(
                    message=(
                        f"Answer checking not implemented for exercise type {exercise.exercise_type} "
                        f"or solution type mismatch."
                    ),
                    error_code=AppErrorCode.SERVICE_ERROR
                )
        except Exception as e:
            logger.error(
                f"Error during answer checking for exercise {submission_data.exercise_public_id}, "
                f"type {exercise.exercise_type}: {str(e)}", 
                exc_info=True
            )
            raise ServiceError(message="Error processing your answer.", error_code=AppErrorCode.SERVICE_ERROR)

        assoc_stmt = select(ChildAccountExerciseAssociation).where(
            ChildAccountExerciseAssociation.child_account_id == student_account.id,
            ChildAccountExerciseAssociation.exercise_id == exercise.id
        )
        association = db.execute(assoc_stmt).scalar_one_or_none()

        now = datetime.now(UTC)
        four_hours_ago = now - timedelta(hours=4)
        
        # Check if exercise was recently submitted (within 4 hours)
        recently_submitted = False
        if association and association.last_attempted_at:
            recently_submitted = association.last_attempted_at > four_hours_ago
            
        if not association:
            association = ChildAccountExerciseAssociation(
                public_id=f"caea-{uuid4()}",
                child_account_id=student_account.id,
                exercise_id=exercise.id,
                status=ExerciseStatusEnum.CORRECT if is_correct else ExerciseStatusEnum.INCORRECT,
                # Convert list of Pydantic models to list of dicts for JSON field
                given_answer=[ans.model_dump() for ans in submitted_ans] 
                if isinstance(submitted_ans, list) and all(
                    isinstance(ans, MatchingPairRequest) for ans in submitted_ans
                ) else submitted_ans,
                attempts_count=1,
                last_attempted_at=now,
                completed_at=now if is_correct else None,
                created_at=now,
            )
            db.add(association)
            logger.info(
                "Created new exercise association for student {}, exercise {}.",
                student_account.public_id, 
                submission_data.exercise_public_id
            )
        else:
            # Only update statistics if not recently submitted
            if not recently_submitted:
                association.status = ExerciseStatusEnum.CORRECT if is_correct else ExerciseStatusEnum.INCORRECT
                # Convert list of Pydantic models to list of dicts for JSON field
                if isinstance(submitted_ans, list) and all(
                    isinstance(ans, MatchingPairRequest) for ans in submitted_ans
                ):
                    association.given_answer = [ans.model_dump() for ans in submitted_ans]
                else:
                    association.given_answer = submitted_ans
                association.attempts_count += 1
                association.last_attempted_at = now
                if is_correct and not association.completed_at:
                    association.completed_at = now
                logger.info(
                    "Updated exercise association for student {}, exercise {} (statistics counted).",
                    student_account.public_id, 
                    submission_data.exercise_public_id
                )
            else:
                logger.info(
                    "Student {} re-submitted exercise {} within 4 hours. Statistics not updated.",
                    student_account.public_id,
                    submission_data.exercise_public_id
                )

        try:
            db.commit()
            db.refresh(association)
        except Exception as e:
            db.rollback()
            logger.error(
                "Database commit failed for exercise submission (student: {}, exercise: {}): {}",
                student_account.public_id, 
                submission_data.exercise_public_id, 
                str(e), 
                exc_info=True
            )
            raise ServiceError(
                message="Could not save submission due to a database issue.",
                error_code=AppErrorCode.SERVICE_ERROR
            )

        try:    
            response_correct_answer: Union[str, List[str], Dict[str, str], List[Dict[str, Any]]]
            if isinstance(correct_answer_detail, (MCSimpleSolutionAnswer, InputSolutionAnswer)):
                response_correct_answer = (
                    correct_answer_detail.correct_answer 
                    if isinstance(correct_answer_detail, InputSolutionAnswer) 
                    else correct_answer_detail.correct_option_id
                )
            elif isinstance(correct_answer_detail, (
                MCMultiSolutionAnswer, 
                ClozeSolutionAnswer, 
                DropdownSolutionAnswer
            )):
                response_correct_answer = (
                    correct_answer_detail.correct_option_ids 
                    if isinstance(correct_answer_detail, MCMultiSolutionAnswer) 
                    else correct_answer_detail.correct_answers 
                    if isinstance(correct_answer_detail, ClozeSolutionAnswer) 
                    else correct_answer_detail.correct_selections
                )
            elif isinstance(correct_answer_detail, HighlightSolutionAnswer):
                response_correct_answer = [str(idx) for idx in correct_answer_detail.correct_indices]
            elif isinstance(correct_answer_detail, MatchingPairsSolutionAnswer):
                response_correct_answer = correct_answer_detail.correct_pairs
            elif isinstance(correct_answer_detail, CategorizeSolutionAnswer):
                response_correct_answer = correct_answer_detail.correct_categories
            elif isinstance(correct_answer_detail, ErrorCorrectionSolutionAnswer):
                response_correct_answer = [correction.model_dump() for correction in correct_answer_detail.corrections]
            elif isinstance(correct_answer_detail, TrueFalseSolutionAnswer):
                response_correct_answer = correct_answer_detail.is_true
            else:
                logger.error(
                    "Solution format not available for response for exercise type {}, exercise {}.",
                    exercise.exercise_type, 
                    submission_data.exercise_public_id
                )
                response_correct_answer = "Solution format not available for this response."

            solution_steps_response = None
            if exercise_solution_model.solution_steps:
                # Collect all image public IDs from solution steps
                image_public_ids = [step.image_public_id for step in exercise_solution_model.solution_steps if step.image_public_id]
                images_map = batch_fetch_images(db, image_public_ids)
                
                solution_steps_response = [
                    SolutionStepResponse(
                        text=step.text,
                        math=step.math,
                        image_url=get_image_url(images_map.get(step.image_public_id)) if step.image_public_id else None
                    ) for step in exercise_solution_model.solution_steps
                ]

            video_url_response = self._resolve_video_url(exercise_solution_model.video_public_id)
            logger.info(
                "Exercise submission processed for student {}, exercise {}.",
                student_account.public_id, 
                submission_data.exercise_public_id
            )
            return ExerciseSubmissionResponse(
                is_correct=is_correct,
                correct_answer=response_correct_answer,
                solution_steps=solution_steps_response,
                video_url=video_url_response,
                recently_submitted=recently_submitted,
            )
        except Exception as e:
            # Catch any error during response preparation/serialization
            logger.error(
                "Error preparing exercise submission response (student: {}, exercise: {}): {}",
                student_account.public_id, 
                submission_data.exercise_public_id, 
                str(e), 
                exc_info=True
            )
            # Re-raise as a ServiceError so it's handled centrally
            raise ServiceError(
                message="Failed to prepare the response after submitting.",
                log_message="Error preparing submission response: {}".format(str(e)),
                error_code=AppErrorCode.SERVICE_ERROR,
                status_code=500
            )