# api/v1/app/student/services/image_service.py
from typing import List, Dict, Optional
from sqlalchemy.orm import Session
from sqlalchemy import select
from loguru import logger

from db.models import ImageFile
from ..services.media_utils import create_signed_image_url


def batch_fetch_images(db: Session, image_ids: List[str]) -> Dict[str, ImageFile]:
    """
    Fetch multiple images in one query to avoid N+1 problems.
    
    Args:
        db: Database session
        image_ids: List of image public_ids to fetch
        
    Returns:
        Dictionary mapping image public_id to ImageFile object
    """
    if not image_ids:
        return {}
    
    # Remove duplicates while preserving order
    unique_ids = list(dict.fromkeys(image_ids))
    
    stmt = select(ImageFile).where(ImageFile.public_id.in_(unique_ids))
    images = db.execute(stmt).scalars().all()
    
    # Create mapping
    image_map = {img.public_id: img for img in images}
    
    # Log any missing images
    missing_ids = set(unique_ids) - set(image_map.keys())
    if missing_ids:
        logger.warning(f"ImageFiles not found for public_ids: {missing_ids}")
    
    return image_map


def get_image_url(image_file: Optional[ImageFile]) -> Optional[str]:
    """
    Generate a signed URL for an ImageFile.
    
    Args:
        image_file: ImageFile object or None
        
    Returns:
        Signed URL string or None if image_file is None
    """
    if not image_file:
        return None
    
    return create_signed_image_url(image_file.storage_path, image_file.mime_type)


def get_image_url_by_id(db: Session, image_id: Optional[str]) -> Optional[str]:
    """
    Generate a signed URL for an image given its public_id.
    
    Args:
        db: Database session
        image_id: Public ID of the image
        
    Returns:
        Signed URL string or None if image not found
    """
    if not image_id:
        return None
    
    stmt = select(ImageFile).where(ImageFile.public_id == image_id)
    image_file = db.execute(stmt).scalar_one_or_none()
    
    if not image_file:
        logger.warning(f"ImageFile not found for public_id: {image_id}")
        return None
    
    return create_signed_image_url(image_file.storage_path, image_file.mime_type)