import boto3
from botocore.exceptions import Client<PERSON>rror
from hashlib import sha256
from typing import Literal, Optional
from datetime import datetime, timezone
from loguru import logger
from core.config.settings import settings


# Use settings instead of direct os.environ.get() for consistency
# The settings object properly loads from .env file
BUNNY_EXERCISES_TOKEN_SECURITY_KEY = settings.BUNNY_EXERCISES_TOKEN_SECURITY_KEY
BUNNY_EXERCISES_VIDEO_LIBRARY_ID = settings.BUNNY_EXERCISES_VIDEO_LIBRARY_ID

BUNNY_LECTURES_TOKEN_SECURITY_KEY = settings.BUNNY_LECTURES_TOKEN_SECURITY_KEY
BUNNY_LECTURES_VIDEO_LIBRARY_ID = settings.BUNNY_LECTURES_VIDEO_LIBRARY_ID



# Structure to match library_id to token_security_key
# So we can pass the library id and we get the security token
# Generalized so that we can easily expand in the future
VIDEO_LIBRARY_ID_TO_TOKEN_SECURITY_KEY = {
    BUNNY_EXERCISES_VIDEO_LIBRARY_ID: BUNNY_EXERCISES_TOKEN_SECURITY_KEY,
    BUNNY_LECTURES_VIDEO_LIBRARY_ID: BUNNY_LECTURES_TOKEN_SECURITY_KEY,
}


def create_signed_video_url(video_id: str, video_library_id: str) -> str:
    if video_id:
        lifetime = 3600
        expiration = str(int(datetime.now(timezone.utc).timestamp() + lifetime))

        token_security_key = VIDEO_LIBRARY_ID_TO_TOKEN_SECURITY_KEY.get(video_library_id)
        
        if not token_security_key:
            raise ValueError(f"Invalid video library ID: {video_library_id}")

        hash = sha256()

        data_to_hash = token_security_key.encode(
            'utf-8') + video_id.encode('utf-8') + expiration.encode('utf-8')
        hash.update(data_to_hash)
        token = hash.hexdigest()

        return f"https://iframe.mediadelivery.net/embed/{video_library_id}/{video_id}?token={token}&expires={expiration}&autoplay=false&loop=false&muted=false&preload=true&responsive=true"
    else:
        return ""


# Use settings for Cloudflare configuration as well
CLOUDFLARE_R2_ENDPOINT = settings.CLOUDFLARE_R2_ENDPOINT
CLOUDFLARE_R2_ACCESS_KEY_ID = settings.CLOUDFLARE_R2_ACCESS_KEY_ID
CLOUDFLARE_R2_SECRET_ACCESS_KEY = settings.CLOUDFLARE_R2_SECRET_ACCESS_KEY
CLOUDFLARE_R2_REGION_NAME = settings.CLOUDFLARE_R2_REGION_NAME

CLOUDFLARE_R2_IMAGE_BUCKET = settings.CLOUDFLARE_R2_IMAGE_BUCKET


def create_signed_image_url(storage_path: str, mime_type: Optional[str] = None, expiry=1800):
    """
    Generate a signed URL for an image file stored in Cloudflare R2
    
    Args:
        storage_path: The path within the bucket where the file is stored (from ImageFile.storage_path)
        mime_type: The MIME type of the image (e.g., 'image/png', 'image/jpeg')
        expiry: The number of seconds the URL is valid for (default: 30 minutes)
        
    Returns:
        A presigned URL that can be used to access the image file
    """
    bucket_name = CLOUDFLARE_R2_IMAGE_BUCKET

    # Map MIME types to file extensions
    mime_to_extension = {
        'image/png': '.png',
        'image/jpeg': '.jpg',
        'image/jpg': '.jpg',
        'image/gif': '.gif',
        'image/webp': '.webp',
        'image/svg+xml': '.svg',
        'image/bmp': '.bmp',
        'image/tiff': '.tiff',
    }

    client = boto3.client(
        "s3",
        region_name="auto",
        endpoint_url=CLOUDFLARE_R2_ENDPOINT,
        aws_access_key_id=CLOUDFLARE_R2_ACCESS_KEY_ID,
        aws_secret_access_key=CLOUDFLARE_R2_SECRET_ACCESS_KEY,
    )

    try:
        # Check if storage_path already has a file extension
        known_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg', '.bmp', '.tiff']
        has_extension = any(storage_path.lower().endswith(ext) for ext in known_extensions)
        
        if has_extension:
            # If storage_path includes the filename with extension, use it directly
            key = storage_path
        else:
            # If storage_path doesn't have an extension, append one based on mime_type
            if mime_type and mime_type in mime_to_extension:
                extension = mime_to_extension[mime_type]
            else:
                # Default to .png if mime_type is not provided or not recognized
                logger.warning(f"Unknown or missing mime_type '{mime_type}' for image, defaulting to .png")
                extension = '.png'
            
            # Ensure there's no trailing slash if storage_path is a directory
            if storage_path.endswith('/'):
                storage_path = storage_path.rstrip('/')
            
            key = f"{storage_path}{extension}"

        logger.info(f"Generating presigned URL for image file with key: {key}")
        
        response = client.generate_presigned_url(
            'get_object', 
            Params={'Bucket': bucket_name, 'Key': key}, 
            ExpiresIn=expiry
        )
        return response
    except ClientError as e:
        logger.error(f"ClientError when generating presigned URL for image {storage_path}: {e}")
    except Exception as e:
        logger.error(f"Unexpected error when generating presigned URL for image {storage_path}: {e}")
    
    # Return None if there was an error
    return None


def create_signed_audio_url(storage_path: str, expiry=1800):
    """
    Generate a signed URL for an audio file stored in Cloudflare R2
    
    Args:
        storage_path: The path within the bucket where the file is stored (from AudioFile.storage_path)
        expiry: The number of seconds the URL is valid for (default: 30 minutes)
        
    Returns:
        A presigned URL that can be used to access the audio file
    """
    # Using the audio bucket name from environment variable or fallback to the languages bucket
    bucket_name = settings.CLOUDFLARE_LANGUAGES_BUCKET
    
    client = boto3.client(
        "s3",
        region_name="auto",
        endpoint_url=settings.CLOUDFLARE_R2_ENDPOINT,
        aws_access_key_id=settings.CLOUDFLARE_LANGUAGES_R2_ACCESS_KEY_ID,
        aws_secret_access_key=settings.CLOUDFLARE_LANGUAGES_R2_SECRET_ACCESS_KEY,
    )

    try:
        # If storage_path is provided, use it directly with the public_id
        # The storage_path might already include the file name or might be a directory
        # We need to handle both cases
        if storage_path.endswith('.mp3') or storage_path.endswith('.wav') or storage_path.endswith('.ogg'):
            # If storage_path includes the filename with extension, use it directly
            key = storage_path
        else:
            # If storage_path is a directory path, append the public_id with extension
            # Ensure there's a trailing slash if not already present
            if not storage_path.endswith('/'):
                storage_path += '/'
            key = f"{storage_path}.mp3"

        logger.info(f"Generating presigned URL for audio file with key: {key}")
        
        response = client.generate_presigned_url(
            'get_object', 
            Params={'Bucket': bucket_name, 'Key': key}, 
            ExpiresIn=expiry
        )
        return response
    except ClientError as e:
        logger.error(f"ClientError when generating presigned URL for audio {storage_path}: {e}")
    except Exception as e:
        logger.error(f"Unexpected error when generating presigned URL for audio {storage_path}: {e}")
    
    # Return None if there was an error
    return None


    