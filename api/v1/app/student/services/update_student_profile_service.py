from sqlalchemy import select
from sqlalchemy.orm import Session
from loguru import logger

from db.models import ChildAccount, Year
from api.v1.app.student.schemas.request import UpdateStudentProfileRequest
from api.v1.app.student.schemas.response import UpdateStudentProfileResponse, SchoolYear
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ValidationError, ServiceError
)
from api.v1.common.schemas import AppErrorCode


class UpdateStudentProfileService:
    def __init__(self, db: Session = None):
        self.db = db

    def update_student_profile(
        self, 
        db: Session, 
        student_public_id: str, 
        update_data: UpdateStudentProfileRequest
    ) -> UpdateStudentProfileResponse:
        logger.info(f"Updating student profile for student_public_id: {student_public_id}")

        # Verify the student_public_id in the request matches the authenticated student
        if update_data.student_public_id != student_public_id:
            raise ValidationError(
                message="Student public ID in request does not match authenticated student.",
                error_code=AppErrorCode.INVALID_REQUEST
            )

        # Find the student account
        stmt = select(ChildAccount).where(ChildAccount.public_id == student_public_id)
        student_account = db.execute(stmt).scalar_one_or_none()

        if not student_account:
            raise NotFoundError(
                message="Student account not found.",
                error_code=AppErrorCode.CHILD_ACCOUNT_NOT_FOUND,
                entity_name="ChildAccount",
                identifier=student_public_id
            )

        # Check if email is being changed and if it's already in use by another student
        if update_data.email != student_account.email:
            existing_email_stmt = select(ChildAccount).where(
                ChildAccount.email == update_data.email,
                ChildAccount.id != student_account.id
            )
            existing_account = db.execute(existing_email_stmt).scalar_one_or_none()
            
            if existing_account:
                raise ValidationError(
                    message="Email address is already in use by another account.",
                    error_code=AppErrorCode.EMAIL_ALREADY_EXISTS
                )

        try:
            # Update the fields
            student_account.name = update_data.name
            student_account.email = update_data.email
            
            # Update PIN if provided
            if update_data.pin is not None:
                student_account.pin = update_data.pin
            
            # Commit changes
            db.add(student_account)
            db.commit()
            db.refresh(student_account)
            
            logger.info(f"Successfully updated student profile for: {student_public_id}")

            # Get the school year information for the response
            student_year = None
            if student_account.year_id:
                year_stmt = select(Year).where(Year.id == student_account.year_id)
                student_year = db.execute(year_stmt).scalar_one_or_none()

            school_year_data = SchoolYear(
                public_id=student_year.public_id if student_year else "unknown",
                name=student_year.name if student_year else "Unknown Year"
            )

            return UpdateStudentProfileResponse(
                public_id=student_account.public_id,
                name=student_account.name,
                email=student_account.email,
                school_year=school_year_data,
                pin=student_account.pin
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating student profile for {student_public_id}: {str(e)}")
            raise ServiceError(
                message="An error occurred while updating the student profile.",
                error_code=AppErrorCode.SERVICE_ERROR
            )


def update_student_profile_service(
    db: Session, 
    student_public_id: str, 
    update_data: UpdateStudentProfileRequest
) -> UpdateStudentProfileResponse:
    """Service function for updating student profile."""
    service = UpdateStudentProfileService(db)
    return service.update_student_profile(db, student_public_id, update_data)