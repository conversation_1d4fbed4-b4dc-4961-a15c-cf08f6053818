from sqlalchemy import select
from sqlalchemy.orm import selectinload
from datetime import datetime, timedelta, UTC
from typing import List, Optional, Dict
from collections import defaultdict
from sqlalchemy.orm import Session

from db.models import (
    ChildAccount, Subject, Chapter, LearningNode,
    ChildAccountExerciseAssociation, ExerciseStatusEnum,
    LearningNodeExerciseAssociation
)
from ..schemas.response import (
    ProgressOverviewResponse, DetailedSubjectProgressResponse, DailyProgressResponse
)
from core.exception_handling.exceptions.custom_exceptions import NotFoundError
from api.v1.common.schemas import AppErrorCode

class StudentProgressService:

    def get_progress_overview(
        self, db: Session, student_account: ChildAccount
    ) -> ProgressOverviewResponse:
        if not student_account or not student_account.id or not student_account.year_id:
            raise NotFoundError(
                message="Student account or year information is missing.",
                error_code=AppErrorCode.CHILD_ACCOUNT_NOT_FOUND,
                entity_name="ChildAccount",
                identifier=student_account.public_id if student_account else "Unknown"
            )

        student_id = student_account.id
        student_year_id = student_account.year_id

        # 1. Fetch relevant subjects for the student's year, eager loading down to exercises
        subjects_stmt = (
            select(Subject)
            .options(
                selectinload(Subject.chapters)
                .selectinload(Chapter.learning_nodes)
                .selectinload(LearningNode.exercise_associations)  # Loads LearningNodeExerciseAssociation
                .selectinload(LearningNodeExerciseAssociation.exercise)  # Loads Exercise from the association
            )
            .where(Subject.year_id == student_year_id, Subject.is_active)
        )
        active_subjects = db.execute(subjects_stmt).scalars().unique().all()

        if not active_subjects:
            return ProgressOverviewResponse(subjects=[])

        # 2. Collect all exercise IDs and map exercises to subjects
        all_exercise_ids_in_year: set[int] = set()
        exercise_to_subject_map: Dict[int, int] = {}  # exercise.id -> subject.id

        for subject_db in active_subjects:
            for chapter in subject_db.chapters:
                for ln in chapter.learning_nodes:
                    for ln_ex_assoc in ln.exercise_associations:
                        if ln_ex_assoc.exercise:
                            exercise_id = ln_ex_assoc.exercise.id
                            all_exercise_ids_in_year.add(exercise_id)
                            exercise_to_subject_map[exercise_id] = subject_db.id
        
        if not all_exercise_ids_in_year:
            # Handle case where subjects exist but have no exercises
            detailed_subject_progress_list: List[DetailedSubjectProgressResponse] = []
            for subject_db in active_subjects:
                detailed_subject_progress_list.append(DetailedSubjectProgressResponse(
                    subject=subject_db.name,
                    public_id=subject_db.public_id,
                    image_url=subject_db.image_url,
                    total_exercises=0,
                    correct_percentage=0.0,
                    weekly_trend="N/A",
                    daily_progress=[DailyProgressResponse(day=(datetime.now(UTC).date() - timedelta(days=i)).strftime("%a"), correct=0, total=0, percentage=0.0) for i in range(6, -1, -1)]
                ))
            return ProgressOverviewResponse(subjects=detailed_subject_progress_list)

        # 3. Fetch all relevant ChildAccountExerciseAssociation records for this student and these exercises in one go
        all_progress_records_stmt = (
            select(ChildAccountExerciseAssociation)
            .where(
                ChildAccountExerciseAssociation.child_account_id == student_id,
                ChildAccountExerciseAssociation.exercise_id.in_(list(all_exercise_ids_in_year)),
                ChildAccountExerciseAssociation.status.in_(
                    [ExerciseStatusEnum.CORRECT, ExerciseStatusEnum.INCORRECT]
                )
            )
        )
        student_exercise_associations_all = db.execute(all_progress_records_stmt).scalars().all()

        # 4. Group these associations by subject_id
        progress_by_subject: Dict[int, List[ChildAccountExerciseAssociation]] = defaultdict(list)
        for assoc in student_exercise_associations_all:
            subject_id_for_assoc = exercise_to_subject_map.get(assoc.exercise_id)
            if subject_id_for_assoc:
                progress_by_subject[subject_id_for_assoc].append(assoc)

        detailed_subject_progress_list: List[DetailedSubjectProgressResponse] = []
        today = datetime.now(UTC).date()

        # 5. Process per subject using the pre-fetched and grouped progress data
        for subject_db in active_subjects:
            current_subject_associations = progress_by_subject.get(subject_db.id, [])
            
            total_exercises_attempted_for_subject = len(current_subject_associations)
            num_correct_for_subject = sum(
                1 for assoc in current_subject_associations if assoc.status == ExerciseStatusEnum.CORRECT
            )
            correct_percentage_for_subject = (
                (num_correct_for_subject / total_exercises_attempted_for_subject * 100)
                if total_exercises_attempted_for_subject > 0 else 0.0
            )

            # Daily progress calculation (remains similar, but uses `current_subject_associations`)
            daily_progress_list: List[DailyProgressResponse] = []
            daily_stats: Dict[datetime.date, Dict[str, int]] = defaultdict(lambda: {"correct": 0, "total": 0})

            for assoc in current_subject_associations:  # Use filtered associations
                if assoc.last_attempted_at:
                    attempt_date = assoc.last_attempted_at.date()
                    if today - timedelta(days=6) <= attempt_date <= today:
                        daily_stats[attempt_date]["total"] += 1
                        if assoc.status == ExerciseStatusEnum.CORRECT:
                            daily_stats[attempt_date]["correct"] += 1
            
            for i in range(6, -1, -1):  # Iterate from 6 days ago to today
                current_day_date = today - timedelta(days=i)
                day_str = current_day_date.strftime("%a")
                stats_for_day = daily_stats[current_day_date]
                daily_percentage = (stats_for_day["correct"] / stats_for_day["total"] * 100) if stats_for_day["total"] > 0 else 0.0
                daily_progress_list.append(DailyProgressResponse(
                    day=day_str,
                    correct=stats_for_day["correct"],
                    total=stats_for_day["total"],
                    percentage=daily_percentage
                ))

            # Weekly trend calculation (remains similar, but uses `current_subject_associations`)
            num_correct_this_week = 0
            total_this_week = 0
            num_correct_last_week = 0
            total_last_week = 0

            start_this_week = today - timedelta(days=6)
            start_last_week = today - timedelta(days=13)
            end_last_week = today - timedelta(days=7)

            for assoc in current_subject_associations:
                if assoc.last_attempted_at:
                    attempt_date = assoc.last_attempted_at.date()
                    if start_this_week <= attempt_date <= today:
                        total_this_week += 1
                        if assoc.status == ExerciseStatusEnum.CORRECT:
                            num_correct_this_week += 1
                    elif start_last_week <= attempt_date <= end_last_week:
                        total_last_week += 1
                        if assoc.status == ExerciseStatusEnum.CORRECT:
                            num_correct_last_week += 1
            
            percentage_this_week = (num_correct_this_week / total_this_week * 100) if total_this_week > 0 else 0.0
            percentage_last_week = (num_correct_last_week / total_last_week * 100) if total_last_week > 0 else 0.0

            weekly_trend_str = "N/A"
            if total_this_week > 0 and total_last_week > 0:
                trend_value = percentage_this_week - percentage_last_week
                weekly_trend_str = f"{trend_value:+.0f}%"
            elif total_this_week > 0:  # Has data this week, but not last week
                weekly_trend_str = f"{percentage_this_week:+.0f}%"

            detailed_subject_progress_list.append(DetailedSubjectProgressResponse(
                subject=subject_db.name,
                public_id=subject_db.public_id,
                image_url=subject_db.image_url,
                total_exercises=total_exercises_attempted_for_subject,  # This is attempted exercises
                correct_percentage=correct_percentage_for_subject,
                weekly_trend=weekly_trend_str,
                daily_progress=daily_progress_list
            ))

        return ProgressOverviewResponse(subjects=detailed_subject_progress_list)