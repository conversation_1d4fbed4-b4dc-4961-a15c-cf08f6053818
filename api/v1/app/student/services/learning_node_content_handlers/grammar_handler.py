from sqlalchemy.orm import Session
from typing import Optional, List

from db.models import LearningNode, ChildAccount
from db.models.learning_node import Grammar<PERSON>ontentModel, LearningNodeTypeEnum
from api.v1.app.student.schemas.response import GrammarNodeContentResponse, ContentOutlineItem
from api.v1.app.student.schemas.exercise import AnyExerciseResponse
from .base_handler import BaseContentHandler
from loguru import logger

class GrammarContentHandler(BaseContentHandler):
    def __init__(self, db: Session, student_account: ChildAccount, is_subscribed: bool = False):
        super().__init__(db, student_account, is_subscribed)

    def process(self, learning_node_db: LearningNode) -> GrammarNodeContentResponse:
        logger.info(f"Processing GRAMMAR node: {learning_node_db.public_id} for student: {self.student_account.public_id}")

        if not isinstance(learning_node_db.content, GrammarContentModel):
            logger.error(
                f"Invalid content type for GRAMMAR node {learning_node_db.public_id}. "
                f"Expected GrammarContentModel, got {type(learning_node_db.content)}"
            )
            # Fallback or raise error - for now, creating a default response
            return GrammarNodeContentResponse(
                node_type=LearningNodeTypeEnum.GRAMMAR,
                exercises=[],
                notes="Error: Invalid content model."
            )

        content_model: GrammarContentModel = learning_node_db.content

        video_url: Optional[str] = None
        video_duration: Optional[float] = None
        if content_model.video_public_id:
            # Assuming _prepare_video_content returns a dict with "video_url" and "video_duration"
            video_data = self._prepare_video_content(content_model.video_public_id)
            video_url = video_data.get("video_url")
            video_duration = video_data.get("video_duration")


        exercises_response: List[AnyExerciseResponse] = self._prepare_exercises_for_node(
            learning_node_db # Pass the LearningNode DB object
        )

        # Prepare notes preview with outline for non-subscribers
        notes, content_outline = self._prepare_notes_preview(content_model.notes)
        
        # Convert internal MarkdownHeader objects to response schema objects
        outline_items = [
            ContentOutlineItem(level=header.level, title=header.title, order=header.order)
            for header in content_outline
        ]

        return GrammarNodeContentResponse(
            node_type=LearningNodeTypeEnum.GRAMMAR, # Explicitly set for discriminated union
            video_url=video_url,
            video_duration=video_duration,
            notes=notes,
            exercises=exercises_response,
            content_outline=outline_items if outline_items else None
        )