# api/v1/app/student/services/learning_node_content_handlers/listening_handler.py
from sqlalchemy import select
from sqlalchemy.orm import Session
from .base_handler import BaseContentHandler
from db.models import LearningNode, AudioFile, ChildAccount
from db.models.learning_node import Listening<PERSON>ontentModel, LearningNodeTypeEnum
from api.v1.app.student.schemas.response import ListeningNodeContentResponse, NodeContentUnion, ContentOutlineItem
from loguru import logger

class ListeningContentHandler(BaseContentHandler):
    def __init__(self, db: Session, student_account: ChildAccount, is_subscribed: bool = False):
        super().__init__(db, student_account, is_subscribed)
    def process(self, learning_node_db: LearningNode) -> NodeContentUnion:
        logger.info(f"Processing LISTENING node: {learning_node_db.public_id}")
        node_content_model: ListeningContentModel = learning_node_db.content # type: ignore

        audio_file_db = None
        if node_content_model.audio_file_id:
            stmt = select(AudioFile).where(AudioFile.id == node_content_model.audio_file_id)
            audio_file_db = self.db.execute(stmt).scalar_one_or_none()
            if not audio_file_db:
                logger.warning(f"AudioFile with public_id '{node_content_model.audio_file_id}' not found.")
        
        # Listening nodes might also have video according to original logic's _prepare_video_content call pattern
        video_data = self._prepare_video_content(node_content_model.video_public_id)
        # Note: ListeningNodeContentResponse doesn't have video_url/duration fields.
        # If needed, schema should be updated. For now, video_data is fetched but not used in response.

        # Prepare notes preview with outline for non-subscribers
        notes, content_outline = self._prepare_notes_preview(node_content_model.notes)
        
        # Convert internal MarkdownHeader objects to response schema objects
        outline_items = [
            ContentOutlineItem(level=header.level, title=header.title, order=header.order)
            for header in content_outline
        ]

        return ListeningNodeContentResponse(
            node_type=LearningNodeTypeEnum.LISTENING.value,
            audio_url=self._resolve_audio_url(audio_file_db),
            transcription=self._truncate_text_for_preview(node_content_model.transcription),
            notes=notes,
            exercises=self._prepare_exercises_for_node(learning_node_db),
            content_outline=outline_items if outline_items else None
        )