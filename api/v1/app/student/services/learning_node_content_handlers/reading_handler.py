# api/v1/app/student/services/learning_node_content_handlers/reading_handler.py
from sqlalchemy import select
from sqlalchemy.orm import Session
from typing import List, Dict

from .base_handler import BaseContentHandler
from db.models import LearningNode, AudioFile, ChildAccount
from db.models.learning_node import ReadingContentModel, ReadingExplanationModel, LearningNodeTypeEnum
from api.v1.app.student.schemas.response import ReadingNodeContentResponse, ReadingExplanationResponse, NodeContentUnion, ContentOutlineItem
from loguru import logger

class ReadingContentHandler(BaseContentHandler):
    def __init__(self, db: Session, student_account: ChildAccount, is_subscribed: bool = False):
        super().__init__(db, student_account, is_subscribed)
    def process(self, learning_node_db: LearningNode) -> NodeContentUnion:
        logger.info(f"Processing READING node: {learning_node_db.public_id}")
        node_content_model: ReadingContentModel = learning_node_db.content # type: ignore

        video_data = self._prepare_video_content(node_content_model.video_public_id)

        # Step 1: Process explanations if they exist
        explanations_response: List[ReadingExplanationResponse] = []
        if node_content_model.explanations:
            # Step 2: Build a map of audio files for explanations
            # First, collect all audio file IDs that need to be fetched
            audio_file_ids = [
                exp.audio_file_id for exp in node_content_model.explanations if exp.audio_file_id
            ]
            
            # Then, fetch all audio files in a single database query
            audio_files_map: Dict[int, AudioFile] = {}
            if audio_file_ids:
                audio_files_db = self.db.execute(
                    select(AudioFile).where(AudioFile.id.in_(audio_file_ids))
                ).scalars().all()
                # Create a lookup dictionary: audio file ID → AudioFile object
                audio_files_map = {audio_file.id: audio_file for audio_file in audio_files_db}

            # Step 3: Create response objects for each explanation
            for exp_model in node_content_model.explanations:
                # Look up the audio file for this explanation (if it has one)
                audio_file_db = None
                if exp_model.audio_file_id:
                    audio_file_db = audio_files_map.get(exp_model.audio_file_id)
                
                # Add the explanation to our response list
                explanations_response.append(ReadingExplanationResponse(
                    term=exp_model.term,
                    match_term=exp_model.match_term,
                    word_type=exp_model.word_type,
                    base_language=exp_model.base_language,
                    target_language=exp_model.target_language,
                    translation=exp_model.translation,
                    explanation=exp_model.explanation,
                    examples=exp_model.examples,
                    audio_url=self._resolve_audio_url(audio_file_db)
                ))

        # Prepare content preview with outline for non-subscribers
        markdown_text, content_outline = self._prepare_content_preview(node_content_model.markdown_text)
        
        # Convert internal MarkdownHeader objects to response schema objects
        outline_items = [
            ContentOutlineItem(level=header.level, title=header.title, order=header.order)
            for header in content_outline
        ]

        return ReadingNodeContentResponse(
            node_type=LearningNodeTypeEnum.READING.value,
            video_url=video_data["video_url"],
            video_duration=video_data["video_duration"],
            markdown_text=markdown_text,
            notes=self._truncate_text_for_preview(node_content_model.notes),
            exercises=self._prepare_exercises_for_node(learning_node_db),
            explanations=explanations_response,
            content_outline=outline_items if outline_items else None
        )