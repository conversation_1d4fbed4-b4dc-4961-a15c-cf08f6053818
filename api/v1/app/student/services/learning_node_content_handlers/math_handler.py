# api/v1/app/student/services/learning_node_content_handlers/math_handler.py
from sqlalchemy.orm import Session
from .base_handler import <PERSON><PERSON>ontent<PERSON>andler
from db.models import LearningNode, ChildAccount
from db.models.learning_node import MathContentModel, LearningNodeTypeEnum
from api.v1.app.student.schemas.response import MathNodeContentResponse, NodeContentUnion, ContentOutlineItem
from loguru import logger

class MathContentHandler(BaseContentHandler):
    def __init__(self, db: Session, student_account: ChildAccount, is_subscribed: bool = False):
        super().__init__(db, student_account, is_subscribed)
    def process(self, learning_node_db: LearningNode) -> NodeContentUnion:
        logger.info(f"Processing MATH node: {learning_node_db.public_id}")
        # Type cast for Pydantic model; .content already returns the Pydantic model
        node_content_model: MathContentModel = learning_node_db.content # type: ignore

        video_data = self._prepare_video_content(node_content_model.video_public_id)

        # Prepare notes preview with outline for non-subscribers
        notes, content_outline = self._prepare_notes_preview(node_content_model.notes)
        
        # Convert internal MarkdownHeader objects to response schema objects
        outline_items = [
            ContentOutlineItem(level=header.level, title=header.title, order=header.order)
            for header in content_outline
        ]

        return MathNodeContentResponse(
            node_type=LearningNodeTypeEnum.MATH.value, # Discriminator
            video_url=video_data["video_url"],
            video_duration=video_data["video_duration"],
            notes=notes,
            exercises=self._prepare_exercises_for_node(learning_node_db),
            content_outline=outline_items if outline_items else None
        )