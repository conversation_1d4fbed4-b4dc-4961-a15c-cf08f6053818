# api/v1/app/student/services/learning_node_content_handlers/vocabulary_handler.py
from sqlalchemy import select
from sqlalchemy.orm import Session
from typing import List, Dict

from .base_handler import BaseContent<PERSON>andler
from db.models import LearningNode, AudioFile, ChildAccount
from db.models.learning_node import VocabContentModel, VocabGroupModel as DBVocabGroupModel, VocabWordModel as DBVocabWordModel, LearningNodeTypeEnum
from api.v1.app.student.schemas.response import (
    VocabNodeContentResponse, VocabGroupResponse, VocabWordResponse, NodeContentUnion,
    ContentOutlineItem
)
from loguru import logger

class VocabularyContentHandler(BaseContentHandler):
    def __init__(self, db: Session, student_account: ChildAccount, is_subscribed: bool = False):
        super().__init__(db, student_account, is_subscribed)
    def process(self, learning_node_db: LearningNode) -> NodeContentUnion:
        logger.info(f"Processing VOCABULARY node: {learning_node_db.public_id}")
        node_content_model: VocabContentModel = learning_node_db.content # type: ignore

        groups_response: List[VocabGroupResponse] = []
        if node_content_model.groups:
            all_audio_file_public_ids: List[str] = []
            for group_model in node_content_model.groups:
                for word_model in group_model.words:
                    if word_model.audio_file_id:
                        all_audio_file_public_ids.append(word_model.audio_file_id)
            
            audio_files_map: Dict[int, AudioFile] = {}
            if all_audio_file_public_ids:
                audio_files_db = self.db.execute(
                    select(AudioFile).where(AudioFile.id.in_(all_audio_file_public_ids))
                ).scalars().all()
                audio_files_map = {af.id: af for af in audio_files_db}

            for group_model in node_content_model.groups:
                words_response: List[VocabWordResponse] = []
                for word_model in group_model.words:
                    audio_file_db = audio_files_map.get(word_model.audio_file_id) if word_model.audio_file_id else None
                    words_response.append(VocabWordResponse(
                        base_language=word_model.base_language,
                        target_language=word_model.target_language,
                        gender=word_model.gender,
                        example_sentence=word_model.example_sentence,
                        explanation=word_model.explanation,
                        audio_url=self._resolve_audio_url(audio_file_db)
                    ))
                groups_response.append(VocabGroupResponse(
                    id=group_model.id,
                    title=group_model.title,
                    words=words_response
                ))
        
        # Vocabulary nodes might also have a general video
        video_data = self._prepare_video_content(getattr(node_content_model, 'video_public_id', None))

        # Prepare notes preview with outline for non-subscribers
        notes, content_outline = self._prepare_notes_preview(getattr(node_content_model, 'notes', None))
        
        # Convert internal MarkdownHeader objects to response schema objects
        outline_items = [
            ContentOutlineItem(level=header.level, title=header.title, order=header.order)
            for header in content_outline
        ]

        return VocabNodeContentResponse(
            node_type=LearningNodeTypeEnum.VOCABULARY.value,
            base_language=node_content_model.base_language,
            target_language=node_content_model.target_language,
            groups=groups_response,
            notes=notes,
            exercises=self._prepare_exercises_for_node(learning_node_db),
            video_url=video_data["video_url"],
            video_duration=video_data["video_duration"],
            content_outline=outline_items if outline_items else None
        )