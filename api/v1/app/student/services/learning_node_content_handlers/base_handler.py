# api/v1/app/student/services/learning_node_content_handlers/base_handler.py
from abc import ABC, abstractmethod
from sqlalchemy.orm import Session
from sqlalchemy import select
from typing import List, Optional, Dict, Union, Tuple
from loguru import logger
from pydantic import BaseModel
import re

from db.models import (
    LearningNode, ChildAccount, Exercise, VideoFile, AudioFile, ImageFile,
    ChildAccountExerciseAssociation
)
from db.models.learning_node import (
    MathContentModel, ReadingContentModel, ListeningContentModel,
    VocabContentModel, VerbContentModel, GrammarContentModel # Add GrammarContentModel
)
from db.models.exercise import (
    MCExerciseData, InputExerciseData, ClozeExerciseData, DropdownExerciseData,
    HighlightExerciseData, MatchingPairsExerciseData, CategorizeExerciseData,
    ErrorCorrectionExerciseData, TrueFalseExerciseData, ExerciseTypeEnum
)
from db.models.associations.child_account_exercise import ExerciseStatusEnum

# Import all response schemas needed for _transform_exercise and handler return types
from api.v1.app.student.schemas.response import (
    NodeContentUnion, # This is the Annotated Union type
    MathNodeContentResponse, ReadingNodeContentResponse, ListeningNodeContentResponse,
    VocabNodeContentResponse, VerbNodeContentResponse, GrammarNodeContentResponse, # Add GrammarNodeContentResponse
    ReadingExplanationResponse, VocabGroupResponse, VocabWordResponse,
    VerbMoodResponse, VerbTenseResponse, VerbResponse, VerbConjugationResponse, VerbFormResponse
)
from api.v1.app.student.schemas.exercise import (
    AnyExerciseResponse, MCSimpleExerciseResponse, MCMultiExerciseResponse,
    InputExerciseResponse, ClozeExerciseResponse, DropdownExerciseResponse,
    HighlightExerciseResponse, MatchingPairsExerciseResponse, CategorizeExerciseResponse,
    ErrorCorrectionExerciseResponse, TrueFalseExerciseResponse, ExerciseOptionResponse, ExerciseImageOptionResponse,
    MatchingPairItemResponse, CategoryItemResponse, CategorizeItemResponse
)

from core.config.settings import settings
from core.exception_handling.exceptions.custom_exceptions import ServiceError
from api.v1.common.schemas import AppErrorCode
from api.v1.app.common.subscription_types import PREVIEW_EXERCISE_LIMIT, PREVIEW_TEXT_LENGTH, PREVIEW_PARAGRAPH_LIMIT
# Ensure media_utils is correctly importable
from ..media_utils import create_signed_audio_url, create_signed_video_url
from ..image_service import batch_fetch_images, get_image_url, get_image_url_by_id


class MarkdownHeader(BaseModel):
    """Represents a markdown header extracted from content."""
    level: int  # 1-6 for H1-H6
    title: str
    order: int  # Position in the document


class BaseContentHandler(ABC):
    def __init__(self, db: Session, student_account: ChildAccount, is_subscribed: bool = False):
        self.db: Session = db
        self.student_account: ChildAccount = student_account
        self.is_subscribed: bool = is_subscribed
        self.settings = settings

    def _get_base_url(self) -> str:
        return self.settings.API_BASE_URL
    
    def _truncate_text_for_preview(self, text: Optional[str]) -> Optional[str]:
        """Truncate text content for non-subscribers, preserving markdown formatting."""
        if text is None or self.is_subscribed:
            return text
        
        # First, try to limit by paragraphs (split by double newlines)
        paragraphs = text.split('\n\n')
        if len(paragraphs) > PREVIEW_PARAGRAPH_LIMIT:
            truncated = '\n\n'.join(paragraphs[:PREVIEW_PARAGRAPH_LIMIT])
            # Add ellipsis if we truncated
            return truncated
        
        # If not enough paragraphs, limit by character count
        if len(text) > PREVIEW_TEXT_LENGTH:
            # Try to cut at a sentence boundary
            truncated = text[:PREVIEW_TEXT_LENGTH]
            last_period = truncated.rfind('.')
            last_exclamation = truncated.rfind('!')
            last_question = truncated.rfind('?')
            
            # Find the last sentence boundary
            last_sentence_end = max(last_period, last_exclamation, last_question)
            if last_sentence_end > PREVIEW_TEXT_LENGTH * 0.8:  # If we have a sentence end in the last 20%
                truncated = truncated[:last_sentence_end + 1]
            else:
                # Otherwise, try to cut at a word boundary
                last_space = truncated.rfind(' ')
                if last_space > PREVIEW_TEXT_LENGTH * 0.9:  # If we have a space in the last 10%
                    truncated = truncated[:last_space]
                truncated += '...'
            
            return truncated
        
        return text
    
    def _extract_markdown_headers(self, markdown_text: Optional[str]) -> List[MarkdownHeader]:
        """Extract all headers from markdown content."""
        if not markdown_text:
            return []
        
        headers = []
        # Regex pattern to match markdown headers (# to ######)
        header_pattern = re.compile(r'^(#{1,6})\s+(.+)$', re.MULTILINE)
        
        for index, match in enumerate(header_pattern.finditer(markdown_text)):
            level = len(match.group(1))  # Count the number of # symbols
            title = match.group(2).strip()
            headers.append(MarkdownHeader(
                level=level,
                title=title,
                order=index
            ))
        
        return headers
    
    def _prepare_content_preview(self, text: Optional[str]) -> Tuple[Optional[str], List[MarkdownHeader]]:
        """Prepare content preview for non-subscribers.
        
        Returns:
            Tuple of (truncated_text, headers_outline)
        """
        if text is None or self.is_subscribed:
            # Subscribers get full content, no need for outline
            return text, []
        
        # Extract all headers first (from full content)
        headers = self._extract_markdown_headers(text)
        
        # Then truncate the content
        truncated_text = self._truncate_text_for_preview(text)
        
        return truncated_text, headers
    
    def _prepare_notes_preview(self, notes: Optional[str]) -> Tuple[Optional[str], List[MarkdownHeader]]:
        """Prepare notes preview for non-subscribers.
        
        This is a convenience wrapper around _prepare_content_preview specifically for notes fields.
        
        Returns:
            Tuple of (truncated_notes, headers_outline)
        """
        return self._prepare_content_preview(notes)

    def _resolve_audio_url(self, audio_file_db: AudioFile) -> Optional[str]:
        if not audio_file_db or not audio_file_db.storage_path:
            return None
        return create_signed_audio_url(audio_file_db.storage_path)

    def _resolve_video_url(self, video_file_db: VideoFile) -> Optional[str]:
        if not video_file_db or not video_file_db.storage_id or not video_file_db.storage_library_id:
            return None
        return create_signed_video_url(video_file_db.storage_id, video_file_db.storage_library_id)

    def _resolve_image_url(self, image_public_id: Optional[str]) -> Optional[str]:
        """Legacy method for backwards compatibility. Use batch_fetch_images for better performance."""
        if not image_public_id:
            return None
        # Use the image service function which properly handles storage_path
        return get_image_url_by_id(self.db, image_public_id)

    def _prepare_video_content(self, video_public_id: Optional[str]) -> Dict[str, Optional[Union[str, float]]]:
        video_url: Optional[str] = None
        video_duration: Optional[float] = None
        
        if video_public_id:
            video_file_stmt = select(VideoFile).where(VideoFile.public_id == video_public_id)
            video_file_db = self.db.execute(video_file_stmt).scalar_one_or_none()
            if video_file_db:
                video_url = self._resolve_video_url(video_file_db)
                video_duration = video_file_db.duration_seconds
            else:
                logger.warning(f"VideoFile with public_id '{video_public_id}' not found.")
        return {"video_url": video_url, "video_duration": video_duration}

    def _transform_exercise(self, exercise_db: Exercise, associations_map: Dict[int, ChildAccountExerciseAssociation], 
                          images_map: Dict[str, ImageFile]) -> AnyExerciseResponse:
        """Transform an exercise from database model to response model, resolving image IDs to URLs."""
        association = associations_map.get(exercise_db.id)
        status = association.status if association else ExerciseStatusEnum.NOT_ATTEMPTED

        common_data = {
            "public_id": exercise_db.public_id,
            "prompt": "",
            "prompt_image_url": None,
            "status": status,
            "type": exercise_db.exercise_type.value,
            "difficulty": exercise_db.difficulty,
        }

        data_model = exercise_db.data # This is already a Pydantic model

        if not isinstance(data_model, BaseModel):
            logger.error(f"Exercise {exercise_db.public_id} has invalid data model type: {type(data_model)}")
            raise ServiceError(message="Invalid exercise data.", error_code=AppErrorCode.SERVICE_ERROR)

        # Populate prompt from data_model
        # For CategorizeExerciseData, prompt is optional, so provide a default.
        common_data["prompt"] = getattr(data_model, 'prompt', "") if not isinstance(data_model, CategorizeExerciseData) else (data_model.prompt or "")
        
        # Resolve prompt image public ID to URL
        prompt_image_public_id = getattr(data_model, 'prompt_image_public_id', None)
        if prompt_image_public_id:
            prompt_image_url = get_image_url(images_map.get(prompt_image_public_id))
            common_data["prompt_image_url"] = prompt_image_url

        if isinstance(data_model, MCExerciseData):
            options_response = []
            for opt in data_model.options: # opt can be ExerciseOptionModel or ExerciseImageOptionModel
                if hasattr(opt, 'image_public_id') and opt.image_public_id:
                    # This is an image option - resolve image_public_id to URL
                    image_url = get_image_url(images_map.get(opt.image_public_id))
                    options_response.append(ExerciseImageOptionResponse(
                        public_id=opt.public_id,
                        image_url=image_url or "",  # Fallback to empty string if image not found
                        text=opt.text
                    ))
                else:
                    # This is a text-only option
                    options_response.append(ExerciseOptionResponse(public_id=opt.public_id, text=opt.text))

            if exercise_db.exercise_type == ExerciseTypeEnum.MC_SIMPLE:
                return MCSimpleExerciseResponse(**common_data, options=options_response)
            else: # MC_MULTI
                return MCMultiExerciseResponse(**common_data, options=options_response)
        elif isinstance(data_model, InputExerciseData):
            return InputExerciseResponse(**common_data)
        elif isinstance(data_model, ClozeExerciseData):
            return ClozeExerciseResponse(**common_data, text_parts=data_model.text_parts)
        elif isinstance(data_model, DropdownExerciseData):
            return DropdownExerciseResponse(**common_data, template=data_model.template, options=data_model.options)
        elif isinstance(data_model, HighlightExerciseData):
            return HighlightExerciseResponse(**common_data, parts=data_model.parts)
        elif isinstance(data_model, MatchingPairsExerciseData):
            return MatchingPairsExerciseResponse(
                **common_data,
                column_a=[MatchingPairItemResponse(public_id=item.public_id, text=item.text) for item in data_model.column_a],
                column_b=[MatchingPairItemResponse(public_id=item.public_id, text=item.text) for item in data_model.column_b]
            )
        elif isinstance(data_model, CategorizeExerciseData):
            return CategorizeExerciseResponse(
                **common_data, # prompt already handled above
                categories=[CategoryItemResponse(public_id=cat.public_id, text=cat.text) for cat in data_model.categories],
                options=[CategorizeItemResponse(public_id=opt.public_id, text=opt.text) for opt in data_model.options]
            )
        elif isinstance(data_model, ErrorCorrectionExerciseData):
            return ErrorCorrectionExerciseResponse(**common_data, parts=data_model.parts)
        elif isinstance(data_model, TrueFalseExerciseData):
            return TrueFalseExerciseResponse(**common_data)

        logger.error(
            f"Unknown exercise data model type: {type(data_model)} for exercise type {exercise_db.exercise_type}, exercise_id: {exercise_db.public_id}")
        raise ServiceError(
            message=f"Unknown exercise data model type: {type(data_model)}", error_code=AppErrorCode.SERVICE_ERROR)

    def _prepare_exercises_for_node(self, learning_node_db: LearningNode) -> List[AnyExerciseResponse]:
        transformed_exercises: List[AnyExerciseResponse] = []
        if not learning_node_db.exercises:
            return []

        exercise_ids = [ex.id for ex in learning_node_db.exercises if ex]

        # Get exercise associations
        associations_map: Dict[int, ChildAccountExerciseAssociation] = {}
        if exercise_ids:
            assoc_stmt = select(ChildAccountExerciseAssociation).where(
                ChildAccountExerciseAssociation.child_account_id == self.student_account.id,
                ChildAccountExerciseAssociation.exercise_id.in_(exercise_ids)
            )
            associations_result = self.db.execute(assoc_stmt).scalars().all()
            associations_map = {assoc.exercise_id: assoc for assoc in associations_result}
        
        # Collect all image IDs from all exercises
        all_image_ids: List[str] = []
        for exercise_db in learning_node_db.exercises:
            if not exercise_db:
                continue
                
            data_model = exercise_db.data
            if not isinstance(data_model, BaseModel):
                continue
                
            # Collect prompt image public ID
            prompt_image_public_id = getattr(data_model, 'prompt_image_public_id', None)
            if prompt_image_public_id:
                all_image_ids.append(prompt_image_public_id)
            
            # Collect image IDs from MC exercises
            if isinstance(data_model, MCExerciseData):
                for opt in data_model.options:
                    if hasattr(opt, 'image_public_id') and opt.image_public_id:
                        all_image_ids.append(opt.image_public_id)
        
        # Batch fetch all images
        images_map = batch_fetch_images(self.db, all_image_ids)
        
        # Transform exercises with the fetched images
        for exercise_db_item in learning_node_db.exercises:
            if exercise_db_item: # Ensure exercise_db_item is not None
                transformed_exercises.append(self._transform_exercise(exercise_db_item, associations_map, images_map))
        
        # Limit exercises for non-subscribers
        if not self.is_subscribed:
            transformed_exercises = transformed_exercises[:PREVIEW_EXERCISE_LIMIT]
            
        return transformed_exercises

    @abstractmethod
    def process(self, learning_node_db: LearningNode) -> NodeContentUnion:
        pass