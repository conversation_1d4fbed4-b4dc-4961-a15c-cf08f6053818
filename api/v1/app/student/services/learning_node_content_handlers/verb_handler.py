# api/v1/app/student/services/learning_node_content_handlers/verb_handler.py
from sqlalchemy.orm import Session
from .base_handler import BaseContentHandler
from db.models import LearningNode, ChildAccount
from db.models.learning_node import VerbContentModel, LearningNodeTypeEnum
from api.v1.app.student.schemas.response import (
    VerbNodeContentResponse, VerbMoodResponse, VerbTenseResponse,
    VerbResponse, VerbConjugationResponse, VerbFormResponse, NodeContentUnion,
    ContentOutlineItem
)
from loguru import logger

class VerbContentHandler(BaseContentHandler):
    def __init__(self, db: Session, student_account: ChildAccount, is_subscribed: bool = False):
        super().__init__(db, student_account, is_subscribed)
    def process(self, learning_node_db: LearningNode) -> NodeContentUnion:
        logger.info(f"Processing VERB node: {learning_node_db.public_id}")
        node_content_model: VerbContentModel = learning_node_db.content # type: ignore

        video_data = self._prepare_video_content(getattr(node_content_model, 'video_public_id', None)) # VerbContentModel doesn't have video_public_id by default

        moods_response = []
        if node_content_model.moods:
            for mood_db in node_content_model.moods:
                tenses_response = [
                    VerbTenseResponse(tense_id=tense_db.tense_id, tense_name=tense_db.tense_name)
                    for tense_db in mood_db.tenses
                ]
                moods_response.append(VerbMoodResponse(
                    mood_id=mood_db.mood_id, mood_name=mood_db.mood_name, tenses=tenses_response
                ))

        verbs_response = []
        if node_content_model.verbs:
            for verb_db in node_content_model.verbs:
                conjugations_response = []
                for conj_db in verb_db.conjugations:
                    forms_response = [
                        VerbFormResponse(person=form_db.person, form=form_db.form, irregular=form_db.irregular)
                        for form_db in conj_db.forms
                    ]
                    conjugations_response.append(VerbConjugationResponse(
                        tense_id=conj_db.tense_id, tense_name=conj_db.tense_name,
                        is_compound=conj_db.is_compound, auxiliary=conj_db.auxiliary,
                        participle=conj_db.participle, forms=forms_response
                    ))
                verbs_response.append(VerbResponse(
                    infinitive=verb_db.infinitive, translation=verb_db.translation,
                    explanation=verb_db.explanation, example=verb_db.example,
                    verb_family=verb_db.verb_family, is_irregular=verb_db.is_irregular,
                    is_reflexive=verb_db.is_reflexive, conjugations=conjugations_response
                ))

        # Prepare notes preview with outline for non-subscribers
        notes, content_outline = self._prepare_notes_preview(getattr(node_content_model, 'notes', None))
        
        # Convert internal MarkdownHeader objects to response schema objects
        outline_items = [
            ContentOutlineItem(level=header.level, title=header.title, order=header.order)
            for header in content_outline
        ]

        return VerbNodeContentResponse(
            node_type=LearningNodeTypeEnum.VERB.value,
            moods=moods_response,
            verbs=verbs_response,
            notes=notes,
            exercises=self._prepare_exercises_for_node(learning_node_db),
            video_url=video_data["video_url"],
            video_duration=video_data["video_duration"],
            content_outline=outline_items if outline_items else None
        )