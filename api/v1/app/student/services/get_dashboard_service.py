from sqlalchemy import select
from sqlalchemy.orm import Session, selectinload
from typing import List, Optional, Dict
from loguru import logger

from db.models import ChildAccount, Subject, Year
from api.v1.app.student.schemas.response import (
    GetStudentDashboardResponse,
    LearningProfile as LearningProfileResponseSchema,
    Subject as SubjectResponseSchema,
    SchoolYear
)
from api.v1.app.common.subscription_types import SubscriptionContext

from core.exception_handling.exceptions.custom_exceptions import NotFoundError
from api.v1.common.schemas import AppErrorCode

class GetDashboardService:
    def __init__(self, db: Session = None):
        self.db = db

    def get_dashboard_data(self, db: Session, student_account: ChildAccount,
                          is_subscribed: bool, trial_active: bool,
                          subscribed_subjects: List[Dict],
                          actual_subscription_type: Optional[str] = None) -> GetStudentDashboardResponse:
        logger.info(f"Fetching dashboard data for student_public_id: {student_account.public_id}")

        if not student_account or not student_account.id or not student_account.year_id:
            raise NotFoundError(
                message="Student account or year information is missing.",
                error_code=AppErrorCode.CHILD_ACCOUNT_NOT_FOUND,
                entity_name="ChildAccount",
                identifier=student_account.public_id if student_account else "Unknown"
            )

        student_year = None
        if student_account.year_id:
            year_stmt = select(Year).where(Year.id == student_account.year_id)
            student_year = db.execute(year_stmt).scalar_one_or_none()

        school_year_data = SchoolYear(
            public_id=student_year.public_id if student_year else "unknown",
            name=student_year.name if student_year else "Unknown Year"
        )

        profile = LearningProfileResponseSchema(
            public_id=student_account.public_id,
            name=student_account.name,
            email=student_account.email,
            school_year=school_year_data,
            pin=student_account.pin
        )

        subjects_stmt = (
            select(Subject)
            .options(selectinload(Subject.chapters)) # Eager load chapters
            .where(Subject.year_id == student_account.year_id, Subject.is_active)
            .order_by(Subject.name)
        )
        subjects_db = db.execute(subjects_stmt).scalars().unique().all()
        
        # Create subscription context to check which subjects are subscribed
        subscription_context = SubscriptionContext.from_subscription_info(
            is_subscribed=is_subscribed,
            trial_active=trial_active,
            subscribed_subjects=subscribed_subjects,
            all_year_subjects=subjects_db,
            actual_subscription_type=actual_subscription_type
        )
        
        subjects_list: List[SubjectResponseSchema] = []
        for subject_db_item in subjects_db:
            is_subject_subscribed = subscription_context.is_subject_subscribed(subject_db_item.public_id)
            
            subjects_list.append(
                SubjectResponseSchema(
                    public_id=subject_db_item.public_id,
                    name=subject_db_item.name,
                    chapter_count=len(subject_db_item.chapters),
                    is_subscribed=is_subject_subscribed,
                    image_url=subject_db_item.image_url
                )
            )
        logger.info(f"Successfully fetched dashboard data for student {student_account.public_id}")
        return GetStudentDashboardResponse(
            profile=profile,
            subjects=subjects_list
        )
