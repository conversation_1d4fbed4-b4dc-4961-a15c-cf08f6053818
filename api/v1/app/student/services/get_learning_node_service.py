# api/v1/app/student/services/get_learning_node_service.py
from sqlalchemy.orm import Session, selectinload
from sqlalchemy import select
from typing import Optional, List, Dict
from loguru import logger

from db.models import LearningNode, ChildAccount, Exercise # Keep Exercise for selectinload type hint
from db.models.learning_node import LearningNodeTypeEnum
from db.models.associations.learning_node_exercise import LearningNodeExerciseAssociation # For selectinload
from db.models.content import Chapter

from ..schemas.response import GetLearningNodeResponse, NodeContentUnion
from core.exception_handling.exceptions.custom_exceptions import NotFoundError, ServiceError
from api.v1.common.schemas import AppErrorCode
from api.v1.app.common.subscription_types import SubscriptionContext

# Import handlers
from .learning_node_content_handlers import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ReadingContentHandler, ListeningContentHand<PERSON>,
    Vocabulary<PERSON><PERSON>nt<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GrammarContentHandler
)

class GetLearningNodeService:
    def __init__(self):
        self.handler_map = {
            LearningNodeTypeEnum.MATH: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
            LearningNodeTypeEnum.READING: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
            LearningNodeTypeEnum.LISTENING: ListeningContentHandler,
            LearningNodeTypeEnum.VOCABULARY: VocabularyContentHandler,
            LearningNodeTypeEnum.VERB: VerbContentHandler,
            LearningNodeTypeEnum.GRAMMAR: GrammarContentHandler,
            LearningNodeTypeEnum.SPEAKING: GrammarContentHandler,
            LearningNodeTypeEnum.WRITING: GrammarContentHandler,
        }

    def get_learning_node(
        self, db: Session,
        student_account: ChildAccount,
        learning_node_public_id: str,
        is_subscribed: bool,
        trial_active: bool,
        subscribed_subjects: List[Dict],
        actual_subscription_type: Optional[str] = None
    ) -> GetLearningNodeResponse:
        logger.info(f"Fetching learning node for student: {student_account.public_id}, node: {learning_node_public_id}")

        stmt = select(LearningNode).options(
            selectinload(LearningNode.chapter).selectinload(Chapter.subject),  # Load chapter and subject for access check
            selectinload(LearningNode.exercise_associations) # Eager load associations
            .selectinload(LearningNodeExerciseAssociation.exercise) # Then the exercise for each association
            # .selectinload(Exercise.child_account_associations) # This loads ALL child associations for an exercise.
                                                              # _transform_exercise will query for the specific student.
        ).where(LearningNode.public_id == learning_node_public_id)
        
        relevant_node: Optional[LearningNode] = db.execute(stmt).scalar_one_or_none()

        if not relevant_node:
            logger.warning(f"Learning node not found: {learning_node_public_id}")
            raise NotFoundError(
                message="Learning node not found",
                error_code=AppErrorCode.LEARNING_NODE_NOT_FOUND,
                entity_name="LearningNode",
                identifier=learning_node_public_id
            )

        # Create subscription context
        subscription_context = SubscriptionContext.from_subscription_info(
            is_subscribed=is_subscribed,
            trial_active=trial_active,
            subscribed_subjects=subscribed_subjects,
            actual_subscription_type=actual_subscription_type
        )
        
        # Check subscription through chapter->subject relationship
        is_subscribed = False
        if relevant_node.chapter and relevant_node.chapter.subject:
            subject_public_id = relevant_node.chapter.subject.public_id
            is_subscribed = subscription_context.is_subject_subscribed(subject_public_id)
        else:
            # Orphaned learning nodes (no chapter/subject)
            # TODO: Decide on subscription policy for orphaned nodes
            # For now, consider subscribed only during trial
            is_subscribed = trial_active
            logger.warning(
                f"Learning node {learning_node_public_id} has no chapter/subject association. "
                f"Considered subscribed: {is_subscribed}"
            )
        
        logger.info(
            f"Subscription check for learning node {learning_node_public_id}: "
            f"student={student_account.public_id}, is_subscribed={is_subscribed}, "
            f"subscription_status={subscription_context.subscription_status}"
        )
        
        # Count total exercises before limiting
        total_exercises = len(relevant_node.exercises)

        content_response_union = self._get_learning_node_content_via_handler(
            db, student_account, relevant_node, is_subscribed
        )
        
        # The 'exercises' list is part of the content_response_union object itself
        shown_exercises = len(content_response_union.exercises) if hasattr(content_response_union, 'exercises') else 0

        # Determine if content will be truncated (for non-subscribers with text content)
        content_truncated = not is_subscribed and relevant_node.node_type in [
            LearningNodeTypeEnum.MATH,
            LearningNodeTypeEnum.READING,
            LearningNodeTypeEnum.LISTENING,
            LearningNodeTypeEnum.VOCABULARY,
            LearningNodeTypeEnum.VERB,
            LearningNodeTypeEnum.GRAMMAR,
            LearningNodeTypeEnum.WRITING,
            LearningNodeTypeEnum.SPEAKING
        ]
        
        return GetLearningNodeResponse(
            public_id=relevant_node.public_id, # Use ID from fetched node
            title=relevant_node.title,
            type=relevant_node.node_type, # Pydantic will serialize Enum to value
            exercise_count=total_exercises,  # Use total count, not limited count
            description=relevant_node.description,
            content=content_response_union,
            is_subscribed=is_subscribed,
            total_exercises=total_exercises,
            shown_exercises=shown_exercises,
            content_truncated=content_truncated
        )

    def _get_learning_node_content_via_handler(
        self, db: Session,
        student_account: ChildAccount,
        learning_node_db: LearningNode,
        is_subscribed: bool
    ) -> NodeContentUnion:
        node_type = learning_node_db.node_type
        HandlerClass = self.handler_map.get(node_type)

        if not HandlerClass:
            logger.error(f"No handler configured for learning node type: {node_type.value}")
            # Potentially add a default handler or raise a more specific error
            raise ServiceError(
                message=f"Content processing for node type {node_type.value} is not implemented.",
                error_code=AppErrorCode.SERVICE_ERROR # Or a new error code like NODE_TYPE_UNSUPPORTED
            )

        handler_instance = HandlerClass(db, student_account, is_subscribed)
        return handler_instance.process(learning_node_db)