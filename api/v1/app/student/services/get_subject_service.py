from sqlalchemy.orm import Session, selectinload
from sqlalchemy import select
from loguru import logger
from typing import List, Dict, Optional

from db.models.content import Subject, Chapter
from db.models.learning_node import LearningNode
from db.models.account import ChildAccount
from db.models.media import VideoFile
from core.exception_handling.exceptions import NotFoundError
from api.v1.common.schemas import AppErrorCode
from api.v1.app.common.subscription_types import SubscriptionContext
from ..schemas.response import (
    Subject as SubjectResponseSchema,
    Chapter as ChapterResponseSchema, LearningNodePreview, GetSubjectResponse
)

class GetSubjectService:
    def get_subject(
        self, db: Session, 
        student_account: ChildAccount, 
        subject_public_id: str,
        is_subscribed: bool,
        trial_active: bool,
        subscribed_subjects: List[Dict],
        actual_subscription_type: Optional[str] = None
    ) -> GetSubjectResponse:
        logger.info(f"Fetching subject details for student: {student_account.public_id}, subject: {subject_public_id}")

        subject_stmt = select(Subject).options(
            selectinload(Subject.chapters)
                .selectinload(Chapter.learning_nodes)
                .selectinload(LearningNode.exercise_associations)  # Ensure this is present
        ).where(
            Subject.public_id == subject_public_id,
            Subject.year_id == student_account.year_id  # Ensure the subject belongs to the student's year
        )

        subject_db = db.execute(subject_stmt).scalar_one_or_none()

        if not subject_db:
            logger.warning(f"Subject not found: {subject_public_id} for student: {student_account.public_id}")
            raise NotFoundError(
                message=f"Subject with public_id {subject_public_id} not found.",
                error_code=AppErrorCode.SUBJECT_NOT_FOUND,
                entity_name="Subject",
                identifier=subject_public_id
            )

        # Create subscription context
        subscription_context = SubscriptionContext.from_subscription_info(
            is_subscribed=is_subscribed,
            trial_active=trial_active,
            subscribed_subjects=subscribed_subjects,
            actual_subscription_type=actual_subscription_type
        )
        
        is_subject_subscribed = subscription_context.is_subject_subscribed(subject_db.public_id)
        
        logger.info(
            f"Subscription check for subject {subject_public_id}: "
            f"student={student_account.public_id}, is_subscribed={is_subject_subscribed}, "
            f"subscription_status={subscription_context.subscription_status}"
        )

        subject_response = SubjectResponseSchema(
            public_id=subject_db.public_id,
            name=subject_db.name,
            chapter_count=len(subject_db.chapters),
            is_subscribed=is_subject_subscribed,
            image_url=subject_db.image_url
        )

        chapters_response = []

        for chapter in sorted(subject_db.chapters, key=lambda ch: ch.ordering):

            current_chapter = ChapterResponseSchema(
                public_id=chapter.public_id,
                title=chapter.title,
                description=chapter.description,
                ordering=chapter.ordering,
                learning_nodes=[]
            )

            for ln in chapter.learning_nodes:
                exercise_associations = ln.exercise_associations  # This will be efficient due to eager loading
                
                # Check for video information in learning node content
                has_video = False
                video_length = None
                
                if ln._content and isinstance(ln._content, dict):
                    video_public_id = ln._content.get('video_public_id')
                    
                    if video_public_id:
                        has_video = True
                        # Query VideoFile to get duration
                        video_file = db.execute(
                            select(VideoFile).where(VideoFile.public_id == video_public_id)
                        ).scalar_one_or_none()
                        
                        if video_file and video_file.duration_seconds:
                            # Convert float seconds to int seconds
                            video_length = int(video_file.duration_seconds)
                
                current_chapter.learning_nodes.append(
                    LearningNodePreview(
                        public_id=ln.public_id,
                        title=ln.title,
                        type=ln.node_type.value,
                        description=ln.description,
                        exercise_count=len(exercise_associations),
                        has_video=has_video,
                        video_length=video_length
                    )
                )

            chapters_response.append(current_chapter)

        return GetSubjectResponse(
            subject=subject_response,
            chapters=chapters_response,
            is_subscribed=is_subject_subscribed,
            subscription_status=subscription_context.subscription_status
        )
