"""
Subscription-related type definitions and utilities for content access control.
"""
from typing import Literal, Optional, List, Dict, Set
from dataclasses import dataclass

# Type definitions
SubscriptionStatus = Literal["active", "trial", "none"]
SubscriptionType = Literal["SC", "YF"]

# Configuration
PREVIEW_EXERCISE_LIMIT = 2  # Number of exercises shown to non-subscribers
PREVIEW_TEXT_LENGTH = 1000  # Maximum characters for text preview (roughly 2 paragraphs)
PREVIEW_PARAGRAPH_LIMIT = 4  # Maximum paragraphs for text preview

assert PREVIEW_EXERCISE_LIMIT > 0, "Preview exercise limit must be positive"
assert PREVIEW_TEXT_LENGTH > 0, "Preview text length must be positive"
assert PREVIEW_PARAGRAPH_LIMIT > 0, "Preview paragraph limit must be positive"


@dataclass
class SubscriptionContext:
    """Encapsulates all subscription-related data for a user."""
    is_subscribed: bool
    trial_active: bool
    subscription_status: SubscriptionStatus
    subscription_type: Optional[SubscriptionType]
    subscribed_subjects: List[Dict[str, str]]
    subscribed_subject_ids: Set[str]  # Subjects included in their subscription
    
    @classmethod
    def from_subscription_info(cls, is_subscribed: bool, trial_active: bool, 
                              subscribed_subjects: List[Dict[str, str]], 
                              all_year_subjects: Optional[List] = None,
                              actual_subscription_type: Optional[str] = None) -> 'SubscriptionContext':
        """Create a SubscriptionContext from subscription info."""
        # Determine subscription status
        subscription_status = determine_subscription_status(is_subscribed, trial_active)
        
        # Create set of subscribed subject IDs
        subscribed_subject_ids = {s['public_id'] for s in subscribed_subjects}
        
        # During trial, all subjects are considered subscribed
        if trial_active:
            if all_year_subjects:
                # If we have all year subjects, use them
                subscribed_subject_ids = {s.public_id for s in all_year_subjects}
            # Note: If all_year_subjects is None but trial is active,
            # the SubscriptionDependency already populated all subjects in subscribed_subjects
        
        # Use actual subscription type if provided, otherwise use heuristic
        if actual_subscription_type and actual_subscription_type in ["SC", "YF"]:
            subscription_type = actual_subscription_type  # type: ignore
        else:
            # Fallback to heuristic-based determination
            subscription_type = determine_subscription_type(
                is_subscribed, trial_active, subscribed_subjects
            )
        
        return cls(
            is_subscribed=is_subscribed,
            trial_active=trial_active,
            subscription_status=subscription_status,
            subscription_type=subscription_type,
            subscribed_subjects=subscribed_subjects,
            subscribed_subject_ids=subscribed_subject_ids
        )
    
    def is_subject_subscribed(self, subject_public_id: str) -> bool:
        """Check if a subject is included in the user's subscription."""
        return subject_public_id in self.subscribed_subject_ids


def determine_subscription_status(is_subscribed: bool, trial_active: bool) -> SubscriptionStatus:
    """Determine the subscription status based on subscription and trial flags."""
    if trial_active:
        return "trial"
    elif is_subscribed:
        return "active"
    return "none"


def determine_subscription_type(is_subscribed: bool, trial_active: bool, 
                               subscribed_subjects: List[Dict[str, str]]) -> Optional[SubscriptionType]:
    """
    Determine subscription type based on subscription data.
    
    Note: This is a simplified version. In production, this should query
    the actual subscription records to determine if it's SC or YF.
    """
    if not is_subscribed or trial_active:
        return None
    
    # This is a placeholder logic. The actual implementation should:
    # 1. Query the ActiveSubscriptionPlanLink table
    # 2. Check the chosen_plan_type field
    # 3. Return the appropriate type
    
    # For now, we can only infer based on the number of subjects
    # which is not reliable but maintains current behavior
    if len(subscribed_subjects) > 0:
        # TODO: Replace with actual subscription type from database
        return "SC"  # Default to SC for now
    
    return None


