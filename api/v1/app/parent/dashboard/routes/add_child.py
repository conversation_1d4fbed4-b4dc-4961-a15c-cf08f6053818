from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from db.database import get_db
from api.v1.app.parent.dashboard.schemas.request import CreateChildRequestSchema
from api.v1.app.parent.dashboard.schemas.response import AddChildResponseSchema
from dependencies.auth_dependencies.auth_dependency import AuthDependency
from ..services.add_child_service import AddChildService
from core.exception_handling.exceptions.custom_exceptions import AuthorizationError
from core.exception_handling.exceptions.custom_exceptions import AppErrorCode
from dependencies.auth_dependencies.base_auth_dependency import UserType

router = APIRouter()
require_auth = AuthDependency()

@router.post("/add-child-account", response_model=AddChildResponseSchema, status_code=201)
def add_child(
    request_data: CreateChildRequestSchema,
    db: Session = Depends(get_db),
    auth: AuthDependency = Depends(require_auth)
):

    if not auth.user_type == UserType.parent or not auth.account:
        raise AuthorizationError(
            message="User is not authorized to access this endpoint.",
            log_message="User is not authorized to access this endpoint.",
            error_code=AppErrorCode.PERMISSION_DENIED
        )
    
    add_child_service = AddChildService(db=db)
    return add_child_service.add_child(request_data=request_data, parent_account=auth.account)