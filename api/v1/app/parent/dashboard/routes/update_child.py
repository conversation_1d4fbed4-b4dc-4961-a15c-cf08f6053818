from fastapi import APIRouter, Depends, Path
from sqlalchemy.orm import Session
from loguru import logger
from db.database import get_db
from api.v1.app.parent.dashboard.schemas.request import UpdateChildRequestSchema
from api.v1.app.parent.dashboard.schemas.response import UpdateChildResponseSchema
from dependencies.auth_dependencies.auth_dependency import AuthDependency
from ..services.update_child_service import UpdateChildService  
from core.exception_handling.exceptions.custom_exceptions import PermissionDeniedError, AppErrorCode
from dependencies.auth_dependencies.base_auth_dependency import UserType

router = APIRouter()
require_auth = AuthDependency()

@router.put("/update-child-account", response_model=UpdateChildResponseSchema)
def update_child(
    request_data: UpdateChildRequestSchema,
    db: Session = Depends(get_db),
    auth: AuthDependency = Depends(require_auth)
):
    logger.info("Updating child {} for parent {}", request_data.child_public_id, auth.account.public_id)

    if not auth.user_type == UserType.parent or not auth.account:
        raise PermissionDeniedError(
            message="User is not authorized to access this endpoint.",
            log_message="User is not authorized to access this endpoint.",
            error_code=AppErrorCode.PERMISSION_DENIED  
        )

    update_child_service = UpdateChildService(db=db)
    return update_child_service.update_child(
        request_data=request_data,
        child_public_id=request_data.child_public_id,
        parent_account=auth.account
    )