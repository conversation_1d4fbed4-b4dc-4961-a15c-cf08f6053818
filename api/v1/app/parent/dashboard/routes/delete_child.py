# /Users/<USER>/Coding/project_edu/project_edu_backend/api/v1/app/parent/dashboard/routes/delete_child.py
from fastapi import APIRouter, Depends, Path
from sqlalchemy.orm import Session

from db.database import get_db
from api.v1.app.parent.dashboard.schemas.response import DeleteChildResponseSchema
from dependencies.auth_dependencies.auth_dependency import AuthDependency
from ..services.delete_child_service import DeleteChildService
from core.exception_handling.exceptions.custom_exceptions import AuthorizationError
from core.exception_handling.exceptions.custom_exceptions import AppErrorCode
from dependencies.auth_dependencies.base_auth_dependency import UserType
from loguru import logger

router = APIRouter()
require_auth = AuthDependency()

@router.delete("/delete-child-account/{child_public_id}", response_model=DeleteChildResponseSchema)
def delete_child(
    child_public_id: str,
    db: Session = Depends(get_db),
    auth: AuthDependency = Depends(require_auth)
):
    logger.info("Attempting to delete child {} for parent {}", child_public_id, auth.account.public_id)
    
    if not auth.user_type == UserType.parent or not auth.account:
        raise AuthorizationError(
            message="User is not authorized to access this endpoint.",
            log_message="User is not authorized to access this endpoint.",
            error_code=AppErrorCode.PERMISSION_DENIED
        )
    
    delete_child_service = DeleteChildService(db=db)
    return delete_child_service.delete_child(child_public_id=child_public_id, parent_account=auth.account)