from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from core.exception_handling.exceptions.custom_exceptions import AuthorizationError
from core.exception_handling.exceptions.custom_exceptions import AppErrorCode
from db.database import get_db
from api.v1.app.parent.dashboard.schemas.response import ParentDashboardResponseSchema
from dependencies.auth_dependencies.auth_dependency import AuthDependency
from ..services.get_dashboard_service import GetDashboardService
from dependencies.auth_dependencies.base_auth_dependency import UserType

router = APIRouter()
require_auth = AuthDependency()

@router.get("/get-dashboard", response_model=ParentDashboardResponseSchema) 
def get_dashboard(
    db: Session = Depends(get_db),
    auth: AuthDependency = Depends(require_auth)
):
    get_dashboard_service = GetDashboardService(db=db)

    if not auth.user_type == UserType.parent or not auth.account:
        raise AuthorizationError(
            message="User is not authorized to access this endpoint.",
            log_message="User is not authorized to access this endpoint.",
            error_code=AppErrorCode.PERMISSION_DENIED
        )
    
    return get_dashboard_service.get_dashboard_data(parent_account=auth.account)