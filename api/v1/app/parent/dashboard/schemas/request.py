from pydantic import BaseModel, Field, EmailStr
from typing import Optional
from .response import LanguageEnum

class CreateChildRequestSchema(BaseModel):
    name: str = Field(..., min_length=1, description="Child's name")
    email: EmailStr = Field(..., description="Child's email address")
    year_public_id: str = Field(..., description="Public ID of the child's school year")
    language: LanguageEnum = Field(..., description="Language of the child")
    pin: Optional[str] = Field(None, min_length=4, max_length=4,
                               pattern=r"^\d{4}$", description="Optional 4-digit PIN for the child")

class UpdateChildRequestSchema(BaseModel):
    child_public_id: str
    name: Optional[str] = Field(None, min_length=1, description="Child's new name")
    email: Optional[EmailStr] = Field(None, description="Child's new email address")
    year_public_id: Optional[str] = Field(None, description="Public ID of the child's new school year")
    language: Optional[LanguageEnum] = Field(None, description="Language of the child")
    pin: Optional[str] = Field(None, min_length=4, max_length=4,
                               pattern=r"^\d{4}$", description="Optional new 4-digit PIN for the child")
