from pydantic import BaseModel, EmailStr
from typing import List, Optional
import enum


class LanguageEnum(str, enum.Enum):
    EN = "en"
    DE = "de"
    FR = "fr"
    LU = "lu"


class SchoolYearSchema(BaseModel):
    public_id: str
    name: str


class SubjectOverviewSchema(BaseModel):
    public_id: str
    name: str
    description: Optional[str] = None
    image_url: Optional[str] = None


class YearContentOverviewSchema(BaseModel):
    year_public_id: str
    year_name: str
    is_classique: bool
    is_general: bool
    subjects: List[SubjectOverviewSchema]


class ParentConnectedProfileSchema(BaseModel):
    public_id: str
    name: str
    email: Optional[EmailStr] = None
    school_year: SchoolYearSchema
    pin: Optional[str] = None
    profile_pic_url: Optional[str] = "/logo.svg"  # Default value

class ParentDashboardResponseSchema(BaseModel):
    profiles: List[ParentConnectedProfileSchema]
    school_years: List[SchoolYearSchema]
    content_overview: Optional[List[YearContentOverviewSchema]] = None

# Generic message response for operations like delete
class GenericMessageResponse(BaseModel):
    message: str
    success: Optional[bool] = None

# ChildResponseSchema for add/update operations
class UpdateChildResponseSchema(ParentConnectedProfileSchema):
    message: Optional[str] = None
    public_id: str
    name: str
    language: LanguageEnum
    email: EmailStr
    school_year: SchoolYearSchema
    pin: Optional[str] = None

class DeleteChildResponseSchema(BaseModel):
    message: Optional[str] = None

class AddChildResponseSchema(BaseModel):
    message: Optional[str] = None
    public_id: str
    name: str
    language: LanguageEnum
    email: EmailStr
    school_year: SchoolYearSchema
    pin: Optional[str] = None