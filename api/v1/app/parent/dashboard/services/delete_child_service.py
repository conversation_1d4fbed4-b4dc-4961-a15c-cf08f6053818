from sqlalchemy.orm import Session
from sqlalchemy import select, and_
from loguru import logger  
from db.models import Account, ChildAccount
from api.v1.app.parent.dashboard.schemas.response import DeleteChildResponseSchema
from core.exception_handling.exceptions.custom_exceptions import NotFoundError  
from api.v1.common.schemas import AppErrorCode

class DeleteChildService:
    def __init__(self, db: Session):
        self.db = db

    def delete_child(self, child_public_id: str, parent_account: Account) -> DeleteChildResponseSchema:
        logger.info("Attempting to delete child {} for parent {}", child_public_id, parent_account.public_id)

        child_stmt = select(ChildAccount).where(
            and_(
                ChildAccount.public_id == child_public_id,
                ChildAccount.parent_account_id == parent_account.id
            )
        )
        child_account = self.db.execute(child_stmt).scalar_one_or_none()

        if not child_account:
            logger.warning("Child account {} not found or does not belong to parent {}.",
                           child_public_id, parent_account.public_id)
            raise NotFoundError(
                message="Child account not found or does not belong to this parent.",
                error_code=AppErrorCode.CHILD_ACCOUNT_NOT_FOUND,
                entity_name="ChildAccount",
                identifier=child_public_id
            )

        self.db.delete(child_account)
        self.db.commit()
        logger.info("Child account {} deleted successfully by parent {}.", child_public_id, parent_account.public_id)

        return DeleteChildResponseSchema(message="Child account deleted successfully.")