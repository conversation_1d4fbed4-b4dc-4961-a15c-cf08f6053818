from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, select, and_

from db.models import Account, ChildAccount, Year
from api.v1.app.parent.dashboard.schemas.request import UpdateChildRequestSchema
from api.v1.app.parent.dashboard.schemas.response import UpdateChildResponseSchema
from core.exception_handling.exceptions.custom_exceptions import NotFoundError, ConflictError, BadRequestError
from api.v1.common.schemas import AppErrorCode
from api.v1.app.parent.dashboard.schemas.response import SchoolYearSchema

class UpdateChildService:
    def __init__(self, db: Session):
        self.db = db

    def update_child(
        self,
        request_data: UpdateChildRequestSchema,
        child_public_id: str,
        parent_account: Account
    ) -> UpdateChildResponseSchema:

        child_stmt = (
            select(ChildAccount)
            .options(joinedload(ChildAccount.year))
            .where(and_(
                ChildAccount.public_id == child_public_id, 
                ChildAccount.parent_account_id == parent_account.id
            ))
        )
        child_account = self.db.execute(child_stmt).scalar_one_or_none()

        if not child_account:
            raise NotFoundError(
                message="Child account not found or does not belong to this parent.",
                entity_name="ChildAccount",
                identifier=child_public_id,
                error_code=AppErrorCode.CHILD_ACCOUNT_NOT_FOUND
            )

        updated_fields = False

        if request_data.name is not None and child_account.name != request_data.name:
            child_account.name = request_data.name
            updated_fields = True

        if request_data.email is not None and child_account.email != request_data.email:
            new_email_lower = request_data.email.lower()
            # Check for email conflicts
            existing_child_email_stmt = select(ChildAccount).where(
                func.lower(ChildAccount.email) == new_email_lower,
                ChildAccount.public_id != child_public_id
            )
            if self.db.execute(existing_child_email_stmt).scalar_one_or_none():
                raise ConflictError(
                    message="Email already associated with another child account.",
                    log_message=f"Email conflict: {request_data.email} already used by another child account",
                    error_code=AppErrorCode.CHILD_ACCOUNT_ALREADY_EXISTS
                )

            existing_parent_email_stmt = select(Account).where(func.lower(Account.email) == new_email_lower)
            existing_parent_account = self.db.execute(existing_parent_email_stmt).scalar_one_or_none()
            if existing_parent_account and existing_parent_account.id != child_account.parent_account_id:
                raise ConflictError(
                    message="Email already associated with a parent account.",
                    log_message=f"Email conflict: {request_data.email} already used by a parent account",
                    error_code=AppErrorCode.PARENT_ACCOUNT_ALREADY_EXISTS
                )

            child_account.email = request_data.email
            updated_fields = True

        if request_data.year_public_id is not None:
            current_year_public_id = child_account.year.public_id if child_account.year else None
            if current_year_public_id != request_data.year_public_id:
                year_stmt = select(Year).where(Year.public_id == request_data.year_public_id)
                school_year = self.db.execute(year_stmt).scalar_one_or_none()
                if not school_year:
                    raise BadRequestError(
                        message="Invalid school year ID",
                        log_message=f"School year with ID {request_data.year_public_id} not found",
                        error_code=AppErrorCode.INVALID_REQUEST
                    )
                child_account.year_id = school_year.id
                child_account.year = school_year  
                updated_fields = True

        if request_data.pin is not None:
            child_account.pin = request_data.pin
            updated_fields = True

        if not updated_fields:
            return UpdateChildResponseSchema(
                public_id=child_account.public_id,
                name=child_account.name,
                language=child_account.language,
                email=child_account.email,
                school_year=SchoolYearSchema(
                    public_id=child_account.year.public_id,
                    name=child_account.year.name
                ) if child_account.year else None,
                pin=child_account.pin,
                message="No changes detected for child account."
            )

        self.db.commit()
        self.db.refresh(child_account)

        # Ensure year relationship is loaded if it was changed by ID and not directly assigned
        # The joinedload at the beginning should handle most cases, but this is a safeguard.
        if not child_account.year and child_account.year_id:
            year_load_stmt = select(Year).where(Year.id == child_account.year_id)
            child_account.year = self.db.execute(year_load_stmt).scalar_one_or_none()

        return UpdateChildResponseSchema(
            public_id=child_account.public_id,
            name=child_account.name,
            language=child_account.language,
            email=child_account.email,
            school_year=SchoolYearSchema(
                public_id=child_account.year.public_id,
                name=child_account.year.name
            ) if child_account.year else None,
            pin=child_account.pin,
            message="Child account updated successfully."
        )