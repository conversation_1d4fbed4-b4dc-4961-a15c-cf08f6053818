from sqlalchemy.orm import Session, joinedload
from sqlalchemy import select
from typing import List, Optional
from loguru import logger

from db.models import Account, ChildAccount, Year, Subject  # noqa: F401
from api.v1.app.parent.dashboard.schemas.response import (
    ParentDashboardResponseSchema,
    ParentConnectedProfileSchema,
    SchoolYearSchema as BackendSchoolYearSchema,
    YearContentOverviewSchema,
    SubjectOverviewSchema
)

class GetDashboardService:
    def __init__(self, db: Session):
        self.db = db

    def get_dashboard_data(self, parent_account: Account) -> ParentDashboardResponseSchema:
        logger.info(f"Fetching dashboard data for parent_public_id: {parent_account.public_id}")

        # Fetch children with their school year eagerly loaded
        children_stmt = (
            select(ChildAccount)
            .options(joinedload(ChildAccount.year))
            .where(ChildAccount.parent_account_id == parent_account.id)
        )
        children_db = self.db.execute(children_stmt).scalars().all()
        logger.info(f"Found {len(children_db)} children for parent {parent_account.public_id}")

        profiles_list: List[ParentConnectedProfileSchema] = []
        for child in children_db:
            if child.year:
                school_year_data = BackendSchoolYearSchema(
                    public_id=child.year.public_id,
                    name=child.year.name
                )
            else:
                logger.warning(
                    f"Child {child.public_id} for parent {parent_account.public_id} has no school year assigned.")
                school_year_data = BackendSchoolYearSchema(public_id="unknown", name="Unknown Year")  # Placeholder

            profiles_list.append(
                ParentConnectedProfileSchema(
                    public_id=child.public_id,
                    name=child.name,
                    email=child.email,
                    school_year=school_year_data,
                    pin=child.pin
                )
            )

        # Fetch all available school years
        all_school_years_stmt = select(Year)
        all_school_years_db = self.db.execute(all_school_years_stmt).scalars().all()
        school_years_list: List[BackendSchoolYearSchema] = [
            BackendSchoolYearSchema(public_id=sy.public_id, name=sy.name) for sy in all_school_years_db
        ]

        # If no profiles exist, fetch content overview
        content_overview: Optional[List[YearContentOverviewSchema]] = None
        if not profiles_list:
            logger.info(f"No profiles found for parent {parent_account.public_id}, fetching content overview")
            content_overview = self._get_content_overview()

        logger.info(f"Successfully fetched dashboard data for parent {parent_account.public_id}")
        return ParentDashboardResponseSchema(
            profiles=profiles_list,
            school_years=school_years_list,
            content_overview=content_overview
        )

    def _get_content_overview(self) -> List[YearContentOverviewSchema]:
        """Fetch all active subjects grouped by year"""
        # Fetch all years with their subjects eagerly loaded
        years_with_subjects_stmt = (
            select(Year)
            .options(joinedload(Year.subjects))
            .order_by(Year.name)
        )
        years_db = self.db.execute(years_with_subjects_stmt).scalars().unique().all()

        content_overview = []
        for year in years_db:
            # Get active subjects for this year
            active_subjects = [
                SubjectOverviewSchema(
                    public_id=subject.public_id,
                    name=subject.name,
                    description=subject.description,
                    image_url=subject.image_url
                )
                for subject in year.subjects
                if subject.is_active
            ]

            # Only include years that have active subjects
            if active_subjects:
                content_overview.append(
                    YearContentOverviewSchema(
                        year_public_id=year.public_id,
                        year_name=year.name,
                        is_classique=year.is_classique,
                        is_general=year.is_general,
                        subjects=active_subjects
                    )
                )

        return content_overview