import uuid
from sqlalchemy.orm import Session
from sqlalchemy import select, func
from loguru import logger 
from db.models import Account, ChildAccount, Year
from api.v1.app.parent.dashboard.schemas.request import CreateChildRequestSchema
from api.v1.app.parent.dashboard.schemas.response import AddChildResponseSchema
from core.exception_handling.exceptions.custom_exceptions import ValidationError, ConflictError
from api.v1.common.schemas import AppErrorCode 
from api.v1.app.parent.dashboard.schemas.response import SchoolYearSchema

class AddChildService:
    def __init__(self, db: Session):
        self.db = db

    def add_child(self, request_data: CreateChildRequestSchema, parent_account: Account) -> AddChildResponseSchema:
        logger.info("Attempting to add child for parent_public_id: {}, child_email: {}",
                    parent_account.public_id, request_data.email)

        # Validate year_public_id
        year_stmt = select(Year).where(Year.public_id == request_data.year_public_id)
        school_year = self.db.execute(year_stmt).scalar_one_or_none()
        if not school_year:
            logger.warning("Invalid school year ID: {} provided by parent {}.",
                           request_data.year_public_id, parent_account.public_id)
            raise ValidationError(
                message="Invalid school year ID",
                error_code=AppErrorCode.INVALID_REQUEST, 
                details=[{"field": "year_public_id", "message": "School year not found."}]
            )

        # Check if email is already used by another child or parent
        existing_child_email_stmt = select(ChildAccount).filter(
            func.lower(ChildAccount.email) == func.lower(request_data.email))
        existing_child_with_email = self.db.execute(existing_child_email_stmt).scalar_one_or_none()
        if existing_child_with_email:
            logger.warning("Child email conflict: {} already exists for another child. Parent: {}",
                           request_data.email, parent_account.public_id)
            raise ConflictError(
                message="Email already associated with another child account.",
                error_code=AppErrorCode.CHILD_ACCOUNT_ALREADY_EXISTS
            )

        # Check if email is already used by a parent account that is not the current parent
        existing_parent_email_stmt = select(Account).filter(
            func.lower(Account.email) == func.lower(request_data.email),
            Account.id != parent_account.id
        )
        existing_parent_with_email = self.db.execute(existing_parent_email_stmt).scalar_one_or_none()
        if existing_parent_with_email:
            logger.warning("Child email conflict: {} already exists for a parent account. Parent attempting add: {}",
                           request_data.email, parent_account.public_id)
            raise ConflictError(
                message="Email already associated with a parent account.",
                error_code=AppErrorCode.CHILD_ACCOUNT_ALREADY_EXISTS
            )

        new_child = ChildAccount(
            public_id=str(uuid.uuid4()),
            name=request_data.name,
            email=request_data.email,
            year_id=school_year.id,
            parent_account_id=parent_account.id,
            language=request_data.language,
            pin=request_data.pin, 
            is_verified=True  # Child created by parent is implicitly verified by parent
        )
        self.db.add(new_child)
        self.db.commit()
        self.db.refresh(new_child)
        logger.info("Child account {} created successfully for parent {}.",
                    new_child.public_id, parent_account.public_id)

        

        return AddChildResponseSchema(
            public_id=str(new_child.public_id),
            name=new_child.name,
            email=new_child.email,
            school_year=SchoolYearSchema(
                public_id=school_year.public_id,
                name=school_year.name
            ),
            pin=new_child.pin,
            language=new_child.language, 
            message="Child account created successfully."
        )