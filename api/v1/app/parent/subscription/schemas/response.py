# <schema>
from __future__ import annotations
from pydantic import BaseModel, Field, field_serializer # Added field_serializer
from typing import List, Optional, Dict, Any # Added Dict, Any
from db.models.subscription import PlanTypeEnum, BillingPeriodEnum, SubscriptionStatusType
from db.models.subscription_changes import SubscriptionChangeType, SubscriptionChangeStatus # Assuming this exists
from datetime import datetime

# --- Existing Schemas (mostly unchanged, ensure they are defined above this point) ---

class SubjectBasicInfoSchema(BaseModel): # Ensure this is defined
    public_id: str
    name: str
    description: Optional[str] = None
    # Removed year_name and year_public_id as they are redundant when subjects are grouped by year
    # year_name: Optional[str] = None
    # year_public_id: Optional[str] = None
    model_config = {
        "from_attributes": True
    }

class PricingTierSchema(BaseModel):
    up_to: str  # "inf" for unlimited, or a number as string
    unit_price_cents: int  # Price in cents
    unit_price_display: float  # Price in currency units (e.g., 33.00 for €33.00)
    
    model_config = {
        "from_attributes": True
    }

class OfferingPriceDetailSchema(BaseModel):
    billing_period: BillingPeriodEnum
    stripe_price_id: Optional[str] = None
    display_price: Optional[float] = None # For YF flat pricing, None for SC plans
    currency: Optional[str] = None
    plan_type: PlanTypeEnum # To distinguish SC from YF prices within the list
    
    # For SC plans with tiered pricing
    tiers: Optional[List[PricingTierSchema]] = None  # Tiered pricing structure for SC plans
    
    model_config = {
        "from_attributes": True
    }

# --- NEW/MODIFIED Schemas for the /plans endpoint ---

class YearPlanOfferingSchema(BaseModel):
    """
    Represents all available subscription offerings for a specific academic year/system.
    """
    year_public_id: str
    year_name: str
    # The subscription_option_public_id is the base for constructing checkout selections
    # It represents the unique combination of (year + price_version)
    subscription_option_public_id: str
    
    available_subjects: List[SubjectBasicInfoSchema] = [] # All subjects for this year
    
    # Combined list of all pricing options (SC & YF, Monthly & Yearly) for this year
    # The frontend can filter/display these as needed.
    pricing_options: List[OfferingPriceDetailSchema] = []

    model_config = {
        "from_attributes": True
    }

class SubscriptionPlanListResponse(BaseModel): # MODIFIED
    """
    The main response for the /plans endpoint, now a list of year-grouped offerings.
    """
    eligible_price_version_slug: str # To inform frontend which price cohort is active
    eligible_price_version_display_name: Optional[str] = None
    year_offerings: List[YearPlanOfferingSchema]

# --- Other schemas (CreateCheckoutSessionResponse, etc.) remain as they were ---
# ... (rest of your existing schema file) ...

class CreateCheckoutSessionResponse(BaseModel):
    checkout_session_id: str
    checkout_session_url: str
    flow_type: str = Field(default="checkout", description="Type of flow: 'checkout' or 'direct_update'")
    message: Optional[str] = Field(None, description="Human-readable message for the frontend")
    items_added: Optional[int] = Field(None, description="Number of items added (for direct updates)")
    discount_applied: bool = Field(default=False, description="Whether discount was successfully applied")

class CreatePortalSessionResponse(BaseModel):
    portal_session_url: str

class ActiveSubscriptionPlanLinkDetailSchema(BaseModel):
    public_id: str
    subscription_option_public_id: Optional[str] = None
    subscription_plan_name: Optional[str] = None
    plan_type: Optional[PlanTypeEnum] = None
    chosen_stripe_price_id: Optional[str] = None
    chosen_billing_period: Optional[BillingPeriodEnum] = None
    stripe_subscription_item_id: Optional[str] = None
    quantity: int
    selected_subjects: List[SubjectBasicInfoSchema] = []
    model_config = {
        "from_attributes": True
    }

class ActiveSubscriptionDetailResponse(BaseModel):
    public_id: str
    parent_account_public_id: str # Assuming this should be parent_account.public_id
    stripe_subscription_id: str
    status: SubscriptionStatusType
    current_period_start: Optional[datetime] = None
    current_period_end: Optional[datetime] = None
    cancel_at_period_end: bool
    plan_links: List[ActiveSubscriptionPlanLinkDetailSchema] = []
    created_at: datetime
    updated_at: datetime
    model_config = {
        "from_attributes": True
    }

class UpdateSubscriptionResponse(BaseModel):
    message: str
    active_subscription: Optional[ActiveSubscriptionDetailResponse] = None

class ManageSelectedSubjectsResponse(BaseModel):
    message: str
    updated_plan_link: ActiveSubscriptionPlanLinkDetailSchema

class VerifyDiscountCodeResponse(BaseModel):
    message: str
    stripe_monthly_id: Optional[str] = None
    stripe_yearly_id: Optional[str] = None
    public_code: str
    is_valid: bool
    applicable_to: Optional[str] = None  # "monthly", "yearly", or "both"

class CheckoutSessionStatusResponse(BaseModel):
    status: str
    message: Optional[str] = None
    subscription_details: Optional[Dict[str, Any]] = Field(
        None, description="Basic non-sensitive information about the subscription if completed.")
    model_config = {
        "from_attributes": True
    }

class PauseSubscriptionResponse(BaseModel):
    message: str
    pause_start_date: datetime
    pause_end_date: datetime
    status: str
    model_config = {
        "from_attributes": True
    }

class ResumeSubscriptionResponse(BaseModel):
    message: str
    resumed_at: datetime
    subscription_status: SubscriptionStatusType
    model_config = {
        "from_attributes": True
    }

class CancelScheduledPauseResponse(BaseModel):
    message: str
    canceled_pause_id: str
    canceled: bool
    model_config = {
        "from_attributes": True
    }

class PlanItemAvailableAction(BaseModel):
    action: str = Field(..., description="Action type: 'change_billing_period', 'remove', 'update_quantity'")
    description: str = Field(..., description="Human-readable description of the action")
    enabled: bool = Field(..., description="Whether this action is currently available")
    reason: Optional[str] = Field(None, description="Reason why action might be disabled")
    model_config = {
        "from_attributes": True
    }

class PriceComparisonInfo(BaseModel):
    """Information about price differences between current and latest pricing"""
    current_price: float = Field(..., description="Current price per unit")
    latest_price: float = Field(..., description="Latest price per unit if user rejoins")
    price_increase: float = Field(..., description="Price increase amount")
    percentage_increase: float = Field(..., description="Percentage increase")
    currency: str = Field(..., description="Currency code")
    
    model_config = {
        "from_attributes": True
    }

class PricingVersionInfo(BaseModel):
    """Information about user's pricing version"""
    current_version_name: str = Field(..., description="Name of user's current pricing version")
    current_version_slug: Optional[str] = Field(None, description="Slug of user's current pricing version")
    latest_version_name: str = Field(..., description="Name of the latest pricing version")
    latest_version_slug: Optional[str] = Field(None, description="Slug of the latest pricing version")
    is_grandfathered: bool = Field(..., description="Whether user has grandfathered pricing")
    
    model_config = {
        "from_attributes": True
    }

class CurrentSubscriptionPlanItem(BaseModel):
    stripe_subscription_item_id: str
    plan_link_public_id: str
    year_public_id: str
    year_name: str
    plan_type: PlanTypeEnum
    billing_period: BillingPeriodEnum
    quantity: int
    selected_subjects: List[SubjectBasicInfoSchema]
    available_subjects: Optional[List[SubjectBasicInfoSchema]] = None
    price_per_subject: Optional[float] = None
    currency: Optional[str] = None
    # Add pricing information from plans
    pricing_tiers: Optional[List[PricingTierSchema]] = None
    stripe_price_id: Optional[str] = None
    available_actions: List[PlanItemAvailableAction]
    # New fields for pricing comparison
    price_comparison: Optional[PriceComparisonInfo] = Field(None, description="Price comparison if on grandfathered pricing")
    model_config = {
        "from_attributes": True
    }

class SubscriptionDetail(BaseModel):
    public_id: str = Field(..., description="Subscription public ID")
    parent_account_public_id: str = Field(..., description="Parent account public ID")
    subscription_public_id: str = Field(..., description="Subscription public ID (same as public_id)")
    stripe_subscription_id: str
    status: SubscriptionStatusType
    current_period_start: Optional[datetime] = None
    current_period_end: Optional[datetime] = None
    cancel_at_period_end: bool
    plan_items: List[CurrentSubscriptionPlanItem] = []
    total_monthly_cost: Optional[str] = Field(None, description="Estimated monthly cost")
    next_billing_date: Optional[datetime] = None
    can_pause: bool = Field(False, description="Whether subscription can be paused")
    can_add_items: bool = Field(False, description="Whether new items can be added")
    pause_info: Optional[Dict[str, Any]] = Field(None, description="Pause availability info if applicable")
    pricing_version_info: Optional[PricingVersionInfo] = Field(None, description="Information about pricing versions")
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @field_serializer('current_period_start', 'current_period_end', 'next_billing_date', 'created_at', 'updated_at')
    def serialize_datetime(self, dt: Optional[datetime]) -> Optional[str]:
        if dt is None: return None
        from datetime import timezone
        if dt.tzinfo is not None: dt = dt.astimezone(timezone.utc)
        return dt.replace(tzinfo=None).isoformat() + 'Z'
    model_config = { "from_attributes": True }

class CurrentSubscriptionResponse(BaseModel):
    has_subscription: bool = Field(..., description="Whether parent has any active subscriptions")
    parent_account_public_id: str = Field(..., description="Parent account public ID")
    subscriptions: List[SubscriptionDetail] = Field(default_factory=list, description="List of all active subscriptions")
    
    # Deprecated fields for backward compatibility (data from first subscription)
    public_id: Optional[str] = Field(None, description="DEPRECATED: Use subscriptions[0].public_id")
    subscription_public_id: Optional[str] = Field(None, description="DEPRECATED: Use subscriptions[0].public_id")
    stripe_subscription_id: Optional[str] = Field(None, description="DEPRECATED: Use subscriptions[0].stripe_subscription_id")
    status: Optional[SubscriptionStatusType] = Field(None, description="DEPRECATED: Use subscriptions[0].status")
    current_period_start: Optional[datetime] = Field(None, description="DEPRECATED: Use subscriptions[0].current_period_start")
    current_period_end: Optional[datetime] = Field(None, description="DEPRECATED: Use subscriptions[0].current_period_end")
    cancel_at_period_end: Optional[bool] = Field(None, description="DEPRECATED: Use subscriptions[0].cancel_at_period_end")
    plan_items: List[CurrentSubscriptionPlanItem] = Field(default_factory=list, description="DEPRECATED: Use subscriptions[0].plan_items")
    total_monthly_cost: Optional[str] = Field(None, description="DEPRECATED: Use subscriptions[0].total_monthly_cost")
    next_billing_date: Optional[datetime] = Field(None, description="DEPRECATED: Use subscriptions[0].next_billing_date")
    can_pause: bool = Field(False, description="DEPRECATED: Use subscriptions[0].can_pause")
    can_add_items: bool = Field(False, description="DEPRECATED: Use subscriptions[0].can_add_items")
    pause_info: Optional[Dict[str, Any]] = Field(None, description="DEPRECATED: Use subscriptions[0].pause_info")
    created_at: Optional[datetime] = Field(None, description="DEPRECATED: Use subscriptions[0].created_at")
    updated_at: Optional[datetime] = Field(None, description="DEPRECATED: Use subscriptions[0].updated_at")

    @field_serializer('current_period_start', 'current_period_end', 'next_billing_date', 'created_at', 'updated_at')
    def serialize_datetime(self, dt: Optional[datetime]) -> Optional[str]:
        if dt is None: return None
        from datetime import timezone
        if dt.tzinfo is not None: dt = dt.astimezone(timezone.utc)
        return dt.replace(tzinfo=None).isoformat() + 'Z'
    model_config = { "from_attributes": True }

class PendingChangeDetailSchema(BaseModel):
    public_id: str
    change_type: SubscriptionChangeType
    status: SubscriptionChangeStatus
    year_name: str # Assuming this is derived, e.g., from associated plan_link or option
    plan_link_public_id: str # Or subscription_option_public_id if change is pre-item
    current_quantity: int
    new_quantity: int
    current_billing_period: BillingPeriodEnum
    new_billing_period: BillingPeriodEnum
    effective_date: datetime
    created_at: datetime

    @field_serializer('effective_date', 'created_at')
    def serialize_datetime(self, dt: Optional[datetime]) -> Optional[str]:
        if dt is None: return None
        from datetime import timezone
        if dt.tzinfo is not None: dt = dt.astimezone(timezone.utc)
        return dt.replace(tzinfo=None).isoformat() + 'Z'
    model_config = { "from_attributes": True }

class PendingChangesListResponse(BaseModel):
    pending_changes: List[PendingChangeDetailSchema]
    total_count: int

class CancelPendingChangeResponse(BaseModel):
    success: bool
    message: str
# </schema>