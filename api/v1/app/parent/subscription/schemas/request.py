from pydantic import BaseModel, Field, field_validator, model_validator
from typing import Optional, List
from db.models.subscription import BillingPeriodEnum  # PlanTypeEnum no longer needed here for request

# REMOVE: PlanSelectionItem class definition

class CheckoutItemSelection(BaseModel):  # New schema for subject-first checkout
    year_public_id: str = Field(
        ..., 
        description="Public ID of the Year for this selection."
    )
    subject_public_ids: List[str] = Field(
        default_factory=list, 
        description="List of public IDs of subjects selected for this year. If empty or a special marker is used, "
        "implies a 'Year Full' (YF) plan for this year."
    )
    full_year_plan: bool = Field(
        False, 
        description="Is this a Year Full plan?"
    )
    # quantity is now derived: for SC, it's len(subject_public_ids); for YF, it's 1.
    chosen_billing_period: BillingPeriodEnum = Field(
        ...,
        description="Chosen billing period (monthly/yearly) for this selection."
    )
    # price_version_slug is global for the checkout session, not per item.

class CreateCheckoutSessionRequest(BaseModel):
    selections: List[CheckoutItemSelection] = Field(
        ..., 
        min_length=1,
        description="List of year/subject selections parent wants to subscribe to."
    )
    language: str = Field(
        ...,
        description="Language code for language-specific redirect URLs (e.g., 'en', 'de', 'fr', 'lu')."
    )
    success_url_override: Optional[str] = Field(
        None, 
        description="Optional override for the success URL."
    )
    cancel_url_override: Optional[str] = Field(
        None, 
        description="Optional override for the cancel URL."
    )
    discount_code: Optional[str] = None

class CreatePortalSessionRequest(BaseModel):
    language: str  # This might be derived from user's profile or locale header

class VerifyDiscountCodeRequest(BaseModel):
    discount_code: str
    subscription_type: Optional[str] = Field(
        None, 
        description="Optional: 'monthly' or 'yearly' context for discount verification. "
        "Helps check DiscountCode.applicable_to."
    )


class UpdateSubscriptionPlanItem(BaseModel):
    action: str = Field(
        ..., 
        description="Action to perform: 'add', 'remove', 'update_quantity', 'change_billing_period'."
    )
    # For 'add': User specifies year, subjects (or all), billing period. Backend finds/creates SC/YF plan link.
    year_public_id: Optional[str] = Field(
        None, 
        description="For 'add' action: Public ID of the Year."
    )
    subject_public_ids: Optional[List[str]] = Field(
        None, 
        description="For 'add' action (SC plan): List of subject public IDs. If empty/not provided for 'add' "
        "and year_public_id is present, could imply YF for that year."
    )
    # subscription_plan_public_id is removed for 'add' as plan is derived.
    chosen_billing_period: Optional[BillingPeriodEnum] = Field(
        None, 
        description="Billing period for the new plan component ('add' action)."
    )
    target_child_account_public_id: Optional[str] = Field(
        None, 
        description="Target child for the new plan component ('add' action)."
    )
    # selected_subject_public_ids is covered by subject_public_ids for 'add'

    # For 'remove', 'update_quantity', or 'change_billing_period'
    stripe_subscription_item_id: Optional[str] = Field(
        None, 
        description="The Stripe Subscription Item ID to remove, update quantity for, or change billing period."
    )
    # For 'change_billing_period'
    new_billing_period: Optional[BillingPeriodEnum] = Field(
        None,
        description="For 'change_billing_period' action: The new billing period to switch to."
    )
    # For 'update_quantity' or 'add' (SC plan quantity)
    new_quantity: Optional[int] = Field(
        None, 
        ge=0, 
        description="For SC 'add': number of subjects (derived from len(subject_public_ids)). "
        "For 'update_quantity': new quantity for the Stripe item."
    )

    @field_validator('action')
    def validate_action_literal(cls, value: str) -> str:
        if value not in ['add', 'remove', 'update_quantity', 'change_billing_period']:
            raise ValueError("Action must be 'add', 'remove', 'update_quantity', or 'change_billing_period'.")
        return value

    @model_validator(mode='after')
    def check_conditional_fields(cls, values: 'UpdateSubscriptionPlanItem') -> 'UpdateSubscriptionPlanItem':
        action = values.action
        if action == 'add':
            if not values.year_public_id or not values.chosen_billing_period:
                raise ValueError(
                    "For 'add' action, 'year_public_id' and 'chosen_billing_period' are required."
                )
            # Quantity for 'add' (SC) will be len(subject_public_ids). If YF, quantity is 1.
            # This validation might be better handled in the service.
            # If subject_public_ids is None or empty for 'add', it implies YF.
            # If subject_public_ids has items, it implies SC.
            if values.subject_public_ids is not None and not isinstance(values.subject_public_ids, list):
                raise ValueError("'subject_public_ids' must be a list if provided.")

        elif action == 'remove':
            if not values.stripe_subscription_item_id:
                raise ValueError(
                    f"For '{action}' action, 'stripe_subscription_item_id' is required."
                )
        elif action == 'update_quantity':
            if not values.stripe_subscription_item_id:
                raise ValueError(
                    "For 'update_quantity' action, 'stripe_subscription_item_id' is required."
                )
            if values.new_quantity is None:
                raise ValueError(
                    "For 'update_quantity' action, 'new_quantity' is required."
                )
            if values.new_quantity < 0:
                raise ValueError(
                    "For 'update_quantity' action, 'new_quantity' cannot be negative (0 means remove)."
                )
        elif action == 'change_billing_period':
            if not values.stripe_subscription_item_id:
                raise ValueError(
                    "For 'change_billing_period' action, 'stripe_subscription_item_id' is required."
                )
            if not values.new_billing_period:
                raise ValueError(
                    "For 'change_billing_period' action, 'new_billing_period' is required."
                )
        return values


class UpdateSubscriptionRequest(BaseModel):
    subscription_public_id: Optional[str] = Field(
        None,
        description="Public ID of the subscription to update. Required for 'add' actions when user has multiple subscriptions."
    )
    items_to_update: List[UpdateSubscriptionPlanItem] = Field(
        ..., 
        min_length=1, 
        description="List of changes to apply to the subscription."
    )
    proration_behavior: Optional[str] = Field(
        "create_prorations", 
        description="Stripe proration behavior (e.g., 'create_prorations', 'none', 'always_invoice')."
    )
    cancel_at_period_end: Optional[bool] = Field(
        None, 
        description="If true, the subscription will be set to cancel at the end of the current period. "
        "If false, a scheduled cancellation will be removed. If null, no change."
    )


class ManageSelectedSubjectsRequest(BaseModel):
    plan_link_public_id: str = Field(
        ..., 
        description="Public ID of the subscription plan link whose subjects are being managed."
    )
    subject_public_ids_to_select: List[str] = Field(
        ..., 
        description="List of public IDs of subjects to be selected for this plan link. Can be empty to deselect all."
    )

class EligibilityCheckRequest(BaseModel):  # Added schema
    promo_code: Optional[str] = None

class PauseSubscriptionRequest(BaseModel):
    """Request to pause subscription for summer break."""
    subscription_public_id: str = Field(
        ..., 
        description="Public ID of the subscription to pause"
    )

class ResumeSubscriptionRequest(BaseModel):
    """Request to resume a paused subscription."""
    subscription_public_id: str = Field(
        ..., 
        description="Public ID of the subscription to resume"
    )


class CancelPendingChangeRequest(BaseModel):
    """Request to cancel a pending subscription change."""
    pending_change_public_id: str = Field(
        ..., 
        description="Public ID of the pending change to cancel"
    )

class CancelScheduledPauseRequest(BaseModel):
    """Request to cancel a scheduled pause."""
    subscription_public_id: str = Field(
        ..., 
        description="Public ID of the subscription with the scheduled pause"
    )
    pause_public_id: str = Field(
        ..., 
        description="Public ID of the scheduled pause to cancel"
    )