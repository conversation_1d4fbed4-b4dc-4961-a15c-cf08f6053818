from fastapi import APIRouter
from .routes import create_checkout_session, create_portal_session, webhook, verify_discount_code
from .routes import get_plans, update_subscription, manage_selected_subjects
from .routes import get_checkout_session_status, pause_subscription, resume_subscription, get_current_subscription
from .routes import get_pending_changes, cancel_pending_change, cancel_scheduled_pause
    
router = APIRouter(
    prefix="/api/v1/app/parent/subscription",
    tags=["Parent Subscription"],
)

router.include_router(create_checkout_session.router)
router.include_router(create_portal_session.router)
router.include_router(webhook.router)
router.include_router(verify_discount_code.router)

router.include_router(get_plans.router)
router.include_router(update_subscription.router)
router.include_router(manage_selected_subjects.router)
router.include_router(get_checkout_session_status.router)
router.include_router(pause_subscription.router)
router.include_router(resume_subscription.router)
router.include_router(get_current_subscription.router)
router.include_router(get_pending_changes.router)
router.include_router(cancel_pending_change.router)
router.include_router(cancel_scheduled_pause.router)