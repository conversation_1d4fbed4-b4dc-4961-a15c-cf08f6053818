from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session as SyncSession
from sqlalchemy import select
from db.database import get_db
from db.models import Account, SubscriptionPendingChange, SubscriptionChangeStatus
from dependencies.auth_dependencies.auth_dependency import AuthDependency, AuthDependencyResponse
from ..schemas.response import PendingChangesListResponse, PendingChangeDetailSchema
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError, NotFoundError
from api.v1.common.schemas import AppErrorCode
from dependencies.auth_dependencies.base_auth_dependency import UserType
from loguru import logger

router = APIRouter()
require_auth = AuthDependency(required=True)

@router.get("/pending-changes", response_model=PendingChangesListResponse)
def get_pending_changes_route(
    db: SyncSession = Depends(get_db),
    auth_data: AuthDependencyResponse = Depends(require_auth)
):
    """
    Get all pending subscription changes for the authenticated parent.
    """
    if auth_data.user_type != UserType.parent:
        raise AuthenticationError(
            message="Only parent accounts can view subscription changes.",
            error_code=AppErrorCode.PERMISSION_DENIED
        )
    
    parent_public_id = auth_data.account_public_id
    if not parent_public_id:
        raise AuthenticationError(
            message="Not authorized! Token is invalid or missing.",
            error_code=AppErrorCode.AUTHENTICATION_REQUIRED
        )
    
    logger.info(f"Fetching pending changes for parent: {parent_public_id}")
    
    # Get parent account
    account = db.execute(
        select(Account).where(Account.public_id == parent_public_id)
    ).scalar_one_or_none()
    
    if not account:
        raise NotFoundError(
            message="Parent account not found.",
            error_code=AppErrorCode.PARENT_ACCOUNT_NOT_FOUND
        )
    
    # Get pending changes
    pending_changes_stmt = (
        select(SubscriptionPendingChange)
        .join(SubscriptionPendingChange.active_subscription)
        .where(
            SubscriptionPendingChange.active_subscription.has(parent_account_id=account.id),
            SubscriptionPendingChange.status.in_([
                SubscriptionChangeStatus.PENDING,
                SubscriptionChangeStatus.SCHEDULED
            ])
        )
        .order_by(SubscriptionPendingChange.effective_date.asc())
    )
    
    pending_changes = db.execute(pending_changes_stmt).scalars().all()
    
    # Build response
    changes_list = []
    for change in pending_changes:
        # Get plan link details
        plan_link = change.plan_link
        year_name = plan_link.subscription_option.year.name if plan_link.subscription_option.year else "Unknown"
        
        change_detail = PendingChangeDetailSchema(
            public_id=change.public_id,
            change_type=change.change_type,
            status=change.status,
            year_name=year_name,
            plan_link_public_id=plan_link.public_id,
            current_quantity=change.current_quantity,
            new_quantity=change.new_quantity,
            current_billing_period=change.current_billing_period,
            new_billing_period=change.new_billing_period,
            effective_date=change.effective_date,
            created_at=change.created_at
        )
        changes_list.append(change_detail)
    
    return PendingChangesListResponse(
        pending_changes=changes_list,
        total_count=len(changes_list)
    )