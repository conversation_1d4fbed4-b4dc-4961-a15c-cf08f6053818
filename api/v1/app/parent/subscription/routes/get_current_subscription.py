from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session as SyncSession
from db.database import get_db
from dependencies.auth_dependencies.auth_dependency import AuthDependency, AuthDependencyResponse
from ..schemas.response import CurrentSubscriptionResponse
from ..services.get_current_subscription_service import get_current_subscription_service
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError
from api.v1.common.schemas import AppErrorCode
from dependencies.auth_dependencies.base_auth_dependency import UserType

router = APIRouter()
require_auth = AuthDependency(required=True)

@router.get("/current", response_model=CurrentSubscriptionResponse)
def get_current_subscription_route(
    db: SyncSession = Depends(get_db),
    auth_data: AuthDependencyResponse = Depends(require_auth)
):
    """
    Get current subscription details with available actions for each plan item.
    Provides comprehensive view of subscription state for UI management.
    """
    if auth_data.user_type != UserType.parent:
        raise AuthenticationError(
            message="Only parent accounts can view subscription details.",
            error_code=AppErrorCode.PERMISSION_DENIED
        )
    
    parent_public_id = auth_data.account_public_id
    if not parent_public_id:
        raise AuthenticationError(
            message="Not authorized! Token is invalid or missing.",
            error_code=AppErrorCode.AUTHENTICATION_REQUIRED
        )
    
    response = get_current_subscription_service(
        db=db,
        parent_public_id=parent_public_id
    )
    return response