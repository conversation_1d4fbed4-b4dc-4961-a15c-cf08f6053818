from fastapi import APIRouter, Depends, Path
from sqlalchemy.orm import Session as SyncSession

from db.database import get_db
from dependencies.auth_dependencies.auth_dependency import AuthDependency, AuthDependencyResponse
from ..schemas.response import CheckoutSessionStatusResponse
from ..services.get_checkout_session_status_service import get_checkout_session_status_service
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError
from api.v1.common.schemas import AppErrorCode
from dependencies.auth_dependencies.base_auth_dependency import UserType

router = APIRouter()
require_auth = AuthDependency(required=True)

@router.get("/checkout-session-status/{checkout_session_id}", response_model=CheckoutSessionStatusResponse)
def get_checkout_session_status_route(
    checkout_session_id: str = Path(..., description="The ID of the Stripe Checkout Session."),
    db: SyncSession = Depends(get_db),
    auth_data: AuthDependencyResponse = Depends(require_auth)
):
    """
    Allows the frontend to query the status of a specific Stripe Checkout Session
    after the user has been redirected back to the application.
    """
    if auth_data.user_type != UserType.parent:
        raise AuthenticationError(
            message="Only parent accounts can query checkout session status.",
            error_code=AppErrorCode.PERMISSION_DENIED
        )

    parent_public_id = auth_data.account_public_id
    
    response = get_checkout_session_status_service(
        db=db,
        parent_public_id=parent_public_id,
        checkout_session_id=checkout_session_id
    )
    return response