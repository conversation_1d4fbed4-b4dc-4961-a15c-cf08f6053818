from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session as SyncSession
from db.database import get_db
from dependencies.auth_dependencies.auth_dependency import AuthDependency, AuthDependencyResponse
from ..schemas.request import CancelScheduledPauseRequest
from ..schemas.response import CancelScheduledPauseResponse
from ..services.cancel_scheduled_pause_service import cancel_scheduled_pause_service
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError
from api.v1.common.schemas import AppErrorCode

router = APIRouter()
require_auth = AuthDependency(required=True)

@router.post("/cancel-scheduled-pause", response_model=CancelScheduledPauseResponse)
def cancel_scheduled_pause_route(
    cancel_data: CancelScheduledPauseRequest,
    db: SyncSession = Depends(get_db),
    auth_data: AuthDependencyResponse = Depends(require_auth)
):
    """Cancel a scheduled pause for a subscription."""
    parent_public_id = auth_data.account_public_id
    if not parent_public_id:
        raise AuthenticationError(
            message="Not authorized! Token is invalid or missing.",
            error_code=AppErrorCode.AUTHENTICATION_REQUIRED
        )

    response = cancel_scheduled_pause_service(
        db=db, 
        parent_public_id=parent_public_id, 
        cancel_data=cancel_data
    )
    return response