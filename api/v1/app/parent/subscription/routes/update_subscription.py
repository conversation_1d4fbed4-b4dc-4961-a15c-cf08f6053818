from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session as SyncSession  
from db.database import get_db
from dependencies.auth_dependencies.auth_dependency import AuthDependency, AuthDependencyResponse  
from ..schemas.request import UpdateSubscriptionRequest
from ..schemas.response import UpdateSubscriptionResponse
from ..services.update_subscription_service import update_subscription_service
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError
from api.v1.common.schemas import AppErrorCode

router = APIRouter()
require_auth = AuthDependency(required=True)

@router.post("/update", response_model=UpdateSubscriptionResponse)
def update_subscription_route(
    update_data: UpdateSubscriptionRequest,
    db: SyncSession = Depends(get_db),  
    auth_data: AuthDependencyResponse = Depends(require_auth)  
):
    parent_public_id = auth_data.account_public_id
    if not parent_public_id:
        raise AuthenticationError(message="Not authorized! Token is invalid or missing.",
                                  error_code=AppErrorCode.AUTHENTICATION_REQUIRED)

    response = update_subscription_service(db, parent_public_id, update_data)
    return response