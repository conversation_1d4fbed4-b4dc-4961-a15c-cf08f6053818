from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException
from sqlalchemy.orm import Session as SyncSession
from sqlalchemy import select
from db.database import get_db
from db.models import Account, SubscriptionPendingChange, SubscriptionChangeStatus
from dependencies.auth_dependencies.auth_dependency import AuthDependency, AuthDependencyResponse
from ..schemas.request import CancelPendingChangeRequest
from ..schemas.response import CancelPendingChangeResponse
from ..services.subscription_change_service import cancel_pending_change
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError, NotFoundError, PermissionDeniedError, ServiceError
from api.v1.common.schemas import AppErrorCode
from dependencies.auth_dependencies.base_auth_dependency import UserType
from loguru import logger

router = APIRouter()
require_auth = AuthDependency(required=True)

@router.post("/cancel-pending-change", response_model=CancelPendingChangeResponse)
def cancel_pending_change_route(
    request_data: CancelPendingChangeRequest,
    db: SyncSession = Depends(get_db),
    auth_data: AuthDependencyResponse = Depends(require_auth)
):
    """
    Cancel a pending subscription change.
    """
    if auth_data.user_type != UserType.parent:
        raise AuthenticationError(
            message="Only parent accounts can cancel subscription changes.",
            error_code=AppErrorCode.PERMISSION_DENIED
        )
    
    parent_public_id = auth_data.account_public_id
    if not parent_public_id:
        raise AuthenticationError(
            message="Not authorized! Token is invalid or missing.",
            error_code=AppErrorCode.AUTHENTICATION_REQUIRED
        )
    
    logger.info(f"Cancelling pending change {request_data.pending_change_public_id} for parent: {parent_public_id}")
    
    # Get parent account
    account = db.execute(
        select(Account).where(Account.public_id == parent_public_id)
    ).scalar_one_or_none()
    
    if not account:
        raise NotFoundError(
            message="Parent account not found.",
            error_code=AppErrorCode.PARENT_ACCOUNT_NOT_FOUND
        )
    
    # Get the pending change and verify ownership
    pending_change = db.execute(
        select(SubscriptionPendingChange)
        .join(SubscriptionPendingChange.active_subscription)
        .where(
            SubscriptionPendingChange.public_id == request_data.pending_change_public_id,
            SubscriptionPendingChange.active_subscription.has(parent_account_id=account.id)
        )
    ).scalar_one_or_none()
    
    if not pending_change:
        raise NotFoundError(
            message="Pending change not found.",
            error_code=AppErrorCode.NOT_FOUND
        )
    
    if pending_change.status not in [SubscriptionChangeStatus.PENDING, SubscriptionChangeStatus.SCHEDULED]:
        raise PermissionDeniedError(
            message=f"Cannot cancel change with status: {pending_change.status.value}",
            error_code=AppErrorCode.INVALID_REQUEST_DATA
        )
    
    # Cancel the change
    success = cancel_pending_change(
        db=db,
        pending_change_id=request_data.pending_change_public_id,
        cancelled_by=f"parent:{parent_public_id}"
    )
    
    if success:
        db.commit()
        return CancelPendingChangeResponse(
            success=True,
            message="Pending change cancelled successfully."
        )
    else:
        raise ServiceError(
            message="Failed to cancel pending change.",
            error_code=AppErrorCode.SERVICE_ERROR
        )