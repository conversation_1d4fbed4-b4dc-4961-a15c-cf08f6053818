from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session as SyncSession  
from db.database import get_db
from dependencies.auth_dependencies.auth_dependency import AuthDependency, AuthDependencyResponse  
from ..schemas.request import ManageSelectedSubjectsRequest
from ..schemas.response import ManageSelectedSubjectsResponse
from ..services.manage_selected_subjects_service import manage_selected_subjects_service
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError
from api.v1.common.schemas import AppErrorCode

router = APIRouter()
require_auth = AuthDependency(required=True)

@router.post("/manage-selected-subjects", response_model=ManageSelectedSubjectsResponse)
def manage_selected_subjects_route(
    manage_data: ManageSelectedSubjectsRequest,
    db: SyncSession = Depends(get_db),  
    auth_data: AuthDependencyResponse = Depends(require_auth)  
):
    parent_public_id = auth_data.account_public_id
    if not parent_public_id:
        raise AuthenticationError(message="Not authorized! Token is invalid or missing.",
                                  error_code=AppErrorCode.AUTHENTICATION_REQUIRED)

    response = manage_selected_subjects_service(db, parent_public_id, manage_data)
    return response