from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session as SyncSession
from db.database import get_db
from dependencies.auth_dependencies.auth_dependency import AuthDependency, AuthDependencyResponse
from ..schemas.request import ResumeSubscriptionRequest
from ..schemas.response import ResumeSubscriptionResponse
from ..services.resume_subscription_service import resume_subscription_service
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError
from api.v1.common.schemas import AppErrorCode
from dependencies.auth_dependencies.base_auth_dependency import UserType

router = APIRouter()
require_auth = AuthDependency(required=True)

@router.post("/resume", response_model=ResumeSubscriptionResponse)
def resume_subscription_route(
    resume_data: ResumeSubscriptionRequest,
    db: SyncSession = Depends(get_db),
    auth_data: AuthDependencyResponse = Depends(require_auth)
):
    """
    Resume a paused subscription immediately.
    Only available to parent accounts with paused subscriptions.
    """
    if auth_data.user_type != UserType.parent:
        raise AuthenticationError(
            message="Only parent accounts can resume subscriptions.",
            error_code=AppErrorCode.PERMISSION_DENIED
        )
    
    parent_public_id = auth_data.account_public_id
    if not parent_public_id:
        raise AuthenticationError(
            message="Not authorized! Token is invalid or missing.",
            error_code=AppErrorCode.AUTHENTICATION_REQUIRED
        )
    
    response = resume_subscription_service(
        db=db,
        parent_public_id=parent_public_id,
        subscription_public_id=resume_data.subscription_public_id
    )
    return response