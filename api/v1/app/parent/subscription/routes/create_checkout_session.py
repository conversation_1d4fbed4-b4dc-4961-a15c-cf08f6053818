from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session as SyncSession 
from db.database import get_db
from ..schemas.request import CreateCheckoutSessionRequest
from ..schemas.response import CreateCheckoutSessionResponse
from dependencies.auth_dependencies.auth_dependency import AuthDependency, AuthDependencyResponse  
from ..services.create_checkout_session_service import create_checkout_session_service
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError
from api.v1.common.schemas import AppErrorCode

router = APIRouter()
require_auth = AuthDependency(required=True)

@router.post('/create-checkout-session', status_code=200, response_model=CreateCheckoutSessionResponse)
def create_checkout_session_route(
    new_session_data: CreateCheckoutSessionRequest,
    db: SyncSession = Depends(get_db),  
    auth_data: AuthDependencyResponse = Depends(require_auth)  
):
    parent_public_id = auth_data.account_public_id
    # This check is redundant if require_auth works as expected, but safe.
    if not parent_public_id:
        raise AuthenticationError(message="Not authorized! Token is invalid or missing.",
                                  error_code=AppErrorCode.AUTHENTICATION_REQUIRED)

    response = create_checkout_session_service(
        db=db,
        parent_public_id=parent_public_id,
        new_session_data=new_session_data
    )
    return response