from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Header, Request
from core.exception_handling.exceptions.custom_exceptions import BadRequestError, ExternalServiceError
from sqlalchemy.orm import Session as SyncSession 
from db.database import get_db
import stripe
from core.config.settings import settings 
from loguru import logger
from ..services.webhook_service import SERVICE_EVENT_HANDLER_MAP
from api.v1.common.schemas import AppErrorCode

router = APIRouter()

stripe.api_key = settings.STRIPE_API_KEY 
WEBHOOK_SECRET = settings.STRIPE_WEBHOOK_SECRET 

if not WEBHOOK_SECRET:
    logger.critical("STRIPE_WEBHOOK_SECRET is not set. Webhook verification will fail.")

@router.post('/webhook', status_code=200)
async def webhook_received_route(
    request: Request,
    stripe_signature: str = Header(None), 
    db: SyncSession = Depends(get_db) 
):
    if not WEBHOOK_SECRET:
        error_msg = "Webhook secret not configured on server."
        logger.error(f"Webhook processing aborted: {error_msg}")
        raise ExternalServiceError(
            message=error_msg,
            log_message="WEBHOOK_SECRET environment variable is missing",
            error_code=AppErrorCode.SERVICE_ERROR
        )

    payload = await request.body()
    
    try:
        event = stripe.Webhook.construct_event(
            payload=payload, sig_header=stripe_signature, secret=WEBHOOK_SECRET
        )
    except ValueError as e:  
        logger.error(f"Invalid webhook payload: {e}")
        raise BadRequestError(
            message="Invalid payload",
            log_message=f"Invalid webhook payload: {e}",
            error_code=AppErrorCode.BAD_REQUEST
        )
    except stripe.error.SignatureVerificationError as e: 
        logger.error(f"Webhook signature verification failed: {e}")
        raise BadRequestError(
            message="Invalid signature",
            log_message=f"Webhook signature verification failed: {e}",
            error_code=AppErrorCode.BAD_REQUEST
        )
    except Exception as e: 
        logger.error(f"Error constructing Stripe event: {e}")
        raise ExternalServiceError(
            message="Could not construct event",
            log_message=f"Error constructing Stripe event: {e}",
            original_exception=e,
            error_code=AppErrorCode.SERVICE_ERROR
        )

    event_type = event['type']
    logger.info(f"Received Stripe webhook. Event ID: {event.get('id')}, Type: {event_type}")

    handler = SERVICE_EVENT_HANDLER_MAP.get(event_type)

    if handler:
        try:
            logger.info(f"Handling event type: {event_type} with handler: {handler.__name__}")
            # Call the synchronous handler. FastAPI handles running sync functions in async routes.
            handler(event, db)
            logger.info(f"Successfully handled event type: {event_type}")
        except (BadRequestError, ExternalServiceError): 
            raise
        except Exception as e: 
            logger.error(f"Error processing event {event_type} with handler {handler.__name__}: {e}")
            logger.exception("Full traceback for handler error:") 
            raise ExternalServiceError(
                message=f"Error processing event: {event_type}",
                log_message=f"Error processing event {event_type} with handler {handler.__name__}: {e}",
                original_exception=e,
                error_code=AppErrorCode.SERVICE_ERROR
            )
    else:
        logger.info(f"No handler configured for event type: {event_type}. Ignoring.")

    return {"status": "success"}