from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session as SyncSession 
from db.database import get_db
from ..schemas.request import CreatePortalSessionRequest
from ..schemas.response import CreatePortalSessionResponse
from dependencies.auth_dependencies.base_auth_dependency import AuthDependencyResponse
from dependencies.auth_dependencies.auth_dependency import AuthDependency
from ..services.create_portal_session_service import create_portal_session_service
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError
from api.v1.common.schemas import AppErrorCode

router = APIRouter()
require_auth = AuthDependency(required=True)

@router.post('/create-portal-session', status_code=200, response_model=CreatePortalSessionResponse)
def create_portal_session_route(
    portal_session_data: CreatePortalSessionRequest,
    db: SyncSession = Depends(get_db),  
    auth_data: AuthDependencyResponse = Depends(require_auth)
):
    parent_public_id = auth_data.account_public_id
    if not parent_public_id:
        raise AuthenticationError(message="Not authorized! Token is invalid or missing.",
                                  error_code=AppErrorCode.AUTHENTICATION_REQUIRED)

    response = create_portal_session_service(
        db=db,
        parent_public_id=parent_public_id,
        portal_session_data=portal_session_data
    )
    return response