from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session as SyncSession
from db.database import get_db
from ..schemas.request import VerifyDiscountCodeRequest
from ..schemas.response import VerifyDiscountCodeResponse
from dependencies.auth_dependencies.base_auth_dependency import AuthDependencyResponse
from dependencies.auth_dependencies.auth_dependency import AuthDependency
from ..services.verify_discount_code_service import verify_discount_code_service
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError
from api.v1.common.schemas import AppErrorCode

router = APIRouter()
require_auth = AuthDependency(required=True)

@router.post("/verify-discount-code", response_model=VerifyDiscountCodeResponse)
def verify_discount_code_route(
    discount_request_data: VerifyDiscountCodeRequest,
    db: SyncSession = Depends(get_db),
    auth_data: AuthDependencyResponse = Depends(require_auth)
):
    parent_public_id = auth_data.account_public_id
    if not parent_public_id:
        raise AuthenticationError(message="Not authorized! Token is invalid or missing.",
                                  error_code=AppErrorCode.AUTHENTICATION_REQUIRED)

    response = verify_discount_code_service(
        db=db,
        parent_public_id=parent_public_id,
        discount_request_data=discount_request_data
    )
    return response