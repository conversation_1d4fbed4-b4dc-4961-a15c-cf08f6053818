"""
Resume subscription service - simplified approach.
Monthly: Clear pause_collection
Yearly: Adjust trial_end for actual pause duration
"""

import stripe
from sqlalchemy.orm import Session as SyncSession
from sqlalchemy import select, and_
from loguru import logger
from datetime import datetime, UTC, timedelta
from db.models import Account, ActiveSubscription, SubscriptionPause, ActiveSubscriptionPlanLink
from db.models.subscription import SubscriptionStatusType, SubscriptionPauseStatus, BillingPeriodEnum
from ..schemas.response import ResumeSubscriptionResponse
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ValidationError, ServiceError, ExternalServiceError
)
from api.v1.common.schemas import AppErrorCode
from core.config.settings import settings

# Initialize Stripe
stripe.api_key = settings.STRIPE_API_KEY


def resume_subscription_service(
    db: SyncSession,
    parent_public_id: str,
    subscription_public_id: str
) -> ResumeSubscriptionResponse:
    """
    Resume a paused subscription.
    
    Monthly: Simply clear pause_collection (removes auto-resume)
    Yearly: Adjust trial_end based on actual pause duration
    """
    logger.info(f"Processing resume request for parent: {parent_public_id}")
    
    current_time = datetime.now(UTC)
    
    # Get parent account
    account_stmt = select(Account).where(Account.public_id == parent_public_id)
    account = db.execute(account_stmt).scalar_one_or_none()
    if not account:
        logger.error(f"Parent account not found: {parent_public_id}")
        raise NotFoundError(
            message="Parent account not found.",
            error_code=AppErrorCode.PARENT_ACCOUNT_NOT_FOUND
        )
    
    # Get the specific subscription to resume
    # For monthly: status will be PAUSED
    # For yearly: status will be ACTIVE (pause tracked via pause record only)
    active_sub_stmt = (
        select(ActiveSubscription)
        .where(
            ActiveSubscription.parent_account_id == account.id,
            ActiveSubscription.public_id == subscription_public_id,
            ActiveSubscription.status.in_([SubscriptionStatusType.PAUSED, SubscriptionStatusType.ACTIVE])
        )
    )
    active_subscription = db.execute(active_sub_stmt).scalar_one_or_none()
    
    if not active_subscription:
        logger.warning(f"No subscription {subscription_public_id} found for parent {parent_public_id}")
        raise ValidationError(
            message="Subscription not found.",
            error_code=AppErrorCode.SUBSCRIPTION_NOT_FOUND
        )
    
    # Get active pause record
    pause_stmt = (
        select(SubscriptionPause)
        .where(
            and_(
                SubscriptionPause.active_subscription_id == active_subscription.id,
                SubscriptionPause.status == SubscriptionPauseStatus.PAUSED,
                SubscriptionPause.start_date <= current_time,
                SubscriptionPause.end_date >= current_time
            )
        )
    )
    pause_record = db.execute(pause_stmt).scalar_one_or_none()
    
    if not pause_record:
        logger.warning(f"No active pause found for subscription {active_subscription.public_id}")
        raise ValidationError(
            message="This subscription is not currently paused.",
            error_code=AppErrorCode.INVALID_REQUEST
        )
    
    try:
        # Get the plan link to determine billing period
        plan_link_stmt = (
            select(ActiveSubscriptionPlanLink)
            .where(ActiveSubscriptionPlanLink.active_subscription_id == active_subscription.id)
            .limit(1)
        )
        plan_link = db.execute(plan_link_stmt).scalar_one_or_none()
        
        if not plan_link:
            logger.error(f"No plan link found for subscription {active_subscription.id}")
            raise ServiceError(
                message="Subscription configuration error. Please contact support.",
                error_code=AppErrorCode.SERVICE_ERROR
            )
        
        billing_period = plan_link.chosen_billing_period
        logger.info(f"Resuming {billing_period.value} subscription {active_subscription.stripe_subscription_id}")
        
        if billing_period == BillingPeriodEnum.MONTHLY:
            # Monthly: Just clear the pause_collection
            logger.info(f"Clearing pause_collection for monthly subscription")
            
            stripe.Subscription.modify(
                active_subscription.stripe_subscription_id,
                pause_collection=""  # Clear the pause
            )
            
            message = "Your subscription has been resumed successfully."
            
        else:  # YEARLY
            # Yearly: Calculate actual pause duration and adjust trial_end
            actual_pause_duration = int((current_time - pause_record.start_date).total_seconds())
            days_paused = actual_pause_duration // 86400
            
            logger.info(f"Yearly subscription paused for {days_paused} days")
            
            # Calculate new trial_end based on original period end + actual pause duration
            new_trial_end = pause_record.original_period_end + timedelta(seconds=actual_pause_duration)
            
            stripe.Subscription.modify(
                active_subscription.stripe_subscription_id,
                trial_end=int(new_trial_end.timestamp()),
                proration_behavior='none'
            )
            
            # Update pause record with new period end
            pause_record.new_period_end = new_trial_end
            
            message = (
                f"Your subscription has been resumed successfully. "
                f"You've been credited {days_paused} days for the pause period. "
                f"Your next billing date is {new_trial_end.date()}."
            )
        
        # Update database records
        pause_record.resumed_at = current_time
        pause_record.status = SubscriptionPauseStatus.RESUMED
        
        # Update subscription status (for monthly that was PAUSED)
        if active_subscription.status == SubscriptionStatusType.PAUSED:
            active_subscription.status = SubscriptionStatusType.ACTIVE
        
        # No need to clear intended_trial_end as we're not using webhook backup anymore
        pause_record.intended_trial_end = None
        
        db.add(pause_record)
        db.add(active_subscription)
        db.commit()
        
        logger.info(f"Successfully resumed {billing_period.value} subscription {active_subscription.public_id}")
        
        return ResumeSubscriptionResponse(
            message=message,
            resumed_at=current_time,
            subscription_status=active_subscription.status
        )
        
    except stripe.error.StripeError as e:
        db.rollback()
        logger.error(
            f"Stripe API error resuming subscription {active_subscription.stripe_subscription_id}: {str(e)}",
            exc_info=True
        )
        user_message = (
            f"Payment gateway error: {e.user_message}"
            if hasattr(e, 'user_message') and e.user_message
            else "Could not resume subscription. Please try again."
        )
        raise ExternalServiceError(
            message=user_message,
            error_code=AppErrorCode.STRIPE_ERROR,
            original_exception=e
        )
    except Exception as e:
        db.rollback()
        logger.error(
            f"Unexpected error resuming subscription for parent {parent_public_id}: {str(e)}",
            exc_info=True
        )
        raise ServiceError(
            message="An unexpected error occurred while resuming the subscription.",
            error_code=AppErrorCode.SERVICE_ERROR
        )

