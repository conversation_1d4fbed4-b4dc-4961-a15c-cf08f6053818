"""
Service for handling subscription changes (upgrades, downgrades, billing changes).
Integrates with <PERSON><PERSON> and manages pending changes in the database.
"""
from sqlalchemy.orm import Session as SyncSession
from sqlalchemy import select
from db.models import (
    ActiveSubscriptionPlanLink, SubscriptionOption, Subject,
    SubscriptionPendingChange, SubscriptionPendingSelectedSubject,
    SubscriptionChangeType, SubscriptionChangeStatus, PlanTypeEnum, BillingPeriodEnum,
    PlanSelectedSubject
)
from core.exception_handling.exceptions.custom_exceptions import ServiceError, ValidationError
from api.v1.common.schemas import AppErrorCode
from loguru import logger
from datetime import datetime, UTC
from typing import List, Optional, Dict, Any
import uuid
import stripe
from core.config.settings import settings

# Initialize Stripe
stripe.api_key = settings.STRIPE_API_KEY


def create_subscription_quantity_change(
    db: SyncSession,
    active_plan_link: ActiveSubscriptionPlanLink,
    new_quantity: int,
    new_subject_ids: List[str],
    effective_immediately: bool = None
) -> SubscriptionPendingChange:
    """
    Create a pending subscription quantity change.
    
    Args:
        db: Database session
        active_plan_link: The subscription plan link being modified
        new_quantity: The new quantity (number of subjects for SC plans)
        new_subject_ids: List of subject public IDs for the new selection
        effective_immediately: If True, upgrade immediately. If False, at period end. If None, auto-detect.
    
    Returns:
        The created SubscriptionPendingChange record
    """
    logger.info(
        f"Creating quantity change for plan link {active_plan_link.public_id}: "
        f"{active_plan_link.quantity} -> {new_quantity}"
    )
    
    # Edge case 5: Check for concurrent changes
    if not handle_concurrent_change_request(db, active_plan_link):
        raise ValidationError(
            "Another change is currently being processed for this subscription. Please try again in a moment.",
            error_code=AppErrorCode.CONCURRENT_MODIFICATION
        )
    
    # Auto-detect if this should be immediate or deferred
    if effective_immediately is None:
        effective_immediately = new_quantity > active_plan_link.quantity  # Upgrades are immediate
    
    # Validate subjects belong to the correct year
    if new_subject_ids:
        subjects = db.execute(
            select(Subject).where(
                Subject.public_id.in_(new_subject_ids),
                Subject.year_id == active_plan_link.subscription_option.year_id,
                Subject.is_active == True
            )
        ).scalars().all()
        
        if len(subjects) != len(new_subject_ids):
            found_ids = {s.public_id for s in subjects}
            missing_ids = set(new_subject_ids) - found_ids
            raise ValidationError(
                f"Invalid subjects: {', '.join(missing_ids)}",
                error_code=AppErrorCode.INVALID_REQUEST_DATA
            )
    
    # Check for existing pending changes
    existing_pending = db.execute(
        select(SubscriptionPendingChange).where(
            SubscriptionPendingChange.plan_link_id == active_plan_link.id,
            SubscriptionPendingChange.status.in_([
                SubscriptionChangeStatus.PENDING,
                SubscriptionChangeStatus.SCHEDULED
            ])
        )
    ).scalar_one_or_none()
    
    if existing_pending:
        logger.warning(
            f"Existing pending change found for plan link {active_plan_link.public_id}. "
            f"Consider cancelling it first."
        )
        # For now, we'll cancel the old one and create a new one
        existing_pending.status = SubscriptionChangeStatus.CANCELLED
        existing_pending.cancelled_at = datetime.now(UTC)
        existing_pending.cancelled_by = "system_auto_cancel"
    
    # Calculate new stripe price ID
    new_stripe_price_id = _calculate_stripe_price_for_quantity(
        subscription_option=active_plan_link.subscription_option,
        plan_type=active_plan_link.chosen_plan_type,
        billing_period=active_plan_link.chosen_billing_period,
        quantity=new_quantity
    )
    
    # Determine effective date
    if effective_immediately:
        effective_date = datetime.now(UTC)
    else:
        # Use the current period end for downgrades
        effective_date = active_plan_link.active_subscription.current_period_end
    
    # Create pending change record
    pending_change = SubscriptionPendingChange(
        public_id=str(uuid.uuid4()),
        active_subscription_id=active_plan_link.active_subscription_id,
        plan_link_id=active_plan_link.id,
        change_type=SubscriptionChangeType.QUANTITY_CHANGE,
        current_quantity=active_plan_link.quantity,
        current_stripe_price_id=active_plan_link.chosen_stripe_price_id,
        current_plan_type=active_plan_link.chosen_plan_type,
        current_billing_period=active_plan_link.chosen_billing_period,
        new_quantity=new_quantity,
        new_stripe_price_id=new_stripe_price_id,
        new_plan_type=active_plan_link.chosen_plan_type,  # Same plan type
        new_billing_period=active_plan_link.chosen_billing_period,  # Same billing period
        effective_date=effective_date,
        status=SubscriptionChangeStatus.PENDING,
        metadata_json={
            "reason": "user_requested",
            "immediate": effective_immediately,
            "subject_count_change": new_quantity - active_plan_link.quantity
        }
    )
    db.add(pending_change)
    db.flush()
    
    # Add selected subjects for the pending change
    for idx, subject in enumerate(subjects):
        pending_subject = SubscriptionPendingSelectedSubject(
            public_id=str(uuid.uuid4()),
            pending_change_id=pending_change.id,
            subject_id=subject.id,
            display_order=idx
        )
        db.add(pending_subject)
    
    # Update Stripe subscription
    try:
        if effective_immediately:
            # Immediate upgrade with proration
            stripe_response = _update_stripe_subscription_immediately(
                active_plan_link=active_plan_link,
                new_stripe_price_id=new_stripe_price_id,
                new_quantity=new_quantity
            )
            pending_change.status = SubscriptionChangeStatus.COMPLETED
            pending_change.processed_at = datetime.now(UTC)
            
            # Also update the actual plan link for immediate changes
            active_plan_link.quantity = new_quantity
            active_plan_link.chosen_stripe_price_id = new_stripe_price_id
            
        else:
            # Schedule for period end
            stripe_response = _schedule_stripe_subscription_update(
                active_plan_link=active_plan_link,
                new_stripe_price_id=new_stripe_price_id,
                new_quantity=new_quantity,
                effective_date=effective_date
            )
            pending_change.status = SubscriptionChangeStatus.SCHEDULED
            pending_change.stripe_subscription_schedule_id = stripe_response.get("schedule_id")
        
        # Store Stripe response
        pending_change.metadata_json = {
            **pending_change.metadata_json,
            "stripe_response": stripe_response
        }
        
    except stripe.error.StripeError as e:
        logger.error(f"Stripe error creating subscription change: {str(e)}")
        pending_change.status = SubscriptionChangeStatus.FAILED
        pending_change.metadata_json = {
            **pending_change.metadata_json,
            "error": str(e),
            "error_type": type(e).__name__
        }
        
        # Edge case 2: Failed payment on upgrade - rollback
        if effective_immediately and isinstance(e, stripe.error.CardError):
            logger.warning(f"Payment failed for immediate upgrade, rolling back changes")
            # The pending change is already marked as failed
            # Don't update the active plan link since payment failed
            db.flush()
            raise ServiceError(
                f"Payment failed: {str(e)}. Please update your payment method and try again.",
                error_code=AppErrorCode.PAYMENT_FAILED
            )
        
        raise ServiceError(
            f"Failed to update subscription: {str(e)}",
            error_code=AppErrorCode.STRIPE_ERROR
        )
    
    db.flush()
    return pending_change


def _calculate_stripe_price_for_quantity(
    subscription_option: SubscriptionOption,
    plan_type: PlanTypeEnum,
    billing_period: BillingPeriodEnum,
    quantity: int
) -> str:
    """
    Calculate the appropriate Stripe price ID for a given quantity.
    For SC plans, this may involve tier calculations.
    """
    # For now, return the same price ID - Stripe handles quantity-based pricing
    # In the future, you might have different price IDs for different quantity tiers
    
    if plan_type == PlanTypeEnum.SC:
        if billing_period == BillingPeriodEnum.MONTHLY:
            return subscription_option.sc_monthly_stripe_price_id
        else:
            return subscription_option.sc_yearly_stripe_price_id
    else:
        # YF plans don't change with quantity
        if billing_period == BillingPeriodEnum.MONTHLY:
            return subscription_option.yf_monthly_stripe_price_id
        else:
            return subscription_option.yf_yearly_stripe_price_id


def _update_stripe_subscription_immediately(
    active_plan_link: ActiveSubscriptionPlanLink,
    new_stripe_price_id: str,
    new_quantity: int
) -> Dict[str, Any]:
    """
    Update Stripe subscription immediately (for upgrades).
    """
    try:
        # Update the subscription item
        updated_sub = stripe.Subscription.modify(
            active_plan_link.active_subscription.stripe_subscription_id,
            items=[{
                'id': active_plan_link.stripe_subscription_item_id,
                'price': new_stripe_price_id,
                'quantity': new_quantity
            }],
            proration_behavior='create_prorations'  # Create prorations for immediate changes
        )
        
        return {
            "subscription_id": updated_sub.id,
            "status": updated_sub.status,
            "updated_items": [{
                "id": item.id,
                "price": item.price.id,
                "quantity": item.quantity
            } for item in updated_sub.items.data]
        }
        
    except stripe.error.StripeError as e:
        logger.error(f"Failed to update Stripe subscription: {str(e)}")
        raise


def _schedule_stripe_subscription_update(
    active_plan_link: ActiveSubscriptionPlanLink,
    new_stripe_price_id: str,
    new_quantity: int,
    effective_date: datetime
) -> Dict[str, Any]:
    """
    Schedule a Stripe subscription update for a future date (for downgrades).
    """
    try:
        # Use subscription schedules for future changes
        # First, check if a schedule already exists
        subscription = stripe.Subscription.retrieve(
            active_plan_link.active_subscription.stripe_subscription_id
        )
        
        if subscription.schedule:
            # Update existing schedule
            schedule = stripe.SubscriptionSchedule.modify(
                subscription.schedule,
                phases=[
                    {
                        'items': [{
                            'price': new_stripe_price_id,
                            'quantity': new_quantity
                        }],
                        'start_date': int(effective_date.timestamp())
                    }
                ]
            )
        else:
            # Create new schedule
            schedule = stripe.SubscriptionSchedule.create(
                from_subscription=active_plan_link.active_subscription.stripe_subscription_id,
                phases=[
                    {
                        'items': [{
                            'price': new_stripe_price_id,
                            'quantity': new_quantity
                        }],
                        'start_date': int(effective_date.timestamp())
                    }
                ]
            )
        
        return {
            "schedule_id": schedule.id,
            "status": schedule.status,
            "phases": [{
                "start_date": phase.start_date,
                "items": phase.items
            } for phase in schedule.phases]
        }
        
    except stripe.error.StripeError as e:
        logger.error(f"Failed to schedule Stripe subscription update: {str(e)}")
        raise


def cancel_pending_change(
    db: SyncSession,
    pending_change_id: str,
    cancelled_by: str = "user"
) -> bool:
    """
    Cancel a pending subscription change.
    """
    pending_change = db.execute(
        select(SubscriptionPendingChange).where(
            SubscriptionPendingChange.public_id == pending_change_id,
            SubscriptionPendingChange.status.in_([
                SubscriptionChangeStatus.PENDING,
                SubscriptionChangeStatus.SCHEDULED
            ])
        )
    ).scalar_one_or_none()
    
    if not pending_change:
        return False
    
    # Cancel in Stripe if scheduled
    if pending_change.stripe_subscription_schedule_id:
        try:
            stripe.SubscriptionSchedule.cancel(
                pending_change.stripe_subscription_schedule_id
            )
        except stripe.error.StripeError as e:
            logger.error(f"Failed to cancel Stripe schedule: {str(e)}")
            # Continue with local cancellation even if Stripe fails
    
    # Update local record
    pending_change.status = SubscriptionChangeStatus.CANCELLED
    pending_change.cancelled_at = datetime.now(UTC)
    pending_change.cancelled_by = cancelled_by
    
    db.flush()
    return True


def cancel_all_pending_changes_for_subscription(
    db: SyncSession,
    active_subscription_id: int,
    reason: str = "subscription_cancelled"
) -> int:
    """
    Cancel all pending changes for a subscription (Edge case 3).
    Called when subscription is cancelled.
    
    Returns:
        Number of changes cancelled
    """
    pending_changes = db.execute(
        select(SubscriptionPendingChange).where(
            SubscriptionPendingChange.active_subscription_id == active_subscription_id,
            SubscriptionPendingChange.status.in_([
                SubscriptionChangeStatus.PENDING,
                SubscriptionChangeStatus.SCHEDULED
            ])
        )
    ).scalars().all()
    
    cancelled_count = 0
    for change in pending_changes:
        # Cancel in Stripe if scheduled
        if change.stripe_subscription_schedule_id:
            try:
                stripe.SubscriptionSchedule.cancel(
                    change.stripe_subscription_schedule_id
                )
            except stripe.error.StripeError as e:
                logger.error(f"Failed to cancel Stripe schedule {change.stripe_subscription_schedule_id}: {str(e)}")
        
        change.status = SubscriptionChangeStatus.CANCELLED
        change.cancelled_at = datetime.now(UTC)
        change.cancelled_by = reason
        cancelled_count += 1
    
    db.flush()
    logger.info(f"Cancelled {cancelled_count} pending changes for subscription {active_subscription_id}")
    return cancelled_count


def handle_concurrent_change_request(
    db: SyncSession,
    active_plan_link: ActiveSubscriptionPlanLink
) -> bool:
    """
    Handle race conditions (Edge case 5) - check if there's a processing change.
    
    Returns:
        True if safe to proceed, False if should wait
    """
    processing_change = db.execute(
        select(SubscriptionPendingChange).where(
            SubscriptionPendingChange.plan_link_id == active_plan_link.id,
            SubscriptionPendingChange.status == SubscriptionChangeStatus.PROCESSING
        )
    ).scalar_one_or_none()
    
    if processing_change:
        logger.warning(
            f"Concurrent change detected: Change {processing_change.public_id} is currently processing "
            f"for plan link {active_plan_link.public_id}"
        )
        return False
    
    return True


def apply_pending_change(
    db: SyncSession,
    pending_change: SubscriptionPendingChange
) -> bool:
    """
    Apply a pending change that has reached its effective date.
    This is typically called from a webhook or scheduled job.
    """
    if pending_change.status != SubscriptionChangeStatus.SCHEDULED:
        logger.warning(f"Attempted to apply non-scheduled change: {pending_change.public_id}")
        return False
    
    try:
        # Update status
        pending_change.status = SubscriptionChangeStatus.PROCESSING
        db.flush()
        
        # Get the plan link
        plan_link = pending_change.plan_link
        
        # Update the plan link with new values
        plan_link.quantity = pending_change.new_quantity
        plan_link.chosen_stripe_price_id = pending_change.new_stripe_price_id
        plan_link.chosen_plan_type = pending_change.new_plan_type
        plan_link.chosen_billing_period = pending_change.new_billing_period
        
        # Update selected subjects
        # First, clear existing selections
        from sqlalchemy import delete
        db.execute(
            delete(PlanSelectedSubject).where(
                PlanSelectedSubject.plan_link_id == plan_link.id
            )
        )
        
        # Then add new selections from pending
        for pending_subject in pending_change.pending_selected_subjects:
            new_selection = PlanSelectedSubject(
                public_id=str(uuid.uuid4()),
                plan_link_id=plan_link.id,
                subject_id=pending_subject.subject_id
            )
            db.add(new_selection)
        
        # Mark as completed
        pending_change.status = SubscriptionChangeStatus.COMPLETED
        pending_change.processed_at = datetime.now(UTC)
        
        db.flush()
        return True
        
    except Exception as e:
        logger.error(f"Failed to apply pending change {pending_change.public_id}: {str(e)}")
        pending_change.status = SubscriptionChangeStatus.FAILED
        pending_change.metadata_json = {
            **pending_change.metadata_json,
            "apply_error": str(e)
        }
        db.flush()
        return False