import stripe
import json
from sqlalchemy.orm import Session as SyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy import select, or_, delete as sqlalchemy_delete
from db.models import Account, Trial, StripeCheckoutSessionData
from db.models.subscription import (
    SubscriptionOption,
    ActiveSubscription,
    ActiveSubscriptionPlanLink, PlanSelectedSubject, BillingPeriodEnum,
    SubscriptionStatusType, PlanTypeEnum, SubscriptionPause, SubscriptionPauseStatus,
    PriceEligibility
)
from db.models.content import Subject
from loguru import logger
from datetime import datetime, UTC
from typing import Optional, Any, Dict
import uuid
from core.config.settings import settings

try:
    def send_admin_notification_email(
        status_key: str, 
        message_content: Optional[str] = None, 
        subject_prefix: Optional[str] = None
    ):
        logger.info(
            f"ADMIN NOTIFICATION [{status_key}]: {subject_prefix or ''} {message_content or ''}"
        )
        pass

except Exception as e:
    logger.warning(f"Admin notification email setup failed: {e}. Notifications will be logged only.")

    def send_admin_notification_email(
        status_key: str, 
        message_content: Optional[str] = None, 
        subject_prefix: Optional[str] = None
    ):
        logger.info(
            f"ADMIN NOTIFICATION (email disabled) [{status_key}]: {subject_prefix or ''} {message_content or ''}"
        )
        pass

# Initialize Stripe with settings
stripe.api_key = settings.STRIPE_API_KEY

def handle_checkout_session_completed(event: Dict[str, Any], db: SyncSession):
    session_data = event["data"]["object"]
    checkout_session_id = session_data.get("id")
    logger.info("Handling checkout.session.completed for session: {}".format(checkout_session_id))

    try:
        checkout_session_stripe = stripe.checkout.Session.retrieve(
            checkout_session_id,
            expand=["customer", "subscription", "subscription.items.data", "line_items.data.price.product"]
        )

        stripe_subscription_obj = checkout_session_stripe.subscription
        if not stripe_subscription_obj:
            logger.error(
                f"Subscription object not found in checkout session {checkout_session_id}. "
                f"Mode was: {checkout_session_stripe.mode}"
            )
            send_admin_notification_email(
                "webhook_error", 
                f"Subscription object missing in checkout.session.completed for {checkout_session_id}"
            )
            return

        stmt_checkout_data = select(StripeCheckoutSessionData).where(
            StripeCheckoutSessionData.stripe_checkout_session_id == checkout_session_id
        )
        db_checkout_data = db.execute(stmt_checkout_data).scalar_one_or_none()

        if not db_checkout_data:
            logger.error(
                f"StripeCheckoutSessionData not found in DB for session_id {checkout_session_id}. "
                f"Cannot reliably provision. Stripe Sub ID: {stripe_subscription_obj.id}"
            )
            send_admin_notification_email(
                "webhook_error", 
                f"DB data for checkout session {checkout_session_id} missing. "
                f"Manual intervention for Stripe sub ID {stripe_subscription_obj.id}."
            )
            return

        if db_checkout_data.processed_at:
            logger.info(
                f"Checkout session {checkout_session_id} "
                f"already processed at {db_checkout_data.processed_at}. Skipping."
            )
            return

        our_line_item_details_list = db_checkout_data.line_item_details_json
        parent_account_public_id = db_checkout_data.parent_account_public_id

        account_stmt = select(Account).where(Account.public_id == parent_account_public_id)
        account = db.execute(account_stmt).scalar_one_or_none()

        if not account:
            logger.error(f"Parent account {parent_account_public_id} not found in DB for session {checkout_session_id}")
            send_admin_notification_email(
                "webhook_error", 
                f"Parent account {parent_account_public_id} not found for {checkout_session_id}"
            )
            return

        active_subscription = db.execute(
            select(ActiveSubscription).where(ActiveSubscription.stripe_subscription_id == stripe_subscription_obj.id)
        ).scalar_one_or_none()

        if not active_subscription:
            active_subscription = ActiveSubscription(
                parent_account_id=account.id,
                stripe_subscription_id=stripe_subscription_obj.id,
                public_id=str(uuid.uuid4())
            )
            db.add(active_subscription)
            logger.info(f"Created new ActiveSubscription for Stripe sub ID: {stripe_subscription_obj.id}")
        else:
            logger.info(
                f"Found existing ActiveSubscription {active_subscription.public_id} for Stripe sub ID: "
                f"{stripe_subscription_obj.id}. Updating."
            )

        active_subscription.status = SubscriptionStatusType(stripe_subscription_obj.status)
        active_subscription.current_period_start = datetime.fromtimestamp(
            stripe_subscription_obj.current_period_start, tz=UTC
        )
        active_subscription.current_period_end = datetime.fromtimestamp(
            stripe_subscription_obj.current_period_end, tz=UTC
        )
        active_subscription.cancel_at_period_end = stripe_subscription_obj.cancel_at_period_end or False
        db.flush()

        # Iterate through actual Stripe Subscription Items from the webhook
        # Handle both property and method access for different Stripe SDK versions
        if hasattr(stripe_subscription_obj.items, 'data'):
            items_data = stripe_subscription_obj.items.data
        else:
            # If items is a method, we need to list them separately
            items_data = stripe.SubscriptionItem.list(subscription=stripe_subscription_obj.id).data
            
        for stripe_item in items_data:
            actual_stripe_price_id_for_item = stripe_item.price.id

            # Find the corresponding our_item_detail from db_checkout_data.line_item_details_json
            matching_our_item_detail = None
            for detail in our_line_item_details_list:
                if detail.get("chosen_stripe_price_id") == actual_stripe_price_id_for_item:
                    # Consider quantity match too if multiple line
                    # items could have the same price ID but different quantities
                    if detail.get("quantity") == stripe_item.quantity:
                        matching_our_item_detail = detail
                        break

            if not matching_our_item_detail:
                logger.error(
                    f"Could not find matching our_item_detail in StripeCheckoutSessionData for Stripe item with "
                    f"price ID {actual_stripe_price_id_for_item} and quantity {stripe_item.quantity}. "
                    f"Stripe Sub Item ID: {stripe_item.id}. Session: {checkout_session_id}"
                )
                send_admin_notification_email(
                    "webhook_error", 
                    f"Data mismatch in checkout {checkout_session_id}\n"
                    f"for Stripe item price {actual_stripe_price_id_for_item}."
                )
                continue

            subscription_option_public_id = matching_our_item_detail.get("subscription_option_public_id")
            so_stmt = select(SubscriptionOption).where(SubscriptionOption.public_id == subscription_option_public_id)
            subscription_option_db = db.execute(so_stmt).scalar_one_or_none()

            if not subscription_option_db:
                logger.error(
                    f"SubscriptionOption {subscription_option_public_id} from DB checkout data not found "
                    f"for session {checkout_session_id}. Skipping this item."
                )
                send_admin_notification_email(
                    "webhook_error", 
                    f"SubscriptionOption {subscription_option_public_id} not found for checkout {checkout_session_id}"
                )
                continue

            # Get derived_plan_type and chosen_billing_period from matching_our_item_detail
            derived_plan_type_str = matching_our_item_detail.get("derived_plan_type")
            chosen_billing_period_str = matching_our_item_detail.get("chosen_billing_period")

            if not derived_plan_type_str or not chosen_billing_period_str:
                logger.error(
                    f"Missing derived_plan_type or chosen_billing_period in checkout data for item "
                    f"in session {checkout_session_id}. Stripe Item ID: {stripe_item.id}"
                )
                send_admin_notification_email(
                    "webhook_error", 
                    f"Data integrity issue in checkout {checkout_session_id}\n"
                    f"for Stripe item {stripe_item.id}"
                )
                continue

            # Check if a plan link for this Stripe item already exists in this specific subscription
            plan_link = db.execute(
                select(ActiveSubscriptionPlanLink).where(
                    ActiveSubscriptionPlanLink.stripe_subscription_item_id == stripe_item.id,
                    ActiveSubscriptionPlanLink.active_subscription_id == active_subscription.id
                )
            ).scalar_one_or_none()

            if not plan_link:
                plan_link = ActiveSubscriptionPlanLink(
                    active_subscription_id=active_subscription.id,
                    subscription_option_id=subscription_option_db.id,
                    chosen_stripe_price_id=stripe_item.price.id,
                    chosen_plan_type=PlanTypeEnum(derived_plan_type_str),
                    chosen_billing_period=BillingPeriodEnum(chosen_billing_period_str),
                    stripe_subscription_item_id=stripe_item.id,
                    quantity=stripe_item.quantity,
                    public_id=str(uuid.uuid4())
                )
                db.add(plan_link)
                logger.info(f"Created new ActiveSubscriptionPlanLink for Stripe item ID: {stripe_item.id}")
            else:
                logger.warning(
                    f"Found existing ActiveSubscriptionPlanLink {plan_link.public_id} for Stripe item ID: "
                    f"{stripe_item.id} during checkout.session.completed. Updating."
                )
                plan_link.subscription_option_id = subscription_option_db.id
                plan_link.chosen_stripe_price_id = stripe_item.price.id
                plan_link.chosen_plan_type = PlanTypeEnum(derived_plan_type_str)
                plan_link.chosen_billing_period = BillingPeriodEnum(chosen_billing_period_str)
                plan_link.quantity = stripe_item.quantity

            db.flush()

            # Process selected subjects (only for SC plans, identified by derived_plan_type_str)
            if derived_plan_type_str == PlanTypeEnum.SC.value:
                selected_subject_public_ids = matching_our_item_detail.get("selected_subject_public_ids", [])
                if selected_subject_public_ids:
                    db.execute(
                        sqlalchemy_delete(PlanSelectedSubject).where(
                            PlanSelectedSubject.plan_link_id == plan_link.id
                        )
                    )

                    subjects_stmt = select(Subject.id).where(Subject.public_id.in_(selected_subject_public_ids))
                    subject_ids = db.execute(subjects_stmt).scalars().all()

                    if len(subject_ids) != plan_link.quantity:
                        logger.error(
                            f"Mismatch between selected subjects ({len(subject_ids)}) and plan link quantity "
                            f"({plan_link.quantity}) for SC plan link {plan_link.public_id}. "
                            f"Subjects: {selected_subject_public_ids}.\n"
                            f"Not creating PlanSelectedSubject records."
                        )
                        send_admin_notification_email(
                            "webhook_error", 
                            f"Subject/quantity mismatch for SC plan link {plan_link.public_id}\n"
                            f"in checkout {checkout_session_id}.\n"
                            f"Subjects: {selected_subject_public_ids}.\n"
                        )
                    else:
                        for sub_id in subject_ids:
                            db.add(
                                PlanSelectedSubject(
                                    plan_link_id=plan_link.id, 
                                    subject_id=sub_id, 
                                    public_id=str(uuid.uuid4())
                                )
                            )
                        logger.info(f"Associated {len(subject_ids)} subjects with SC plan link {plan_link.public_id}.")
            elif derived_plan_type_str == PlanTypeEnum.YF.value:
                db.execute(
                    sqlalchemy_delete(PlanSelectedSubject).where(
                        PlanSelectedSubject.plan_link_id == plan_link.id
                    )
                )
                logger.info(
                    f"Plan link {plan_link.public_id} is YF type, ensuring no specific subjects are linked "
                    f"via PlanSelectedSubject."
                )

        # End any active trials for the parent
        trials_stmt = select(Trial).where(Trial.account_id == account.id, Trial.status == 'active')
        active_trials = db.execute(trials_stmt).scalars().all()
        for trial in active_trials:
            trial.status = 'ended_by_subscription'
            db.add(trial)
            logger.info(f"Ended trial {trial.public_id} for account {account.public_id} due to new subscription.")

        db_checkout_data.processed_at = datetime.now(UTC)
        db.add(db_checkout_data)

        db.commit()
        logger.info(
            f"Successfully processed checkout.session.completed for session {checkout_session_id} "
            f"and Stripe sub ID {stripe_subscription_obj.id}"
        )
        send_admin_notification_email(
            "success", 
            f"New subscription created for account {account.email} (ID: {account.public_id}). "
            f"Stripe Sub ID: {stripe_subscription_obj.id}"
        )

    except Exception as e:
        db.rollback()
        logger.critical(
            f"Critical error in handle_checkout_session_completed for session {checkout_session_id}: {str(e)}", 
            exc_info=True
        )
        send_admin_notification_email(
            "webhook_error", 
            f"Critical error processing checkout.session.completed for {checkout_session_id}: {str(e)}"
        )

def _get_subscription_option_and_details_from_stripe_price_id(
    db: SyncSession,
    stripe_price_id: str
) -> Optional[tuple[SubscriptionOption, PlanTypeEnum, BillingPeriodEnum]]:
    """
    Finds the SubscriptionOption containing the given Stripe Price ID and
    determines the plan type and billing period for that price.
    """
    so_stmt = select(SubscriptionOption).where(
        or_(
            SubscriptionOption.sc_monthly_stripe_price_id == stripe_price_id,
            SubscriptionOption.sc_yearly_stripe_price_id == stripe_price_id,
            SubscriptionOption.yf_monthly_stripe_price_id == stripe_price_id,
            SubscriptionOption.yf_yearly_stripe_price_id == stripe_price_id
        )
    ).options(
        selectinload(SubscriptionOption.price_version), 
        selectinload(SubscriptionOption.year)
    )  # Eager load

    subscription_option_db = db.execute(so_stmt).scalar_one_or_none()

    if not subscription_option_db:
        return None

    item_chosen_plan_type: PlanTypeEnum
    item_chosen_billing_period: BillingPeriodEnum

    if stripe_price_id == subscription_option_db.sc_monthly_stripe_price_id:
        item_chosen_plan_type = PlanTypeEnum.SC
        item_chosen_billing_period = BillingPeriodEnum.MONTHLY
    elif stripe_price_id == subscription_option_db.sc_yearly_stripe_price_id:
        item_chosen_plan_type = PlanTypeEnum.SC
        item_chosen_billing_period = BillingPeriodEnum.YEARLY
    elif stripe_price_id == subscription_option_db.yf_monthly_stripe_price_id:
        item_chosen_plan_type = PlanTypeEnum.YF
        item_chosen_billing_period = BillingPeriodEnum.MONTHLY
    elif stripe_price_id == subscription_option_db.yf_yearly_stripe_price_id:
        item_chosen_plan_type = PlanTypeEnum.YF
        item_chosen_billing_period = BillingPeriodEnum.YEARLY
    else:
        # This case should ideally not be reached if the initial query was successful
        logger.error(
            f"Logic error: Matched SubscriptionOption but couldn't determine plan_type/period "
            f"for Stripe Price ID {stripe_price_id} within SO {subscription_option_db.public_id}."
        )
        return None

    return subscription_option_db, item_chosen_plan_type, item_chosen_billing_period

def _sync_active_subscription_plan_link(
    db: SyncSession,
    active_subscription_id: int,
    stripe_item: stripe.SubscriptionItem,  # Type hint for Stripe item
    subscription_option_db: SubscriptionOption,
    item_chosen_plan_type: PlanTypeEnum,
    item_chosen_billing_period: BillingPeriodEnum
) -> Optional[ActiveSubscriptionPlanLink]:
    """
    Creates or updates an ActiveSubscriptionPlanLink and syncs its PlanSelectedSubject
    records based on the Stripe item data and derived plan details.
    Returns the synced plan_link or None if a critical error occurs.
    """
    stripe_item_id = stripe_item.id
    actual_stripe_price_id_for_item = stripe_item.price.id
    item_metadata = stripe_item.metadata or {}

    plan_link = db.execute(
        select(ActiveSubscriptionPlanLink)
        .options(
            selectinload(ActiveSubscriptionPlanLink.selected_subjects)
        )  # Eager load for modification
        .where(ActiveSubscriptionPlanLink.stripe_subscription_item_id == stripe_item_id)
    ).scalar_one_or_none()

    if not plan_link:
        logger.info(
            f"New Stripe subscription item {stripe_item_id} for sub ID {active_subscription_id}. Adding to DB."
        )
        plan_link = ActiveSubscriptionPlanLink(
            active_subscription_id=active_subscription_id,
            subscription_option_id=subscription_option_db.id,
            chosen_stripe_price_id=actual_stripe_price_id_for_item,
            chosen_plan_type=item_chosen_plan_type,
            chosen_billing_period=item_chosen_billing_period,
            stripe_subscription_item_id=stripe_item_id,
            quantity=stripe_item.quantity,
            public_id=str(uuid.uuid4())
        )
        db.add(plan_link)
    else:
        logger.info(
            f"Updating existing ActiveSubscriptionPlanLink {plan_link.public_id} for Stripe item {stripe_item_id}."
        )
        plan_link.subscription_option_id = subscription_option_db.id
        plan_link.chosen_stripe_price_id = actual_stripe_price_id_for_item
        plan_link.chosen_plan_type = item_chosen_plan_type
        plan_link.chosen_billing_period = item_chosen_billing_period
        plan_link.quantity = stripe_item.quantity
        db.add(plan_link)  # Mark as dirty for update

    db.flush()  # Ensure plan_link.id is available

    # Sync PlanSelectedSubject records
    if item_chosen_plan_type == PlanTypeEnum.SC:
        selected_subject_ids_str = item_metadata.get("app_selected_subject_public_ids")

        # Always clear existing subjects for this link first to ensure sync with metadata
        # This handles cases where subjects are removed or changed.
        if plan_link.selected_subjects:  # Check if there's anything to delete
            logger.debug(
                f"Clearing existing {len(plan_link.selected_subjects)} subjects for SC plan link "
                f"{plan_link.public_id} before syncing."
            )
            for uss in list(plan_link.selected_subjects):  # Iterate over a copy
                db.delete(uss)
            db.flush()  # Apply deletes

        if selected_subject_ids_str:
            try:
                selected_subject_public_ids = json.loads(selected_subject_ids_str)
                if selected_subject_public_ids and isinstance(selected_subject_public_ids, list):
                    if len(selected_subject_public_ids) != plan_link.quantity:
                        logger.error(
                            f"Subject count {len(selected_subject_public_ids)} from metadata does not match "
                            f"item quantity {plan_link.quantity} for SC\n"
                            f"link {plan_link.public_id}. Not linking subjects."
                        )
                        send_admin_notification_email(
                            "webhook_error", 
                            f"Subject/quantity mismatch for SC link {plan_link.public_id} in subscription update."
                        )
                    else:
                        subjects_stmt = select(Subject.id).where(Subject.public_id.in_(selected_subject_public_ids))
                        subject_ids_to_link = db.execute(subjects_stmt).scalars().all()

                        if len(subject_ids_to_link) != len(selected_subject_public_ids):
                            found = db.query(Subject).filter(Subject.id.in_(subject_ids_to_link)).all()
                            logger.warning(
                                f"Some subjects from metadata not found in DB for SC link {plan_link.public_id}. "
                                f"Requested: {selected_subject_public_ids}, "
                                f"Found: {[s.public_id for s in found]}"
                            )
                            # Decide if this is an error or just link what's found

                        for sub_id_to_link in subject_ids_to_link:
                            db.add(PlanSelectedSubject(
                                plan_link_id=plan_link.id, 
                                subject_id=sub_id_to_link, 
                                public_id=str(uuid.uuid4())
                            ))
                        logger.info(
                            f"Synced {len(subject_ids_to_link)} subjects for SC\n"
                            f"plan link {plan_link.public_id} based on metadata."
                        )
            except json.JSONDecodeError:
                logger.error(
                    f"Failed to parse app_selected_subject_public_ids from item metadata: "
                    f"'{selected_subject_ids_str}' for link {plan_link.public_id}"
                )
        elif plan_link.quantity > 0:  # SC plan with quantity but no subject metadata
            # Check if subjects are already associated (e.g., from previous checkout.session.completed)
            if len(plan_link.selected_subjects) > 0:
                logger.info(
                    f"SC plan link {plan_link.public_id} has {len(plan_link.selected_subjects)} subjects "
                    f"already associated. No metadata update needed."
                )
            else:
                logger.warning(
                    f"SC plan link {plan_link.public_id} (quantity: {plan_link.quantity}) has no "
                    f"'app_selected_subject_public_ids' metadata and no associated subjects. "
                    f"User may need to select subjects."
                )
            # No subjects are linked in this case.

    elif item_chosen_plan_type == PlanTypeEnum.YF:
        if plan_link.selected_subjects:  # Check if there's anything to delete
            logger.info(
                f"Plan link {plan_link.public_id} is YF type. "
                f"Clearing {len(plan_link.selected_subjects)} specific subject selections."
            )
            for uss in list(plan_link.selected_subjects):  # Iterate over a copy
                db.delete(uss)
            db.flush()  # Apply deletes

    return plan_link


def handle_customer_subscription_updated(event: Dict[str, Any], db: SyncSession):
    stripe_subscription_data = event["data"]["object"]
    stripe_sub_id = stripe_subscription_data.get("id")
    logger.info(f"Handling customer.subscription.updated for Stripe subscription ID: {stripe_sub_id}")

    try:
        account = _get_account_from_stripe_customer_id(db, stripe_subscription_data.get("customer"))
        if not account:  # Error logged in helper
            send_admin_notification_email(
                "webhook_error", 
                f"Account not found for Stripe customer {stripe_subscription_data.get('customer')} "
                f"during subscription update {stripe_sub_id}."
            )
            return

        # Try to find an existing ActiveSubscription
        active_subscription = db.execute(
            select(ActiveSubscription)
            .options(
                selectinload(ActiveSubscription.plan_links).selectinload(ActiveSubscriptionPlanLink.selected_subjects)
            )  # Eager load
            .where(
                ActiveSubscription.stripe_subscription_id == stripe_sub_id, 
                ActiveSubscription.parent_account_id == account.id
            )
        ).scalar_one_or_none()

        is_new_db_subscription = False
        if not active_subscription:
            is_new_db_subscription = True
            logger.warning(
                f"ActiveSubscription not found in DB for Stripe sub ID {stripe_sub_id} and account {account.public_id} "
                f"during update. Creating it now."
            )
            active_subscription = ActiveSubscription(
                parent_account_id=account.id,
                stripe_subscription_id=stripe_sub_id,
                public_id=str(uuid.uuid4())
                # Status and period dates will be set below
            )
            db.add(active_subscription)
            # No db.flush() here yet, let the status update happen first, then one flush before item processing.

        # Check if this is a pause/resume event
        pause_collection = stripe_subscription_data.get("pause_collection")
        
        # Check if this is a pause/resume event
        pause_collection = stripe_subscription_data.get("pause_collection")
        
        # Update status and period dates from the incoming Stripe event
        # Special handling: Don't override PAUSED status when pause_collection is active
        stripe_status = SubscriptionStatusType(stripe_subscription_data.status)
        current_status = active_subscription.status
        
        # Format pause_collection for logging - escape braces for logger
        if pause_collection:
            pause_collection_str = str(pause_collection).replace('\n', ' ')
            # Don't double-escape braces in f-strings
        else:
            pause_collection_str = 'None'
            
        logger.info(
            f"Webhook status handling - Stripe status: {stripe_status}, "
            f"Current DB status: {current_status}, "
            f"pause_collection: {pause_collection_str}"
        )
        
        if pause_collection and pause_collection.get('behavior') == 'void':
            # Subscription is paused in Stripe, keep local PAUSED status if set
            if active_subscription.status != SubscriptionStatusType.PAUSED:
                logger.info(f"Setting subscription {stripe_sub_id} status to PAUSED due to pause_collection")
                active_subscription.status = SubscriptionStatusType.PAUSED
            else:
                logger.info(f"Subscription {stripe_sub_id} already marked as PAUSED, keeping status")
        else:
            # Normal case: use Stripe status (should be ACTIVE for resumed subscriptions)
            logger.info(f"Setting subscription {stripe_sub_id} status to {stripe_status} (from Stripe)")
            active_subscription.status = stripe_status
            
        active_subscription.current_period_start = datetime.fromtimestamp(
            stripe_subscription_data.current_period_start, tz=UTC
        )
        active_subscription.current_period_end = datetime.fromtimestamp(
            stripe_subscription_data.current_period_end, tz=UTC
        )
        active_subscription.cancel_at_period_end = stripe_subscription_data.cancel_at_period_end or False
        
        # Handle auto-resume when pause_collection is cleared by Stripe
        if pause_collection is None:
            # pause_collection cleared - check if we have an active pause record
            pause_stmt = select(SubscriptionPause).where(
                SubscriptionPause.active_subscription_id == active_subscription.id,
                SubscriptionPause.status == SubscriptionPauseStatus.PAUSED
            )
            active_pause = db.execute(pause_stmt).scalar_one_or_none()
            
            if active_pause:
                # Handle auto-resume for monthly subscriptions
                logger.info(
                    f"Detected pause_collection auto-cleared by Stripe for subscription {stripe_sub_id}. "
                    f"This indicates auto-resume at scheduled time for monthly subscription."
                )
                
                # Mark pause as resumed
                active_pause.status = SubscriptionPauseStatus.RESUMED
                active_pause.resumed_at = datetime.now(UTC)
                db.add(active_pause)
                
                # Ensure subscription is ACTIVE
                if active_subscription.status != SubscriptionStatusType.ACTIVE:
                    active_subscription.status = SubscriptionStatusType.ACTIVE
        
        
        db.add(active_subscription)  # Mark as dirty if it was existing, or ensure it's added if new
        db.flush()  # Ensure active_subscription.id is available if it was new, and updates are staged
        db.flush()  # Ensure active_subscription.id is available if it was new, and updates are staged

        if is_new_db_subscription:
            logger.info(
                f"Newly created ActiveSubscription {active_subscription.public_id} (ID: {active_subscription.id}) "
                f"for Stripe sub ID {stripe_sub_id}."
            )
        else:
            logger.info(
                f"Updating existing ActiveSubscription {active_subscription.public_id} (ID: {active_subscription.id}) "
                f"for Stripe sub ID {stripe_sub_id}."
            )


        stripe_items_from_event_map = {
            item.id: item for item in stripe_subscription_data.get("items", {}).get("data", [])
        }

        # Sync items present in the Stripe event
        # This loop will now correctly create/update plan links for both new and existing active_subscription records
        processed_db_link_stripe_item_ids = set()  # Keep track of stripe_item_ids we've processed from the event

        for stripe_item_id, stripe_item in stripe_items_from_event_map.items():
            processed_db_link_stripe_item_ids.add(stripe_item_id)  # Mark as processed from event
            lookup_result = _get_subscription_option_and_details_from_stripe_price_id(db, stripe_item.price.id)

            if not lookup_result:
                logger.error(
                    f"Cannot find SubscriptionOption for Stripe Price ID {stripe_item.price.id} "
                    f"(Stripe Item ID: {stripe_item_id}). Skipping sync for this item."
                )
                send_admin_notification_email(
                    "webhook_error", 
                    f"SubscriptionOption lookup failed for Stripe Price ID {stripe_item.price.id} "
                    f"during update of sub {stripe_sub_id}."
                )
                continue

            subscription_option_db, item_chosen_plan_type, item_chosen_billing_period = lookup_result

            # _sync_active_subscription_plan_link will handle creating new links if active_subscription was new,
            # or updating existing ones.
            _sync_active_subscription_plan_link(
                db,
                active_subscription.id,  # Pass the ID of the (potentially new) active_subscription
                stripe_item,
                subscription_option_db,
                item_chosen_plan_type,
                item_chosen_billing_period
            )

        # Remove DB links that are no longer in the Stripe subscription
        # This needs to be done carefully, especially if active_subscription.plan_links was not fully populated
        # for a newly created active_subscription before this point.
        # A safer way is to query all current links for this active_subscription_id *after* processing event items.

        # Re-fetch all current links from DB for this subscription to ensure we have the complete list
        # This is important if active_subscription was newly created and plan_links was empty initially.
        current_db_links_for_sub_stmt = select(ActiveSubscriptionPlanLink).where(
            ActiveSubscriptionPlanLink.active_subscription_id == active_subscription.id
        )
        all_db_links_for_this_sub = db.execute(current_db_links_for_sub_stmt).scalars().all()

        for db_link in all_db_links_for_this_sub:
            if db_link.stripe_subscription_item_id not in processed_db_link_stripe_item_ids:
                logger.info(
                    f"Stripe subscription item {db_link.stripe_subscription_item_id} (DB link ID: {db_link.id}) "
                    f"no longer exists on Stripe sub {stripe_sub_id}. Deleting from DB."
                )
                db.delete(db_link)  # Cascade will delete PlanSelectedSubject

        db.commit()
        logger.info(f"Successfully processed customer.subscription.updated for Stripe ID {stripe_sub_id}")
        send_admin_notification_email(
            "update", 
            f"Subscription updated for account {account.email}. Stripe Sub ID: {stripe_sub_id}"
        )

    except Exception as e:
        db.rollback()
        logger.critical(
            f"Critical error in handle_customer_subscription_updated for sub {stripe_sub_id}: {str(e)}", 
            exc_info=True
        )
        send_admin_notification_email(
            "webhook_error", 
            f"Critical error processing customer.subscription.updated for {stripe_sub_id}: {str(e)}"
        )


def handle_customer_subscription_deleted(event: Dict[str, Any], db: SyncSession):
    stripe_subscription_data = event["data"]["object"]
    stripe_sub_id = stripe_subscription_data.get("id")
    logger.info(f"Handling customer.subscription.deleted for Stripe subscription ID: {stripe_sub_id}")

    try:
        account = _get_account_from_stripe_customer_id(db, stripe_subscription_data.get("customer"))
        if not account:
            logger.error(
                f"Account not found for Stripe customer {stripe_subscription_data.get('customer')} "
                f"during subscription deletion {stripe_sub_id}."
            )
            send_admin_notification_email(
                "webhook_error", 
                f"Account not found for Stripe customer {stripe_subscription_data.get('customer')} "
                f"during subscription deletion {stripe_sub_id}."
            )
            return

        active_subscription = db.execute(
            select(ActiveSubscription).where(
                ActiveSubscription.stripe_subscription_id == stripe_sub_id, 
                ActiveSubscription.parent_account_id == account.id
            )
        ).scalar_one_or_none()

        if active_subscription:
            final_stripe_status_str = stripe_subscription_data.get("status", "canceled").lower()
            try:
                active_subscription.status = SubscriptionStatusType(final_stripe_status_str)
            except ValueError:
                logger.error(
                    f"Unknown subscription status '{final_stripe_status_str}' from Stripe. Defaulting to CANCELED."
                )
                active_subscription.status = SubscriptionStatusType.CANCELED

            active_subscription.cancel_at_period_end = stripe_subscription_data.get("cancel_at_period_end", True)
            if stripe_subscription_data.get("current_period_start"):
                active_subscription.current_period_start = datetime.fromtimestamp(
                    stripe_subscription_data.get("current_period_start"), tz=UTC
                )
            if stripe_subscription_data.get("current_period_end"):
                active_subscription.current_period_end = datetime.fromtimestamp(
                    stripe_subscription_data.get("current_period_end"), tz=UTC
                )

            db.add(active_subscription)
            
            # Check if this account has any other active or trialing subscriptions
            other_active_subs_stmt = select(ActiveSubscription).where(
                ActiveSubscription.parent_account_id == account.id,
                ActiveSubscription.id != active_subscription.id,  # Exclude current subscription
                ActiveSubscription.status.in_([
                    SubscriptionStatusType.ACTIVE,
                    SubscriptionStatusType.TRIALING
                ])
            )
            other_active_subs = db.execute(other_active_subs_stmt).scalars().all()
            
            # If no other active subscriptions, remove price eligibility
            if not other_active_subs:
                logger.info(
                    f"No other active subscriptions found for account {account.public_id}. "
                    f"Removing price eligibility to ensure current pricing on re-subscription."
                )
                
                # Delete all price eligibilities for this account
                delete_stmt = sqlalchemy_delete(PriceEligibility).where(
                    PriceEligibility.account_id == account.id
                )
                result = db.execute(delete_stmt)
                
                if result.rowcount > 0:
                    logger.info(
                        f"Deleted {result.rowcount} price eligibility entries for account {account.public_id}"
                    )
                else:
                    logger.debug(
                        f"No price eligibility entries found to delete for account {account.public_id}"
                    )
            else:
                logger.info(
                    f"Account {account.public_id} has {len(other_active_subs)} other active subscriptions. "
                    f"Keeping price eligibility."
                )
            
            db.commit()
            logger.info(
                f"Successfully processed customer.subscription.deleted for Stripe ID {stripe_sub_id}. "
                f"Marked as {active_subscription.status.value}."
            )
            send_admin_notification_email(
                "cancel", 
                f"Subscription {active_subscription.status.value} for account {account.email}. "
                f"Stripe Sub ID: {stripe_sub_id}"
            )
        else:
            logger.warning(
                f"No ActiveSubscription found in DB to update/delete for Stripe sub ID {stripe_sub_id} "
                f"and account {account.public_id}."
            )

    except Exception as e:
        db.rollback()
        logger.critical(
            f"Critical error in handle_customer_subscription_deleted for sub {stripe_sub_id}: {str(e)}", 
            exc_info=True
        )
        send_admin_notification_email(
            "webhook_error", 
            f"Critical error processing customer.subscription.deleted for {stripe_sub_id}: {str(e)}"
        )


def handle_invoice_created(event: Dict[str, Any], db: SyncSession):
    """Handle invoice.created events to detect auto-resume from pause."""
    invoice_data = event["data"]["object"]
    stripe_sub_id = invoice_data.get("subscription")
    customer_id = invoice_data.get("customer")
    
    if not stripe_sub_id or not customer_id:
        logger.debug("Invoice created event missing subscription or customer ID")
        return
    
    logger.info(f"Processing invoice.created for subscription {stripe_sub_id}")
    
    try:
        account = _get_account_from_stripe_customer_id(db, customer_id)
        if not account:
            logger.warning(f"Account not found for customer {customer_id} in invoice.created")
            return
        
        # Check if we have a paused subscription
        active_sub_stmt = select(ActiveSubscription).where(
            ActiveSubscription.stripe_subscription_id == stripe_sub_id,
            ActiveSubscription.parent_account_id == account.id
        )
        active_sub = db.execute(active_sub_stmt).scalar_one_or_none()
        
        if active_sub:
            # Check for active pause record
            pause_stmt = select(SubscriptionPause).where(
                SubscriptionPause.active_subscription_id == active_sub.id,
                SubscriptionPause.status == SubscriptionPauseStatus.PAUSED
            )
            active_pause = db.execute(pause_stmt).scalar_one_or_none()
            
            if active_pause:
                logger.info(
                    f"Invoice created for paused subscription {stripe_sub_id}. "
                    f"This indicates auto-resume. Updating pause record."
                )
                
                # Mark pause as resumed
                active_pause.status = SubscriptionPauseStatus.RESUMED
                active_pause.resumed_at = datetime.now(UTC)
                db.add(active_pause)
                
                # Update subscription status
                active_sub.status = SubscriptionStatusType.ACTIVE
                db.add(active_sub)
                
                db.commit()
                
                send_admin_notification_email(
                    "auto_resume",
                    f"Subscription auto-resumed for account {account.email} (ID: {account.public_id}). "
                    f"Stripe Sub ID: {stripe_sub_id}"
                )
    except Exception as e:
        db.rollback()
        logger.error(
            f"Error processing invoice.created for subscription {stripe_sub_id}: {str(e)}",
            exc_info=True
        )

def handle_invoice_payment_failed(event: Dict[str, Any], db: SyncSession):
    invoice_data = event["data"]["object"]
    stripe_sub_id = invoice_data.get("subscription")
    customer_id = invoice_data.get("customer")
    attempt_count = invoice_data.get("attempt_count")
    next_payment_attempt_ts = invoice_data.get("next_payment_attempt")
    next_payment_attempt_dt = datetime.fromtimestamp(next_payment_attempt_ts, tz=UTC)\
        if next_payment_attempt_ts else None
    invoice_status = invoice_data.get("status")
    logger.warning(
        f"Invoice payment failed (status: {invoice_status}) for Stripe subscription ID: {stripe_sub_id}, "
        f"Customer: {customer_id}, Attempt: {attempt_count}, Next attempt: {next_payment_attempt_dt}"
    )

    if stripe_sub_id and customer_id:
        account = _get_account_from_stripe_customer_id(db, customer_id)
        if account:
            active_sub = db.execute(
                select(ActiveSubscription).where(
                    ActiveSubscription.stripe_subscription_id == stripe_sub_id, 
                    ActiveSubscription.parent_account_id == account.id
                )
            ).scalar_one_or_none()

            if active_sub:
                try:
                    stripe_sub_obj = stripe.Subscription.retrieve(stripe_sub_id)
                    new_status = SubscriptionStatusType(stripe_sub_obj.status)
                    if active_sub.status != new_status:
                        active_sub.status = new_status
                        db.add(active_sub)
                        db.commit()
                        logger.info(
                            f"Updated ActiveSubscription {active_sub.public_id} status to {new_status.value} "
                            f"due to payment failure."
                        )
                    else:
                        logger.info(
                            f"ActiveSubscription {active_sub.public_id} status already {active_sub.status.value}. "
                            f"No change from payment failure event."
                        )
                except stripe.error.StripeError as se:
                    logger.error(
                        f"Could not retrieve Stripe subscription {stripe_sub_id}\
                             to update status after payment failure: {se}"
                    )
                except Exception as e:
                    db.rollback()
                    logger.error(
                        f"Error updating subscription status after payment failure for {stripe_sub_id}: {e}"
                    )

            send_admin_notification_email(
                "payment_failed", 
                f"Invoice payment failed (status: {invoice_status}) for account {account.email} "
                f"(Stripe Sub ID: {stripe_sub_id}). Attempt: {attempt_count}. "
                f"Next attempt: {next_payment_attempt_dt or 'N/A'}."
            )
        else:
            send_admin_notification_email(
                "payment_failed", 
                f"Invoice payment failed (status: {invoice_status}) for unknown account "
                f"(Customer: {customer_id}, Stripe Sub ID: {stripe_sub_id})."
            )
    else:
        logger.error(f"Invoice payment failed event missing subscription or customer ID: {invoice_data.get('id')}")


def handle_early_fraud_warning(event: Dict[str, Any], db: SyncSession):
    efw_data = event["data"]["object"]
    charge_id = efw_data.get("charge")
    fraud_type = efw_data.get("fraud_type")
    actionable = efw_data.get("actionable")
    logger.info(
        f"Received early fraud warning for charge: {charge_id}, type: {fraud_type}, actionable: {actionable}"
    )

    account = _get_account_from_stripe_charge_id(db, charge_id)
    if account:
        send_admin_notification_email(
            "fraud_warning", 
            f"Early Fraud Warning on charge {charge_id} for account {account.email} (ID: {account.public_id}). "
            f"Type: {fraud_type}. Actionable: {actionable}."
        )
    else:
        send_admin_notification_email(
            "fraud_warning", 
            f"Early Fraud Warning on charge {charge_id}. Account not found via charge. "
            f"Type: {fraud_type}. Actionable: {actionable}."
        )


def _get_account_from_stripe_customer_id(db: SyncSession, customer_id: Optional[str]) -> Optional[Account]:
    if not customer_id:
        logger.error("Stripe Customer ID missing in event data.")
        return None
    account = db.execute(select(Account).where(Account.stripe_customer_id == customer_id)).scalar_one_or_none()
    if not account:
        logger.error(f"No account found for Stripe customer ID: {customer_id} from event.")
    return account

def _get_account_from_stripe_charge_id(db: SyncSession, charge_id: Optional[str]) -> Optional[Account]:
    if not charge_id:
        logger.error("Stripe Charge ID missing for fraud warning.")
        return None
    try:
        charge = stripe.Charge.retrieve(charge_id)
        customer_id = charge.customer
        if not customer_id:
            logger.error(f"Customer ID missing for charge {charge_id}.")
            return None
        return _get_account_from_stripe_customer_id(db, customer_id)
    except stripe.error.StripeError as se:
        logger.error(f"Stripe API error retrieving charge {charge_id}: {str(se)}", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"Unexpected error retrieving charge {charge_id}: {str(e)}", exc_info=True)
        return None


SERVICE_EVENT_HANDLER_MAP = {
    'checkout.session.completed': handle_checkout_session_completed,
    'customer.subscription.updated': handle_customer_subscription_updated,
    'customer.subscription.deleted': handle_customer_subscription_deleted,
    'invoice.created': handle_invoice_created,
    'invoice.payment_failed': handle_invoice_payment_failed,
    'radar.early_fraud_warning.created': handle_early_fraud_warning,
}