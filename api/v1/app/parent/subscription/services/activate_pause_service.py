"""
Activate pause service.
This service is called by QStash to activate scheduled pauses.
"""

import stripe
from sqlalchemy.orm import Session as SyncSession
from sqlalchemy import select
from loguru import logger
from datetime import datetime, UTC, timedelta
from dateutil import parser
from db.models import ActiveSubscription, SubscriptionPause, ActiveSubscriptionPlanLink
from db.models.subscription import SubscriptionStatusType, SubscriptionPauseStatus, BillingPeriodEnum
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ServiceError, ExternalServiceError, ValidationError
)
from api.v1.common.schemas import AppErrorCode
from core.config.settings import settings

# Initialize Stripe
stripe.api_key = settings.STRIPE_API_KEY


def activate_pause(
    db: SyncSession,
    active_subscription_id: int,
    subscription_pause_public_id: str
) -> bool:
    """
    Activate a scheduled pause.
    
    Monthly: Apply pause_collection with mark_uncollectible and resumes_at
    Yearly: Extend trial_end by the pause duration
    
    Args:
        db: Database session
        active_subscription_id: ID of the subscription to pause
        subscription_pause_public_id: Public ID of the pause record
        
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(
        f"Activating scheduled pause for subscription_id={active_subscription_id}, "
        f"pause_public_id={subscription_pause_public_id}"
    )
    
    try:
        # Get the active subscription
        active_sub_stmt = select(ActiveSubscription).where(
            ActiveSubscription.id == active_subscription_id
        )
        active_subscription = db.execute(active_sub_stmt).scalar_one_or_none()
        
        if not active_subscription:
            logger.error(f"Active subscription not found: {active_subscription_id}")
            raise NotFoundError(
                message="Active subscription not found.",
                error_code=AppErrorCode.SUBSCRIPTION_NOT_FOUND,
                entity_name="ActiveSubscription",
                identifier=str(active_subscription_id)
            )
        
        # Get the pause record - first try to find a scheduled pause
        pause_stmt = select(SubscriptionPause).where(
            SubscriptionPause.public_id == subscription_pause_public_id,
            SubscriptionPause.status == SubscriptionPauseStatus.SCHEDULED
        )
        subscription_pause = db.execute(pause_stmt).scalar_one_or_none()
        
        if not subscription_pause:
            # Check if it's already paused (idempotency check)
            pause_stmt_any = select(SubscriptionPause).where(
                SubscriptionPause.public_id == subscription_pause_public_id
            )
            subscription_pause_any = db.execute(pause_stmt_any).scalar_one_or_none()
            
            if subscription_pause_any and subscription_pause_any.status == SubscriptionPauseStatus.PAUSED:
                # Already paused - idempotent operation
                logger.info(f"Pause {subscription_pause_public_id} is already active. Idempotent operation.")
                return True
            
            logger.error(f"Scheduled pause not found: {subscription_pause_public_id}")
            raise NotFoundError(
                message="Scheduled pause not found.",
                error_code=AppErrorCode.SUBSCRIPTION_PAUSE_NOT_FOUND,
                entity_name="SubscriptionPause",
                identifier=subscription_pause_public_id
            )
        
        # Validate that the pause belongs to this subscription
        if subscription_pause.active_subscription_id != active_subscription_id:
            logger.error(
                f"Pause {subscription_pause_public_id} does not belong to subscription {active_subscription_id}"
            )
            raise ServiceError(
                message="Pause record mismatch.",
                error_code=AppErrorCode.SERVICE_ERROR
            )
        
        # Get current time for the pause activation
        current_time = datetime.now(UTC)
        
        # Get billing period to determine pause method
        plan_link_stmt = (
            select(ActiveSubscriptionPlanLink)
            .where(ActiveSubscriptionPlanLink.active_subscription_id == active_subscription.id)
            .limit(1)
        )
        plan_link = db.execute(plan_link_stmt).scalar_one_or_none()
        
        if not plan_link:
            logger.error(f"No plan link found for subscription {active_subscription.id}")
            raise ServiceError(
                message="Subscription configuration error.",
                error_code=AppErrorCode.SERVICE_ERROR
            )
        
        billing_period = plan_link.chosen_billing_period
        logger.info(f"Activating pause for {billing_period.value} subscription")
        
        # Get pause end date from settings
        pause_end = parser.parse(settings.SUMMER_PAUSE_END_UTC)
        
        # Generate idempotency key for Stripe operation
        idempotency_key = f"activate_pause_v4_{active_subscription.stripe_subscription_id}_{current_time.timestamp()}"
        
        if billing_period == BillingPeriodEnum.MONTHLY:
            # Monthly: Apply pause_collection with auto-resume
            stripe.Subscription.modify(
                active_subscription.stripe_subscription_id,
                pause_collection={
                    'behavior': 'mark_uncollectible',
                    'resumes_at': int(pause_end.timestamp())
                },
                idempotency_key=idempotency_key
            )
            
            # Update subscription status to PAUSED
            active_subscription.status = SubscriptionStatusType.PAUSED
            
            logger.info(
                f"Successfully paused MONTHLY subscription {active_subscription.stripe_subscription_id} "
                f"with auto-resume on {pause_end.date()}"
            )
            
        else:  # YEARLY
            # Get current subscription details from Stripe
            stripe_sub = stripe.Subscription.retrieve(active_subscription.stripe_subscription_id)
            current_period_end = datetime.fromtimestamp(stripe_sub.current_period_end, tz=UTC)
            
            # Calculate freeze duration from activation time to pause end
            freeze_duration = int((pause_end - current_time).total_seconds())
            extended_trial_end = current_period_end + timedelta(seconds=freeze_duration)
            
            # Apply trial_end extension
            stripe.Subscription.modify(
                active_subscription.stripe_subscription_id,
                trial_end=int(extended_trial_end.timestamp()),
                proration_behavior='none',
                idempotency_key=idempotency_key
            )
            
            # Update pause record with extended period
            subscription_pause.new_period_end = extended_trial_end
            
            # Note: Keep subscription status as ACTIVE/TRIALING for yearly
            
            logger.info(
                f"Successfully extended YEARLY subscription {active_subscription.stripe_subscription_id} "
                f"by {freeze_duration // 86400} days. New billing date: {extended_trial_end.date()}"
            )
        
        # Update database records
        subscription_pause.status = SubscriptionPauseStatus.PAUSED
        subscription_pause.start_date = current_time  # Update to actual pause time
        
        db.add(active_subscription)
        db.add(subscription_pause)
        db.commit()
        
        logger.info(
            f"Successfully activated pause for subscription {active_subscription.public_id}. "
            f"Pause record {subscription_pause_public_id} updated to PAUSED status. "
            f"Pause started at: {current_time}"
        )
        
        # TODO: Schedule auto-resume for subscription_pause.end_date if needed
        
        return True
        
    except stripe.error.StripeError as e:
        db.rollback()
        logger.error(
            f"Stripe API error activating pause for subscription {active_subscription_id}: {str(e)}",
            exc_info=True
        )
        raise ExternalServiceError(
            message=f"Payment gateway error: {e.user_message if hasattr(e, 'user_message') and e.user_message else str(e)}",
            error_code=AppErrorCode.STRIPE_ERROR,
            original_exception=e
        )
    except (NotFoundError, ServiceError, ValidationError):
        # Re-raise our custom exceptions
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        logger.error(
            f"Unexpected error activating pause for subscription {active_subscription_id}: {str(e)}",
            exc_info=True
        )
        raise ServiceError(
            message="An unexpected error occurred while activating the pause.",
            error_code=AppErrorCode.SERVICE_ERROR
        )