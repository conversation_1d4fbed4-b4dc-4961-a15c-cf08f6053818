from sqlalchemy.orm import Session as SyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy import select
from db.models import (
    Account, ActiveSubscription, Subject, 
    ActiveSubscriptionPlanLink, PlanSelectedSubject, Year
)
from db.models.subscription import BillingPeriodEnum, PlanTypeEnum, SubscriptionStatusType, SubscriptionOption
from ..schemas.request import UpdateSubscriptionRequest
from ..schemas.response import (
    UpdateSubscriptionResponse, ActiveSubscriptionDetailResponse, 
    ActiveSubscriptionPlanLinkDetailSchema, SubjectBasicInfoSchema
)
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ValidationError, ServiceError, ExternalServiceError, AuthenticationError
)
from api.v1.common.schemas import AppErrorCode
# from .utils import _get_or_create_eligible_price_version
import stripe
from loguru import logger
from datetime import datetime, UTC
import uuid
from typing import List, Optional, Dict

def _build_active_subscription_detail_response(
    db: SyncSession, 
    active_sub_orm: ActiveSubscription
) -> ActiveSubscriptionDetailResponse:
    if not active_sub_orm.parent_account:
        parent_account_for_response = db.get(Account, active_sub_orm.parent_account_id)
        if not parent_account_for_response:
            logger.error(
                f"Parent account {active_sub_orm.parent_account_id} not found for ActiveSubscription "
                f"{active_sub_orm.public_id}"
            )
            raise ServiceError("Parent account data missing for subscription details.")
    else:
        parent_account_for_response = active_sub_orm.parent_account

    plan_links_details: List[ActiveSubscriptionPlanLinkDetailSchema] = []
    for link in active_sub_orm.plan_links:
        if not link.subscription_option or not link.subscription_option.year:
            logger.warning(
                f"Skipping ActiveSubscriptionPlanLink {link.public_id}, missing subscription_option or year."
            )
            continue

        plan_type: Optional[PlanTypeEnum] = None
        billing_period: Optional[BillingPeriodEnum] = None
        option = link.subscription_option
        stripe_price_id = link.chosen_stripe_price_id

        if stripe_price_id == option.sc_monthly_stripe_price_id:
            plan_type = PlanTypeEnum.SC
            billing_period = BillingPeriodEnum.MONTHLY
        elif stripe_price_id == option.sc_yearly_stripe_price_id:
            plan_type = PlanTypeEnum.SC
            billing_period = BillingPeriodEnum.YEARLY
        elif stripe_price_id == option.yf_monthly_stripe_price_id:
            plan_type = PlanTypeEnum.YF
            billing_period = BillingPeriodEnum.MONTHLY
        elif stripe_price_id == option.yf_yearly_stripe_price_id:
            plan_type = PlanTypeEnum.YF
            billing_period = BillingPeriodEnum.YEARLY
        else:
            logger.warning(
                f"Could not determine plan type/billing period for "
                f"link {link.public_id} with price ID {stripe_price_id}"
            )

        year_name = option.year.name
        plan_name_suffix = "Unknown Plan"
        if plan_type == PlanTypeEnum.SC:
            plan_name_suffix = "Subject Credits"
        elif plan_type == PlanTypeEnum.YF:
            plan_name_suffix = "Full Access"
        subscription_plan_name = f"{year_name} - {plan_name_suffix}"

        selected_subjects_details = []
        if link.selected_subjects:
            for uss in link.selected_subjects:
                if uss.subject:
                    year_name_subj = uss.subject.year.name if uss.subject.year else None
                    year_public_id_subj = uss.subject.year.public_id if uss.subject.year else None
                    selected_subjects_details.append(SubjectBasicInfoSchema(
                        public_id=uss.subject.public_id,
                        name=uss.subject.name,
                        year_name=year_name_subj,
                        year_public_id=year_public_id_subj
                    ))

        plan_links_details.append(ActiveSubscriptionPlanLinkDetailSchema(
            public_id=link.public_id,
            subscription_option_public_id=option.public_id,
            subscription_plan_name=subscription_plan_name,
            plan_type=plan_type,
            chosen_stripe_price_id=stripe_price_id,
            chosen_billing_period=billing_period,
            stripe_subscription_item_id=link.stripe_subscription_item_id,
            quantity=link.quantity,
            selected_subjects=selected_subjects_details
        ))

    return ActiveSubscriptionDetailResponse(
        public_id=active_sub_orm.public_id,
        parent_account_public_id=parent_account_for_response.public_id,
        stripe_subscription_id=active_sub_orm.stripe_subscription_id,
        status=active_sub_orm.status,
        current_period_start=active_sub_orm.current_period_start,
        current_period_end=active_sub_orm.current_period_end,
        cancel_at_period_end=active_sub_orm.cancel_at_period_end,
        plan_links=plan_links_details,
        created_at=active_sub_orm.created_at,
        updated_at=active_sub_orm.updated_at
    )

def update_subscription_service(
    db: SyncSession, 
    parent_public_id: str, 
    update_data: UpdateSubscriptionRequest
) -> UpdateSubscriptionResponse:
    logger.info(
        f"Updating subscription for parent_public_id: {parent_public_id}\n"
        f"with {len(update_data.items_to_update)} actions."
    )

    account_stmt = select(Account).where(Account.public_id == parent_public_id)
    account = db.execute(account_stmt).scalar_one_or_none()
    if not account:
        logger.warning(f"Parent account not found: {parent_public_id}")
        raise AuthenticationError(
            message="Parent account not found.", 
            error_code=AppErrorCode.PARENT_ACCOUNT_NOT_FOUND
        )

    # Get all active subscriptions with related data
    active_subs_stmt = (
        select(ActiveSubscription)
        .options(
            selectinload(ActiveSubscription.parent_account),
            selectinload(ActiveSubscription.plan_links).options(
                selectinload(ActiveSubscriptionPlanLink.subscription_option)
                .selectinload(SubscriptionOption.year),
                selectinload(ActiveSubscriptionPlanLink.target_child_account),
                selectinload(ActiveSubscriptionPlanLink.selected_subjects)
                .selectinload(PlanSelectedSubject.subject)
                .selectinload(Subject.year)
            )
        )
        .where(
            ActiveSubscription.parent_account_id == account.id, 
            ActiveSubscription.status == SubscriptionStatusType.ACTIVE
        )
        .order_by(ActiveSubscription.created_at.desc())
    )
    active_subscriptions = db.execute(active_subs_stmt).scalars().all()

    if not active_subscriptions:
        logger.warning(f"No active subscriptions found for parent {parent_public_id}")
        raise NotFoundError(
            message="No active subscription found to update.", 
            error_code=AppErrorCode.ACTIVE_SUBSCRIPTION_NOT_FOUND
        )
    
    # Determine which subscription to operate on
    active_subscription = _determine_target_subscription(
        active_subscriptions, 
        update_data, 
        parent_public_id
    )

    # try:
    #     price_version = _get_or_create_eligible_price_version(db, account)
    # except ServiceError as e:
    #     logger.error(
    #         "Service error determining price version for account {} during subscription update: {}", 
    #         account.id, 
    #         e.log_message
    #     )
    #     raise
    # except Exception as e:
    #     logger.error(
    #         "Unexpected error determining price version for account {} during subscription update: {}", 
    #         account.id, 
    #         str(e), 
    #         exc_info=True
    #     )
    #     raise ServiceError(
    #         message="Could not determine pricing information for subscription update.", 
    #         error_code=AppErrorCode.SERVICE_ERROR
    #     )

    stripe_items_to_update = []
    items_to_add_on_stripe = []
    plan_links_to_create_details = []

    for item_action in update_data.items_to_update:

        if item_action.action == "add":
            if not item_action.year_public_id or not item_action.chosen_billing_period:
                raise ValidationError(
                    "Missing year_public_id or chosen_billing_period for 'add' action.", 
                    error_code=AppErrorCode.STRIPE_ERROR
                )

            # Determine plan type and quantity (same logic as before)
            subjects_to_add_public_ids = item_action.subject_public_ids or []
            quantity_to_add = len(subjects_to_add_public_ids)
            billing_period_to_add = item_action.chosen_billing_period
            plan_type_to_add = PlanTypeEnum.SC
            if quantity_to_add == 0:  # Interpret as YF request
                plan_type_to_add = PlanTypeEnum.YF
                quantity_to_add = 1
            elif quantity_to_add > 0:
                plan_type_to_add = PlanTypeEnum.SC
            else:
                raise ValidationError(
                    "Invalid quantity derived for 'add' action.", 
                    error_code=AppErrorCode.STRIPE_ERROR
                )

            logger.info(
                f"Processing plan item to add: Year '{item_action.year_public_id}', Type '{plan_type_to_add}', "
                f"Billing '{billing_period_to_add}'"
            )

            # 1. Find Year DB object
            year_stmt_add = select(Year).where(Year.public_id == item_action.year_public_id)
            year_db_add = db.execute(year_stmt_add).scalar_one_or_none()
            if not year_db_add:
                raise ValidationError(
                    message=f"Year '{item_action.year_public_id}' not found.", 
                    error_code=AppErrorCode.STRIPE_ERROR
                )

            # 2. Find the corresponding SubscriptionOption
            option_stmt = select(SubscriptionOption).where(
                SubscriptionOption.year_id == year_db_add.id, 
                SubscriptionOption.is_active
            )
            subscription_option = db.execute(option_stmt).scalar_one_or_none()
            if not subscription_option:
                raise ValidationError(
                    message=f"Active subscription options for Year '{item_action.year_public_id}' not found.", 
                    error_code=AppErrorCode.STRIPE_ERROR
                )

            # 3. Select the correct Stripe Price ID from the SubscriptionOption
            stripe_price_id_to_add: Optional[str] = None
            if plan_type_to_add == PlanTypeEnum.SC:
                if billing_period_to_add == BillingPeriodEnum.MONTHLY:
                    stripe_price_id_to_add = subscription_option.sc_monthly_stripe_price_id
                elif billing_period_to_add == BillingPeriodEnum.YEARLY:
                    stripe_price_id_to_add = subscription_option.sc_yearly_stripe_price_id
            elif plan_type_to_add == PlanTypeEnum.YF:
                if billing_period_to_add == BillingPeriodEnum.MONTHLY:
                    stripe_price_id_to_add = subscription_option.yf_monthly_stripe_price_id
                elif billing_period_to_add == BillingPeriodEnum.YEARLY:
                    stripe_price_id_to_add = subscription_option.yf_yearly_stripe_price_id

            if not stripe_price_id_to_add:
                raise ValidationError(
                    message=(
                        f"No active Stripe Price ID found for Year '{item_action.year_public_id}',\n"
                        f"Type '{plan_type_to_add}',\n"
                        f"Billing '{billing_period_to_add}'.\n"
                    ),
                    error_code=AppErrorCode.STRIPE_ERROR
                )

            # 4. Check if this price is already on the subscription (optional but good practice)
            if any(item.get('price') == stripe_price_id_to_add for item in active_subscription.plan_links):
                logger.warning(
                    f"Attempted to add price {stripe_price_id_to_add} which is already on subscription "
                    f"{active_subscription.stripe_subscription_id}. Skipping add."
                )
                continue  # Skip adding this item

            # 5. Prepare Stripe item parameters
            new_plan_link_public_id = str(uuid.uuid4())  # Pre-generate ID for new link
            stripe_item_add_params = {
                'price': stripe_price_id_to_add,
                'quantity': quantity_to_add,
                'metadata': {
                    'app_plan_link_public_id': new_plan_link_public_id,  # Consistent naming with webhook handler?
                    'app_parent_account_public_id': parent_public_id,
                    'app_subscription_option_public_id': subscription_option.public_id, 
                    'app_year_public_id': year_db_add.public_id,
                    'app_plan_type': plan_type_to_add.value,
                    'app_billing_period': billing_period_to_add.value
                }
            }
            items_to_add_on_stripe.append(stripe_item_add_params)

            # 6. Store details to create ActiveSubscriptionPlanLink after Stripe update
            plan_links_to_create_details.append({
                'stripe_price_id': stripe_price_id_to_add,
                'quantity': quantity_to_add,
                'plan_link_public_id': new_plan_link_public_id,
                'subscription_option_id': subscription_option.id,
                'target_child_account_id': None,
                'subject_public_ids': subjects_to_add_public_ids,
                'plan_type': plan_type_to_add,
                'billing_period': billing_period_to_add
            })

        elif item_action.action == "remove":
            if not item_action.stripe_subscription_item_id:
                raise ValidationError(
                    "Missing Stripe subscription item ID for 'remove' action.", 
                    error_code=AppErrorCode.INVALID_REQUEST
                )
            stripe_items_to_update.append({
                "id": item_action.stripe_subscription_item_id,
                "deleted": True
            })

        elif item_action.action == "update_quantity":
            if not item_action.stripe_subscription_item_id or item_action.new_quantity is None:
                raise ValidationError(
                    "Missing Stripe subscription item ID or new quantity for 'update_quantity' action.", 
                    error_code=AppErrorCode.INVALID_REQUEST
                )
            if item_action.new_quantity == 0:
                stripe_items_to_update.append({"id": item_action.stripe_subscription_item_id, "deleted": True})
            elif item_action.new_quantity < 0:
                raise ValidationError("New quantity cannot be negative.", error_code=AppErrorCode.INVALID_REQUEST)
            else:
                stripe_items_to_update.append({
                    "id": item_action.stripe_subscription_item_id, 
                    "quantity": item_action.new_quantity
                })
                
        elif item_action.action == "change_billing_period":
            if not item_action.stripe_subscription_item_id or not item_action.new_billing_period:
                raise ValidationError(
                    "Missing Stripe subscription item ID or new billing period for 'change_billing_period' action.", 
                    error_code=AppErrorCode.INVALID_REQUEST
                )
            
            # For billing period change, we need to:
            # 1. Find the current plan link to get year, plan type, subjects
            # 2. Remove the old item 
            # 3. Add a new item with the same details but different billing period
            
            # Find the plan link that corresponds to this Stripe item
            plan_link_stmt = select(ActiveSubscriptionPlanLink).where(
                ActiveSubscriptionPlanLink.stripe_subscription_item_id == item_action.stripe_subscription_item_id,
                ActiveSubscriptionPlanLink.active_subscription_id == active_subscription.id
            ).options(
                selectinload(ActiveSubscriptionPlanLink.subscription_option).selectinload(SubscriptionOption.year),
                selectinload(ActiveSubscriptionPlanLink.selected_subjects).selectinload(PlanSelectedSubject.subject)
            )
            existing_plan_link = db.execute(plan_link_stmt).scalar_one_or_none()
            
            if not existing_plan_link:
                raise ValidationError(
                    f"Plan link for Stripe item {item_action.stripe_subscription_item_id} not found.", 
                    error_code=AppErrorCode.INVALID_REQUEST
                )
            
            # Get the current billing period to check if change is needed
            current_billing_period = existing_plan_link.chosen_billing_period
            if current_billing_period == item_action.new_billing_period:
                logger.info(f"Billing period is already {item_action.new_billing_period}, skipping change.")
                continue
            
            # Get the subscription option and determine new price
            subscription_option = existing_plan_link.subscription_option
            plan_type = existing_plan_link.chosen_plan_type
            current_quantity = existing_plan_link.quantity
            
            # Find the new Stripe price ID
            new_stripe_price_id: Optional[str] = None
            if plan_type == PlanTypeEnum.SC:
                if item_action.new_billing_period == BillingPeriodEnum.MONTHLY:
                    new_stripe_price_id = subscription_option.sc_monthly_stripe_price_id
                elif item_action.new_billing_period == BillingPeriodEnum.YEARLY:
                    new_stripe_price_id = subscription_option.sc_yearly_stripe_price_id
            elif plan_type == PlanTypeEnum.YF:
                if item_action.new_billing_period == BillingPeriodEnum.MONTHLY:
                    new_stripe_price_id = subscription_option.yf_monthly_stripe_price_id
                elif item_action.new_billing_period == BillingPeriodEnum.YEARLY:
                    new_stripe_price_id = subscription_option.yf_yearly_stripe_price_id
            
            if not new_stripe_price_id:
                raise ValidationError(
                    f"No Stripe price found for plan type {plan_type} and billing period {item_action.new_billing_period}.", 
                    error_code=AppErrorCode.INVALID_REQUEST
                )
            
            # Remove the old item and add the new one in the same Stripe call
            stripe_items_to_update.append({
                "id": item_action.stripe_subscription_item_id, 
                "deleted": True
            })
            
            # Add the new item with same details but different billing period
            items_to_add_on_stripe.append({
                "price": new_stripe_price_id,
                "quantity": current_quantity
            })
            
            # Prepare plan link details for database update
            new_plan_link_public_id = str(uuid.uuid4())
            subject_public_ids = [uss.subject.public_id for uss in existing_plan_link.selected_subjects]
            
            plan_links_to_create_details.append({
                'stripe_price_id': new_stripe_price_id,
                'plan_type': plan_type,
                'billing_period': item_action.new_billing_period,
                'quantity': current_quantity,
                'plan_link_public_id': new_plan_link_public_id,
                'subscription_option_id': subscription_option.id,
                'target_child_account_id': None,  # Keep same as existing
                'subject_public_ids': subject_public_ids
            })
            
            logger.info(
                f"Scheduled billing period change for item {item_action.stripe_subscription_item_id}: "
                f"{current_billing_period} -> {item_action.new_billing_period}"
            )
            
        else:
            raise ValidationError(f"Invalid action: {item_action.action}", error_code=AppErrorCode.INVALID_REQUEST)

    if not stripe_items_to_update and update_data.cancel_at_period_end is None and not items_to_add_on_stripe:
        logger.info("No items to update in Stripe subscription and no cancellation change.")
        response_details = _build_active_subscription_detail_response(db, active_subscription)
        return UpdateSubscriptionResponse(
            message="No changes were applied to the subscription.", 
            active_subscription=response_details
        )

    try:
        params_for_modify = {}
        if stripe_items_to_update:
            params_for_modify["items"] = stripe_items_to_update

        if update_data.proration_behavior:
            params_for_modify["proration_behavior"] = update_data.proration_behavior
        if update_data.cancel_at_period_end is not None:
            params_for_modify["cancel_at_period_end"] = update_data.cancel_at_period_end

        if any(item.get("price") for item in items_to_add_on_stripe):
            params_for_modify["payment_behavior"] = "error_if_incomplete"

        if not params_for_modify:
            logger.info("No parameters to send for Stripe subscription modification.")
            response_details = _build_active_subscription_detail_response(db, active_subscription)
            return UpdateSubscriptionResponse(
                message="No effective changes to apply to the subscription.", 
                active_subscription=response_details
            )

        updated_stripe_sub = stripe.Subscription.modify(
            active_subscription.stripe_subscription_id,
            **params_for_modify
        )

        active_subscription.status = SubscriptionStatusType(updated_stripe_sub.status)
        active_subscription.current_period_start = datetime.fromtimestamp(
            updated_stripe_sub.current_period_start, tz=UTC
        )
        active_subscription.current_period_end = datetime.fromtimestamp(
            updated_stripe_sub.current_period_end, tz=UTC
        )
        active_subscription.cancel_at_period_end = updated_stripe_sub.cancel_at_period_end
        db.add(active_subscription)

        logger.info(
            "Successfully requested Stripe subscription modification for {}.", 
            active_subscription.stripe_subscription_id
        )
        db.flush()

        # 4. Create new ActiveSubscriptionPlanLink records for added items
        if items_to_add_on_stripe:
            subjects_map_by_public_id: Dict[str, Subject] = {}
            all_subject_ids_needed = {
                subj_id for detail in plan_links_to_create_details 
                for subj_id in detail.get('subject_public_ids', [])
            }
            if all_subject_ids_needed:
                subjects_query = select(Subject).where(Subject.public_id.in_(all_subject_ids_needed))
                subjects_result = db.execute(subjects_query).scalars().all()
                subjects_map_by_public_id = {subj.public_id: subj for subj in subjects_result}
                if len(subjects_map_by_public_id) != len(all_subject_ids_needed):
                    logger.error("Mismatch finding subjects for new plan links. Not all requested subjects found.")
                    # Decide how to handle this - maybe proceed without some subjects?
                    # Or raise an error? For now, log and proceed.
                    missing_ids = all_subject_ids_needed - set(subjects_map_by_public_id.keys())
                    logger.warning("Missing Subject IDs during PlanLink creation: {}", missing_ids)

            for detail in plan_links_to_create_details:
                # Find the corresponding Stripe item ID from the updated subscription
                stripe_item_id = None
                for item in updated_stripe_sub.items.data:
                    if (item.price.id == detail['stripe_price_id'] 
                            and item.metadata.get('app_plan_link_public_id') == detail['plan_link_public_id']):
                        stripe_item_id = item.id
                        break

                if not stripe_item_id:
                    logger.error(
                        "Could not find matching Stripe Item ID for added price {} "
                        "with plan link public ID {}",
                        detail['stripe_price_id'],
                        detail['plan_link_public_id']
                    )
                    # Decide how to handle: skip this link, raise error?
                    continue  # Skip creating this link

                new_plan_link = ActiveSubscriptionPlanLink(
                    public_id=detail['plan_link_public_id'],
                    active_subscription_id=active_subscription.id,
                    subscription_option_id=detail['subscription_option_id'],
                    chosen_stripe_price_id=detail['stripe_price_id'],
                    chosen_plan_type=detail.get('plan_type', plan_type_to_add),
                    chosen_billing_period=detail.get('billing_period', billing_period_to_add),
                    stripe_subscription_item_id=stripe_item_id,
                    quantity=detail['quantity'],
                    target_child_account_id=detail.get('target_child_account_id')
                )
                db.add(new_plan_link)
                db.flush()  # Flush to get the new_plan_link.id

                # Create PlanSelectedSubject records
                selected_subject_links = []
                for subj_public_id in detail.get('subject_public_ids', []):
                    subject_db = subjects_map_by_public_id.get(subj_public_id)
                    if subject_db:
                        selected_subject_link = PlanSelectedSubject(
                            plan_link_id=new_plan_link.id,
                            subject_id=subject_db.id
                        )
                        selected_subject_links.append(selected_subject_link)
                    else:
                        logger.warning(
                            "Subject with public_id {} not found, "
                            "cannot link to PlanLink {}",
                            subj_public_id,
                            new_plan_link.public_id
                        )

                if selected_subject_links:
                    db.add_all(selected_subject_links)

            # Commit link and subject selections together after processing all added items
            try:
                db.commit()
                logger.info(
                    "Successfully created {} new ActiveSubscriptionPlanLink records.", 
                    len(plan_links_to_create_details)
                )
            except Exception as e:
                db.rollback()
                logger.error("Error committing new ActiveSubscriptionPlanLink records: {}", e, exc_info=True)
                # Potentially raise a specific error or add to a list of failures

        # 5. Refresh the active_subscription object to get all updates
        db.refresh(
            active_subscription,
            options=[
                selectinload(ActiveSubscription.parent_account),
                selectinload(ActiveSubscription.plan_links).options(
                    selectinload(ActiveSubscriptionPlanLink.subscription_option)
                    .selectinload(SubscriptionOption.year),
                    selectinload(ActiveSubscriptionPlanLink.target_child_account),
                    selectinload(ActiveSubscriptionPlanLink.selected_subjects)
                    .selectinload(PlanSelectedSubject.subject)
                    .selectinload(Subject.year)
                )
            ]
        )
        response_details = _build_active_subscription_detail_response(db, active_subscription)

        return UpdateSubscriptionResponse(
            message="Subscription update requested successfully.",
            active_subscription=response_details
        )

    except stripe.error.StripeError as e:
        logger.error(
            "Stripe API error updating subscription {}:{}", 
            active_subscription.stripe_subscription_id,
            str(e),
            exc_info=True
        )
        user_message = (
            "Payment gateway error: {}".format(e.user_message) if hasattr(e, 'user_message')
            and e.user_message else "Could not update subscription. Please try again."
        )
        if (hasattr(e, 'code') and e.code == 'resource_missing' 
            and any(item.get('id') for item in stripe_items_to_update 
                    if item.get('deleted') or item.get('quantity'))):
            raise NotFoundError(
                message="One of the items to update or remove was not found on the Stripe subscription.", 
                error_code=AppErrorCode.STRIPE_ERROR, 
                original_exception=e
            )
        raise ExternalServiceError(
            message=user_message, 
            error_code=AppErrorCode.STRIPE_ERROR, 
            original_exception=e
        )
    except Exception as e:
        logger.error(
            "Unexpected error updating subscription {}: {}", 
            active_subscription.stripe_subscription_id,
            str(e),
            exc_info=True
        )
        raise ServiceError(
            message="An unexpected error occurred while updating subscription: {}".format(str(e)), 
            error_code=AppErrorCode.SERVICE_ERROR
        )


def _determine_target_subscription(
    active_subscriptions: List[ActiveSubscription], 
    update_data: UpdateSubscriptionRequest, 
    parent_public_id: str
) -> ActiveSubscription:
    """
    Determine which subscription to operate on based on the update request.
    
    For 'add' actions: Use subscription_public_id if provided, otherwise default to 
    the most recent subscription if there's only one, or raise error if multiple.
    
    For other actions: Find the subscription containing the stripe_subscription_item_id.
    """
    from core.exception_handling.exceptions.custom_exceptions import ValidationError
    
    # Check if any actions are 'add' type
    add_actions = [item for item in update_data.items_to_update if item.action == 'add']
    non_add_actions = [item for item in update_data.items_to_update if item.action != 'add']
    
    # If we have both add and non-add actions, this is complex and needs careful handling
    if add_actions and non_add_actions:
        if update_data.subscription_public_id:
            # Use the specified subscription
            target_subscription = next(
                (sub for sub in active_subscriptions if sub.public_id == update_data.subscription_public_id), 
                None
            )
            if not target_subscription:
                raise ValidationError(
                    message=f"Subscription {update_data.subscription_public_id} not found.",
                    error_code=AppErrorCode.SUBSCRIPTION_NOT_FOUND
                )
            
            # Validate that non-add actions reference items in this subscription
            subscription_item_ids = {
                link.stripe_subscription_item_id 
                for link in target_subscription.plan_links
            }
            for action in non_add_actions:
                if action.stripe_subscription_item_id not in subscription_item_ids:
                    raise ValidationError(
                        message=f"Item {action.stripe_subscription_item_id} not found in subscription {update_data.subscription_public_id}.",
                        error_code=AppErrorCode.SUBSCRIPTION_ITEM_NOT_FOUND
                    )
            
            return target_subscription
        else:
            raise ValidationError(
                message="When mixing add and non-add actions with multiple subscriptions, subscription_public_id is required.",
                error_code=AppErrorCode.SUBSCRIPTION_ID_REQUIRED
            )
    
    # If only add actions
    if add_actions and not non_add_actions:
        if update_data.subscription_public_id:
            target_subscription = next(
                (sub for sub in active_subscriptions if sub.public_id == update_data.subscription_public_id), 
                None
            )
            if not target_subscription:
                raise ValidationError(
                    message=f"Subscription {update_data.subscription_public_id} not found.",
                    error_code=AppErrorCode.SUBSCRIPTION_NOT_FOUND
                )
            return target_subscription
        elif len(active_subscriptions) == 1:
            return active_subscriptions[0]
        else:
            raise ValidationError(
                message="Multiple subscriptions found. Please specify subscription_public_id for add actions.",
                error_code=AppErrorCode.SUBSCRIPTION_ID_REQUIRED
            )
    
    # If only non-add actions, find subscription containing the referenced items
    if non_add_actions and not add_actions:
        # All non-add actions should reference items in the same subscription
        target_subscriptions = set()
        for action in non_add_actions:
            for subscription in active_subscriptions:
                subscription_item_ids = {
                    link.stripe_subscription_item_id 
                    for link in subscription.plan_links
                }
                if action.stripe_subscription_item_id in subscription_item_ids:
                    target_subscriptions.add(subscription.public_id)
                    break
            else:
                raise ValidationError(
                    message=f"Item {action.stripe_subscription_item_id} not found in any active subscription.",
                    error_code=AppErrorCode.SUBSCRIPTION_ITEM_NOT_FOUND
                )
        
        if len(target_subscriptions) > 1:
            raise ValidationError(
                message="Actions reference items from multiple subscriptions. This is not supported.",
                error_code=AppErrorCode.MULTIPLE_SUBSCRIPTIONS_REFERENCED
            )
        
        target_subscription_id = next(iter(target_subscriptions))
        return next(sub for sub in active_subscriptions if sub.public_id == target_subscription_id)
    
    # Should not reach here
    raise ValidationError(
        message="No valid actions found in update request.",
        error_code=AppErrorCode.INVALID_REQUEST
    )