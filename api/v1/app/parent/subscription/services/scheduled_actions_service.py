import stripe
from sqlalchemy.orm import Session as SyncSession
from sqlalchemy import select, func
from loguru import logger
from datetime import datetime, UTC
from db.models import ActiveSubscription, SubscriptionPause
from db.models.subscription import SubscriptionStatusType, SubscriptionPauseStatus
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ServiceError, ExternalServiceError
)
from api.v1.common.schemas import AppErrorCode
from core.config.settings import settings

# Initialize Stripe
stripe.api_key = settings.STRIPE_API_KEY

def send_admin_notification_email(notification_type: str, message: str):
    """Send notification email to admin for critical failures."""
    # This would integrate with your existing email service
    logger.critical(f"ADMIN NOTIFICATION [{notification_type}]: {message}")
    # TODO: Implement actual email sending via your mailer service

def activate_single_pause(
    db: SyncSession,
    active_subscription_id: int,
    subscription_pause_public_id: str
) -> bool:
    """
    Activate a single scheduled pause by calling <PERSON><PERSON> and updating database status.
    This function is called by QStash when it's time to activate a scheduled pause.
    
    Args:
        db: Database session
        active_subscription_id: ID of the subscription to pause
        subscription_pause_public_id: Public ID of the pause record
        
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(
        f"Activating scheduled pause for subscription_id={active_subscription_id}, "
        f"pause_public_id={subscription_pause_public_id}"
    )
    
    try:
        # Get the active subscription
        active_sub_stmt = select(ActiveSubscription).where(
            ActiveSubscription.id == active_subscription_id
        )
        active_subscription = db.execute(active_sub_stmt).scalar_one_or_none()
        
        if not active_subscription:
            logger.error(f"Active subscription not found: {active_subscription_id}")
            raise NotFoundError(
                message="Active subscription not found.",
                error_code=AppErrorCode.SUBSCRIPTION_NOT_FOUND,
                entity_name="ActiveSubscription",
                identifier=str(active_subscription_id)
            )
        
        # Get the pause record
        pause_stmt = select(SubscriptionPause).where(
            SubscriptionPause.public_id == subscription_pause_public_id,
            SubscriptionPause.status == SubscriptionPauseStatus.SCHEDULED
        )
        subscription_pause = db.execute(pause_stmt).scalar_one_or_none()
        
        if not subscription_pause:
            logger.error(f"Scheduled pause not found: {subscription_pause_public_id}")
            raise NotFoundError(
                message="Scheduled pause not found.",
                error_code=AppErrorCode.SUBSCRIPTION_PAUSE_NOT_FOUND,
                entity_name="SubscriptionPause",
                identifier=subscription_pause_public_id
            )
        
        # Validate that the pause belongs to this subscription
        if subscription_pause.active_subscription_id != active_subscription_id:
            logger.error(
                f"Pause {subscription_pause_public_id} does not belong to subscription {active_subscription_id}"
            )
            raise ServiceError(
                message="Pause record mismatch.",
                error_code=AppErrorCode.SERVICE_ERROR
            )
        
        # Generate idempotency key for Stripe operation
        current_time = datetime.now(UTC)
        idempotency_key = f"activate_pause_{active_subscription.stripe_subscription_id}_{current_time.timestamp()}"
        
        # Set pause_collection on Stripe subscription
        stripe_params = {
            "pause_collection": {
                "behavior": "void",
                "resumes_at": int(subscription_pause.end_date.timestamp())
            }
        }
        
        stripe.Subscription.modify(
            active_subscription.stripe_subscription_id,
            **stripe_params,
            idempotency_key=idempotency_key
        )
        
        logger.info(
            f"Successfully set pause_collection on Stripe subscription "
            f"{active_subscription.stripe_subscription_id}"
        )
        
        # Update database records
        active_subscription.status = SubscriptionStatusType.PAUSED
        subscription_pause.status = SubscriptionPauseStatus.PAUSED
        
        db.add(active_subscription)
        db.add(subscription_pause)
        db.commit()
        
        logger.info(
            f"Successfully activated pause for subscription {active_subscription.public_id}. "
            f"Pause record {subscription_pause_public_id} updated to PAUSED status."
        )
        
        return True
        
    except stripe.error.StripeError as e:
        db.rollback()
        logger.error(
            f"Stripe API error activating pause for subscription {active_subscription_id}: {str(e)}",
            exc_info=True
        )
        raise ExternalServiceError(
            message=f"Payment gateway error: {e.user_message if hasattr(e, 'user_message') and e.user_message else str(e)}",
            error_code=AppErrorCode.STRIPE_ERROR,
            original_exception=e
        )
    except Exception as e:
        db.rollback()
        logger.error(
            f"Unexpected error activating pause for subscription {active_subscription_id}: {str(e)}",
            exc_info=True
        )
        raise ServiceError(
            message="An unexpected error occurred while activating the pause.",
            error_code=AppErrorCode.SERVICE_ERROR
        )

def cleanup_failed_scheduled_pauses(db: SyncSession) -> dict:
    """
    Find scheduled pauses that should have been activated but weren't.
    This handles QStash failures or missed webhooks.
    
    Returns:
        dict: Summary of cleanup operations performed
    """
    current_time = datetime.now(UTC)
    
    # Find scheduled pauses that should be active
    overdue_pauses_stmt = (
        select(SubscriptionPause)
        .join(ActiveSubscription)
        .where(
            SubscriptionPause.status == SubscriptionPauseStatus.SCHEDULED,
            SubscriptionPause.start_date < current_time,
            ActiveSubscription.status == SubscriptionStatusType.ACTIVE
        )
    )
    overdue_pauses = db.execute(overdue_pauses_stmt).scalars().all()
    
    cleanup_summary = {
        "found_overdue": len(overdue_pauses),
        "successfully_activated": 0,
        "failed_activations": 0,
        "errors": []
    }
    
    for pause in overdue_pauses:
        logger.warning(
            f"Found overdue scheduled pause {pause.public_id} that should have started at {pause.start_date}"
        )
        
        try:
            # Attempt to activate it now
            success = activate_single_pause(
                db=db,
                active_subscription_id=pause.active_subscription_id,
                subscription_pause_public_id=pause.public_id
            )
            
            if success:
                logger.info(f"Successfully activated overdue pause {pause.public_id}")
                cleanup_summary["successfully_activated"] += 1
            else:
                logger.error(f"Failed to activate overdue pause {pause.public_id}")
                cleanup_summary["failed_activations"] += 1
                cleanup_summary["errors"].append(f"Failed to activate pause {pause.public_id}")
                
                send_admin_notification_email(
                    "pause_activation_failed",
                    f"Failed to activate overdue pause {pause.public_id}. Manual intervention required."
                )
                
        except Exception as e:
            logger.error(f"Error activating overdue pause {pause.public_id}: {e}")
            cleanup_summary["failed_activations"] += 1
            cleanup_summary["errors"].append(f"Error activating pause {pause.public_id}: {str(e)}")
    
    # Also check for pauses that should have auto-resumed (if you have auto-resume logic)
    overdue_paused_stmt = (
        select(func.count(SubscriptionPause.id))
        .where(
            SubscriptionPause.status == SubscriptionPauseStatus.PAUSED,
            SubscriptionPause.end_date < current_time
        )
    )
    overdue_paused_count = db.execute(overdue_paused_stmt).scalar()
    
    if overdue_paused_count > 0:
        logger.warning(f"Found {overdue_paused_count} pauses that should have auto-resumed")
        cleanup_summary["overdue_paused_count"] = overdue_paused_count
        
        send_admin_notification_email(
            "auto_resume_needed",
            f"Found {overdue_paused_count} subscriptions that should have auto-resumed. "
            f"Check resume functionality or implement manual resume."
        )
    
    logger.info(f"Pause cleanup completed: {cleanup_summary}")
    return cleanup_summary