from sqlalchemy.orm import Session as SyncSession
from sqlalchemy import select
from loguru import logger
from db.models import DiscountCode, Account, DiscountApplicabilityType
from ..schemas.request import VerifyDiscountCodeRequest
from ..schemas.response import VerifyDiscountCodeResponse
from core.exception_handling.exceptions.custom_exceptions import ValidationError, NotFoundError
from api.v1.common.schemas import AppErrorCode
from datetime import datetime, UTC

def verify_discount_code_service(
    db: SyncSession,
    parent_public_id: str,
    discount_request_data: VerifyDiscountCodeRequest
) -> VerifyDiscountCodeResponse:
    logger.info(
        f"Verifying discount code '{discount_request_data.discount_code} for parent {parent_public_id}, "
        f"type context: {discount_request_data.subscription_type}"
    )
    
    stmt_account = select(Account).where(Account.public_id == parent_public_id)
    relevant_account = db.execute(stmt_account).scalar_one_or_none()

    if not relevant_account:
        logger.warning(f"Discount code verification attempt by non-existent parent: {parent_public_id}")
        raise NotFoundError(
            message="Account not found.",
            log_message=f"Account not found for discount code verification: {parent_public_id}",
            error_code=AppErrorCode.PARENT_ACCOUNT_NOT_FOUND
        )

    normalized_code = discount_request_data.discount_code.strip().upper()
    stmt_discount = select(DiscountCode).where(
        DiscountCode.is_active,
        DiscountCode.public_code == normalized_code
    )
    relevant_discount = db.execute(stmt_discount).scalar_one_or_none()

    if not relevant_discount:
        logger.info(f"Invalid or inactive discount code '{normalized_code}' for parent {parent_public_id}")
        raise ValidationError(
            message="The discount code entered is invalid or has expired.",
            log_message=f"Invalid or inactive discount code: {normalized_code}",
            error_code=AppErrorCode.INVALID_DISCOUNT_CODE
        )

    # Account age-based restrictions
    if relevant_discount.min_account_age_days is not None or relevant_discount.max_account_age_days is not None:
        if not relevant_account.created_at:
            logger.error(
                f"Account {parent_public_id} missing creation date "
                f"for account age-based discount check for code {normalized_code}."
            )
            raise ValidationError(
                "Cannot verify discount eligibility due to missing account information.", 
                error_code=AppErrorCode.SERVICE_ERROR
            )

        account_creation_dt_utc = (
            relevant_account.created_at.replace(tzinfo=UTC) 
            if relevant_account.created_at.tzinfo is None 
            else relevant_account.created_at
        )
        account_age_days = (datetime.now(UTC) - account_creation_dt_utc).days

        if relevant_discount.min_account_age_days \
                is not None and account_age_days < relevant_discount.min_account_age_days:
            logger.info(
                f"Account {parent_public_id} "
                f"(age: {account_age_days} days) too new for discount {normalized_code} "
                f"(min age: {relevant_discount.min_account_age_days} days)."
            )
            raise ValidationError(
                message="This discount code is not yet available for your account.",
                log_message=(
                    f"Account age {account_age_days} days is less than min_account_age_days "
                    f"{relevant_discount.min_account_age_days} for code {normalized_code}."
                ),
                error_code=AppErrorCode.INVALID_DISCOUNT_CODE
            )
        
        if relevant_discount.max_account_age_days \
                is not None and account_age_days > relevant_discount.max_account_age_days:
            logger.info(
                f"Account {parent_public_id} "
                f"(age: {account_age_days} days) too old for discount {normalized_code}"
                f"(max age: {relevant_discount.max_account_age_days} days)."
            )
            raise ValidationError(
                message="This discount code is no longer available for your account.",
                log_message=(
                    f"Account age {account_age_days} days is greater than max_account_age_days "
                    f"{relevant_discount.max_account_age_days} for code {normalized_code}."
                ),
                error_code=AppErrorCode.INVALID_DISCOUNT_CODE
            )

    if discount_request_data.subscription_type:
        requested_type_str = discount_request_data.subscription_type.lower()
        if requested_type_str not in ["monthly", "yearly"]:
            logger.warning(
                f"Invalid subscription_type '{requested_type_str}' "
                f"in discount verification for parent {parent_public_id}"
            )
            raise ValidationError(
                "Invalid subscription type specified for discount check.",
                error_code=AppErrorCode.INVALID_REQUEST
            )

        is_applicable_for_type = False
        if requested_type_str == "monthly" and (
            relevant_discount.applicable_to == DiscountApplicabilityType.MONTHLY 
            or relevant_discount.applicable_to == DiscountApplicabilityType.BOTH
        ):
            is_applicable_for_type = True
        elif requested_type_str == "yearly" and (
            relevant_discount.applicable_to == DiscountApplicabilityType.YEARLY 
            or relevant_discount.applicable_to == DiscountApplicabilityType.BOTH
        ):
            is_applicable_for_type = True
        
        if not is_applicable_for_type:
            log_msg = (
                f"Discount code {normalized_code} (applies to: {relevant_discount.applicable_to.value}) "
                f"not valid for requested '{requested_type_str}' subscription context."
            )
            logger.info(log_msg + f" Parent: {parent_public_id}")
            raise ValidationError(
                message="This discount code is not valid for the selected subscription type.",
                log_message=log_msg,
                error_code=AppErrorCode.INVALID_DISCOUNT_FOR_TYPE
            )
    
    # Check if appropriate Stripe IDs exist
    has_monthly_id = bool(relevant_discount.stripe_monthly_id)
    has_yearly_id = bool(relevant_discount.stripe_yearly_id)
    
    if not has_monthly_id and not has_yearly_id:
        logger.warning(
            f"Discount code {normalized_code} is DB-valid but has no Stripe IDs. "
            f"It might be for manual application or internal use."
        )
    
    logger.info(
        f"Discount code {normalized_code} (Monthly ID: {relevant_discount.stripe_monthly_id or 'N/A'}, "
        f"Yearly ID: {relevant_discount.stripe_yearly_id or 'N/A'}) "
        f"verified successfully for parent {parent_public_id}."
    )
    
    return VerifyDiscountCodeResponse(
        message="Discount code is valid.",
        stripe_monthly_id=relevant_discount.stripe_monthly_id,
        stripe_yearly_id=relevant_discount.stripe_yearly_id,
        public_code=relevant_discount.public_code,
        is_valid=True,
        applicable_to=relevant_discount.applicable_to.value
    )