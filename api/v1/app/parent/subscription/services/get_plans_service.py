from sqlalchemy.orm import Session as SyncSession
from sqlalchemy.orm import selectinload
from db.models import Account # Assuming Account is in db.models
from db.models.content import Subject # Assuming Subject is in db.models.content
from db.models.subscription import SubscriptionOption, BillingPeriodEnum, PlanTypeEnum, PriceVersion
# Import your new/modified response schemas
from ..schemas.response import (
    YearPlanOfferingSchema, # New
    OfferingPriceDetailSchema,
    PricingTierSchema,
    SubscriptionPlanListResponse, # Modified
    SubjectBasicInfoSchema
)
from typing import List
from .utils import _get_or_create_eligible_price_version # Assuming this is in .utils
from core.exception_handling.exceptions.custom_exceptions import NotFoundError, ServiceError
from api.v1.common.schemas import AppErrorCode # Assuming this is your error code enum
from sqlalchemy import select
from loguru import logger
import json

def get_subscription_plans_service(db: SyncSession, parent_public_id: str) -> SubscriptionPlanListResponse:
    logger.info(f"Fetching subscription plans for parent_public_id: {parent_public_id}")

    account_stmt = select(Account).where(Account.public_id == parent_public_id)
    account = db.execute(account_stmt).scalar_one_or_none()
    if not account:
        logger.warning(f"Parent account not found: {parent_public_id}")
        raise NotFoundError(
            message="Parent account not found.",
            error_code=AppErrorCode.PARENT_ACCOUNT_NOT_FOUND
        )

    price_version = None
    try:
        price_version = _get_or_create_eligible_price_version(db, account)
        if price_version:
            logger.debug(f"Eligible PriceVersion found: ID={price_version.id}, Name='{price_version.name}', Slug='{price_version.slug}', Active={price_version.is_active}, Current={price_version.is_current}")
        else:
            logger.error("No eligible PriceVersion returned by _get_or_create_eligible_price_version!")
            # Return empty but with price version info if possible, or handle as error
            return SubscriptionPlanListResponse(
                eligible_price_version_slug="unknown",
                eligible_price_version_display_name="Pricing not available",
                year_offerings=[]
            )
    except ServiceError as e:
        logger.error(f"Service error determining price version for account {account.id}: {e.log_message if hasattr(e, 'log_message') else str(e)}")
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error determining price version for account {account.id}: {str(e)}",
            exc_info=True
        )
        raise ServiceError(
            message="Could not determine pricing information.",
            error_code=AppErrorCode.SERVICE_ERROR
        )

    if not price_version or not price_version.is_active:
        logger.warning(f"PriceVersion (ID: {price_version.id if price_version else 'None'}, Name: {price_version.name if price_version else 'N/A'}) is not active or not found. No plans will be available.")
        return SubscriptionPlanListResponse(
            eligible_price_version_slug=price_version.slug if price_version else "inactive",
            eligible_price_version_display_name=price_version.display_name if price_version else "Pricing Inactive",
            year_offerings=[]
        )

    options_stmt = (
        select(SubscriptionOption)
        .options(
            selectinload(SubscriptionOption.year),
            # price_version is already loaded if we query by price_version.id
        )
        .where(
            SubscriptionOption.price_version_id == price_version.id,
            SubscriptionOption.is_active == True
        )
        .order_by(SubscriptionOption.year_id) # Optional: order by year for consistent output
    )
    eligible_subscription_options = db.execute(options_stmt).scalars().unique().all()

    logger.debug(f"Found {len(eligible_subscription_options)} active SubscriptionOptions for PriceVersion ID {price_version.id} ('{price_version.name}').")
    if not eligible_subscription_options:
         logger.warning(f"No *active* SubscriptionOptions found for PriceVersion '{price_version.name}' (ID: {price_version.id}).")

    year_offerings_list: List[YearPlanOfferingSchema] = []

    for option in eligible_subscription_options:
        logger.debug(f"Processing Option - ID: {option.public_id}, Year: {option.year.name if option.year else 'N/A'}, Active: {option.is_active}")
        logger.debug(f"  Option display_price_config_json: {json.dumps(option.display_price_config_json, indent=2) if option.display_price_config_json else 'No JSON Config'}")

        if not option.year:
            logger.warning(f"SubscriptionOption {option.public_id} missing linked year. Skipping.")
            continue
        
        # Subjects for this year (from SubscriptionOption.year)
        available_subjects_for_year = _get_subjects_for_year(db, option.year.id)
        
        current_option_pricing_list: List[OfferingPriceDetailSchema] = []

        # Populate SC prices
        if option.sc_monthly_stripe_price_id:
            logger.debug(f"  Attempting to extract SC Monthly pricing for Option {option.public_id}")
            sc_monthly_pricing = _extract_display_pricing(option.display_price_config_json, PlanTypeEnum.SC, BillingPeriodEnum.MONTHLY)
            logger.debug(f"  _extract_display_pricing for SC Monthly returned: {sc_monthly_pricing}")
            if sc_monthly_pricing and sc_monthly_pricing.get('currency'):
                current_option_pricing_list.append(OfferingPriceDetailSchema(
                    plan_type=PlanTypeEnum.SC,
                    billing_period=BillingPeriodEnum.MONTHLY,
                    stripe_price_id=option.sc_monthly_stripe_price_id,
                    display_price=None,  # SC plans don't have flat pricing
                    currency=sc_monthly_pricing.get('currency'),
                    tiers=sc_monthly_pricing.get('tiers', [])
                ))
            else:
                logger.warning(f"  SC Monthly pricing extraction failed for Option {option.public_id}.")

        if option.sc_yearly_stripe_price_id:
            logger.debug(f"  Attempting to extract SC Yearly pricing for Option {option.public_id}")
            sc_yearly_pricing = _extract_display_pricing(option.display_price_config_json, PlanTypeEnum.SC, BillingPeriodEnum.YEARLY)
            logger.debug(f"  _extract_display_pricing for SC Yearly returned: {sc_yearly_pricing}")
            if sc_yearly_pricing and sc_yearly_pricing.get('currency'):
                current_option_pricing_list.append(OfferingPriceDetailSchema(
                    plan_type=PlanTypeEnum.SC,
                    billing_period=BillingPeriodEnum.YEARLY,
                    stripe_price_id=option.sc_yearly_stripe_price_id,
                    display_price=None,  # SC plans don't have flat pricing
                    currency=sc_yearly_pricing.get('currency'),
                    tiers=sc_yearly_pricing.get('tiers', [])
                ))
            else:
                logger.warning(f"  SC Yearly pricing extraction failed for Option {option.public_id}.")

        # Populate YF prices (will be empty if YF Stripe IDs are null)
        if option.yf_monthly_stripe_price_id:
            logger.debug(f"  Attempting to extract YF Monthly pricing for Option {option.public_id}")
            yf_monthly_pricing = _extract_display_pricing(option.display_price_config_json, PlanTypeEnum.YF, BillingPeriodEnum.MONTHLY)
            logger.debug(f"  _extract_display_pricing for YF Monthly returned: {yf_monthly_pricing}")
            if yf_monthly_pricing and yf_monthly_pricing.get('display_price') is not None:
                current_option_pricing_list.append(OfferingPriceDetailSchema(
                    plan_type=PlanTypeEnum.YF,
                    billing_period=BillingPeriodEnum.MONTHLY,
                    stripe_price_id=option.yf_monthly_stripe_price_id,
                    display_price=yf_monthly_pricing.get('display_price'),
                    currency=yf_monthly_pricing.get('currency')
                ))
            else:
                logger.warning(f"  YF Monthly pricing extraction failed or returned no display_price for Option {option.public_id}.")

        if option.yf_yearly_stripe_price_id:
            logger.debug(f"  Attempting to extract YF Yearly pricing for Option {option.public_id}")
            yf_yearly_pricing = _extract_display_pricing(option.display_price_config_json, PlanTypeEnum.YF, BillingPeriodEnum.YEARLY)
            logger.debug(f"  _extract_display_pricing for YF Yearly returned: {yf_yearly_pricing}")
            if yf_yearly_pricing and yf_yearly_pricing.get('display_price') is not None:
                current_option_pricing_list.append(OfferingPriceDetailSchema(
                    plan_type=PlanTypeEnum.YF,
                    billing_period=BillingPeriodEnum.YEARLY,
                    stripe_price_id=option.yf_yearly_stripe_price_id,
                    display_price=yf_yearly_pricing.get('display_price'),
                    currency=yf_yearly_pricing.get('currency')
                ))
            else:
                logger.warning(f"  YF Yearly pricing extraction failed or returned no display_price for Option {option.public_id}.")


        if not current_option_pricing_list:
            logger.warning(
                f"SubscriptionOption {option.public_id} for year {option.year.name}"
                + " results in no valid offering prices after extraction. Skipping this year's offering."
            )
            continue

        year_offering = YearPlanOfferingSchema(
            year_public_id=option.year.public_id,
            year_name=option.year.name,
            subscription_option_public_id=option.public_id, # Key for checkout
            available_subjects=available_subjects_for_year,
            pricing_options=current_option_pricing_list
        )
        year_offerings_list.append(year_offering)

    # Sort year_offerings_list by year ID (lowest IDs first)
    year_offerings_list.sort(key=lambda offering: next(
        (opt.year.id for opt in eligible_subscription_options 
         if opt.year and opt.year.public_id == offering.year_public_id),
        float('inf')  # fallback for any edge cases
    ))
    
    logger.info(f"Successfully constructed {len(year_offerings_list)} year offerings "
                f"for parent_public_id: {parent_public_id} under PriceVersion '{price_version.name}'.")
    
    return SubscriptionPlanListResponse(
        eligible_price_version_slug=price_version.slug,
        eligible_price_version_display_name=price_version.display_name,
        year_offerings=year_offerings_list
    )

def _get_subjects_for_year(db: SyncSession, year_id: int) -> List[SubjectBasicInfoSchema]:
    """Get all active subjects for a given year."""
    try:
        subjects_stmt = (
            select(Subject)
            # .options(selectinload(Subject.year)) # Not strictly needed if not accessing subject.year properties here
            .where(
                Subject.year_id == year_id,
                Subject.is_active == True
            )
            .order_by(Subject.name)
        )
        subjects = db.execute(subjects_stmt).scalars().all()
        
        return [
            SubjectBasicInfoSchema(
                public_id=subject.public_id,
                name=subject.name,
                description=subject.description
                # year_name and year_public_id removed from SubjectBasicInfoSchema
            )
            for subject in subjects
        ]
    except Exception as e:
        logger.error(f"Error fetching subjects for year {year_id}: {e}", exc_info=True)
        return []

def _extract_display_pricing(display_config_json: dict, plan_type: PlanTypeEnum, billing_period: BillingPeriodEnum) -> dict:
    """Extract display pricing from subscription option config."""
    logger.debug(f"Attempting to extract pricing. Plan: {plan_type}, Period: {billing_period}. Input JSON: {json.dumps(display_config_json, indent=2) if display_config_json else 'No JSON'}")
    try:
        if not display_config_json:
            logger.debug("display_config_json is empty or None.")
            return {}
        
        plan_key = plan_type.value.upper()
        period_key = billing_period.value
        
        plan_config = display_config_json.get(plan_key, {})
        if not plan_config:
            logger.debug(f"No config found for plan_key '{plan_key}'.")
            return {}
            
        period_config = plan_config.get(period_key, {})
        if not period_config:
            logger.debug(f"No config found for period_key '{period_key}' within plan_key '{plan_key}'.")
            return {}
        
        currency = period_config.get('currency', 'USD')
        
        if plan_type == PlanTypeEnum.SC:
            tiers_info = period_config.get('tiers_info', []) 
            if not tiers_info:
                logger.debug(f"No 'tiers_info' found in period_config: {period_config}")
                return {'currency': currency}

            # Convert tiers_info to PricingTierSchema format
            pricing_tiers = []
            for tier in tiers_info:
                try:
                    unit_price_str = tier.get('unit_price', "0.00")
                    unit_price_cents = int(float(unit_price_str))
                    unit_price_display = unit_price_cents / 100.0
                    
                    pricing_tier = PricingTierSchema(
                        up_to=str(tier.get('up_to', 'inf')),
                        unit_price_cents=unit_price_cents,
                        unit_price_display=unit_price_display
                    )
                    pricing_tiers.append(pricing_tier)
                    
                    logger.debug(f"Added pricing tier: up_to={pricing_tier.up_to}, price={pricing_tier.unit_price_display} {currency}")
                
                except (ValueError, TypeError) as e:
                    logger.error(f"Could not parse tier info {tier}: {e}")
                    continue
            
            return {
                'currency': currency,
                'tiers': pricing_tiers
            }
        
        elif plan_type == PlanTypeEnum.YF:
            # For YF plans, look for a flat price
            price_str = period_config.get('flat_price', "0.00")

            try:
                price_cents_float = float(price_str)
                display_price = price_cents_float / 100.0
                logger.debug(f"YF Pricing: DisplayPrice={display_price}, Currency={currency}")
                return {
                    'display_price': display_price,
                    'currency': currency
                }
            except ValueError:
                logger.error(f"Could not convert YF price '{price_str}' to float.")
                return {'currency': currency}
        
        logger.warning(f"Fell through pricing logic for plan_type {plan_type}. Returning currency only.")
        return {'currency': currency} 
        
    except Exception as e:
        logger.error(f"Error extracting display pricing: {e}", exc_info=True)
        return {}