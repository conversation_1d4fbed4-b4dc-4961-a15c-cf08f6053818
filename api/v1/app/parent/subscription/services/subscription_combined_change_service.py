"""
Service for handling combined subscription changes (quantity + billing period).
This handles edge case 4: atomic operations for multiple change types.
"""
from sqlalchemy.orm import Session as SyncSession
from sqlalchemy import select
from db.models import (
    ActiveSubscriptionPlanLink, SubscriptionOption,
    SubscriptionPendingChange, SubscriptionChangeType, 
    SubscriptionChangeStatus, PlanTypeEnum, BillingPeriodEnum
)
from .subscription_change_service import (
    handle_concurrent_change_request,
    _calculate_stripe_price_for_quantity,
    cancel_pending_change
)
from core.exception_handling.exceptions.custom_exceptions import ServiceError, ValidationError
from api.v1.common.schemas import AppErrorCode
from loguru import logger
from datetime import datetime, UTC
from typing import Optional, List
import uuid
import stripe
from core.config.settings import settings

# Initialize Stripe
stripe.api_key = settings.STRIPE_API_KEY


def create_combined_subscription_change(
    db: SyncSession,
    active_plan_link: ActiveSubscriptionPlanLink,
    new_quantity: Optional[int] = None,
    new_billing_period: Optional[BillingPeriodEnum] = None,
    new_subject_ids: Optional[List[str]] = None
) -> SubscriptionPendingChange:
    """
    Create a combined subscription change (quantity and/or billing period).
    Handles edge case 4: atomic operations for multiple changes.
    
    Args:
        db: Database session
        active_plan_link: The subscription plan link being modified
        new_quantity: New quantity (if changing)
        new_billing_period: New billing period (if changing)
        new_subject_ids: New subject selections (for SC plans with quantity change)
    
    Returns:
        The created SubscriptionPendingChange record
    """
    # Validate inputs
    if new_quantity is None and new_billing_period is None:
        raise ValidationError(
            "At least one change (quantity or billing period) must be specified.",
            error_code=AppErrorCode.INVALID_REQUEST_DATA
        )
    
    # Use current values for unchanged fields
    final_quantity = new_quantity if new_quantity is not None else active_plan_link.quantity
    final_billing_period = new_billing_period if new_billing_period is not None else active_plan_link.chosen_billing_period
    
    # Check for concurrent changes
    if not handle_concurrent_change_request(db, active_plan_link):
        raise ValidationError(
            "Another change is currently being processed for this subscription. Please try again in a moment.",
            error_code=AppErrorCode.CONCURRENT_MODIFICATION
        )
    
    # Determine change type
    quantity_changed = new_quantity is not None and new_quantity != active_plan_link.quantity
    billing_changed = new_billing_period is not None and new_billing_period != active_plan_link.chosen_billing_period
    
    if quantity_changed and billing_changed:
        # Combined change - treat as immediate if upgrading quantity OR changing to yearly
        is_upgrade = (new_quantity > active_plan_link.quantity or 
                     (new_billing_period == BillingPeriodEnum.YEARLY and 
                      active_plan_link.chosen_billing_period == BillingPeriodEnum.MONTHLY))
        effective_immediately = is_upgrade
        change_type = SubscriptionChangeType.QUANTITY_CHANGE  # Primary change type
        logger.info(f"Combined change: quantity {active_plan_link.quantity}->{new_quantity}, "
                   f"billing {active_plan_link.chosen_billing_period.value}->{new_billing_period.value}")
    elif quantity_changed:
        effective_immediately = new_quantity > active_plan_link.quantity
        change_type = SubscriptionChangeType.QUANTITY_CHANGE
    else:  # billing_changed
        # Billing period changes: monthly->yearly is immediate, yearly->monthly at period end
        effective_immediately = (new_billing_period == BillingPeriodEnum.YEARLY and 
                               active_plan_link.chosen_billing_period == BillingPeriodEnum.MONTHLY)
        change_type = SubscriptionChangeType.BILLING_PERIOD_CHANGE
    
    # Cancel any existing pending changes
    existing_stmt = select(SubscriptionPendingChange).where(
        SubscriptionPendingChange.plan_link_id == active_plan_link.id,
        SubscriptionPendingChange.status.in_([
            SubscriptionChangeStatus.PENDING,
            SubscriptionChangeStatus.SCHEDULED
        ])
    )
    existing_pending = db.execute(existing_stmt).scalar_one_or_none()
    
    if existing_pending:
        logger.info(f"Cancelling existing pending change {existing_pending.public_id}")
        cancel_pending_change(db, existing_pending.public_id, "replaced_by_new_change")
    
    # Calculate new Stripe price
    new_stripe_price_id = _calculate_stripe_price_for_quantity(
        subscription_option=active_plan_link.subscription_option,
        plan_type=active_plan_link.chosen_plan_type,
        billing_period=final_billing_period,
        quantity=final_quantity
    )
    
    # Determine effective date
    if effective_immediately:
        effective_date = datetime.now(UTC)
    else:
        effective_date = active_plan_link.active_subscription.current_period_end
    
    # Create pending change
    pending_change = SubscriptionPendingChange(
        public_id=str(uuid.uuid4()),
        active_subscription_id=active_plan_link.active_subscription_id,
        plan_link_id=active_plan_link.id,
        change_type=change_type,
        current_quantity=active_plan_link.quantity,
        current_stripe_price_id=active_plan_link.chosen_stripe_price_id,
        current_plan_type=active_plan_link.chosen_plan_type,
        current_billing_period=active_plan_link.chosen_billing_period,
        new_quantity=final_quantity,
        new_stripe_price_id=new_stripe_price_id,
        new_plan_type=active_plan_link.chosen_plan_type,
        new_billing_period=final_billing_period,
        effective_date=effective_date,
        status=SubscriptionChangeStatus.PENDING,
        metadata_json={
            "combined_change": quantity_changed and billing_changed,
            "quantity_changed": quantity_changed,
            "billing_changed": billing_changed,
            "immediate": effective_immediately
        }
    )
    db.add(pending_change)
    db.flush()
    
    # Update Stripe - single API call for atomic operation
    try:
        stripe_response = None
        if effective_immediately:
            # Immediate change with proration
            updated_sub = stripe.Subscription.modify(
                active_plan_link.active_subscription.stripe_subscription_id,
                items=[{
                    'id': active_plan_link.stripe_subscription_item_id,
                    'price': new_stripe_price_id,
                    'quantity': final_quantity
                }],
                proration_behavior='create_prorations'
            )
            
            pending_change.status = SubscriptionChangeStatus.COMPLETED
            pending_change.processed_at = datetime.now(UTC)
            
            # Update the plan link immediately
            active_plan_link.quantity = final_quantity
            active_plan_link.chosen_stripe_price_id = new_stripe_price_id
            active_plan_link.chosen_billing_period = final_billing_period
            
            stripe_response = {
                "subscription_id": updated_sub.id,
                "immediate": True
            }
            
        else:
            # Schedule for period end
            subscription = stripe.Subscription.retrieve(
                active_plan_link.active_subscription.stripe_subscription_id
            )
            
            if subscription.schedule:
                # Update existing schedule
                schedule = stripe.SubscriptionSchedule.modify(
                    subscription.schedule,
                    phases=[{
                        'items': [{
                            'price': new_stripe_price_id,
                            'quantity': final_quantity
                        }],
                        'start_date': int(effective_date.timestamp())
                    }]
                )
            else:
                # Create new schedule
                schedule = stripe.SubscriptionSchedule.create(
                    from_subscription=active_plan_link.active_subscription.stripe_subscription_id,
                    phases=[{
                        'items': [{
                            'price': new_stripe_price_id,
                            'quantity': final_quantity
                        }],
                        'start_date': int(effective_date.timestamp())
                    }]
                )
            
            pending_change.status = SubscriptionChangeStatus.SCHEDULED
            pending_change.stripe_subscription_schedule_id = schedule.id
            
            stripe_response = {
                "schedule_id": schedule.id,
                "effective_date": effective_date.isoformat()
            }
        
        pending_change.metadata_json = {
            **pending_change.metadata_json,
            "stripe_response": stripe_response
        }
        
    except stripe.error.CardError as e:
        # Payment failed for immediate change
        logger.error(f"Payment failed for combined change: {str(e)}")
        pending_change.status = SubscriptionChangeStatus.FAILED
        pending_change.metadata_json = {
            **pending_change.metadata_json,
            "error": str(e),
            "error_type": "CardError"
        }
        db.flush()
        raise ServiceError(
            f"Payment failed: {str(e)}. Please update your payment method and try again.",
            error_code=AppErrorCode.PAYMENT_FAILED
        )
        
    except stripe.error.StripeError as e:
        logger.error(f"Stripe error in combined change: {str(e)}")
        pending_change.status = SubscriptionChangeStatus.FAILED
        pending_change.metadata_json = {
            **pending_change.metadata_json,
            "error": str(e),
            "error_type": type(e).__name__
        }
        db.flush()
        raise ServiceError(
            f"Failed to update subscription: {str(e)}",
            error_code=AppErrorCode.STRIPE_ERROR
        )
    
    db.flush()
    logger.info(f"Successfully created combined change {pending_change.public_id}")
    return pending_change