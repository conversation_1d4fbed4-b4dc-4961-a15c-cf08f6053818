import stripe
from sqlalchemy.orm import Session as SyncSession  # Changed to SyncSession
from sqlalchemy import select
from loguru import logger

from db.models import Account
from ..schemas.request import CreatePortalSessionRequest
from ..schemas.response import CreatePortalSessionResponse
from core.exception_handling.exceptions.custom_exceptions import ServiceError, ExternalServiceError, NotFoundError
from api.v1.common.schemas import AppErrorCode
from core.config.settings import settings

# Initialize Stripe with settings
stripe.api_key = settings.STRIPE_API_KEY
FRONTEND_BASE_URL = settings.FRONTEND_URL

def create_portal_session_service(
    db: SyncSession,  # Changed to SyncSession
    parent_public_id: str,
    portal_session_data: CreatePortalSessionRequest
) -> CreatePortalSessionResponse:
    logger.info(
        f"Creating Stripe portal session for parent: {parent_public_id}, language: {portal_session_data.language}")

    stmt_account = select(Account).where(Account.public_id == parent_public_id)
    relevant_account = db.execute(stmt_account).scalar_one_or_none()

    if not relevant_account:
        logger.warning(f"Account not found for portal session: {parent_public_id}")
        raise NotFoundError(
            message="Account not found.",
            log_message=f"Account not found with public_id {parent_public_id} for Stripe portal session.",
            error_code=AppErrorCode.PARENT_ACCOUNT_NOT_FOUND
        )

    if not relevant_account.stripe_customer_id:
        logger.error(
            f"Stripe customer ID not found for account {relevant_account.public_id}. Cannot create portal session.")
        raise ServiceError(
            message="Billing information not configured for this account. Cannot manage subscription.",
            log_message=f"Account {relevant_account.id} missing stripe_customer_id for portal session.",
            error_code=AppErrorCode.STRIPE_CUSTOMER_ID_NOT_FOUND
        )

    return_path = f"/{portal_session_data.language}/parent/account-settings"
    full_return_url = f"{FRONTEND_BASE_URL.rstrip('/')}{return_path}"


    try:
        portal_session_params = {
            'customer': relevant_account.stripe_customer_id,
            'return_url': full_return_url,
            # 'locale': portal_session_data.language,
        }

        portalSession = stripe.billing_portal.Session.create(**portal_session_params)
        logger.info(f"Stripe portal session {portalSession.id} created successfully for parent {parent_public_id}")
        return CreatePortalSessionResponse(portal_session_url=portalSession.url)
    except stripe.error.StripeError as e:
        logger.error(f"Stripe API error creating portal session for parent {parent_public_id}: {str(e)}", exc_info=True)
        user_message = f"Payment gateway error: {e.user_message}" if hasattr(
            e, 'user_message') and e.user_message else "Could not open subscription management page."
        raise ExternalServiceError(
            message=user_message,
            error_code=AppErrorCode.STRIPE_ERROR,
            original_exception=e
        )
    except Exception as e:
        logger.error(f"Unexpected error creating portal session for parent {parent_public_id}: {str(e)}", exc_info=True)
        raise ServiceError(
            message="An unexpected error occurred while creating the portal session.",
            error_code=AppErrorCode.SERVICE_ERROR
        )