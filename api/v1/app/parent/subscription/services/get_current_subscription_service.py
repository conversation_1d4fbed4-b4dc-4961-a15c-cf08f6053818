from sqlalchemy.orm import Session as SyncSession, selectinload
from sqlalchemy import select
from db.models import (
    Account, ActiveSubscription, ActiveSubscriptionPlanLink, PlanSelectedSubject, 
    SubscriptionPause, PriceVersion, PriceEligibility
)
from db.models.content import Subject
from db.models.subscription import (
    SubscriptionStatusType, SubscriptionPauseStatus, BillingPeriodEnum, 
    PlanTypeEnum, SubscriptionOption
)
from ..schemas.response import (
    CurrentSubscriptionResponse, CurrentSubscriptionPlanItem, 
    PlanItemAvailableAction, SubjectBasicInfoSchema, PricingTierSchema,
    SubscriptionDetail, PriceComparisonInfo, PricingVersionInfo
)
from core.exception_handling.exceptions.custom_exceptions import NotFoundError
from api.v1.common.schemas import AppErrorCode
from loguru import logger
from datetime import datetime, UTC
from dateutil import parser
from core.config.settings import settings
from typing import List, Dict, Any, Optional
import json

def get_current_subscription_service(
    db: SyncSession,
    parent_public_id: str
) -> CurrentSubscriptionResponse:
    """
    Get comprehensive current subscription details with available management actions.
    Now returns ALL active subscriptions for a parent.
    """
    logger.info(f"Fetching all current subscriptions for parent: {parent_public_id}")
    
    # Get parent account
    account_stmt = select(Account).where(Account.public_id == parent_public_id)
    account = db.execute(account_stmt).scalar_one_or_none()
    if not account:
        raise NotFoundError(
            message="Parent account not found.",
            error_code=AppErrorCode.PARENT_ACCOUNT_NOT_FOUND,
            entity_name="Account",
            identifier=parent_public_id
        )
    
    # Get ALL active subscriptions with all related data
    active_subs_stmt = (
        select(ActiveSubscription)
        .where(
            ActiveSubscription.parent_account_id == account.id,
            ActiveSubscription.status.in_([
                SubscriptionStatusType.ACTIVE,
                SubscriptionStatusType.PAUSED,
                SubscriptionStatusType.TRIALING
            ])
        )
        .options(
            selectinload(ActiveSubscription.plan_links)
            .selectinload(ActiveSubscriptionPlanLink.subscription_option)
            .selectinload(SubscriptionOption.year),
            selectinload(ActiveSubscription.plan_links)
            .selectinload(ActiveSubscriptionPlanLink.selected_subjects)
            .selectinload(PlanSelectedSubject.subject)
        )
        .order_by(ActiveSubscription.created_at.desc())  # Most recent first
    )
    active_subscriptions = db.execute(active_subs_stmt).scalars().all()
    
    if not active_subscriptions:
        logger.info(f"No active subscriptions found for parent {parent_public_id}")
        return CurrentSubscriptionResponse(
            has_subscription=False,
            parent_account_public_id=parent_public_id,
            subscriptions=[],
            # Deprecated fields
            public_id=None,
            stripe_subscription_id=None,
            status=None,
            plan_items=[],
            can_pause=False,
            can_add_items=False,
            pause_info=None
        )
    
    # Get pricing version info for the account
    pricing_version_info = _get_pricing_version_info(db, account)
    
    # If user has grandfathered pricing, we need to fetch the latest subscription options
    latest_subscription_options_map = {}
    if pricing_version_info and pricing_version_info.is_grandfathered:
        # Get the latest price version
        latest_pv_stmt = select(PriceVersion).where(
            PriceVersion.is_current.is_(True),
            PriceVersion.is_active.is_(True)
        )
        latest_price_version = db.execute(latest_pv_stmt).scalar_one_or_none()
        
        if latest_price_version:
            # Get all subscription options for the latest price version
            latest_options_stmt = (
                select(SubscriptionOption)
                .where(
                    SubscriptionOption.price_version_id == latest_price_version.id,
                    SubscriptionOption.is_active.is_(True)
                )
                .options(selectinload(SubscriptionOption.year))
            )
            latest_options = db.execute(latest_options_stmt).scalars().all()
            
            # Create a map by year_id for easy lookup
            for opt in latest_options:
                latest_subscription_options_map[opt.year_id] = opt
    
    # Process all subscriptions
    subscription_details: List[SubscriptionDetail] = []
    
    for active_subscription in active_subscriptions:
        plan_items: List[CurrentSubscriptionPlanItem] = []
        
        # Check for pauses on this subscription
        current_pause = db.execute(
            select(SubscriptionPause)
            .where(
                SubscriptionPause.active_subscription_id == active_subscription.id,
                SubscriptionPause.status.in_([
                    SubscriptionPauseStatus.SCHEDULED,
                    SubscriptionPauseStatus.PAUSED
                ])
            )
        ).scalar_one_or_none()
        
        # Process plan links for this subscription
        for plan_link in active_subscription.plan_links:
            if not plan_link.subscription_option or not plan_link.subscription_option.year:
                logger.warning(f"Plan link {plan_link.public_id} missing subscription option or year data")
                continue
                
            year = plan_link.subscription_option.year
            
            # Get subjects for this plan item
            selected_subjects: List[SubjectBasicInfoSchema] = []
            if plan_link.chosen_plan_type == PlanTypeEnum.SC:
                for ps in plan_link.selected_subjects:
                    if ps.subject:
                        selected_subjects.append(SubjectBasicInfoSchema(
                            public_id=ps.subject.public_id,
                            name=ps.subject.name,
                            description=ps.subject.description
                        ))
            
            # Get available subjects for the year
            available_subjects = (
                _get_available_subjects_for_year(db, year.id) 
                if plan_link.chosen_plan_type == PlanTypeEnum.SC else None
            )
            
            # Extract pricing information
            pricing_info = _extract_display_pricing(
                plan_link.subscription_option.display_price_config_json,
                plan_link.chosen_plan_type,
                plan_link.chosen_billing_period
            )
            
            # For SC plans, calculate the correct price per subject based on quantity and tiers
            if plan_link.chosen_plan_type == PlanTypeEnum.SC and 'pricing_tiers' in pricing_info:
                pricing_tiers = pricing_info['pricing_tiers']
                if pricing_tiers:
                    # Find the correct tier based on quantity
                    for tier in pricing_tiers:
                        if tier.up_to == "inf" or int(tier.up_to) >= plan_link.quantity:
                            pricing_info['price_per_subject'] = tier.unit_price_display
                            break
            
            # Determine available actions
            actions = _determine_available_actions(plan_link, active_subscription.status, current_pause)
            
            # Get price comparison if user has grandfathered pricing
            price_comparison = None
            if (pricing_version_info and pricing_version_info.is_grandfathered
                    and year.id in latest_subscription_options_map):
                latest_option = latest_subscription_options_map[year.id]
                price_comparison = _get_price_comparison(
                    plan_link.subscription_option,
                    latest_option,
                    plan_link.chosen_plan_type,
                    plan_link.chosen_billing_period,
                    plan_link.quantity
                )
            
            plan_item = CurrentSubscriptionPlanItem(
                stripe_subscription_item_id=plan_link.stripe_subscription_item_id,
                plan_link_public_id=plan_link.public_id,
                year_public_id=year.public_id,
                year_name=year.name,
                plan_type=plan_link.chosen_plan_type,
                billing_period=plan_link.chosen_billing_period,
                quantity=plan_link.quantity,
                selected_subjects=selected_subjects,
                available_subjects=available_subjects,
                price_per_subject=pricing_info.get('price_per_subject'),
                currency=pricing_info.get('currency'),
                pricing_tiers=pricing_info.get('pricing_tiers'),
                stripe_price_id=plan_link.chosen_stripe_price_id,
                available_actions=actions,
                price_comparison=price_comparison
            )
            plan_items.append(plan_item)
        
        # Check pause availability
        can_pause, pause_info = _check_pause_availability(current_pause)
        
        # Determine if new items can be added to this subscription
        can_add_items = (
            active_subscription.status == SubscriptionStatusType.ACTIVE
            and not current_pause
        )
        
        # Create subscription detail
        subscription_detail = SubscriptionDetail(
            public_id=active_subscription.public_id,
            parent_account_public_id=parent_public_id,
            subscription_public_id=active_subscription.public_id,
            stripe_subscription_id=active_subscription.stripe_subscription_id,
            status=active_subscription.status,
            current_period_start=active_subscription.current_period_start,
            current_period_end=active_subscription.current_period_end,
            cancel_at_period_end=active_subscription.cancel_at_period_end,
            plan_items=plan_items,
            next_billing_date=active_subscription.current_period_end,
            can_pause=can_pause,
            can_add_items=can_add_items,
            pause_info=pause_info,
            pricing_version_info=pricing_version_info,
            created_at=active_subscription.created_at,
            updated_at=active_subscription.updated_at
        )
        subscription_details.append(subscription_detail)
    
    # Prepare response with backward compatibility
    # Use the first (most recent) subscription for deprecated fields
    first_sub = subscription_details[0] if subscription_details else None
    
    return CurrentSubscriptionResponse(
        has_subscription=True,
        parent_account_public_id=parent_public_id,
        subscriptions=subscription_details,
        # Deprecated fields from first subscription for backward compatibility
        public_id=first_sub.public_id if first_sub else None,
        subscription_public_id=first_sub.public_id if first_sub else None,
        stripe_subscription_id=first_sub.stripe_subscription_id if first_sub else None,
        status=first_sub.status if first_sub else None,
        current_period_start=first_sub.current_period_start if first_sub else None,
        current_period_end=first_sub.current_period_end if first_sub else None,
        cancel_at_period_end=first_sub.cancel_at_period_end if first_sub else None,
        plan_items=first_sub.plan_items if first_sub else [],
        next_billing_date=first_sub.next_billing_date if first_sub else None,
        can_pause=first_sub.can_pause if first_sub else False,
        can_add_items=first_sub.can_add_items if first_sub else False,
        pause_info=first_sub.pause_info if first_sub else None,
        created_at=first_sub.created_at if first_sub else None,
        updated_at=first_sub.updated_at if first_sub else None
    )

def _determine_available_actions(
    plan_link: ActiveSubscriptionPlanLink,
    subscription_status: SubscriptionStatusType,
    current_pause: Optional[SubscriptionPause]
) -> List[PlanItemAvailableAction]:
    """Determine what actions are available for a specific plan item."""
    actions: List[PlanItemAvailableAction] = []
    
    # Base conditions
    is_active = subscription_status == SubscriptionStatusType.ACTIVE
    is_paused = current_pause is not None
    
    # Change billing period action
    other_billing_period = (
        BillingPeriodEnum.YEARLY if plan_link.chosen_billing_period == BillingPeriodEnum.MONTHLY 
        else BillingPeriodEnum.MONTHLY
    )
    
    actions.append(PlanItemAvailableAction(
        action="change_billing_period",
        description=f"Change to {other_billing_period.value} billing",
        enabled=is_active and not is_paused,
        reason="Subscription must be active and not paused" if not (is_active and not is_paused) else None
    ))
    
    # Remove item action
    actions.append(PlanItemAvailableAction(
        action="remove",
        description="Remove this plan from subscription",
        enabled=is_active and not is_paused,
        reason="Subscription must be active and not paused" if not (is_active and not is_paused) else None
    ))
    
    # Update quantity action (only for SC plans)
    if plan_link.chosen_plan_type == PlanTypeEnum.SC:
        actions.append(PlanItemAvailableAction(
            action="update_quantity",
            description="Change number of subjects",
            enabled=is_active and not is_paused,
            reason="Subscription must be active and not paused" if not (is_active and not is_paused) else None
        ))
    
    return actions

def _get_available_subjects_for_year(db: SyncSession, year_id: int) -> List[SubjectBasicInfoSchema]:
    """Get all available subjects for a given year."""
    try:
        subjects_stmt = (
            select(Subject)
            .options(selectinload(Subject.year))
            .where(
                Subject.year_id == year_id,
                Subject.is_active.is_(True)
            )
            .order_by(Subject.name)
        )
        subjects = db.execute(subjects_stmt).scalars().all()
        
        return [
            SubjectBasicInfoSchema(
                public_id=subject.public_id,
                name=subject.name,
                description=subject.description,
                year_name=subject.year.name if subject.year else None,
                year_public_id=subject.year.public_id if subject.year else None
            )
            for subject in subjects
        ]
    except Exception as e:
        logger.error(f"Error fetching subjects for year {year_id}: {e}")
        return []

def _extract_display_pricing(
    display_config_json: dict, plan_type: PlanTypeEnum, billing_period: BillingPeriodEnum
) -> dict:
    """Extract display pricing from subscription option config."""
    logger.debug(
        f"Attempting to extract pricing. Plan: {plan_type}, Period: {billing_period}. "
        f"Input JSON: {json.dumps(display_config_json, indent=2) if display_config_json else 'No JSON'}"
    )
    try:
        if not display_config_json:
            logger.debug("display_config_json is empty or None.")
            return {}
        
        plan_key = plan_type.value.upper()
        period_key = billing_period.value
        
        plan_config = display_config_json.get(plan_key, {})
        if not plan_config:
            logger.debug(f"No config found for plan_key '{plan_key}'.")
            return {}
            
        period_config = plan_config.get(period_key, {})
        if not period_config:
            logger.debug(f"No config found for period_key '{period_key}' within plan_key '{plan_key}'.")
            return {}
        
        currency = period_config.get('currency', 'USD')
        
        if plan_type == PlanTypeEnum.SC:
            tiers_info = period_config.get('tiers_info', []) 
            if not tiers_info:
                logger.debug(f"No 'tiers_info' found in period_config: {period_config}")
                return {'currency': currency}

            # Convert tiers_info to PricingTierSchema format
            pricing_tiers = []
            for tier in tiers_info:
                try:
                    unit_price_str = tier.get('unit_price', "0.00")
                    unit_price_cents = int(float(unit_price_str))
                    unit_price_display = unit_price_cents / 100.0
                    
                    pricing_tier = PricingTierSchema(
                        up_to=str(tier.get('up_to', 'inf')),
                        unit_price_cents=unit_price_cents,
                        unit_price_display=unit_price_display
                    )
                    pricing_tiers.append(pricing_tier)
                    
                    logger.debug(
                        f"Added pricing tier: up_to={pricing_tier.up_to}, "
                        f"price={pricing_tier.unit_price_display} {currency}"
                    )
                
                except (ValueError, TypeError) as e:
                    logger.error(f"Could not parse tier info {tier}: {e}")
                    continue
            
            return {
                'currency': currency,
                'pricing_tiers': pricing_tiers
            }
        
        elif plan_type == PlanTypeEnum.YF:
            # For YF plans, look for a flat price
            price_str = period_config.get('price', "0.00")
            if price_str == "0.00" and 'unit_price' in period_config:
                price_str = period_config.get('unit_price', "0.00")

            try:
                price_cents_float = float(price_str)
                display_price = price_cents_float / 100.0
                logger.debug(f"YF Pricing: DisplayPrice={display_price}, Currency={currency}")
                return {
                    'price_per_subject': display_price,
                    'currency': currency
                }
            except ValueError:
                logger.error(f"Could not convert YF price '{price_str}' to float.")
                return {'currency': currency}
        
        logger.warning(f"Fell through pricing logic for plan_type {plan_type}. Returning currency only.")
        return {'currency': currency} 
        
    except Exception as e:
        logger.error(f"Error extracting display pricing: {e}", exc_info=True)
        return {}

def _get_pricing_version_info(db: SyncSession, account: Account) -> Optional[PricingVersionInfo]:
    """Get pricing version information for the account."""
    try:
        # Get user's current price version through eligibility
        stmt_price_eligibility = (
            select(PriceEligibility)
            .options(selectinload(PriceEligibility.price_version))
            .where(PriceEligibility.account_id == account.id)
        )
        account_price_eligibility = db.execute(stmt_price_eligibility).scalar_one_or_none()
        
        # Get the latest (current default) price version
        stmt_latest_pv = select(PriceVersion).where(
            PriceVersion.is_current.is_(True),
            PriceVersion.is_active.is_(True)
        )
        latest_price_version = db.execute(stmt_latest_pv).scalar_one_or_none()
        
        if not latest_price_version:
            logger.warning("No current active price version found in the system")
            return None
        
        # Determine user's current price version
        user_price_version = None
        if (account_price_eligibility and account_price_eligibility.price_version
                and account_price_eligibility.price_version.is_active):
            user_price_version = account_price_eligibility.price_version
        else:
            # User doesn't have a specific eligibility, they would get the default
            user_price_version = latest_price_version
        
        is_grandfathered = user_price_version.id != latest_price_version.id
        
        return PricingVersionInfo(
            current_version_name=user_price_version.name,
            current_version_slug=user_price_version.slug,
            latest_version_name=latest_price_version.name,
            latest_version_slug=latest_price_version.slug,
            is_grandfathered=is_grandfathered
        )
    except Exception as e:
        logger.error(f"Error getting pricing version info: {e}")
        return None

def _get_price_comparison(
    current_subscription_option: SubscriptionOption,
    latest_subscription_option: Optional[SubscriptionOption],
    plan_type: PlanTypeEnum,
    billing_period: BillingPeriodEnum,
    quantity: int
) -> Optional[PriceComparisonInfo]:
    """Calculate price comparison between current and latest pricing."""
    if not latest_subscription_option:
        return None
    
    try:
        # Extract current price
        current_config = current_subscription_option.display_price_config_json
        current_price_info = _extract_display_pricing(current_config, plan_type, billing_period)
        
        # Extract latest price
        latest_config = latest_subscription_option.display_price_config_json
        latest_price_info = _extract_display_pricing(latest_config, plan_type, billing_period)
        
        currency = current_price_info.get('currency', 'EUR')
        
        # Get the price per unit based on plan type
        if plan_type == PlanTypeEnum.SC:
            # For SC plans, find the appropriate tier price based on quantity
            current_price = None
            latest_price = None
            
            if current_price_info.get('pricing_tiers'):
                for tier in current_price_info['pricing_tiers']:
                    if tier.up_to == "inf" or int(tier.up_to) >= quantity:
                        current_price = tier.unit_price_display
                        break
            
            if latest_price_info.get('pricing_tiers'):
                for tier in latest_price_info['pricing_tiers']:
                    if tier.up_to == "inf" or int(tier.up_to) >= quantity:
                        latest_price = tier.unit_price_display
                        break
        else:
            # For YF plans, use flat price
            current_price = current_price_info.get('price_per_subject')
            latest_price = latest_price_info.get('price_per_subject')
        
        if current_price is None or latest_price is None:
            logger.warning(
                f"Could not extract prices for comparison: "
                f"current={current_price}, latest={latest_price}"
            )
            return None
        
        # Calculate increase
        price_increase = latest_price - current_price
        percentage_increase = (price_increase / current_price * 100) if current_price > 0 else 0
        
        # Only return comparison if there's an actual increase
        if price_increase > 0:
            return PriceComparisonInfo(
                current_price=current_price,
                latest_price=latest_price,
                price_increase=price_increase,
                percentage_increase=round(percentage_increase, 1),
                currency=currency
            )
        
        return None
        
    except Exception as e:
        logger.error(f"Error calculating price comparison: {e}")
        return None

def _check_pause_availability(current_pause: Optional[SubscriptionPause]) -> tuple[bool, Optional[Dict[str, Any]]]:
    """Check if subscription can be paused and return pause info."""
    if current_pause:
        # Handle different pause states
        if current_pause.status == SubscriptionPauseStatus.PAUSED:
            # Subscription is currently paused
            return False, {
                "status": "currently_paused",
                "pause_start": current_pause.start_date.isoformat() if current_pause.start_date else None,
                "pause_end": current_pause.end_date.isoformat() if current_pause.end_date else None,
                "resume_date": current_pause.resumed_at.isoformat() if current_pause.resumed_at else None
            }
        
        elif current_pause.status == SubscriptionPauseStatus.SCHEDULED:
            # Subscription has a scheduled pause
            now = datetime.now(UTC)
            return False, {
                "status": "pause_scheduled",
                "scheduled_pause_start": current_pause.start_date.isoformat() if current_pause.start_date else None,
                "scheduled_pause_end": current_pause.end_date.isoformat() if current_pause.end_date else None,
                "pause_public_id": current_pause.public_id,
                "qstash_message_id": current_pause.qstash_message_id,
                "can_cancel": True,  # Users can cancel scheduled pauses
                "days_until_pause": (current_pause.start_date - now).days if current_pause.start_date else None
            }
    
    # No active or scheduled pause - check if we're in the pause window using settings
    now = datetime.now(UTC)
    
    # Use the same settings-based configuration as pause_subscription_service
    ui_visible_from = parser.parse(settings.PAUSE_UI_VISIBLE_FROM)
    ui_visible_to = parser.parse(settings.PAUSE_UI_VISIBLE_TO)
    
    # Check if we're in the UI visibility window (when pause functionality should be available)
    if ui_visible_from <= now < ui_visible_to:
        return True, {
            "status": "available",
            "window_end": ui_visible_to.isoformat()
        }
    else:
        # Calculate next window start (assuming it repeats yearly)
        next_year = now.year + 1
        next_window_start = ui_visible_from.replace(year=next_year)
        
        return False, {
            "status": "outside_window", 
            "next_window_start": next_window_start.isoformat()
        }