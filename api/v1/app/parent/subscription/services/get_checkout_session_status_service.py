import stripe
from sqlalchemy.orm import Session as SyncSession
from sqlalchemy import select
from loguru import logger

from db.models import Account, StripeCheckoutSessionData, ActiveSubscription
from db.models.subscription import SubscriptionStatusType
from ..schemas.response import CheckoutSessionStatusResponse
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, 
    ServiceError, 
    ExternalServiceError, 
    AuthenticationError
)
from api.v1.common.schemas import AppErrorCode
from core.config.settings import settings

# Initialize Stripe with settings
stripe.api_key = settings.STRIPE_API_KEY

def get_checkout_session_status_service(
    db: SyncSession,
    parent_public_id: str,
    checkout_session_id: str
) -> CheckoutSessionStatusResponse:
    logger.info(
        f"Fetching status for checkout session ID: {checkout_session_id} "
        f"for parent: {parent_public_id}"
    )

    account_stmt = select(Account).where(Account.public_id == parent_public_id)
    parent_account = db.execute(account_stmt).scalar_one_or_none()
    if not parent_account:
        # This case should ideally be caught by AuthDependency if parent_public_id is from a valid token
        logger.warning(
            f"Parent account not found: {parent_public_id} when checking checkout session status."
        )
        raise AuthenticationError(
            message="Parent account not found.", 
            error_code=AppErrorCode.PARENT_ACCOUNT_NOT_FOUND
        )

    stmt = select(StripeCheckoutSessionData).where(
        StripeCheckoutSessionData.stripe_checkout_session_id == checkout_session_id,
        StripeCheckoutSessionData.parent_account_public_id == parent_public_id
    )
    db_checkout_data = db.execute(stmt).scalar_one_or_none()

    if not db_checkout_data:
        logger.warning(
            f"StripeCheckoutSessionData not found for session_id: {checkout_session_id} "
            f"and parent: {parent_public_id}"
        )
        # To avoid leaking information about whether a session ID exists for *another* user,
        # we can return a generic "not found or not accessible".
        # However, since parent_public_id is part of the query, a simple NotFoundError is also acceptable.
        raise NotFoundError(
            message="Checkout session not found or not accessible.",
            log_message=(
                f"StripeCheckoutSessionData not found for session_id: {checkout_session_id} "
                f"and parent: {parent_public_id}"
            ),
            error_code=AppErrorCode.CHECKOUT_SESSION_NOT_FOUND
        )

    if db_checkout_data.processed_at:
        logger.info(
            f"Checkout session {checkout_session_id} already processed at {db_checkout_data.processed_at}."
        )
        # Webhook has processed. Verify active subscription.
        active_sub_stmt = select(ActiveSubscription).where(
            ActiveSubscription.parent_account_id == parent_account.id,
            ActiveSubscription.status == SubscriptionStatusType.ACTIVE
        )
        active_subscription = db.execute(active_sub_stmt).scalar_one_or_none()

        if active_subscription:
            return CheckoutSessionStatusResponse(
                status="completed_active",
                message="Your subscription is now active!"
                # Optionally add non-sensitive subscription details here
            )
        else:
            logger.error(
                f"Checkout session {checkout_session_id} processed, but no active subscription found "
                f"for parent {parent_public_id}."
            )
            return CheckoutSessionStatusResponse(
                status="completed_issue_contact_support",
                message="Your payment was processed, but there was an issue activating your subscription. "
                "Please contact support."
            )
    else:  # processed_at is NULL, webhook likely not processed yet.
        logger.info(
            f"Checkout session {checkout_session_id} is pending webhook processing. "
            f"Checking Stripe live status."
        )
        try:
            stripe_session = stripe.checkout.Session.retrieve(checkout_session_id)
            
            payment_status = stripe_session.payment_status
            session_status = stripe_session.status

            logger.info(
                f"Stripe live status for session {checkout_session_id}: "
                f"status='{session_status}', payment_status='{payment_status}'"
            )

            if session_status == 'complete':
                if payment_status == 'paid':
                    return CheckoutSessionStatusResponse(
                        status="processing",
                        message="Payment successful. Your subscription is being activated."
                    )
                else:  # e.g., payment_status == 'unpaid' or 'no_payment_required' (if free trial setup)
                    logger.warning(
                        f"Stripe session {checkout_session_id} is 'complete' but payment_status is '{payment_status}'."
                    )
                    return CheckoutSessionStatusResponse(
                        status="failed",  # Or a more specific status like "payment_issue"
                        message=(
                            f"There was an issue with the payment (Status: {payment_status}). "
                            f"Please check your payment method or contact support."
                        )
                    )
            elif session_status == 'open':
                return CheckoutSessionStatusResponse(
                    status="pending_payment",
                    message="Your payment is still pending. Please complete the payment process."
                )
            elif session_status == 'expired':
                return CheckoutSessionStatusResponse(
                    status="failed",
                    message="The checkout session has expired. Please try creating a new subscription."
                )
            else:  # Other statuses
                logger.warning(
                    f"Unhandled Stripe session status '{session_status}' for session {checkout_session_id}."
                )
                return CheckoutSessionStatusResponse(
                    status="failed",
                    message="There was an issue with your checkout session. Please try again or contact support."
                )
        except stripe.error.StripeError as e:
            logger.error(f"Stripe API error retrieving session {checkout_session_id}: {str(e)}")
            raise ExternalServiceError(
                message="Could not retrieve checkout session status from payment provider.",
                original_exception=e,
                error_code=AppErrorCode.STRIPE_ERROR
            )
        except Exception as e:
            logger.error(f"Unexpected error retrieving Stripe session {checkout_session_id}: {str(e)}")
            raise ServiceError(
                message="An unexpected error occurred while checking session status.",
                error_code=AppErrorCode.SERVICE_ERROR
            )