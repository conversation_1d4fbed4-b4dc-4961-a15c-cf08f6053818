# Example in a shared services utility file or one of the existing service files
from sqlalchemy.orm import Session as SyncSession 
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from loguru import logger

from db.models import Account, PriceVersion, PriceEligibility
from core.exception_handling.exceptions.custom_exceptions import ServiceError
from api.v1.common.schemas import AppErrorC<PERSON>


def _get_or_create_eligible_price_version(db: SyncSession, account: Account) -> PriceVersion: 
    logger.debug(
        "Getting or creating eligible price version for account_id: {} (public_id: {})", 
        account.id, 
        account.public_id
    )

    stmt_price_eligibility = (
        select(PriceEligibility)
        .options(selectinload(PriceEligibility.price_version))
        .where(PriceEligibility.account_id == account.id)
    )
    account_price_eligibility = db.execute(stmt_price_eligibility).scalar_one_or_none()

    final_price_version = None
    if account_price_eligibility:
        if account_price_eligibility.price_version and account_price_eligibility.price_version.is_active:
            final_price_version = account_price_eligibility.price_version
            logger.debug(
                "Found existing eligible active price version {} (name: {}) for account {}", 
                final_price_version.public_id, 
                final_price_version.name, 
                account.public_id
            )
        else:
            log_msg = (
                "Account {} eligible for PriceVersion ID {} "
                "(Name: {}, "
                "Active: {}). "
                "This version is not active. Will try to assign default."
            )
            logger.warning(
                log_msg.format(
                    account.public_id, 
                    account_price_eligibility.price_version_id, 
                    account_price_eligibility.price_version.name 
                    if account_price_eligibility.price_version else 'N/A', 
                    account_price_eligibility.price_version.is_active 
                    if account_price_eligibility.price_version else 'N/A'
                )
            )
            # Invalidate this eligibility by deleting it.
            # The calling service is responsible for committing this change.
            db.delete(account_price_eligibility)
            # db.flush() # Avoid flush here; let caller manage transaction.
            account_price_eligibility = None 
            # Reset to trigger default assignment logic below

    if not final_price_version:
        stmt_default_pv = select(PriceVersion).where(
            PriceVersion.is_current,
            PriceVersion.is_active
        )
        default_price_version = db.execute(stmt_default_pv).scalar_one_or_none()

        if default_price_version:
            if account_price_eligibility is None: 
                # True if no prior eligibility or if it was for an inactive version and deleted from session
                logger.info(
                    "Assigning account {} to default PriceVersion {} (name: {}).", 
                    account.public_id, 
                    default_price_version.public_id, 
                    default_price_version.name
                )

            # Check if an eligibility for this default version already exists for this account
            # This prevents adding a duplicate to the session if this function is called multiple times
            # before a commit, especially if the previous eligibility was for an inactive version.
            existing_default_eligibility = db.execute(
                select(PriceEligibility).where(
                    PriceEligibility.account_id == account.id,
                    PriceEligibility.price_version_id == default_price_version.id
                )
            ).scalar_one_or_none()

            if not existing_default_eligibility:
                new_eligibility = PriceEligibility(
                    account_id=account.id, 
                    price_version_id=default_price_version.id
                )
                db.add(new_eligibility)
                logger.info(
                    "Prepared new PriceEligibility for account {} with default PriceVersion {}. "
                    "Calling service must commit/flush.", 
                    account.public_id, 
                    default_price_version.public_id
                )
            else:
                logger.debug(
                    "Account {} already has PriceEligibility for default PriceVersion {}.", 
                    account.public_id, 
                    default_price_version.public_id
                )
                db.commit()

            final_price_version = default_price_version
        else:
            log_msg = "No default and active PriceVersion found, and no existing valid eligibility for account."
            logger.error(log_msg + " Account public_id: {}", account.public_id)
            raise ServiceError(
                message="No pricing information available at this time. Please contact support.",
                log_message=log_msg,
                error_code=AppErrorCode.SERVICE_ERROR
            )

    if not final_price_version:
        critical_log_msg = "Critical: Could not determine price version for account {} despite logic paths.".format(
            account.public_id
        )
        logger.critical(critical_log_msg)
        raise ServiceError(
            message="A critical error occurred while determining pricing. Please contact support.",
            log_message=critical_log_msg,
            error_code=AppErrorCode.SERVICE_ERROR
        )   


    return final_price_version