"""
Cancel scheduled pause service.
This service cancels a scheduled pause and the associated QStash message.
"""

import httpx
from sqlalchemy.orm import Session as SyncSession
from sqlalchemy import select
from loguru import logger
from datetime import datetime, UTC
from db.models import Account, ActiveSubscription, SubscriptionPause
from db.models.subscription import SubscriptionStatusType, SubscriptionPauseStatus
from ..schemas.request import CancelScheduledPauseRequest
from ..schemas.response import CancelScheduledPauseResponse
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ValidationError, ServiceError
)
from api.v1.common.schemas import AppErrorCode
from core.config.settings import settings


def cancel_scheduled_pause_service(
    db: SyncSession,
    parent_public_id: str,
    cancel_data: CancelScheduledPauseRequest
) -> CancelScheduledPauseResponse:
    """
    Cancel a scheduled pause for a subscription.
    
    This will:
    1. Find the scheduled pause record
    2. Cancel the QStash message if it exists
    3. Mark the pause as cancelled in the database
    """
    logger.info(
        f"Processing cancel scheduled pause request for parent: {parent_public_id}, "
        f"subscription: {cancel_data.subscription_public_id}, "
        f"pause: {cancel_data.pause_public_id}"
    )
    
    # Get parent account
    account_stmt = select(Account).where(Account.public_id == parent_public_id)
    account = db.execute(account_stmt).scalar_one_or_none()
    if not account:
        logger.error(f"Parent account not found: {parent_public_id}")
        raise NotFoundError(
            message="Parent account not found.",
            error_code=AppErrorCode.PARENT_ACCOUNT_NOT_FOUND
        )
    
    # Get the specific subscription
    subscription_stmt = (
        select(ActiveSubscription)
        .where(
            ActiveSubscription.parent_account_id == account.id,
            ActiveSubscription.public_id == cancel_data.subscription_public_id,
            ActiveSubscription.status.in_([
                SubscriptionStatusType.ACTIVE, 
                SubscriptionStatusType.TRIALING
            ])
        )
    )
    subscription = db.execute(subscription_stmt).scalar_one_or_none()
    
    if not subscription:
        logger.warning(
            f"Subscription {cancel_data.subscription_public_id} not found for parent {parent_public_id}"
        )
        raise NotFoundError(
            message="Subscription not found or not active.",
            error_code=AppErrorCode.SUBSCRIPTION_NOT_FOUND
        )
    
    # Get the scheduled pause
    pause_stmt = (
        select(SubscriptionPause)
        .where(
            SubscriptionPause.public_id == cancel_data.pause_public_id,
            SubscriptionPause.active_subscription_id == subscription.id,
            SubscriptionPause.status == SubscriptionPauseStatus.SCHEDULED
        )
    )
    scheduled_pause = db.execute(pause_stmt).scalar_one_or_none()
    
    if not scheduled_pause:
        logger.warning(
            f"Scheduled pause {cancel_data.pause_public_id} not found for subscription {subscription.id}"
        )
        raise NotFoundError(
            message="Scheduled pause not found or already processed.",
            error_code=AppErrorCode.SUBSCRIPTION_PAUSE_NOT_FOUND
        )
    
    # Check if pause is still in the future (can't cancel if already started)
    now = datetime.now(UTC)
    if scheduled_pause.start_date <= now:
        logger.warning(
            f"Cannot cancel pause {cancel_data.pause_public_id} - pause has already started"
        )
        raise ValidationError(
            message="Cannot cancel pause - it has already started.",
            error_code=AppErrorCode.INVALID_REQUEST
        )
    
    # Cancel QStash message if it exists
    qstash_cancelled = False
    if scheduled_pause.qstash_message_id:
        qstash_cancelled = _cancel_qstash_message(scheduled_pause.qstash_message_id)
        if not qstash_cancelled:
            logger.warning(
                f"Failed to cancel QStash message {scheduled_pause.qstash_message_id}, "
                f"but continuing with database cancellation"
            )
    
    try:
        # Mark pause as cancelled
        scheduled_pause.status = SubscriptionPauseStatus.CANCELED
        scheduled_pause.updated_at = now
        
        db.add(scheduled_pause)
        db.commit()
        
        logger.info(
            f"Successfully cancelled scheduled pause {cancel_data.pause_public_id}. "
            f"QStash message cancelled: {qstash_cancelled}"
        )
        
        return CancelScheduledPauseResponse(
            message="Scheduled pause has been cancelled successfully.",
            canceled_pause_id=scheduled_pause.public_id,
            canceled=qstash_cancelled
        )
        
    except Exception as e:
        db.rollback()
        logger.error(
            f"Error cancelling scheduled pause {cancel_data.pause_public_id}: {str(e)}",
            exc_info=True
        )
        raise ServiceError(
            message="An unexpected error occurred while cancelling the scheduled pause.",
            error_code=AppErrorCode.SERVICE_ERROR
        )


def _cancel_qstash_message(message_id: str) -> bool:
    """
    Cancel a QStash message.
    
    Returns:
        True if cancellation successful or message doesn't exist, False on error
    """
    try:
        qstash_url = f"{settings.QSTASH_URL}/v2/messages/{message_id}"
        
        headers = {
            "Authorization": f"Bearer {settings.QSTASH_TOKEN}"
        }
        
        with httpx.Client() as client:
            response = client.delete(qstash_url, headers=headers)
            
            if response.status_code in (200, 202, 204):
                logger.info(f"Successfully cancelled QStash message: {message_id}")
                return True
            elif response.status_code == 404:
                logger.info(f"QStash message not found (may have already been processed): {message_id}")
                return True  # Consider this a success - message doesn't exist to cancel
            else:
                # Log response text without escaping in f-string
                logger.error(
                    f"Failed to cancel QStash message. "
                    f"Status: {response.status_code}, Response: {response.text}"
                )
                return False
                
    except Exception as e:
        logger.error(f"Error cancelling QStash message {message_id}: {e}", exc_info=True)
        return False