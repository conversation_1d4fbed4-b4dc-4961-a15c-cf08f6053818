"""
Pause subscription service using pause_collection approach.
This implementation uses pause_collection + trial_end for billing extensions.
"""

import stripe
from sqlalchemy.orm import Session as SyncSession
from sqlalchemy import select
from loguru import logger
from datetime import datetime, UTC, timedelta
from dateutil import parser
import httpx
from db.models import Account, ActiveSubscription, SubscriptionPause, ActiveSubscriptionPlanLink
from db.models.subscription import SubscriptionStatusType, SubscriptionPauseStatus, BillingPeriodEnum
from ..schemas.response import PauseSubscriptionResponse
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ValidationError, ServiceError, ExternalServiceError
)
from api.v1.common.schemas import AppErrorCode
from core.config.settings import settings
import uuid

# Initialize Stripe
stripe.api_key = settings.STRIPE_API_KEY


def pause_subscription_service(
    db: SyncSession,
    parent_public_id: str,
    subscription_public_id: str
) -> PauseSubscriptionResponse:
    """
    Pause a subscription using pause_collection approach.
    
    This approach:
    1. Checks if we're in the pause request window
    2. If pause should start immediately (May 1+), applies pause_collection
    3. If pause is future (before May 1), schedules via QStash
    4. Stores remaining subscription time for later credit
    """
    logger.info(f"Processing pause request for parent: {parent_public_id}")
    
    # Parse configuration dates
    pause_start = parser.parse(settings.SUMMER_PAUSE_START_UTC)
    pause_end = parser.parse(settings.SUMMER_PAUSE_END_UTC)
    ui_visible_from = parser.parse(settings.PAUSE_UI_VISIBLE_FROM)
    ui_visible_to = parser.parse(settings.PAUSE_UI_VISIBLE_TO)
    current_time = datetime.now(UTC)
    
    # Check if we're in the visibility window
    if not (ui_visible_from <= current_time < ui_visible_to):
        logger.warning(
            f"Pause request outside visibility window. Current: {current_time}, "
            f"Window: {ui_visible_from} to {ui_visible_to}"
        )
        raise ValidationError(
            message="Summer pause is not available at this time.",
            error_code=AppErrorCode.INVALID_REQUEST
        )
    
    # Get parent account
    account_stmt = select(Account).where(Account.public_id == parent_public_id)
    account = db.execute(account_stmt).scalar_one_or_none()
    if not account:
        logger.error(f"Parent account not found: {parent_public_id}")
        raise NotFoundError(
            message="Parent account not found.",
            error_code=AppErrorCode.PARENT_ACCOUNT_NOT_FOUND
        )
    
    # Get the specific subscription to pause
    # First, try to find with ACTIVE or TRIALING status
    active_sub_stmt = (
        select(ActiveSubscription)
        .where(
            ActiveSubscription.parent_account_id == account.id,
            ActiveSubscription.public_id == subscription_public_id,
            ActiveSubscription.status.in_([SubscriptionStatusType.ACTIVE, SubscriptionStatusType.TRIALING])
        )
    )
    active_subscription = db.execute(active_sub_stmt).scalar_one_or_none()
    
    if not active_subscription:
        # Check if subscription exists with any status for better error messaging
        any_sub_stmt = (
            select(ActiveSubscription)
            .where(
                ActiveSubscription.parent_account_id == account.id,
                ActiveSubscription.public_id == subscription_public_id
            )
        )
        any_subscription = db.execute(any_sub_stmt).scalar_one_or_none()
        
        if any_subscription:
            logger.warning(
                f"Subscription {subscription_public_id} found but status is {any_subscription.status}, "
                f"cannot pause (needs ACTIVE or TRIALING)"
            )
            raise ValidationError(
                message=f"Subscription cannot be paused. Current status: {any_subscription.status.value}",
                error_code=AppErrorCode.INVALID_REQUEST
            )
        else:
            logger.warning(f"No subscription {subscription_public_id} found for parent {parent_public_id}")
            raise ValidationError(
                message="Subscription not found.",
                error_code=AppErrorCode.SUBSCRIPTION_NOT_FOUND
            )
    
    # Check if already paused or scheduled for current period
    existing_pause_stmt = (
        select(SubscriptionPause)
        .where(
            SubscriptionPause.active_subscription_id == active_subscription.id,
            SubscriptionPause.status.in_([
                SubscriptionPauseStatus.SCHEDULED,
                SubscriptionPauseStatus.PAUSED
            ]),
            SubscriptionPause.end_date >= current_time
        )
    )
    existing_pause = db.execute(existing_pause_stmt).scalar_one_or_none()
    
    if existing_pause:
        if (pause_start < existing_pause.end_date and 
            pause_end > existing_pause.start_date):
            logger.warning(
                f"Subscription {active_subscription.public_id} already has a pause "
                f"that overlaps with requested period"
            )
            raise ValidationError(
                message="This subscription already has a pause scheduled that overlaps with the requested period.",
                error_code=AppErrorCode.INVALID_REQUEST
            )
    
    # Don't pre-calculate pause duration - we'll calculate actual duration when resuming
    # This handles early resumptions and makes the logic cleaner
    
    try:
        # Get subscription details
        stripe_sub = stripe.Subscription.retrieve(active_subscription.stripe_subscription_id)
        current_period_end = datetime.fromtimestamp(stripe_sub.current_period_end, tz=UTC)
        
        # Get billing period to determine pause method
        plan_link_stmt = (
            select(ActiveSubscriptionPlanLink)
            .where(ActiveSubscriptionPlanLink.active_subscription_id == active_subscription.id)
            .limit(1)
        )
        plan_link = db.execute(plan_link_stmt).scalar_one_or_none()
        if not plan_link:
            logger.error(f"No plan link found for subscription {active_subscription.id}")
            raise ServiceError(
                message="Subscription configuration error. Please contact support.",
                error_code=AppErrorCode.SERVICE_ERROR
            )
        
        billing_period = plan_link.chosen_billing_period
        logger.info(f"Subscription billing period: {billing_period}")
        
        # Determine if we should pause now or schedule for later
        should_pause_now = current_time >= pause_start
        
        # For development: Only skip QStash scheduling if using localhost (not production URL)
        is_localhost_env = "localhost" in settings.QSTASH_PAUSE_ACTIVATION_URL
        if is_localhost_env and not should_pause_now:
            logger.info(
                f"Localhost environment detected. Skipping QStash scheduling and pausing immediately "
                f"instead of waiting until {pause_start}"
            )
            should_pause_now = True
        elif not should_pause_now:
            logger.info(
                f"Pause scheduled for future date {pause_start}. Will use QStash to schedule activation."
            )
        
        # Create pause record
        pause_public_id = str(uuid.uuid4())
        # If pausing immediately (after May 1), use current time as actual start
        # If scheduling for future, use the scheduled pause_start
        actual_pause_start = current_time if should_pause_now else pause_start
        
        subscription_pause = SubscriptionPause(
            active_subscription_id=active_subscription.id,
            start_date=actual_pause_start,  # Actual pause start time
            end_date=pause_end,  # Policy end date (Sept 1) - NOT actual resume time
            paused_seconds=0,  # Will be updated when resuming with actual pause duration
            original_period_end=current_period_end,
            new_period_end=current_period_end,  # Will be updated when resuming
            status=SubscriptionPauseStatus.SCHEDULED if not should_pause_now else SubscriptionPauseStatus.PAUSED,
            public_id=pause_public_id
        )
        
        if should_pause_now:
            # Apply pause immediately based on subscription type
            logger.info(f"Applying pause immediately for {billing_period.value} subscription {active_subscription.stripe_subscription_id}")
            
            if billing_period == BillingPeriodEnum.MONTHLY:
                # Monthly: Use pause_collection with auto-resume
                stripe.Subscription.modify(
                    active_subscription.stripe_subscription_id,
                    pause_collection={
                        'behavior': 'mark_uncollectible',
                        'resumes_at': int(pause_end.timestamp())
                    }
                )
                # Update subscription status to PAUSED
                active_subscription.status = SubscriptionStatusType.PAUSED
                subscription_pause.status = SubscriptionPauseStatus.PAUSED
                
                logger.info(
                    f"Successfully paused MONTHLY subscription {active_subscription.public_id} until {pause_end}. "
                    f"Will auto-resume on {pause_end.date()}"
                )
                
            else:  # YEARLY
                # Calculate extension duration and apply trial_end
                freeze_duration = int((pause_end - current_time).total_seconds())
                extended_trial_end = current_period_end + timedelta(seconds=freeze_duration)
                
                stripe.Subscription.modify(
                    active_subscription.stripe_subscription_id,
                    trial_end=int(extended_trial_end.timestamp()),
                    proration_behavior='none'
                )
                
                # Update pause record with extended period
                subscription_pause.new_period_end = extended_trial_end
                subscription_pause.status = SubscriptionPauseStatus.PAUSED
                
                # Note: Keep subscription status as ACTIVE/TRIALING for yearly
                # The pause record indicates it's in a "pause" state for UI purposes
                
                logger.info(
                    f"Successfully extended YEARLY subscription {active_subscription.public_id} by {freeze_duration // 86400} days. "
                    f"New billing date: {extended_trial_end.date()} (was {current_period_end.date()})"
                )
            
        else:
            # Schedule pause for future via QStash
            logger.info(f"Scheduling pause for {pause_start} via QStash")
            
            # Store the pause record first
            db.add(subscription_pause)
            db.flush()  # Get the ID
            
            # Schedule with QStash
            qstash_message_id = schedule_pause_activation(
                active_subscription_id=active_subscription.id,
                subscription_pause_id=subscription_pause.public_id,
                activation_time=pause_start
            )
            
            if not qstash_message_id:
                # For delays > 7 days, QStash cannot be used
                # For now, create a scheduled pause record without QStash message ID
                # This will require alternative scheduling (cron job, etc.)
                logger.warning(
                    f"QStash scheduling failed for pause date {pause_start}. "
                    f"Creating scheduled pause record without QStash. "
                    f"Manual activation will be required on {pause_start.date()}"
                )
                # Don't rollback - create the pause record anyway
            
            # Store QStash message ID for potential cancellation
            subscription_pause.qstash_message_id = qstash_message_id
            
            logger.info(f"Successfully scheduled pause for {pause_start}")
        
        db.add(subscription_pause)
        db.add(active_subscription)
        db.commit()
        
        # Create appropriate message based on subscription type and timing
        if not should_pause_now:
            message = (
                f"Your subscription has been scheduled to pause for the summer break starting {pause_start.date()}. "
                f"{'It will automatically resume on ' + pause_end.date().strftime('%B %d, %Y') if billing_period == BillingPeriodEnum.MONTHLY else 'Your billing will be extended when the pause activates'}."
            )
        else:
            if billing_period == BillingPeriodEnum.MONTHLY:
                message = (
                    f"Your subscription is now paused for the summer break. "
                    f"It will automatically resume on {pause_end.date().strftime('%B %d, %Y')}."
                )
            else:  # YEARLY
                message = (
                    f"Your subscription billing has been extended for the summer break. "
                    f"Your next billing date is now {subscription_pause.new_period_end.date().strftime('%B %d, %Y')}."
                )
        
        return PauseSubscriptionResponse(
            message=message,
            pause_start_date=subscription_pause.start_date,  # Use actual start date
            pause_end_date=pause_end,
            status=subscription_pause.status.value
        )
        
    except stripe.error.StripeError as e:
        db.rollback()
        logger.error(
            f"Stripe API error pausing subscription {active_subscription.stripe_subscription_id}: {str(e)}",
            exc_info=True
        )
        user_message = (
            f"Payment gateway error: {e.user_message}"
            if hasattr(e, 'user_message') and e.user_message
            else "Could not pause subscription. Please try again."
        )
        raise ExternalServiceError(
            message=user_message,
            error_code=AppErrorCode.STRIPE_ERROR,
            original_exception=e
        )
    except Exception as e:
        db.rollback()
        error_msg = str(e).replace("{", "{{").replace("}", "}}")
        logger.error(
            f"Unexpected error pausing subscription for parent {parent_public_id}: {error_msg}",
            exc_info=True
        )
        raise ServiceError(
            message="An unexpected error occurred while pausing the subscription.",
            error_code=AppErrorCode.SERVICE_ERROR
        )


def schedule_pause_activation(
    active_subscription_id: int,
    subscription_pause_id: str,
    activation_time: datetime
) -> str:
    """
    Schedule a pause activation via QStash.
    
    Returns:
        str: QStash message ID if successful, empty string otherwise
    """
    try:
        # Calculate delay in seconds
        delay_seconds = int((activation_time - datetime.now(UTC)).total_seconds())
        if delay_seconds <= 0:
            logger.warning("Activation time is in the past, cannot schedule")
            return ""
        
        # QStash has a maximum delay limit, use 6 days for safety margin
        max_qstash_delay = 518400  # 6 days (6 * 24 * 60 * 60)
        
        if delay_seconds > max_qstash_delay:
            # For long delays, we need to chain QStash tasks
            # Calculate how many 6-day chunks we need
            days_until_pause = delay_seconds // 86400
            
            # Schedule the earliest possible pre-pause task (≤6 days from now)
            # This task will handle the next step in the chain
            earliest_schedule_time = datetime.now(UTC) + timedelta(days=6)
            earliest_delay = int((earliest_schedule_time - datetime.now(UTC)).total_seconds())
            
            # Make sure we don't exceed the limit
            if earliest_delay > max_qstash_delay:
                earliest_delay = max_qstash_delay
                earliest_schedule_time = datetime.now(UTC) + timedelta(seconds=earliest_delay)
            
            logger.info(
                f"Pause activation is {days_until_pause} days away. "
                f"Scheduling first pre-pause task for {earliest_schedule_time} "
                f"(in {earliest_delay // 86400} days) which will continue the scheduling chain."
            )
            
            # Schedule the first pre-pause task in the chain
            return _schedule_pre_pause_task(
                active_subscription_id=active_subscription_id,
                subscription_pause_id=subscription_pause_id,
                pre_pause_time=earliest_schedule_time,
                actual_pause_time=activation_time
            )
        
        # Prepare QStash request
        qstash_url = f"{settings.QSTASH_URL}/v2/publish/{settings.QSTASH_PAUSE_ACTIVATION_URL}"
        
        headers = {
            "Authorization": f"Bearer {settings.QSTASH_TOKEN}",
            "Content-Type": "application/json",
            "Upstash-Delay": f"{delay_seconds}s",
            "Upstash-Retries": "3"
        }
        
        # Match the expected payload format for the existing endpoint
        payload = {
            "active_subscription_id": active_subscription_id,
            "subscription_pause_public_id": subscription_pause_id,
            "action": "activate_pause"
        }
        
        # Use synchronous httpx client
        with httpx.Client() as client:
            response = client.post(
                qstash_url,
                headers=headers,
                json=payload
            )
            
            if response.status_code in (200, 201, 202):
                response_data = response.json()
                message_id = response_data.get("messageId", "")
                logger.info(
                    f"Successfully scheduled pause activation for {activation_time}. "
                    f"QStash message ID: {message_id}"
                )
                
                return message_id
            else:
                # Log response text without escaping in f-string
                logger.error(
                    f"Failed to schedule pause activation. "
                    f"Status: {response.status_code}, Response: {response.text}"
                )
                return ""
                
    except Exception as e:
        logger.error(f"Error scheduling pause activation: {e}", exc_info=True)
        return ""


def _schedule_pre_pause_task(
    active_subscription_id: int,
    subscription_pause_id: str,
    pre_pause_time: datetime,
    actual_pause_time: datetime
) -> str:
    """
    Schedule a pre-pause task that will then schedule the actual pause.
    Used for pauses more than 6 days in the future.
    
    Returns:
        str: QStash message ID if successful, empty string otherwise
    """
    try:
        # Calculate delay for the pre-pause task
        pre_pause_delay = int((pre_pause_time - datetime.now(UTC)).total_seconds())
        if pre_pause_delay <= 0:
            logger.warning("Pre-pause time is in the past, cannot schedule")
            return ""
        
        # Prepare QStash request for pre-pause task
        qstash_url = f"{settings.QSTASH_URL}/v2/publish/{settings.QSTASH_PAUSE_ACTIVATION_URL}"
        
        headers = {
            "Authorization": f"Bearer {settings.QSTASH_TOKEN}",
            "Content-Type": "application/json",
            "Upstash-Delay": f"{pre_pause_delay}s",
            "Upstash-Retries": "3"
        }
        
        # Special payload for pre-pause task
        payload = {
            "active_subscription_id": active_subscription_id,
            "subscription_pause_public_id": subscription_pause_id,
            "action": "schedule_pause",  # Different action - schedule rather than activate
            "actual_pause_time": actual_pause_time.isoformat(),  # When the real pause should happen
            "is_pre_pause_task": True  # Flag to indicate this is a pre-pause task
        }
        
        # Use synchronous httpx client
        with httpx.Client() as client:
            response = client.post(
                qstash_url,
                headers=headers,
                json=payload
            )
            
            if response.status_code in (200, 201, 202):
                response_data = response.json()
                message_id = response_data.get("messageId", "")
                logger.info(
                    f"Successfully scheduled pre-pause task for {pre_pause_time}. "
                    f"This will schedule the actual pause for {actual_pause_time}. "
                    f"QStash message ID: {message_id}"
                )
                
                return message_id
            else:
                # Log response text without escaping in f-string
                logger.error(
                    f"Failed to schedule pre-pause task. "
                    f"Status: {response.status_code}, Response: {response.text}"
                )
                return ""
                
    except Exception as e:
        logger.error(f"Error scheduling pre-pause task: {e}", exc_info=True)
        return ""