from sqlalchemy.orm import Session as SyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy import select, delete as sqlalchemy_delete
from db.models import (
    Account, ActiveSubscriptionPlanLink, PlanSelectedSubject, Subject,
    ActiveSubscription, PlanTypeEnum, SubscriptionOption, SubscriptionPendingChange
)
from db.models.subscription import BillingPeriodEnum
from ..schemas.request import ManageSelectedSubjectsRequest
from ..schemas.response import (
    ManageSelectedSubjectsResponse, ActiveSubscriptionPlanLinkDetailSchema, SubjectBasicInfoSchema
)
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ValidationError, PermissionDeniedError, ServiceError
)
from api.v1.common.schemas import AppErrorCode
from loguru import logger
from typing import List, Optional
import uuid
from .subscription_change_service import create_subscription_quantity_change

def manage_selected_subjects_service(
    db: SyncSession, 
    parent_public_id: str, 
    manage_data: ManageSelectedSubjectsRequest
) -> ManageSelectedSubjectsResponse:
    logger.info(
        f"Managing selected subjects for parent_public_id: {parent_public_id}, "
        f"link_public_id: {manage_data.plan_link_public_id}"
    )

    account_stmt = select(Account).where(Account.public_id == parent_public_id)
    account = db.execute(account_stmt).scalar_one_or_none()
    if not account:
        logger.warning(f"Parent account not found: {parent_public_id} during subject management.")
        raise NotFoundError(
            message="Parent account not found.", 
            error_code=AppErrorCode.PARENT_ACCOUNT_NOT_FOUND
        )

    link_stmt = (
        select(ActiveSubscriptionPlanLink)
        .options(
            selectinload(ActiveSubscriptionPlanLink.active_subscription).joinedload(ActiveSubscription.parent_account),
            selectinload(ActiveSubscriptionPlanLink.subscription_option).selectinload(SubscriptionOption.year),
            selectinload(ActiveSubscriptionPlanLink.selected_subjects)
            .selectinload(PlanSelectedSubject.subject)
            .selectinload(Subject.year)
        )
        .where(ActiveSubscriptionPlanLink.public_id == manage_data.plan_link_public_id)
    )
    active_plan_link = db.execute(link_stmt).scalar_one_or_none()

    if not active_plan_link:
        logger.warning(f"ActiveSubscriptionPlanLink not found: {manage_data.plan_link_public_id}")
        raise NotFoundError(
            message="Subscription plan link not found.", 
            error_code=AppErrorCode.STRIPE_ERROR
        )

    if active_plan_link.active_subscription.parent_account_id != account.id:
        logger.error(
            f"Permission denied: Parent {parent_public_id} trying to manage subjects for link "
            f"{manage_data.plan_link_public_id} not belonging to them."
        )
        raise PermissionDeniedError(
            message="You do not have permission to manage subjects for this subscription component.", 
            error_code=AppErrorCode.PERMISSION_DENIED
        )

    if not active_plan_link.subscription_option:
        logger.error(
            f"Subscription Option not found for link {active_plan_link.public_id}. "
            f"Cannot determine plan type."
        )
        raise ServiceError(
            "Subscription details incomplete.", 
            error_code=AppErrorCode.SERVICE_ERROR
        )

    # Use the plan type already stored on the active plan link
    plan_type = active_plan_link.chosen_plan_type
    
    logger.debug(f"Plan type from link: {plan_type}")
    logger.debug(f"Chosen stripe price ID: {active_plan_link.chosen_stripe_price_id}")

    if plan_type != PlanTypeEnum.SC:
        logger.warning(
            f"Attempt to manage subjects for a non-SC plan type {plan_type}. "
            f"Link ID: {active_plan_link.public_id}"
        )
        raise ValidationError(
            "Subject selection is only available for Subject Credit (SC) plans.", 
            error_code=AppErrorCode.SERVICE_ERROR
        )

    num_selected_new = len(manage_data.subject_public_ids_to_select)
    
    # Validate minimum selection
    if num_selected_new < 1:
        logger.warning(
            f"Invalid subject selection: {num_selected_new} subjects. "
            f"At least 1 subject must be selected for SC plans."
        )
        raise ValidationError(
            "You must select at least one subject for Subject Credit plans.",
            error_code=AppErrorCode.INVALID_REQUEST_DATA
        )

    # Handle quantity changes through the subscription change service
    if num_selected_new != active_plan_link.quantity:
        is_downgrade = num_selected_new < active_plan_link.quantity
        is_upgrade = num_selected_new > active_plan_link.quantity
        
        logger.info(
            f"Quantity change detected for SC plan link {active_plan_link.public_id}. "
            f"Current quantity: {active_plan_link.quantity}, Requested: {num_selected_new}. "
            f"Type: {'downgrade' if is_downgrade else 'upgrade'}"
        )
        
        try:
            # Create a pending change and update Stripe
            pending_change = create_subscription_quantity_change(
                db=db,
                active_plan_link=active_plan_link,
                new_quantity=num_selected_new,
                new_subject_ids=manage_data.subject_public_ids_to_select,
                effective_immediately=is_upgrade  # Upgrades are immediate, downgrades at period end
            )
            
            # For immediate upgrades, the subjects are already updated by the change service
            if is_upgrade:
                # Refresh the plan link to get updated data
                db.refresh(active_plan_link)
                
                # Build and return the response
                return _build_success_response(db, active_plan_link, is_quantity_change=True)
            else:
                # For downgrades, return info about the pending change
                return _build_pending_change_response(db, active_plan_link, pending_change)
                
        except Exception as e:
            logger.error(f"Failed to create subscription change: {str(e)}")
            # Fall back to allowing the subject change without Stripe update
            logger.warning("Falling back to subject-only update without Stripe integration")
            # Continue with normal flow below

    selected_subject_entities: List[Subject] = []
    if manage_data.subject_public_ids_to_select:
        subject_stmt = (
            select(Subject)
            .options(selectinload(Subject.year))
            .where(
                Subject.public_id.in_(manage_data.subject_public_ids_to_select),
                Subject.is_active,
                Subject.year_id == active_plan_link.subscription_option.year_id
            )
        )
        selected_subject_entities = db.execute(subject_stmt).scalars().all()

        if len(selected_subject_entities) != num_selected_new:
            found_ids = {s.public_id for s in selected_subject_entities}
            missing_ids = set(manage_data.subject_public_ids_to_select) - found_ids
            logger.warning(
                f"Some selected subjects not found or inactive. "
                f"Requested: {manage_data.subject_public_ids_to_select}, Missing/Inactive: {missing_ids}"
            )
            raise ValidationError(
                f"One or more selected subjects are invalid or not active: {', '.join(missing_ids)}.", 
                error_code=AppErrorCode.SERVICE_ERROR
            )

    try:
        db.execute(
            sqlalchemy_delete(PlanSelectedSubject).where(
                PlanSelectedSubject.plan_link_id == active_plan_link.id
            )
        )

        new_selection_orms: List[PlanSelectedSubject] = []
        for subject_entity in selected_subject_entities:
            new_selection = PlanSelectedSubject(
                plan_link_id=active_plan_link.id,
                subject_id=subject_entity.id,
                public_id=str(uuid.uuid4())
            )
            new_selection_orms.append(new_selection)

        if new_selection_orms:
            db.add_all(new_selection_orms)

        db.flush()
        refreshed_link_stmt = (
            select(ActiveSubscriptionPlanLink)
            .options(
                selectinload(ActiveSubscriptionPlanLink.subscription_option).selectinload(SubscriptionOption.year),
                selectinload(ActiveSubscriptionPlanLink.selected_subjects)
                .selectinload(PlanSelectedSubject.subject)
                .selectinload(Subject.year)
            )
            .where(ActiveSubscriptionPlanLink.id == active_plan_link.id)
        )
        refreshed_plan_link = db.execute(refreshed_link_stmt).scalar_one()

        selected_subjects_details = []
        for uss in refreshed_plan_link.selected_subjects:
            if uss.subject:
                selected_subjects_details.append(SubjectBasicInfoSchema(
                    public_id=uss.subject.public_id,
                    name=uss.subject.name,
                    year_name=uss.subject.year.name if uss.subject.year else None,
                    year_public_id=uss.subject.year.public_id if uss.subject.year else None
                ))

        refreshed_option = refreshed_plan_link.subscription_option
        refreshed_stripe_price_id = refreshed_plan_link.chosen_stripe_price_id
        # Use the plan type and billing period already stored on the link
        refreshed_plan_type = refreshed_plan_link.chosen_plan_type
        refreshed_billing_period = refreshed_plan_link.chosen_billing_period

        year_name = refreshed_option.year.name if refreshed_option.year else "Unknown Year"
        plan_name_suffix = "Unknown Plan"
        if refreshed_plan_type == PlanTypeEnum.SC:
            plan_name_suffix = "Subject Credits"
        elif refreshed_plan_type == PlanTypeEnum.YF:
            plan_name_suffix = "Full Access"
        subscription_plan_name = f"{year_name} - {plan_name_suffix}"

        updated_plan_link_response = ActiveSubscriptionPlanLinkDetailSchema(
            public_id=refreshed_plan_link.public_id,
            subscription_option_public_id=refreshed_option.public_id,
            subscription_plan_name=subscription_plan_name,
            plan_type=refreshed_plan_type,
            chosen_stripe_price_id=refreshed_stripe_price_id,
            chosen_billing_period=refreshed_billing_period,
            stripe_subscription_item_id=refreshed_plan_link.stripe_subscription_item_id,
            quantity=refreshed_plan_link.quantity,
            selected_subjects=selected_subjects_details
        )

        return ManageSelectedSubjectsResponse(
            message="Successfully updated selected subjects.",
            updated_plan_link=updated_plan_link_response
        )

    except ValidationError as ve:
        raise ve
    except Exception as e:
        logger.error(
            f"Error managing selected subjects for parent_public_id: {parent_public_id}, "
            f"link_public_id: {manage_data.plan_link_public_id}: {str(e)}", 
            exc_info=True
        )
        raise ServiceError(
            message="Could not update selected subjects due to an internal error.", 
            error_code=AppErrorCode.SERVICE_ERROR
        )


def _build_success_response(
    db: SyncSession,
    plan_link: ActiveSubscriptionPlanLink,
    is_quantity_change: bool = False
) -> ManageSelectedSubjectsResponse:
    """Build response for successful subject/quantity update."""
    # Refresh to get latest data
    db.refresh(plan_link)
    
    # Get selected subjects
    selected_subjects_details = []
    for uss in plan_link.selected_subjects:
        if uss.subject:
            selected_subjects_details.append(SubjectBasicInfoSchema(
                public_id=uss.subject.public_id,
                name=uss.subject.name,
                year_name=uss.subject.year.name if uss.subject.year else None,
                year_public_id=uss.subject.year.public_id if uss.subject.year else None
            ))
    
    # Build plan name
    year_name = plan_link.subscription_option.year.name if plan_link.subscription_option.year else "Unknown Year"
    plan_name_suffix = "Subject Credits" if plan_link.chosen_plan_type == PlanTypeEnum.SC else "Full Access"
    subscription_plan_name = f"{year_name} - {plan_name_suffix}"
    
    updated_plan_link_response = ActiveSubscriptionPlanLinkDetailSchema(
        public_id=plan_link.public_id,
        subscription_option_public_id=plan_link.subscription_option.public_id,
        subscription_plan_name=subscription_plan_name,
        plan_type=plan_link.chosen_plan_type,
        chosen_stripe_price_id=plan_link.chosen_stripe_price_id,
        chosen_billing_period=plan_link.chosen_billing_period,
        stripe_subscription_item_id=plan_link.stripe_subscription_item_id,
        quantity=plan_link.quantity,
        selected_subjects=selected_subjects_details
    )
    
    message = "Successfully updated subscription." if is_quantity_change else "Successfully updated selected subjects."
    
    return ManageSelectedSubjectsResponse(
        message=message,
        updated_plan_link=updated_plan_link_response
    )


def _build_pending_change_response(
    db: SyncSession,
    plan_link: ActiveSubscriptionPlanLink,
    pending_change: 'SubscriptionPendingChange'
) -> ManageSelectedSubjectsResponse:
    """Build response for pending change (e.g., downgrade)."""
    # For now, return current state with a message about pending change
    # In the future, this could include pending change details
    
    response = _build_success_response(db, plan_link, is_quantity_change=False)
    response.message = (
        f"Subject selection updated. Your plan will change from {pending_change.current_quantity} "
        f"to {pending_change.new_quantity} subjects at your next billing date "
        f"({pending_change.effective_date.strftime('%Y-%m-%d')})."
    )
    
    # TODO: Add pending_change_id to response schema so frontend can track/cancel it
    
    return response