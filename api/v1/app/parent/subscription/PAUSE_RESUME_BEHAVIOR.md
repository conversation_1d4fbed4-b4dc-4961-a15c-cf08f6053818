# Pause/Resume Behavior Documentation

## Overview
The v3 implementation uses <PERSON><PERSON>'s `pause_collection` with `behavior: void` combined with `trial_end` to handle subscription pauses and billing extensions.

## Key Concepts

### Pause Behavior
1. When pausing, we calculate the remaining time in the current billing period
2. If the subscription is in a trial period (from a previous resume), we use `trial_end` instead of `current_period_end`
3. The remaining time is stored in the database for use when resuming
4. `pause_collection` is applied with `behavior: void` (no invoices generated)

### Resume Behavior
1. When resuming, we remove `pause_collection`
2. We set `trial_end` to `current_time + remaining_seconds` (stored during pause)
3. `proration_behavior: none` prevents immediate charges
4. A $0 invoice is generated, then normal billing resumes after trial ends

## Edge Cases and Solutions

### 1. Multiple Pause/Resume Cycles
**Scenario**: User pauses → resumes → pauses again (while in trial)

**Solution**: 
- During second pause, we detect if `trial_end > current_time`
- If yes, we calculate remaining time from `trial_end` instead of `current_period_end`
- This ensures correct billing extension calculation

**Example**:
```
Original billing date: Jan 31
Pause 1: Jan 10 (21 days remaining)
Resume 1: Jan 15 (trial_end set to Feb 5)
Pause 2: Jan 20 (still 16 days remaining from trial_end)
Resume 2: Jan 25 (trial_end set to Feb 10)
Final billing: Feb 10 (correctly extended by total pause time)
```

### 2. Overlapping Trial Periods
**Scenario**: Resume sets trial_end, but subscription already has a future trial_end

**Solution**:
- We check for existing `trial_end` when resuming
- Use the later of the two dates to ensure customer gets full credit
- Log warning for monitoring

### 3. Pause Scheduled Before Fixed Period
**Scenario**: User requests pause in April for May-August period

**Solution**:
- Create pause record with SCHEDULED status
- Use QStash to schedule activation for May 1
- Store QStash message ID for potential cancellation

### 4. Early Resume from Fixed Pause
**Scenario**: User resumes in July instead of waiting until August 31

**Solution**:
- Calculate actual pause duration (May 1 - July X)
- Credit only the actual paused time via trial_end
- Update pause record with `actual_paused_seconds` and `resumed_at`

### 5. Trial Period Limits
**Scenario**: Calculated trial_end exceeds Stripe's maximum (typically 1 year)

**Solution**:
- Cap trial_end at `current_time + 365 days`
- Log warning for manual intervention if needed
- Consider alternative credit methods for extreme cases

### 6. Subscription State Validation
**Scenario**: Attempting to pause already paused subscription

**Solution**:
- Check subscription status before pause/resume
- Validate Stripe state matches database state
- Update database if mismatch detected

## Database Fields

### SubscriptionPause Table
- `remaining_seconds`: Time left when pause started (crucial for trial calculation)
- `actual_paused_seconds`: Actual duration if resumed early
- `resumed_at`: Timestamp of early resume
- `qstash_message_id`: For cancelling scheduled pauses

## Implementation Notes

1. **Idempotency**: All Stripe operations use idempotency keys
2. **State Sync**: Database and Stripe states are kept in sync
3. **Logging**: Extensive logging for debugging complex scenarios
4. **Validation**: Multiple checks to prevent invalid operations

## Testing Scenarios

1. **Single Pause/Resume Cycle**
   - Pause on May 15
   - Resume on June 1
   - Verify billing extended correctly

2. **Multiple Pause/Resume Cycles**
   - Pause → Resume → Pause → Resume
   - Verify cumulative extension is correct

3. **Trial Period Pause**
   - Resume from pause (enters trial)
   - Pause again during trial
   - Verify remaining time calculated from trial_end

4. **Scheduled Pause**
   - Request pause in April for May
   - Verify QStash scheduling works
   - Verify activation on May 1

5. **Edge Date Handling**
   - Pause/resume near billing boundaries
   - Pause/resume at exact midnight UTC
   - Multiple pauses in same billing period