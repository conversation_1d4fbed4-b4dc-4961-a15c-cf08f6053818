# Simplified Pause/Resume Behavior Documentation

## Overview
The simplified implementation uses different strategies for monthly and yearly subscriptions:
- **Monthly**: Uses <PERSON><PERSON>'s `pause_collection` with `behavior: mark_uncollectible` and `resumes_at`
- **Yearly**: Extends billing via `trial_end` immediately when pause is applied

## Key Differences from Previous Implementation

| Feature | Old Approach | New Approach |
|---------|--------------|--------------|
| Monthly Pause | `behavior: void`, manual resume | `behavior: mark_uncollectible`, auto-resume |
| Yearly Pause | `behavior: void`, trial_end on resume | Immediate trial_end extension |
| Resume Logic | Complex calculations + webhook backup | Simple: clear pause (monthly) or no-op (yearly) |
| Code Complexity | ~500 lines for resume | ~100 lines for resume |

## How It Works

### Monthly Subscriptions

**Pause:**
```python
stripe.Subscription.modify(
    subscription_id,
    pause_collection={
        'behavior': 'mark_uncollectible',
        'resumes_at': pause_end_timestamp  # e.g., Sept 1
    }
)
```
- Subscription status: `PAUSED`
- Billing is paused until the `resumes_at` date
- <PERSON><PERSON> automatically resumes on the specified date

**Resume (early):**
```python
stripe.Subscription.modify(
    subscription_id,
    pause_collection=""  # Clear the pause
)
```
- Removes the pause immediately
- Billing resumes from current date

### Yearly Subscriptions

**Pause:**
```python
# Calculate extension
current_period_end = subscription.current_period_end
freeze_duration = pause_end - now()
extended_trial_end = current_period_end + freeze_duration

# Apply immediately
stripe.Subscription.modify(
    subscription_id,
    trial_end=extended_trial_end,
    proration_behavior='none'
)
```
- Subscription status: Remains `ACTIVE` or `TRIALING`
- Next billing date is pushed out by the pause duration
- Customer sees the extended date immediately

**Resume (early):**
```python
# Calculate actual pause duration
actual_duration = now() - pause_start
new_trial_end = original_period_end + actual_duration

# Adjust trial_end for actual pause time
stripe.Subscription.modify(
    subscription_id,
    trial_end=new_trial_end,
    proration_behavior='none'
)
```
- Recalculates credit based on actual pause duration
- Updates the trial_end to reflect early resume

## Database Schema (Simplified)

The `SubscriptionPause` table can be simplified:
- **Remove**: `intended_trial_end` (no webhook backup needed)
- **Remove**: `remaining_seconds` (already deprecated)
- **Keep**: Basic tracking fields for audit trail

## Benefits

1. **Simplicity**: Dramatically reduced code complexity
2. **Reliability**: No webhook dependencies or backup mechanisms
3. **Clarity**: Customers see billing changes immediately
4. **Native Stripe**: Uses Stripe's built-in features as intended

## Edge Cases Handled

### 1. Multiple Pause Attempts
- System checks for existing pauses before allowing new ones
- Prevents overlapping pause periods

### 2. Early Resume
- Monthly: Clears pause, billing resumes immediately
- Yearly: Adjusts trial_end for actual pause duration

### 3. Scheduled Pauses
- QStash still handles future pause activation
- Activation logic checks subscription type and applies appropriate method

## Migration Notes

Since there's no existing data:
1. No migration needed for database
2. Can remove unused fields from schema
3. Clean implementation from the start

## Testing Considerations

1. **Monthly Flow**: 
   - Pause → Auto-resume on date
   - Pause → Manual early resume

2. **Yearly Flow**:
   - Pause → See immediate billing extension
   - Pause → Early resume with adjusted credit

3. **Scheduled Pauses**:
   - Schedule → QStash activation → Correct pause method applied