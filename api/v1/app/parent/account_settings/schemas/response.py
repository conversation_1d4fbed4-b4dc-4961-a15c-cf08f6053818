from pydantic import BaseModel, EmailStr
from typing import List, Optional
from datetime import datetime


class SchoolYearResponse(BaseModel):
    public_id: str
    name: str

class ChildResponse(BaseModel):
    public_id: str
    name: str
    email: Optional[EmailStr] = None
    school_year: SchoolYearResponse
    pin: Optional[str] = None


class SubscribedSubjectDetailResponse(BaseModel):
    public_id: str
    name: str
    year_public_id: Optional[str] = None
    year_name: Optional[str] = None


class SubscriptionPlanComponentResponse(BaseModel):
    plan_link_public_id: str
    plan_public_id: str
    plan_name: str
    plan_type: str  
    billing_period: str  
    granted_subjects: List[SubscribedSubjectDetailResponse]


class ParentActiveSubscriptionResponse(BaseModel):
    subscription_public_id: str
    status: str  
    current_period_end: datetime
    cancel_at_period_end: bool
    components: List[SubscriptionPlanComponentResponse]


class AccountSettingsResponse(BaseModel):
    email: EmailStr
    default_language: str
    children: Optional[List[ChildResponse]] = None
    active_subscription: Optional[ParentActiveSubscriptionResponse] = None
    school_years: Optional[List[SchoolYearResponse]] = None

class ContactMessageResponse(BaseModel):
    message: str
    success: bool = True