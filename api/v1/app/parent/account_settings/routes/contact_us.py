from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from core.exception_handling.exceptions.custom_exceptions import AuthorizationError
from db.database import get_db
from dependencies.auth_dependencies.auth_dependency import AuthDependency, AuthDependencyResponse
from ..schemas.request import ContactMessageRequest
from ..schemas.response import ContactMessageResponse
from ..services.account_settings_service import AccountSettingsService
from core.exception_handling.exceptions.custom_exceptions import AppErrorCode
from dependencies.auth_dependencies.base_auth_dependency import UserType

router = APIRouter()
require_auth = AuthDependency(required=True)

@router.post("/contact-us", response_model=ContactMessageResponse)
def contact_us(
    payload: ContactMessageRequest,
    db: Session = Depends(get_db),
    auth_dependency: AuthDependencyResponse = Depends(require_auth),
    account_settings_service: AccountSettingsService = Depends(),
):
    """
    Handle contact form submission from authenticated parent users.
    """
    if not auth_dependency.user_type == UserType.parent:
        raise AuthorizationError(
            message="Unauthorized access to parent contact form.",
            error_code=AppErrorCode.PERMISSION_DENIED,
        )
    
    return account_settings_service.handle_contact_message(
        parent_account=auth_dependency.account,
        payload=payload
    ) 