from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from core.exception_handling.exceptions.custom_exceptions import AuthorizationError
from db.database import get_db
from dependencies.auth_dependencies.auth_dependency import AuthDependency, AuthDependencyResponse
from ..schemas.response import AccountSettingsResponse
from ..services.account_settings_service import AccountSettingsService
from core.exception_handling.exceptions.custom_exceptions import AppErrorCode
from dependencies.auth_dependencies.base_auth_dependency import UserType

router = APIRouter()
require_auth = AuthDependency(required=True)

@router.get("/get-account-settings", response_model=AccountSettingsResponse)
def get_account_settings(
    db: Session = Depends(get_db),
    auth_dependency: AuthDependencyResponse = Depends(require_auth),
    account_settings_service: AccountSettingsService = Depends(),
):
    """
    Retrieve the account settings for the currently authenticated parent.
    """
    if not auth_dependency.user_type == UserType.parent:
        raise AuthorizationError(
            message="Unauthorized access to parent account settings.",
            error_code=AppErrorCode.PERMISSION_DENIED,
        )
    
    return account_settings_service.get_account_settings(db, parent_account=auth_dependency.account)