from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from db.database import get_db
from dependencies.auth_dependencies.auth_dependency import AuthDependency, AuthDependencyResponse
from ..schemas.request import UpdateAccountSettingsRequest
from ..schemas.response import AccountSettingsResponse
from dependencies.auth_dependencies.base_auth_dependency import UserType
from ..services.account_settings_service import AccountSettingsService
from core.exception_handling.exceptions.custom_exceptions import AuthorizationError
from core.exception_handling.exceptions.custom_exceptions import AppErrorCode

router = APIRouter()
require_auth = AuthDependency(required=True)

@router.put("/update-account-settings", response_model=AccountSettingsResponse)
def update_account_settings(
    payload: UpdateAccountSettingsRequest,
    db: Session = Depends(get_db),
    auth_dependency: AuthDependencyResponse = Depends(require_auth),
    account_settings_service: AccountSettingsService = Depends(),
):
    """
    Update parts of the parent's account settings. Currently supports updating default language.
    """
    if not auth_dependency.user_type == UserType.parent:
        raise AuthorizationError(
            message="Unauthorized access to parent account settings.",
            error_code=AppErrorCode.PERMISSION_DENIED,
        )
        
    return account_settings_service.update_account_settings(db, parent_account=auth_dependency.account, payload=payload)