from sqlalchemy.orm import Session as SyncSession, selectinload
from sqlalchemy import select
from typing import List, Optional
from loguru import logger
from fastapi import Depends # Added for AuthMailerService dependency

from db.models import (
    Account, Year, ActiveSubscription, ActiveSubscriptionPlanLink,  
    Subject, PlanSelectedSubject, SubscriptionOption
)
from db.models.subscription import PlanTypeEnum, SubscriptionStatusType
from ..schemas.response import (
    AccountSettingsResponse, ChildResponse, SchoolYearResponse,
    ParentActiveSubscriptionResponse, SubscriptionPlanComponentResponse, SubscribedSubjectDetailResponse,
    ContactMessageResponse # Added
)
from ..schemas.request import UpdateAccountSettingsRequest, ContactMessageRequest # Added
from core.exception_handling.exceptions.custom_exceptions import ValidationError, ServiceError # Added ServiceError
from api.v1.common.schemas import AppErrorCode
from db.models.account import LanguageEnum
from db.models import ChildAccount
from services.mailer.auth_mailer_service import AuthMailerService # Added
from dependencies.email_dependency import get_auth_mailer_service # Added

class AccountSettingsService:
    def __init__(self, auth_mailer_service: AuthMailerService = Depends(get_auth_mailer_service)): # Injected AuthMailerService
        self.auth_mailer_service = auth_mailer_service

    def _get_subscribed_subject_details(self, subject: Subject) -> SubscribedSubjectDetailResponse:
        return SubscribedSubjectDetailResponse(
            public_id=subject.public_id,
            name=subject.name,
            year_public_id=subject.year.public_id if subject.year else None,
            year_name=subject.year.name if subject.year else None
        )

    def get_account_settings(self, db: SyncSession, parent_account: Account) -> AccountSettingsResponse:
        logger.info("Fetching account settings for parent_id: {} (public_id: {})",
                    parent_account.id, parent_account.public_id)

        children_query_result = (
            db.query(ChildAccount)
            .filter(ChildAccount.parent_account_id == parent_account.id)
            .options(selectinload(ChildAccount.year))
            .all()
        )

        children_response_list: List[ChildResponse] = []
        if children_query_result:
            for child_db in children_query_result:
                school_year_for_child_resp = SchoolYearResponse(public_id="N/A", name="Not Assigned")
                if child_db.year:
                    school_year_for_child_resp = SchoolYearResponse(
                        public_id=child_db.year.public_id,
                        name=child_db.year.name
                    )
                else:
                    logger.warning(
                        "Child account public_id: {} has no school year assigned.",
                        child_db.public_id,
                    )

                children_response_list.append(
                    ChildResponse(
                        public_id=child_db.public_id,
                        name=child_db.name,
                        email=child_db.email,
                        school_year=school_year_for_child_resp,
                        pin=child_db.pin
                    )
                )
        else:
            logger.info("No children found for parent_id: {} (public_id: {})",
                        parent_account.id, parent_account.public_id)

        parent_active_subscription_response: Optional[ParentActiveSubscriptionResponse] = None

        stmt_active_sub = (
            select(ActiveSubscription)
            .options(
                selectinload(ActiveSubscription.plan_links).options(
                    selectinload(ActiveSubscriptionPlanLink.subscription_option).options(
                        selectinload(SubscriptionOption.year).options(
                            selectinload(Year.subjects).selectinload(Subject.year)
                        )
                    ),
                    selectinload(ActiveSubscriptionPlanLink.selected_subjects).options(
                        selectinload(PlanSelectedSubject.subject).selectinload(Subject.year)
                    )
                )
            )
            .where(ActiveSubscription.parent_account_id == parent_account.id)
            .where(ActiveSubscription.status.in_([SubscriptionStatusType.ACTIVE, SubscriptionStatusType.TRIALING]))
            .order_by(ActiveSubscription.created_at.desc())
        )
        # Handle multiple subscriptions by taking the most recent one
        active_sub_db = db.execute(stmt_active_sub).scalars().first()

        if active_sub_db and active_sub_db.plan_links:
            logger.info("Found active subscription {} with {} plan links for parent {}",
                        active_sub_db.public_id, len(active_sub_db.plan_links), parent_account.public_id)
            plan_components_response_list: List[SubscriptionPlanComponentResponse] = []

            for plan_link in active_sub_db.plan_links:
                granted_subjects_list: List[SubscribedSubjectDetailResponse] = []

                subscription_option = plan_link.subscription_option
                if not subscription_option:
                    logger.warning(f"Skipping plan link {plan_link.public_id} due to missing subscription_option")
                    continue

                if not subscription_option.year:
                    logger.warning(
                        f"Skipping plan link {plan_link.public_id}"
                        f"because its SubscriptionOption {subscription_option.public_id}"
                        f"has no associated Year loaded.")
                    continue

                plan_type = plan_link.chosen_plan_type 
                plan_name = f"{subscription_option.year.name} - {plan_type.value}" 
                plan_public_id = subscription_option.public_id 
                billing_period = plan_link.chosen_billing_period 

                if plan_type == PlanTypeEnum.SC: 
                    if plan_link.selected_subjects:
                        for user_selected_subject in plan_link.selected_subjects:
                            if user_selected_subject.subject and user_selected_subject.subject.is_active:
                                granted_subjects_list.append(
                                    self._get_subscribed_subject_details(user_selected_subject.subject))
                            elif not user_selected_subject.subject:
                                logger.warning(
                                    f"PlanSelectedSubject entry"
                                    f"for plan link {plan_link.public_id}"
                                    f"has no subject loaded.")

                elif plan_type == PlanTypeEnum.YF:
                    if subscription_option.year.subjects:
                        for subject_in_year in subscription_option.year.subjects:
                            if subject_in_year.is_active:
                                granted_subjects_list.append(self._get_subscribed_subject_details(subject_in_year))
                    else:
                        logger.warning(
                            f"SubscriptionOption {subscription_option.public_id}"
                            f"for YF plan link {plan_link.public_id}"
                            f"has no subjects loaded for its year.")

                plan_components_response_list.append(
                    SubscriptionPlanComponentResponse(
                        plan_link_public_id=plan_link.public_id,
                        plan_public_id=plan_public_id, 
                        plan_name=plan_name, 
                        plan_type=plan_type.value, 
                        billing_period=billing_period.value,
                        granted_subjects=granted_subjects_list
                    )
                )

            parent_active_subscription_response = ParentActiveSubscriptionResponse(
                subscription_public_id=active_sub_db.public_id,
                status=active_sub_db.status.value, 
                current_period_end=active_sub_db.current_period_end,
                cancel_at_period_end=active_sub_db.cancel_at_period_end,
                components=plan_components_response_list
            )
        else:
            logger.info("No active or trialing subscription found for parent {}", parent_account.public_id)

        all_school_years_db = db.execute(select(Year)).scalars().all()
        all_school_years_response_list: List[SchoolYearResponse] = [
            SchoolYearResponse(public_id=sy.public_id, name=sy.name) for sy in all_school_years_db
        ]

        logger.info("Successfully retrieved account settings for parent: {}", parent_account.public_id)
        return AccountSettingsResponse(
            email=parent_account.email,
            default_language=parent_account.language.value, 
            children=children_response_list,
            active_subscription=parent_active_subscription_response,
            school_years=all_school_years_response_list
        )

    def update_account_settings(
        self, db: SyncSession, parent_account: Account, payload: UpdateAccountSettingsRequest
    ) -> AccountSettingsResponse:
        logger.info("Updating account settings for parent_id: {} with payload: {}",
                    parent_account.id, payload.model_dump_json(exclude_none=True))

        try:
            parent_account.language = LanguageEnum(payload.default_language)
        except ValueError:
            logger.warning("Invalid language value '{}' provided for parent {}. Update failed for language.",
                           payload.default_language, parent_account.public_id)
            raise ValidationError(
                message=f"Invalid language code: {payload.default_language}", error_code=AppErrorCode.INVALID_REQUEST)

        db.add(parent_account)
        db.commit()
        db.refresh(parent_account)

        logger.info("Successfully updated account settings for parent_id: {} with new language: {}",
                    parent_account.id, payload.default_language)

        return self.get_account_settings(db, parent_account)

    def handle_contact_message(
        self,
        parent_account: Account,
        payload: ContactMessageRequest
    ) -> ContactMessageResponse:
        logger.info(
            "Handling contact message from parent_id: {} (email: {}) with name: {}",
            parent_account.id,
            parent_account.email,
            payload.name,
        )

        try:
            self.auth_mailer_service.send_internal_contact_submission_email(
                user_name=payload.name,
                user_email=parent_account.email, # Use authenticated parent's email
                user_message=payload.message,
                lang=parent_account.language.value # Use parent's language for the internal email context if desired
            )
            logger.info(
                "Contact message from {} successfully processed and email sent to support.",
                parent_account.email,
            )
            return ContactMessageResponse(message="Message sent successfully.")
        except Exception as e:
            logger.error(
                "Failed to send contact message email for parent {}: {}",
                parent_account.email,
                str(e),
            )
            # Depending on the exception, you might want to raise a more specific error
            # For now, a generic ServiceError
            raise ServiceError(
                message="Failed to send your message. Please try again later.",
                log_message=f"Error in handle_contact_message for {parent_account.email}: {str(e)}",
                error_code=AppErrorCode.SERVICE_ERROR
            )