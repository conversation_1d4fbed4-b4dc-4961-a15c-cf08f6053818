from jose import jwt
from datetime import datetime, timedelta, UTC
from core.config.settings import settings
from api.v1.common.schemas import AppErrorCode
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError
import bcrypt

def create_access_token(data, expires_delta=None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(UTC) + expires_delta
    else:
        expire = datetime.now(UTC) + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
    return encoded_jwt


def decode_jwt_token(token: str) -> dict:
    try:
        decoded_token = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        return decoded_token
    except jwt.JWTError as e:
        print("Invalid authentication token", e)
        raise AuthenticationError(
            message="Invalid authentication token.",
            error_code=AppErrorCode.AUTHENTICATION_ERROR
        )

def check_auth_lock(auth_security):
    """
    Checks if an account is currently locked due to too many failed PIN attempts.

    This function examines the AccountAuthSecurity record to determine if the account
    is currently in a locked state. If locked, it calculates the remaining lock time.

    Args:
        auth_security: The AccountAuthSecurity record for the account being checked

    Returns:
        dict: A status dictionary with the following keys:
            - "status": Either "locked" or "unlocked"
            - If locked, additional keys include:
                - "message": A user-friendly message
                - "retry_after_minutes": Minutes until the lock expires
                - "status_code": HTTP status code (429 Too Many Requests)
                - "error_type": Error identifier for the frontend
    """
    if auth_security.locked_until and datetime.now(UTC) < auth_security.locked_until:
        remaining = (auth_security.locked_until - datetime.now(UTC)).total_seconds()

        return {
            "status": "locked",
            "message": "Account locked",
            "retry_after_minutes": int(remaining // 60),
            "status_code": 429,
            "error_type": "account_locked"
        }
    return {
        "status": "unlocked"
    }

def update_failed_attempt(auth_security):
    """
    Updates the failed attempt counter and applies lockout if necessary.

    This function is called when a PIN verification fails. It increments the
    failed attempts counter and applies a lockout based on the PIN_ATTEMPT_LIMITS
    configuration in settings. The lockout duration increases with more failed attempts.

    The PIN_ATTEMPT_LIMITS is a list of tuples in the format [(attempts, minutes), ...]
    For example, with [(6, 1), (10, 5), (15, 15), (20, 60)]:
    - After 6 attempts: 1 minute lockout
    - After 10 attempts: 5 minute lockout
    - After 15 attempts: 15 minute lockout
    - After 20 attempts: 60 minute lockout

    Args:
        auth_security: The AccountAuthSecurity record to update

    Returns:
        The updated auth_security object (with failed_attempts incremented
        and possibly locked_until set)
    """
    auth_security.failed_attempts += 1
    auth_security.last_failed_attempt = datetime.now(UTC)

    # Find matching threshold - we use the highest applicable threshold
    # The list is processed in order, so later entries (with higher attempt counts)
    # will overwrite earlier ones if multiple thresholds are met
    for attempts, minutes in settings.PIN_ATTEMPT_LIMITS:
        if auth_security.failed_attempts >= attempts:
            auth_security.locked_until = datetime.now(UTC) + timedelta(minutes=minutes)

    return auth_security

def hash_parent_pin(pin: str) -> str:
    if len(pin) != 6 or not pin.isdigit():
        raise ValueError("PIN must be 6 digits")
    return bcrypt.hashpw(pin.encode(), bcrypt.gensalt()).decode()

def verify_parent_pin(pin: str, account_pin_hash: str) -> bool:
    if len(pin) != 6 or not pin.isdigit():
        return False
    return bcrypt.checkpw(pin.encode(), account_pin_hash.encode()) 
