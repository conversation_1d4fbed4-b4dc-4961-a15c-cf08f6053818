from pydantic import BaseModel, Field
from typing import Optional

class LoginRequest(BaseModel):
    email: str
    login_pin: str = Field(..., min_length=4, max_length=4, pattern=r'^\d+$')

class SignupRequest(BaseModel):
    name: str
    email: str
    pin: str = Field(..., min_length=4, max_length=4, pattern=r'^\d+$')
    language: str
    school_year_public_id: str
    searchparams: str

class ForgotPinRequest(BaseModel):
    email: str

class VerifyAccountRequest(BaseModel):
    email: str
    verification_code: str
    
class ResendVerificationCodeRequest(BaseModel):
    email: str
    language: str

class AssignParentRequest(BaseModel):
    parent_email: str
    language: str
    
class CheckVerificationStatusRequest(BaseModel):
    email: str
    language: str

class VerifyParentAssignmentRequest(BaseModel):
    email: str
    login_pin: Optional[str] = Field(None, min_length=6, max_length=6, pattern=r'^\d+$')
    verification_code: str