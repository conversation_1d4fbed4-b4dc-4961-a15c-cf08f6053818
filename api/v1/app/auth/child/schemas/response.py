from pydantic import BaseModel
from typing import Optional, List
from api.v1.app.auth.schemas.shared_schemas import User, SubscriptionDetails, YearSubscription # Updated imports

class LoginResponse(BaseModel):
    access_token: str 
    user: User
    subscriptions: List[YearSubscription] = []

class ValidateSessionResponse(BaseModel):
    access_token: str
    user: User
    subscriptions: List[YearSubscription] = []

class ForgotPinResponse(BaseModel):
    message: str


class VerifyAccountResponse(BaseModel):
    message: str

class ResendVerificationCodeResponse(BaseModel):
    message: str


class AssignParentResponse(BaseModel):
    message: str
    existing_parent_account: Optional[bool] = None


class CheckVerificationStatusResponse(BaseModel):
    is_verified: bool


class SignupResponse(BaseModel):
    access_token: str
    user: User
    subscriptions: List[YearSubscription] = []

class VerifyParentAssignmentResponse(BaseModel):
    message: str
    public_id: str 
    access_token: str 
    refresh_token: Optional[str] 
    user: User 
    subscriptions: List[YearSubscription] = []