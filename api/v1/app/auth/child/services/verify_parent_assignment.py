from sqlalchemy.orm import Session
from sqlalchemy import select
from datetime import <PERSON><PERSON><PERSON>
from typing import Optional, List
from loguru import logger

from db.models import Account, ChildAccount, ParentChildAssignment
from db.models.account import LanguageEnum as DbLanguageEnum
from ..schemas.request import VerifyParentAssignmentRequest
from ..schemas.response import VerifyParentAssignmentResponse
# Updated imports for new response structure
from api.v1.app.auth.schemas.shared_schemas import UserTypeEnum, ParentUser, SubscriptionDetails
# Import the utility for fetching subscriptions
from api.v1.app.auth.parent.services.subscription_utils import get_formatted_raw_subscriptions, RawSubscription, get_formatted_subscriptions_by_year

from core.exception_handling.exceptions.custom_exceptions import NotFoundError, ServiceError
from api.v1.common.schemas import AppErrorCode
from api.v1.app.auth.utils import create_access_token

from api.v1.app.auth.parent.utils.account_creation import create_parent_account

from core.config.settings import settings

from services.mailer.auth_mailer_service import AuthMailerService
from services.email_list.service import ListService
from fastapi import BackgroundTasks

ACCESS_TOKEN_EXPIRE_MINUTES = int(settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)


class ParentVerificationService:
    def __init__(self, db: Session):
        self.db = db

    def verify_parent_account(
        self,
        parent_account_request: VerifyParentAssignmentRequest,
        auth_mailer_service: AuthMailerService,
        list_service: ListService,
        background_tasks: BackgroundTasks
    ) -> VerifyParentAssignmentResponse:
        logger.info(f"Verifying parent assignment for parent email:\n"
                    f"{parent_account_request.email}\n"
                    f"with code: {parent_account_request.verification_code}")

        stmt_existing_account = select(Account).filter(
            Account.email == parent_account_request.email
        )
        existing_account: Optional[Account] = self.db.execute(stmt_existing_account).scalar_one_or_none()

        stmt_assignment = select(ParentChildAssignment).filter(
            ParentChildAssignment.parent_email == parent_account_request.email,
            ParentChildAssignment.verification_code == parent_account_request.verification_code
        )
        relevant_parent_assignment: Optional[ParentChildAssignment] = self.db.execute(stmt_assignment).scalar_one_or_none()

        if relevant_parent_assignment is None:
            logger.warning(f"Invalid verification code or assignment not found for parent email:\n"
                           f"{parent_account_request.email}\n"
                           f"with code: {parent_account_request.verification_code}")
            raise NotFoundError(
                message="Invalid verification code or assignment not found.",
                error_code=AppErrorCode.ASSIGNMENT_NOT_FOUND_OR_INVALID_CODE,
                entity_name="ParentChildAssignment",
                identifier=parent_account_request.email
            )

        if existing_account is not None:
            logger.info("Handling parent assignment for existing parent account:\n"
                        f"{existing_account.email}")
            return self._handle_existing_account(
                existing_account, 
                relevant_parent_assignment
            )
        else:
            logger.info("Handling parent assignment by creating new parent account for email:\n"
                        f"{relevant_parent_assignment.parent_email}")
            login_pin_from_request = getattr(parent_account_request, 'login_pin', None)
            if not login_pin_from_request:
                logger.warning("login_pin not provided for new parent account\n"
                               "creation via child assignment for {}\n"
                               "Account will be created without a PIN\n"
                               "if create_parent_account allows.", parent_account_request.email)

            return self._handle_new_account(
                parent_email=relevant_parent_assignment.parent_email,
                language=str(relevant_parent_assignment.language.value), # Ensure language is string
                login_pin=login_pin_from_request,
                parent_assignment=relevant_parent_assignment,
                auth_mailer_service=auth_mailer_service,
                list_service=list_service,
                background_tasks=background_tasks
            )

    def _handle_existing_account(
        self, 
        existing_account: Account, 
        parent_assignment: ParentChildAssignment
    ) -> VerifyParentAssignmentResponse:
        """Handle verification for an existing parent account"""
        if not existing_account.is_verified:
            existing_account.is_verified = True
            logger.info("Marked existing parent account {} as verified.", existing_account.email)

        stmt_child = select(ChildAccount).filter(
            ChildAccount.id == parent_assignment.child_account_id
        )
        relevant_child_account: Optional[ChildAccount] = self.db.execute(stmt_child).scalar_one_or_none()

        if not relevant_child_account:
            self.db.rollback()
            logger.error("Associated child account (ID: {}) not found for parent assignment.",
                         parent_assignment.child_account_id)
            raise NotFoundError(
                message="Associated child account not found.",
                error_code=AppErrorCode.CHILD_ACCOUNT_NOT_FOUND,
                entity_name="ChildAccount",
                identifier=str(parent_assignment.child_account_id) # Ensure identifier is string
            )

        relevant_child_account.parent_account_id = existing_account.id
        if not relevant_child_account.is_verified:
            relevant_child_account.is_verified = True
            logger.info("Marked child account {} as verified during parent linking.", relevant_child_account.email)

        logger.info("Linked child {} to parent {}", relevant_child_account.public_id, existing_account.public_id)

        self.db.delete(parent_assignment)
        self.db.commit()
        logger.info("Deleted parent assignment for parent {} and child {}",
                    existing_account.email, relevant_child_account.email)

        db_language = existing_account.language
        api_language = DbLanguageEnum(db_language.value if db_language else 'lu')

        user_payload = ParentUser(
            public_id=str(existing_account.public_id),
            email=existing_account.email,
            first_name=None,
            language=api_language,
            user_type=UserTypeEnum.PARENT
        )

        token_data = {
            "account_public_id": str(existing_account.public_id),
            "user_type": "parent",
            "email": existing_account.email,
            "language": api_language.value,
        }
        access_token = create_access_token(
            data=token_data,
            expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        )

        subscriptions_api = get_formatted_subscriptions_by_year(self.db, existing_account.id)

        # Delete the assignment entry
        self.db.delete(parent_assignment)
        self.db.commit()
        logger.info("Deleted parent assignment for parent {} and child {}",
                    existing_account.email, relevant_child_account.email)

        return VerifyParentAssignmentResponse(
            message="Account linked successfully.",
            public_id=str(existing_account.public_id), # Kept for backward compatibility if some intermediate step relies on it
            access_token=access_token,
            refresh_token=None,
            user=user_payload,
            subscriptions=subscriptions_api
        )

    def _handle_new_account(
        self,
        parent_email: str,
        language: str, # Expect string from ParentChildAssignment.language
        login_pin: Optional[str],
        parent_assignment: ParentChildAssignment,
        auth_mailer_service: AuthMailerService,
        list_service: ListService,
        background_tasks: BackgroundTasks
    ) -> VerifyParentAssignmentResponse:
        """Create and link a new parent account"""
        try:
            new_account_orm, access_token = create_parent_account( # Renamed from new_account_entry
                db=self.db,
                email=parent_email,
                language=language, # Pass as string
                pin=login_pin,
                is_verified=True, 
                auth_mailer_service=auth_mailer_service,
                list_service=list_service,
                background_tasks=background_tasks,
                signup_source='child_assignment',
                is_auth_migrated=True, 
                child_account_id=parent_assignment.child_account_id 
            )

            # create_parent_account does not commit, so we commit after linking.
            stmt_child = select(ChildAccount).where(ChildAccount.id == parent_assignment.child_account_id)
            relevant_child_account: Optional[ChildAccount] = self.db.execute(stmt_child).scalar_one_or_none()
            if not relevant_child_account:
                # This should ideally not happen if create_parent_account succeeded with child_account_id
                self.db.rollback()
                raise NotFoundError(
                    message="Associated child account not found after parent creation.",
                    error_code=AppErrorCode.CHILD_ACCOUNT_NOT_FOUND,
                    entity_name="ChildAccount",
                    identifier=str(parent_assignment.child_account_id)
                )

            relevant_child_account.parent_account_id = new_account_orm.id
            if not relevant_child_account.is_verified: # Also verify child if not already
                relevant_child_account.is_verified = True

            self.db.delete(parent_assignment)
            self.db.commit() # Commit all changes: new parent, child link, assignment deletion
            logger.info("New parent account {} \n"
                        "created and linked via child assignment. Assignment record deleted.", parent_email)

            db_language = new_account_orm.language
            api_language = DbLanguageEnum(db_language.value if db_language else 'lu')

            user_payload = ParentUser(
                public_id=str(new_account_orm.public_id),
                email=new_account_orm.email,
                first_name=None,
                language=api_language,
                user_type=UserTypeEnum.PARENT
            )

            subscriptions_api = get_formatted_subscriptions_by_year(self.db, new_account_orm.id)

        except ServiceError as se: 
            self.db.rollback()
            logger.error("ServiceError creating new parent account via child assignment for {}: {}",
                         parent_email, se.log_message, exc_info=True)
            raise se 
        except Exception as e: 
            self.db.rollback()
            logger.error("Unexpected error creating new parent account via child assignment for {}: {}",
                         parent_email, str(e), exc_info=True)
            raise ServiceError(
                message="Could not register account",
                log_message=f"Unexpected issue creating parent account via child assignment: {str(e)}",
                error_code=AppErrorCode.SERVICE_ERROR
            )
        
        # Delete the assignment entry
        self.db.delete(parent_assignment)
        self.db.commit()
        logger.info("Deleted parent assignment for parent {} and child {}",
                    parent_email, relevant_child_account.email)

        return VerifyParentAssignmentResponse(
            message="Account created and linked successfully.",
            public_id=str(new_account_orm.public_id), # Kept for backward compatibility
            access_token=access_token,
            refresh_token=None,
            user=user_payload,
            subscriptions=subscriptions_api
        )