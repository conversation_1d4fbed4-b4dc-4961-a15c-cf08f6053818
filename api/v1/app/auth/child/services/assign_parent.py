from sqlalchemy import select
from sqlalchemy.orm import Session
from fastapi import BackgroundTasks
import random
from loguru import logger

from db.models import Account, ParentChildAssignment, ChildAccount
from ..schemas.request import AssignParentRequest
from ..schemas.response import AssignParentResponse
from core.exception_handling.exceptions.custom_exceptions import NotFoundError, ConflictError
from api.v1.common.schemas import AppErrorCode
from services.mailer.auth_mailer_service import AuthMailerService
from api.v1.app.auth.parent.services.email_rate_limiter import EmailRateLimiterService

class ParentAssignmentService:
    def __init__(self, db: Session, auth_mailer_service: AuthMailerService):
        self.db = db
        self.auth_mailer_service = auth_mailer_service


    def assign_parent(
        self, new_assignment: AssignParentRequest, child_public_id: str, background_tasks: BackgroundTasks
    ) -> AssignParentResponse:
        logger.info("Attempting to assign parent {} to child {}", new_assignment.parent_email, child_public_id)
        
        # Check rate limiting before processing
        rate_limiter = EmailRateLimiterService(self.db)
        rate_limiter.check_and_update_rate_limit(new_assignment.parent_email, "parent_assignment")
        
        # Get child account based on public id
        stmt = select(ChildAccount).filter(
            ChildAccount.public_id == child_public_id)
        relevant_child_account = self.db.execute(stmt).scalar_one_or_none()

        if not relevant_child_account:
            logger.warning("Child account not found with public_id: {}", child_public_id)
            raise NotFoundError(message="Child account not found", error_code=AppErrorCode.CHILD_ACCOUNT_NOT_FOUND,
                                entity_name="ChildAccount", identifier=child_public_id)

        # Check if parent account exists
        stmt = select(Account).filter(
            Account.email == new_assignment.parent_email)
        relevant_parent_account = self.db.execute(stmt).scalar_one_or_none()

        # First, check if the child is already linked to a parent
        if relevant_child_account.parent_account_id:
            logger.warning("Child {} is already linked to a parent account (ID: {}), cannot assign new parent {}",
                          child_public_id, relevant_child_account.parent_account_id, new_assignment.parent_email)
            raise ConflictError(
                message="Child account is already linked to a parent",
                error_code=AppErrorCode.CHILD_ALREADY_HAS_PARENT
            )

        # Check if there's a pending assignment for this specific parent-child combination
        stmt = select(ParentChildAssignment).filter(
            ParentChildAssignment.child_account_id == relevant_child_account.id,
            ParentChildAssignment.parent_email == new_assignment.parent_email
        )
        existing_assignment = self.db.execute(stmt).scalar_one_or_none()

        if existing_assignment:
            logger.info("Reusing existing pending assignment for child {} and parent {}",
                        child_public_id, new_assignment.parent_email)
            # Reuse existing assignment
            background_tasks.add_task(
                self.auth_mailer_service.send_child_parent_assignment_email,
                to=new_assignment.parent_email,
                verification_code=existing_assignment.verification_code,
                lang=new_assignment.language
            )
        else:
            logger.info("Creating new parent assignment for child {} and parent {}",
                        child_public_id, new_assignment.parent_email)
            # Create new assignment with 6-digit verification code
            verification_code = str(random.randint(100000, 999999))
            new_temp_assignment = ParentChildAssignment(
                parent_email=new_assignment.parent_email, 
                child_account_id=relevant_child_account.id, 
                verification_code=verification_code,
                language=new_assignment.language
            )
            self.db.add(new_temp_assignment)
            self.db.commit()

            background_tasks.add_task(
                self.auth_mailer_service.send_child_parent_assignment_email,
                to=new_assignment.parent_email,
                verification_code=verification_code,
                lang=new_assignment.language
            )
        logger.info("Parent assignment verification email initiated for parent {} and child {}",
                    new_assignment.parent_email, child_public_id)
        return AssignParentResponse(
            message="Verification email sent", 
            existing_parent_account=relevant_parent_account is not None
        )