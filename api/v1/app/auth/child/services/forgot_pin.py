from sqlalchemy import select
from sqlalchemy.orm import Session
from fastapi import BackgroundTasks
from loguru import logger

from db.models import ChildAccount
from ..schemas.request import ForgotPinRequest
from ..schemas.response import ForgotPinResponse
from core.exception_handling.exceptions.custom_exceptions import NotFoundError
from api.v1.common.schemas import AppErrorCode
from services.mailer.auth_mailer_service import AuthMailerService
from api.v1.app.auth.parent.services.email_rate_limiter import EmailRateLimiterService

class ForgotPinService:
    def __init__(self, db: Session, auth_mailer_service: AuthMailerService):  
        self.db = db
        self.auth_mailer_service = auth_mailer_service

    def process_forgot_pin(
        self,
        forgot_pin_request: ForgotPinRequest,
        background_tasks: BackgroundTasks
    ) -> ForgotPinResponse:
        """Process forgotten PIN request and send email with PIN"""
        logger.info("Processing forgot PIN request for child: {}", forgot_pin_request.email)
        
        # Check rate limiting before processing
        rate_limiter = EmailRateLimiterService(self.db)
        rate_limiter.check_and_update_rate_limit(forgot_pin_request.email, "child_forgot_pin")
        
        # Find the child account by email
        stmt = select(ChildAccount).where(
            ChildAccount.email == forgot_pin_request.email)
        relevant_child_account = self.db.execute(stmt).scalar_one_or_none()

        # Validate account exists
        if relevant_child_account is None:
            logger.warning("Forgot PIN request for non-existent child account: {}", forgot_pin_request.email)
            # Note: For security, we might still return a generic success message to prevent account enumeration.
            # However, the brief implies raising an error.
            raise NotFoundError(
                message="Could not find email",  # User-facing
                log_message=f"Could not find child account with email {forgot_pin_request.email} for PIN reset.", 
                error_code=AppErrorCode.CHILD_ACCOUNT_NOT_FOUND,
                entity_name="ChildAccount",
                identifier=forgot_pin_request.email
            )

        # Send email with PIN in background
        # Ensure language is a string value, not an enum
        child_language = getattr(relevant_child_account, 'language', None)
        lang_to_use = child_language.value if child_language else 'en'

        background_tasks.add_task(
            self.auth_mailer_service.send_child_forgot_pin_email,
            to=relevant_child_account.email,
            pin=relevant_child_account.pin,
            lang=lang_to_use
        )
        logger.info("Forgot PIN email initiated for child: {}", relevant_child_account.email)
        return ForgotPinResponse(message="Pin code sent")