import random
from sqlalchemy import select
from sqlalchemy.orm import Session
from fastapi import BackgroundTasks
from loguru import logger

from db.models import ChildAccount
from ..schemas.request import CheckVerificationStatusRequest
from ..schemas.response import CheckVerificationStatusResponse
from core.exception_handling.exceptions.custom_exceptions import NotFoundError
from api.v1.common.schemas import AppErrorCode
from services.mailer.auth_mailer_service import AuthMailerService
from api.v1.app.auth.parent.services.email_rate_limiter import EmailRateLimiterService


class VerificationStatusService:
    def __init__(self, db: Session, auth_mailer_service: AuthMailerService):
        self.db = db
        self.auth_mailer_service = auth_mailer_service
    
    def check_verification_status(
        self,
        request: CheckVerificationStatusRequest,
        background_tasks: BackgroundTasks
    ) -> CheckVerificationStatusResponse:
        """Check verification status of a child account"""
        input_email = request.email.strip().lower()
        logger.info(f"Checking verification status for child: {input_email}")

        # Check if child account exists
        stmt = select(ChildAccount).filter(
            ChildAccount.email == input_email)
        child_account = self.db.execute(stmt).scalar_one_or_none()
        
        if not child_account:
            logger.warning(f"Child account not found for email: {input_email}")
            raise NotFoundError(
                message="Child account not found",
                error_code=AppErrorCode.CHILD_ACCOUNT_NOT_FOUND,
                entity_name="ChildAccount",
                identifier=input_email
            )

        # Return verification status if already verified
        if child_account.is_verified:
            logger.info(f"Child account {input_email} is already verified.")
            return CheckVerificationStatusResponse(
                is_verified=True
            )

        # Generate verification code if none exists
        if not child_account.verification_code:
            child_account.verification_code = str(random.randint(100000, 999999))
            self.db.commit()
            logger.info(f"Generated new verification code for child {input_email}.")

        # Check rate limiting before sending email
        rate_limiter = EmailRateLimiterService(self.db)
        rate_limiter.check_and_update_rate_limit(input_email, "child_verification")
        
        # Send verification email in background
        logger.info(f"Child account {input_email} is not verified. Sending verification email.")
        background_tasks.add_task(
            self.auth_mailer_service.send_child_verification_email,
            to=input_email,
            verification_code=child_account.verification_code,
            lang=child_account.language
        )

        return CheckVerificationStatusResponse(
            is_verified=False
        )