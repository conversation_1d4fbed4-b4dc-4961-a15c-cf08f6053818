from sqlalchemy import select
from sqlalchemy.orm import Session
from fastapi import BackgroundTasks
import uuid
import random
from datetime import datetime, timedelta, UTC
import os
from loguru import logger
from typing import List, Optional

from db.models import ChildAccount, Year, Trial, Account
from db.models.account import LanguageEnum as DbLanguageEnum
from ..schemas.request import SignupRequest
from ..schemas.response import SignupResponse
# Updated imports for new response structure
from api.v1.app.auth.schemas.shared_schemas import UserTypeEnum, ChildUser, SubscriptionDetails, SchoolYearResponse, LanguageEnum
# Import the utility for fetching subscriptions (though likely empty for new child)
from api.v1.app.auth.parent.services.subscription_utils import get_formatted_raw_subscriptions, RawSubscription


from core.exception_handling.exceptions.custom_exceptions import ConflictError, NotFoundError, ServiceError
from api.v1.common.schemas import AppErrorCode
from services.mailer.auth_mailer_service import AuthMailerService
from api.v1.app.auth.parent.services.email_rate_limiter import EmailRateLimiterService
from api.v1.app.auth.utils import create_access_token 
from services.email_list.service import ListService, ListContext
from core.config.settings import settings


class ChildSignupService:
    def __init__(self, db: Session, auth_mailer_service: AuthMailerService, list_service: ListService):
        self.db = db
        self.auth_mailer_service = auth_mailer_service
        self.list_service = list_service

    def create_child_account(
        self, 
        new_child_account_request: SignupRequest, # Renamed for clarity
        background_tasks: BackgroundTasks
    ) -> SignupResponse:
        input_email = new_child_account_request.email.strip().lower()
        logger.info("Child signup attempt: {}, name: {}", input_email, new_child_account_request.name)
        
        # Check rate limiting before processing signup (which includes sending verification email)
        rate_limiter = EmailRateLimiterService(self.db)
        rate_limiter.check_and_update_rate_limit(input_email, "child_verification")

        stmt_child_exists = select(ChildAccount).where(
            ChildAccount.email == input_email
        )
        existing_child_account = self.db.execute(stmt_child_exists).scalar_one_or_none()

        if existing_child_account is not None:
            logger.warning("Child signup attempt with existing child email: {}", input_email)
            raise ConflictError(
                message="An account with this email already exists.",
                error_code=AppErrorCode.CHILD_ACCOUNT_ALREADY_EXISTS
            )

        stmt_parent_exists = select(Account).where(
            Account.email == input_email
        )
        existing_parent_account = self.db.execute(stmt_parent_exists).scalar_one_or_none()

        if existing_parent_account is not None:
            logger.warning("Child signup attempt with email already used by a parent account: {}", input_email)
            raise ConflictError(
                message="An account with this email is already registered as a parent.",
                error_code=AppErrorCode.PARENT_ACCOUNT_ALREADY_EXISTS
            )

        stmt_year = select(Year).where(
            Year.public_id == new_child_account_request.school_year_public_id
        )
        relevant_school_year = self.db.execute(stmt_year).scalar_one_or_none()

        if relevant_school_year is None:
            logger.warning("Child signup attempt with invalid school year public_id: {}",
                           new_child_account_request.school_year_public_id)
            raise NotFoundError(
                message="School year not found",
                error_code=AppErrorCode.SCHOOL_YEAR_NOT_FOUND,
                entity_name="Year",
                identifier=new_child_account_request.school_year_public_id
            )

        public_id = str(uuid.uuid4())
        verification_code = str(random.randint(100000, 999999))

        db_language_enum_val = DbLanguageEnum(new_child_account_request.language)


        new_account_orm = ChildAccount( # Renamed for clarity
            email=input_email,
            name=new_child_account_request.name,
            year_id=relevant_school_year.id,
            public_id=public_id,
            verification_code=verification_code,
            pin=new_child_account_request.pin, # Assuming PIN is stored as is for child, or hash if needed
            language=db_language_enum_val,
        )

        self.db.add(new_account_orm)
        self.db.flush()

        new_trial = Trial(
            child_account_id=new_account_orm.id,
            status='active',
            start_date=datetime.now(UTC),
            end_date=datetime.now(UTC) + timedelta(days=settings.TRIAL_DURATION_DAYS)
        )
        self.db.add(new_trial)

        try:
            self.db.commit()
            logger.info("Child account created:\n"
                        "Email: {}\n"
                        "Public ID: {}\n"
                        "Trial ID: {}",
                        input_email, public_id, new_trial.id)
        except Exception:
            self.db.rollback()
            logger.exception("Database commit failed during child signup for {}", input_email)
            raise ServiceError(message="Could not create account due to a database issue.",
                               error_code=AppErrorCode.SERVICE_ERROR)

        try: 
            if os.environ.get("ENVIRONMENT") != "dev":
                child_list_ctx = ListContext(email=input_email, language=new_child_account_request.language)
                self.list_service.add_child_to_lists(child_list_ctx)
                logger.info("Added child {} to mailing lists.", input_email)
        except Exception:
            logger.exception("Error adding child {} to mailerlite", input_email)

        try:
            background_tasks.add_task(
                self.auth_mailer_service.send_child_verification_email,
                to=input_email,
                verification_code=verification_code,
                lang=new_child_account_request.language,
            )
            logger.info("Child verification email initiated for {}", input_email)
        except Exception:
            logger.exception("Failed to enqueue child verification email for {}", input_email)

        # Construct payload
        school_year_response = SchoolYearResponse(
            public_id=relevant_school_year.public_id,
            name=relevant_school_year.name
        )
        
        user_payload = ChildUser(
            public_id=public_id,
            email=input_email,
            name=new_account_orm.name,
            language=LanguageEnum(new_child_account_request.language), # Convert to shared schema enum
            parent_public_id=None, # New child is not linked to a parent yet
            user_type=UserTypeEnum.CHILD,
            school_year=school_year_response
        )

        token_data = {
            "account_public_id": public_id,
            "user_type": "child", 
            "name": new_child_account_request.name,
            "parent_public_id": None,
            "language": new_child_account_request.language
        }

        access_token_expires = timedelta(minutes=settings.CHILD_SIGNUP_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data=token_data, expires_delta=access_token_expires
        )

        # Subscriptions will be empty for a new, unlinked child
        subscriptions_api: List[SubscriptionDetails] = []

        return SignupResponse(access_token=access_token, user=user_payload, subscriptions=subscriptions_api)