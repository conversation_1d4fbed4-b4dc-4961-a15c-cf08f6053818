from sqlalchemy import select
from sqlalchemy.orm import Session
from loguru import logger 

from db.models import ChildAccount
from ..schemas.request import VerifyAccountRequest
from ..schemas.response import VerifyAccountResponse
from core.exception_handling.exceptions.custom_exceptions import NotFoundError, ConflictError, ValidationError  
from api.v1.common.schemas import AppErrorCode 

class EmailVerificationService:
    def __init__(self, db: Session):
        self.db = db

    def verify_child_account(self, verify_request: VerifyAccountRequest) -> VerifyAccountResponse:
        """Handle child account email verification"""
        logger.info("Verifying child email: {}", verify_request.email)
        # Find child account by email
        stmt = select(ChildAccount).where(
            ChildAccount.email == verify_request.email)
        relevant_child_account = self.db.execute(stmt).scalar_one_or_none()

        # Validate account exists
        if relevant_child_account is None:
            logger.warning("Child account not found for verification: {}", verify_request.email)
            raise NotFoundError(
                message="Child account not found",
                error_code=AppErrorCode.CHILD_ACCOUNT_NOT_FOUND,
                entity_name="ChildAccount",
                identifier=verify_request.email
            )

        # Check if already verified
        if relevant_child_account.is_verified:
            logger.info("Child account {} is already verified.", verify_request.email)
            raise ConflictError(
                message="Account already verified",
                error_code=AppErrorCode.ACCOUNT_ALREADY_VERIFIED
            )

        # Validate verification code
        if relevant_child_account.verification_code != verify_request.verification_code:
            logger.warning("Incorrect verification code for child {}\n"
                           "Submitted: {}, Expected: {}",
                           verify_request.email,
                           verify_request.verification_code,
                           relevant_child_account.verification_code)
            raise ValidationError(
                message="Wrong verification code!",
                error_code=AppErrorCode.INVALID_VERIFICATION_CODE
            )

        # Mark account as verified
        relevant_child_account.is_verified = True
        self.db.commit()
        logger.info("Child account {} successfully verified.", verify_request.email)

        return VerifyAccountResponse(message="Successfully verified")