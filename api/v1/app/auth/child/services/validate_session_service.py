from sqlalchemy.orm import Session
from datetime import timed<PERSON><PERSON>
from loguru import logger
from typing import List, Optional

from db.models import ChildAccount, Account # Added Account import
from db.models.account import LanguageEnum as DbLanguageEnum
from api.v1.app.auth.utils import create_access_token
from ..schemas.response import ValidateSessionResponse
# Updated imports for new response structure
from api.v1.app.auth.schemas.shared_schemas import UserType<PERSON><PERSON>, ChildUser, SubscriptionDetails, SchoolYearResponse
# Import the utility for fetching subscriptions
from api.v1.app.auth.parent.services.subscription_utils import get_formatted_raw_subscriptions, RawSubscription, get_formatted_subscriptions_by_year
from core.config.settings import settings

class ChildValidateSessionService:
    def __init__(self, db: Session):
        self.db = db

    def validate_session(self, authenticated_child_account: ChildAccount) -> ValidateSessionResponse:
        logger.info(f"Validating session for child:\n"
                    f"{authenticated_child_account.email}\n"
                    f"public_id: {authenticated_child_account.public_id}")

        parent_account_public_id: Optional[str] = None
        parent_account_db_id: Optional[int] = None
        if authenticated_child_account.parent_account:
            parent_account_public_id = authenticated_child_account.parent_account.public_id
            parent_account_db_id = authenticated_child_account.parent_account.id

        db_language = authenticated_child_account.language
        api_language = DbLanguageEnum(db_language.value if db_language else 'lu')

        relevant_school_year = authenticated_child_account.year

        user_payload = ChildUser(
            public_id=authenticated_child_account.public_id,
            email=authenticated_child_account.email,
            name=authenticated_child_account.name,
            language=api_language,
            school_year=SchoolYearResponse(public_id=relevant_school_year.public_id, name=relevant_school_year.name),
            parent_public_id=parent_account_public_id,
            user_type=UserTypeEnum.CHILD
        )

        token_data = {
            "account_public_id": authenticated_child_account.public_id,
            "user_type": "child",
            "parent_public_id": parent_account_public_id,
            "name": authenticated_child_account.name,
            "language": api_language.value
        }
        access_token = create_access_token(
            data=token_data,
            expires_delta=timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
        )

        # Get subscriptions from parent if linked
        if parent_account_db_id:
            subscriptions_api = get_formatted_subscriptions_by_year(self.db, parent_account_db_id)
        else:
            subscriptions_api = []


        logger.info(f"Session validated and token re-issued for child:\n"
                    f"{authenticated_child_account.email}\n"
                    f"public_id: {authenticated_child_account.public_id}")
        return ValidateSessionResponse(
            access_token=access_token,
            user=user_payload,
            subscriptions=subscriptions_api
        )