from sqlalchemy import select
from sqlalchemy.orm import Session
from fastapi import BackgroundTasks
from loguru import logger  

from db.models import ChildAccount
from ..schemas.request import ResendVerificationCodeRequest
from ..schemas.response import ResendVerificationCodeResponse
from core.exception_handling.exceptions.custom_exceptions import NotFound<PERSON>rror, ConflictError, ServiceError  
from api.v1.common.schemas import AppErrorCode  
from services.mailer.auth_mailer_service import AuthMailerService
from api.v1.app.auth.parent.services.email_rate_limiter import EmailRateLimiterService

class ResendVerificationService:
    def __init__(self, db: Session, auth_mailer_service: AuthMailerService):
        self.db = db
        self.auth_mailer_service = auth_mailer_service

    def resend_code(
        self,
        request: ResendVerificationCodeRequest,
        background_tasks: BackgroundTasks
    ) -> ResendVerificationCodeResponse:
        """Resend verification code to child's email"""
        logger.info("Resending verification code to child: {}", request.email)
        
        # Check rate limiting before processing
        rate_limiter = EmailRateLimiterService(self.db)
        rate_limiter.check_and_update_rate_limit(request.email, "child_verification")
        
        # Find child account
        stmt = select(ChildAccount).where(
            ChildAccount.email == request.email)
        relevant_child_account = self.db.execute(stmt).scalar_one_or_none()

        # Validate account exists
        if not relevant_child_account:
            logger.warning("Child account not found for resend verification: {}", request.email)
            raise NotFoundError(
                message="Child account not found",
                error_code=AppErrorCode.CHILD_ACCOUNT_NOT_FOUND,
                entity_name="ChildAccount",
                identifier=request.email
            )

        if relevant_child_account.is_verified:
            logger.info("Child account {} is already verified. No code resent.", request.email)
            raise ConflictError(
                message="Account already verified",
                error_code=AppErrorCode.ACCOUNT_ALREADY_VERIFIED
            )

        if not relevant_child_account.verification_code:
            logger.error("No verification code found for unverified child account: {}", request.email)
            # This case should ideally be handled by generating a code if missing,
            # or ensuring it's always present for unverified accounts.
            raise ServiceError(
                message="No verification code found for this account.",
                log_message=f"No verification code on record for unverified child account {request.email}",
                error_code=AppErrorCode.SERVICE_ERROR  
            )

        # Send verification code in background
        # Ensure language is a string value, not an enum
        language = request.language or (relevant_child_account.language.value if relevant_child_account.language else None) or 'lu'
        
        background_tasks.add_task(
            self.auth_mailer_service.send_child_verification_email,
            to=relevant_child_account.email,
            verification_code=relevant_child_account.verification_code,
            lang=language  
        )
        logger.info("Child verification code resent to {}", relevant_child_account.email)
        return ResendVerificationCodeResponse(message="Verification code sent")