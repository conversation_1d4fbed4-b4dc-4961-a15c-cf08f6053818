from sqlalchemy import select
from sqlalchemy.orm import Session
from datetime import <PERSON><PERSON><PERSON>
from loguru import logger 
from typing import List, Optional

from db.models import ChildAccount, Account # Added Account import
from db.models.account import LanguageEnum as DbLanguageEnum
from ..schemas.request import LoginRequest
from ..schemas.response import LoginResponse 
# Updated imports for new response structure
from api.v1.app.auth.schemas.shared_schemas import UserTypeEnum, ChildUser, SubscriptionDetails, SchoolYearResponse
# Import the utility for fetching subscriptions
from api.v1.app.auth.parent.services.subscription_utils import get_formatted_raw_subscriptions, RawSubscription, get_formatted_subscriptions_by_year


from core.exception_handling.exceptions.custom_exceptions import AuthenticationError, NotFoundError  
from api.v1.common.schemas import AppErrorCode  
from api.v1.app.auth.utils import create_access_token 
from core.config.settings import settings

class ChildLoginService:
    def __init__(self, db: Session):
        self.db = db

    def login(self, child_account_request: LoginRequest) -> LoginResponse: 
        """Handle child account login logic"""
        logger.info(f"Child login attempt: {child_account_request.email}")

        stmt = select(ChildAccount).where(
            ChildAccount.email == child_account_request.email)
        relevant_child_account: Optional[ChildAccount] = self.db.execute(stmt).scalar_one_or_none()

        if relevant_child_account is None:
            logger.warning(f"Child login attempt for non-existent account: {child_account_request.email}")
            raise NotFoundError(
                message="Could not sign you in",
                log_message=f"Child account not found with email {child_account_request.email} during login.",
                error_code=AppErrorCode.CHILD_ACCOUNT_NOT_FOUND,
                entity_name="ChildAccount",
                identifier=child_account_request.email
            )

        if relevant_child_account.pin != child_account_request.login_pin:
            logger.warning(f"Incorrect PIN for child login: {child_account_request.email}")
            raise AuthenticationError(
                message="Could not sign you in",
                log_message=f"Incorrect PIN for child account {child_account_request.email}.",
                error_code=AppErrorCode.INVALID_CREDENTIALS
            )

        if relevant_child_account.is_verified is False:
            logger.warning(f"Child login attempt for unverified account: {child_account_request.email}")
            raise AuthenticationError(
                message="Account not verified",
                log_message=f"Child account {relevant_child_account.email} is not verified.",
                error_code=AppErrorCode.ACCOUNT_NOT_VERIFIED
            )

        db_language = relevant_child_account.language
        api_language = DbLanguageEnum(db_language.value if db_language else 'lu')

        relevant_school_year = relevant_child_account.year

        user_payload = ChildUser(
            public_id=relevant_child_account.public_id,
            email=relevant_child_account.email,
            name=relevant_child_account.name,
            language=api_language,
            parent_public_id=relevant_child_account.parent_account.public_id if relevant_child_account.parent_account else None,
            school_year=SchoolYearResponse(
                public_id=relevant_school_year.public_id,
                name=relevant_school_year.name,
            ),
            user_type=UserTypeEnum.CHILD
        )

        token_data = {
            "account_public_id": relevant_child_account.public_id, 
            "user_type": "child",
            "parent_public_id": relevant_child_account.parent_account.public_id if relevant_child_account.parent_account else None,
            "name": relevant_child_account.name,
            "language": api_language.value
        }
        access_token_expires = timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data=token_data,
            expires_delta=access_token_expires
        )

        # Get subscriptions from parent if linked
        if relevant_child_account.parent_account_id:
            subscriptions_api = get_formatted_subscriptions_by_year(self.db, relevant_child_account.parent_account_id)
        else:
            subscriptions_api = []


        logger.info("Child login successful: \n"
                    f"email: {relevant_child_account.email}, \n"
                    f"public_id: {relevant_child_account.public_id}")
        return LoginResponse(access_token=access_token, user=user_payload, subscriptions=subscriptions_api)