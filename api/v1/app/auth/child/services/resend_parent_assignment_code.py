from sqlalchemy import select
from sqlalchemy.orm import Session
from fastapi import BackgroundTasks
from loguru import logger  

from db.models import ParentChildAssignment, Account  
from ..schemas.request import AssignParentRequest
from ..schemas.response import AssignParentResponse
from core.exception_handling.exceptions.custom_exceptions import NotFoundError  
from api.v1.common.schemas import AppErrorCode  
from services.mailer.auth_mailer_service import AuthMailerService
from api.v1.app.auth.parent.services.email_rate_limiter import EmailRateLimiterService

class ResendParentAssignmentService:
    def __init__(self, db: Session, auth_mailer_service: AuthMailerService):
        self.db = db
        self.auth_mailer_service = auth_mailer_service

    def resend_assignment_code(
        self,
        assignment_request: AssignParentRequest,
        background_tasks: BackgroundTasks
    ) -> AssignParentResponse:
        """Resend parent assignment verification code"""
        logger.info("Resending parent assignment code to: {} for language {}",
                    assignment_request.parent_email, assignment_request.language)
        
        # Check rate limiting before processing
        rate_limiter = EmailRateLimiterService(self.db)
        rate_limiter.check_and_update_rate_limit(assignment_request.parent_email, "parent_assignment")

        # To find the specific assignment, we usually need the child's context.
        # The AssignParentRequest schema only has parent_email and language.
        # This implies we might be looking for any assignment to this parent_email.
        # If a specific child is implied (e.g., if this route is authenticated for a child),
        # that child's ID should be used in the query.
        # For now, proceeding with parent_email as the primary key for the lookup.

        stmt = select(ParentChildAssignment).filter(
            ParentChildAssignment.parent_email == assignment_request.parent_email
            # TODO: Consider if child_account_id should be part of the filter if available
            # e.g., ParentChildAssignment.child_account_id == child_id_from_auth
        ).order_by(ParentChildAssignment.created_at.desc())  # Get the latest one if multiple

        relevant_temp_assignment = self.db.execute(stmt).first() 
        if relevant_temp_assignment: 
            relevant_temp_assignment = relevant_temp_assignment[0]


        # Validate assignment exists
        if not relevant_temp_assignment:
            logger.warning("No pending parent assignment found for email: {}", assignment_request.parent_email)
            raise NotFoundError(
                message="No pending assignment found for this email.",
                error_code=AppErrorCode.ASSIGNMENT_NOT_FOUND_OR_INVALID_CODE,
                entity_name="ParentChildAssignment",
                identifier=assignment_request.parent_email
            )

        # Send verification code in background
        background_tasks.add_task(
            self.auth_mailer_service.send_child_parent_assignment_email,
            to=assignment_request.parent_email,
            verification_code=relevant_temp_assignment.verification_code,
            lang=assignment_request.language  
        )
        logger.info("Parent assignment verification code resent to {}", assignment_request.parent_email)

        # Determine if a parent account already exists with this email
        existing_parent_stmt = select(Account.id).filter(Account.email == assignment_request.parent_email)
        existing_parent_account = self.db.execute(existing_parent_stmt).scalar_one_or_none()

        return AssignParentResponse(
            message="Verification code sent",
            existing_parent_account=existing_parent_account is not None
        )