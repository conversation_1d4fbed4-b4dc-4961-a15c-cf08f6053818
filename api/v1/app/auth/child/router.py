from fastapi import APIRouter

# Import all route modules

from .routes import (
    signup,
    login,
    verify_email,
    check_verification_status,
    forgot_pin,
    assign_parent,
    verify_parent_assignment,
    resend_verification_code,
    resend_parent_assignment_code,
    validate_session
)

# Create main router
router = APIRouter(prefix="/api/v1/auth/child", tags=["Child Authentication"])

# Include all routes
router.include_router(signup.router)
router.include_router(login.router)
router.include_router(verify_email.router)
router.include_router(check_verification_status.router)
router.include_router(forgot_pin.router)
router.include_router(assign_parent.router)
router.include_router(verify_parent_assignment.router)
router.include_router(resend_verification_code.router)
router.include_router(resend_parent_assignment_code.router)
router.include_router(validate_session.router)