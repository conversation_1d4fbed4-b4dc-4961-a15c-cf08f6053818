from fastapi import APIRouter, Depends, BackgroundTasks
from sqlalchemy.orm import Session
from db.database import get_db
from ..schemas.request import SignupRequest
from ..schemas.response import SignupResponse
from dependencies.email_dependency import get_auth_mailer_service
from dependencies.list_dependency import get_list_service
from services.mailer.auth_mailer_service import AuthMailerService
from services.email_list.service import ListService
from ..services.signup import ChildSignupService

router = APIRouter()

@router.post('/signup', status_code=201, response_model=SignupResponse)
def create_child_account(
    new_child_account_request: SignupRequest,  
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db), 
    auth_mailer_service: AuthMailerService = Depends(get_auth_mailer_service),
    list_service: ListService = Depends(get_list_service)
):
    # Initialize the service
    service = ChildSignupService(db=db, auth_mailer_service=auth_mailer_service, list_service=list_service)

    # Delegate business logic to the service
    response = service.create_child_account(
        new_child_account_request=new_child_account_request,
        background_tasks=background_tasks
    )

    return response