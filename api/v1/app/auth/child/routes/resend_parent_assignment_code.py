from fastapi import APIRouter, Depends, BackgroundTasks
from sqlalchemy.orm import Session
from db.database import get_db
from ..schemas.request import AssignParentRequest
from ..schemas.response import AssignParentResponse
from services.mailer.auth_mailer_service import AuthMailerService
from dependencies.email_dependency import get_auth_mailer_service
from ..services.resend_parent_assignment_code import ResendParentAssignmentService

router = APIRouter()


@router.post('/resend-parent-assignment-code', response_model=AssignParentResponse)
def resend_parent_assignment_code(
    assignment_request: AssignParentRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db), 
    auth_mailer_service: AuthMailerService = Depends(get_auth_mailer_service),
):
    # Initialize the service
    service = ResendParentAssignmentService(db=db, auth_mailer_service=auth_mailer_service)

    # Delegate business logic to the service
    # The service method resend_assignment_code is not async, but it schedules async tasks.
    response = service.resend_assignment_code(
        assignment_request=assignment_request,
        background_tasks=background_tasks
    )

    return response