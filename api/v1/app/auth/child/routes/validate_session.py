from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.auth_dependency import AuthDependency, AuthDependencyResponse
from ..services.validate_session_service import ChildValidateSessionService
from ..schemas.response import ValidateSessionResponse
from db.models import ChildAccount
from dependencies.auth_dependencies.base_auth_dependency import UserType 
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError, ServiceError 
from api.v1.common.schemas import AppErrorCode 

router = APIRouter()

require_child_auth = AuthDependency(required=True) 

@router.post(
    "/validate-session", 
    response_model=ValidateSessionResponse
)
def validate_child_session(
    db: Session = Depends(get_db),
    auth_response: AuthDependencyResponse = Depends(require_child_auth)
):
    if auth_response.user_type != UserType.child:
        raise AuthenticationError(
            message="Access denied. Child account required.",
            log_message=f"Permission denied for validate_child_session.\n"
            f"User type '{auth_response.user_type}' is not CHILD.",
            error_code=AppErrorCode.PERMISSION_DENIED
        )

    # Ensure account object is of the correct type (ChildAccount for child)
    if not isinstance(auth_response.account, ChildAccount):
        # This case implies a mismatch between token user_type and actual account DB record
        # or an issue in AuthDependencyResponse creation.
        raise ServiceError(
            message="Internal server error: Invalid account data for child session.",
            log_message=f"Validate_child_session: Expected ChildAccount model\n"
            f"got {type(auth_response.account)} for user_type {auth_response.user_type}.\n"
            f"Account ID: {auth_response.account_id}",
            error_code=AppErrorCode.SERVICE_ERROR
        )

    service = ChildValidateSessionService(db=db)
    return service.validate_session(authenticated_child_account=auth_response.account)