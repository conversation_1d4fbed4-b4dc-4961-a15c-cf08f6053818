from fastapi import APIRouter, Depends, BackgroundTasks
from sqlalchemy.orm import Session
from db.database import get_db
from ..schemas.request import CheckVerificationStatusRequest
from ..schemas.response import CheckVerificationStatusResponse
from dependencies.email_dependency import get_auth_mailer_service
from services.mailer.auth_mailer_service import AuthMailerService
from ..services.check_verification_status import VerificationStatusService

router = APIRouter()

@router.post('/check-verification-status', response_model=CheckVerificationStatusResponse)
def check_child_verification_status(
    request: CheckVerificationStatusRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    auth_mailer_service: AuthMailerService = Depends(get_auth_mailer_service)
):
    # Initialize the service
    service = VerificationStatusService(db=db, auth_mailer_service=auth_mailer_service)
    
    # Delegate business logic to the service
    response = service.check_verification_status(request, background_tasks)
    
    return response