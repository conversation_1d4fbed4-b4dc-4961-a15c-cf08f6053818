from fastapi import APIRouter, Depends, BackgroundTasks
from sqlalchemy.orm import Session
from db.database import get_db
from ..schemas.request import ForgotPinRequest
from ..schemas.response import ForgotPinResponse
from dependencies.email_dependency import get_auth_mailer_service
from services.mailer.auth_mailer_service import AuthMailerService
from ..services.forgot_pin import ForgotPinService

router = APIRouter()

@router.post('/forgot-pin', response_model=ForgotPinResponse)
def forgot_pin(
    forgot_pin_request: ForgotPinRequest,  
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db), 
    auth_mailer_service: AuthMailerService = Depends(get_auth_mailer_service),
):
    # Initialize the service
    service = ForgotPinService(db=db, auth_mailer_service=auth_mailer_service)

    # Delegate business logic to the service
    response = service.process_forgot_pin(
        forgot_pin_request=forgot_pin_request,
        background_tasks=background_tasks
    )

    return response