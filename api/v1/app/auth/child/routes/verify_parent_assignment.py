from fastapi import APIRouter, Depends, BackgroundTasks 
from sqlalchemy.orm import Session

from db.database import get_db
from ..schemas.request import VerifyParentAssignmentRequest
from ..schemas.response import VerifyParentAssignmentResponse

# Import the service
from ..services.verify_parent_assignment import ParentVerificationService

# For AuthMailerService and ListService injection
from services.mailer.auth_mailer_service import AuthMailerService 
from services.email_list.service import ListService
from dependencies.email_dependency import get_auth_mailer_service
from dependencies.list_dependency import get_list_service


router = APIRouter()


@router.post('/verify-parent-account', status_code=200, response_model=VerifyParentAssignmentResponse)
def verify_parent_account(
    parent_account_request: VerifyParentAssignmentRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    auth_mailer_service: AuthMailerService = Depends(get_auth_mailer_service),
    list_service: ListService = Depends(get_list_service),
):
    # Initialize the service
    service = ParentVerificationService(db=db)

    # Delegate business logic to the service, passing necessary dependencies
    response = service.verify_parent_account(
        parent_account_request,
        auth_mailer_service=auth_mailer_service,
        list_service=list_service,
        background_tasks=background_tasks
    )

    return response