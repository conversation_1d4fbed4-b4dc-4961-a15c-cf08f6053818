from fastapi import APIRouter, Depends, BackgroundTasks
from core.exception_handling.exceptions.custom_exceptions import AuthorizationError
from sqlalchemy.orm import Session
from db.database import get_db
from ..schemas.request import AssignParentRequest
from ..schemas.response import AssignParentResponse
from dependencies.auth_dependencies import AuthDependency
from services.mailer.auth_mailer_service import AuthMailerService
from dependencies.email_dependency import get_auth_mailer_service
from ..services.assign_parent import ParentAssignmentService
from loguru import logger
router = APIRouter()

require_auth = AuthDependency()



@router.post('/assign-parent', status_code=200, response_model=AssignParentResponse)
def assign_parent(
    new_assignment: AssignParentRequest,  
    db: Session = Depends(get_db), 
    auth=Depends(require_auth), 
    auth_mailer_service: AuthMailerService = Depends(get_auth_mailer_service),
    background_tasks: BackgroundTasks = BackgroundTasks()
):  
    logger.info("Assigning parent to child with public ID: {}", auth.account_public_id)
    logger.info("Auth response: {}", auth)
    # Authenticate that this is a child account
    if not hasattr(auth, 'user_type') or not auth.user_type == 'child':
        logger.info("Unauthorized: Operation requires child account authentication.")
        raise AuthorizationError(message="Unauthorized: Operation requires child account authentication.")

    child_public_id = getattr(auth, 'account_public_id', None)
    if not child_public_id:
        logger.info("Unauthorized: Child public ID missing from token.")
        raise AuthorizationError(message="Unauthorized: Child public ID missing from token.")

    # Initialize the service
    service = ParentAssignmentService(db=db, auth_mailer_service=auth_mailer_service)

    # Delegate business logic to the service
    response = service.assign_parent(
        new_assignment=new_assignment,
        child_public_id=str(child_public_id),
        background_tasks=background_tasks
    )
    logger.info("Parent assigned to child: child_public_id: {}, parent_public_id: {}",
                child_public_id, new_assignment.parent_email)
    return response