from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from db.database import get_db
from ..schemas.request import VerifyAccountRequest
from ..schemas.response import VerifyAccountResponse

# Import the service
from ..services.verify_email import EmailVerificationService

router = APIRouter()

@router.post('/verify-email', status_code=200, response_model=VerifyAccountResponse)
def verify_child_account(child_account: VerifyAccountRequest, db: Session = Depends(get_db)):
    # Initialize the service
    service = EmailVerificationService(db=db)
    
    # Delegate business logic to the service
    response = service.verify_child_account(child_account)
    
    return response