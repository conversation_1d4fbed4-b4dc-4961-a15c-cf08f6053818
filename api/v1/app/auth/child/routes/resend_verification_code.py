from fastapi import APIRouter, Depends, BackgroundTasks
from sqlalchemy.orm import Session
from db.database import get_db
from ..schemas.request import ResendVerificationCodeRequest
from ..schemas.response import ResendVerificationCodeResponse
from services.mailer.auth_mailer_service import AuthMailerService
from dependencies.email_dependency import get_auth_mailer_service
from ..services.resend_verification_code import ResendVerificationService

router = APIRouter()


@router.post('/resend-verification-code', status_code=200, response_model=ResendVerificationCodeResponse)
def resend_verification_code(
    request: ResendVerificationCodeRequest,  
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db), 
    auth_mailer_service: AuthMailerService = Depends(get_auth_mailer_service),
):
    # Initialize the service
    service = ResendVerificationService(db=db, auth_mailer_service=auth_mailer_service)
    
    # Delegate business logic to the service
    response = service.resend_code(
        request=request,
        background_tasks=background_tasks
    )
    
    return response