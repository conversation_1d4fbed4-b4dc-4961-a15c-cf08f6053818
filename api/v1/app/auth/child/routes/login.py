from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from db.database import get_db

from ..schemas.request import LoginRequest
from ..schemas.response import LoginResponse

# Import the service
from ..services.login import ChildLoginService

router = APIRouter()


@router.post('/login', response_model=LoginResponse)
def login(child_account: LoginRequest, db: Session = Depends(get_db)):
    # Initialize the service
    service = ChildLoginService(db=db)
    
    # Delegate business logic to the service
    response = service.login(child_account)
    
    return response
