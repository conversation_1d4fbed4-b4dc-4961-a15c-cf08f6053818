from pydantic import BaseModel, Field, ConfigDict, EmailStr
from typing import List, Optional, Union, Literal as PyLiteral # Renamed Literal to PyLiteral to avoid conflict
from enum import Enum

# Ensure LanguageEnum is available, if not defined here, it should be imported
# from db.models.account import LanguageEnum # Assuming it's defined in models

# Placeholder for LanguageEnum if not imported - replace with actual import or definition
class LanguageEnum(str, Enum):
    LU = "lu"
    EN = "en"
    DE = "de"
    FR = "fr"

class UserTypeEnum(str, Enum):
    UNREGISTERED = "unregistered"
    PARENT = "parent"
    CHILD = "child"


class SchoolYearResponse(BaseModel):
    public_id: str
    name: str
    

class BaseUser(BaseModel):
    public_id: str
    email: EmailStr
    language: LanguageEnum

class ParentUser(BaseUser):
    user_type: PyLiteral[UserTypeEnum.PARENT] = UserTypeEnum.PARENT

class ChildUser(BaseUser):
    user_type: PyLiteral[UserTypeEnum.CHILD] = UserTypeEnum.CHILD
    parent_public_id: Optional[str] = None
    name: str
    school_year: SchoolYearResponse

User = Union[ParentUser, ChildUser]


class SubscriptionDetails(BaseModel):
    subject_public_id: str
    subject_name: str
    subscription_public_id: str


class SubscribedSubject(BaseModel):
    subject_public_id: str
    subject_name: str


class YearSubscription(BaseModel):
    year_public_id: str
    year_name: str
    subscribed_subjects: List[SubscribedSubject]


