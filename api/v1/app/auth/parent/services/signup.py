from sqlalchemy.orm import Session
from fastapi import BackgroundTasks
from typing import Optional, List
from loguru import logger

from db.models.account import LanguageEnum as DbLanguageEnum
from ..schemas.request import SignupRequest
from ..schemas.response import SignupResponse
# Updated imports for new response structure
from api.v1.app.auth.schemas.shared_schemas import User<PERSON>ype<PERSON><PERSON>, ParentUser, SubscriptionDetails
# Import the utility for fetching subscriptions
from api.v1.app.auth.parent.services.subscription_utils import get_formatted_raw_subscriptions, RawSubscription, get_formatted_subscriptions_by_year

from ..utils.account_creation import create_parent_account
from core.exception_handling.exceptions.custom_exceptions import ValidationError, ServiceError, ExternalServiceError
from api.v1.common.schemas import AppErrorCode
from services.mailer.auth_mailer_service import AuthMailerService
from services.email_list.service import ListService

class ParentSignupService:
    def __init__(
        self,
        db: Session,
        auth_mailer_service: AuthMailerService,
        list_service: ListService
    ):
        self.db = db
        self.auth_mailer_service = auth_mailer_service
        self.list_service = list_service

    def signup_parent(
        self,
        new_account_request: SignupRequest,
        ip_address: Optional[str],
        background_tasks: BackgroundTasks
    ) -> SignupResponse:
        logger.info(f"Parent signup attempt for email: {new_account_request.email}")

        if len(new_account_request.login_pin) != 6 or not new_account_request.login_pin.isdigit():
            logger.warning(f"Invalid PIN format during signup for {new_account_request.email}")
            raise ValidationError(
                message="PIN must be 6 digits.",
                error_code=AppErrorCode.INVALID_PIN_FORMAT
            )

        marketing_data = None
        if new_account_request.marketing_consent:
            marketing_data = {
                'marketing_consent_version': new_account_request.marketing_consent_version,
                'privacy_policy_version': new_account_request.privacy_policy_version
            }

        try:
            created_account_orm, access_token = create_parent_account( # Renamed from created_account_entry
                db=self.db,
                email=new_account_request.email,
                language=new_account_request.language,
                pin=new_account_request.login_pin,
                is_verified=False,
                background_tasks=background_tasks,
                auth_mailer_service=self.auth_mailer_service,
                list_service=self.list_service,
                marketing_data=marketing_data,
                signup_source=new_account_request.signup_source or "direct_parent_signup",
                signup_searchparams=new_account_request.signup_searchparams,
                year_options=new_account_request.year_options,
                system_options=new_account_request.system_options,
                ip_address=ip_address,
                user_agent=new_account_request.user_agent,
                is_auth_migrated=True
            )

            self.db.commit() # Commit after create_parent_account returns the ORM object
            logger.info(
                f"Parent account {created_account_orm.email}\n"
                f"(ID: {created_account_orm.id}) committed successfully.")

            db_language = created_account_orm.language
            api_language = DbLanguageEnum(db_language.value if db_language else 'lu')

            user_payload = ParentUser(
                public_id=str(created_account_orm.public_id),
                email=created_account_orm.email,
                first_name=None, # Parent account model does not have first_name
                language=api_language,
                user_type=UserTypeEnum.PARENT
            )

            # Fetch subscriptions (will be empty for a new account)
            subscriptions_api = get_formatted_subscriptions_by_year(self.db, created_account_orm.id)


        except ServiceError as e:
            logger.error(
                f"ServiceError during parent signup for {new_account_request.email}: {e.log_message}", exc_info=True)
            raise e

        except ExternalServiceError as e:
            # create_parent_account handles rollback for Stripe errors
            logger.error(
                f"ExternalServiceError during parent signup for\n"
                f"{new_account_request.email}: {e.log_message}", exc_info=True)
            raise e


        except Exception as e:
            self.db.rollback() # Ensure rollback for other unexpected errors
            logger.error(
                "Unexpected error during parent signup for {email}: {error_details}",
                email=new_account_request.email,
                error_details=str(e).replace('%', '%%'),
                exc_info=True
            )
            raise ServiceError(
                message="Could not complete signup due to an unexpected issue.",
                log_message=f"Unexpected error in ParentSignupService for {new_account_request.email}: {str(e)}",
                error_code=AppErrorCode.SERVICE_ERROR
            )

        return SignupResponse(
            message="Account created successfully. Please check your email to verify your account.",
            access_token=access_token,
            user=user_payload,
            subscriptions=subscriptions_api
        )