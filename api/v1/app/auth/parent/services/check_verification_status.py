from sqlalchemy.orm import Session
from sqlalchemy import select
from fastapi import BackgroundTasks
from datetime import datetime, timedelta, UTC
import random
from loguru import logger

from db.models import Account, PinResetRequest
from ..schemas.request import VerificationStatusRequest
from ..schemas.response import VerificationStatusResponse
from services.mailer.auth_mailer_service import AuthMailerService
from core.exception_handling.exceptions.custom_exceptions import NotFoundError, TooManyRequestsError, ServiceError
from api.v1.common.schemas import AppErrorCode

class ParentVerificationStatusService:
    def __init__(self, db: Session, auth_mailer_service: AuthMailerService):
        self.db = db
        self.auth_mailer_service = auth_mailer_service

    def check_status(
        self,
        request: VerificationStatusRequest,
        background_tasks: BackgroundTasks
    ) -> VerificationStatusResponse:
        input_email = request.email.strip().lower()
        logger.info(f"Checking parent verification status for: {input_email}")

        stmt = select(Account).where(Account.email == input_email)
        account = self.db.execute(stmt).scalar_one_or_none()

        if not account:
            logger.warning(f"Account not found for verification status check: {input_email}")
            raise NotFoundError(
                message="Account not found",
                error_code=AppErrorCode.USER_NOT_FOUND,
                entity_name="Account",
                identifier=input_email
            )
        
        if not account.is_auth_migrated:
            logger.info(f"Account {input_email} is not auth migrated. Triggering PIN reset flow.")
            stmt_pin_req = select(PinResetRequest).filter(
                PinResetRequest.account_id == account.id,
                PinResetRequest.used.is_(False)
            ).order_by(PinResetRequest.created_at.desc())
            
            existing_pin_reset_request_row = self.db.execute(stmt_pin_req).first()
            existing_pin_reset_request = existing_pin_reset_request_row[0] if existing_pin_reset_request_row else None

            pin_reset_request_to_use = existing_pin_reset_request
            
            if pin_reset_request_to_use and pin_reset_request_to_use.expires_at > datetime.now(UTC):
                if pin_reset_request_to_use.created_at > datetime.now(UTC) - timedelta(minutes=1):
                    logger.warning(f"Too many PIN reset requests for unmigrated account: {input_email}")
                    raise TooManyRequestsError(
                        message="Too many requests, please wait a moment before trying again.",
                        error_code=AppErrorCode.TOO_MANY_REQUESTS
                    )
            else:
                if pin_reset_request_to_use:
                    pass
                
                pin_reset_request_to_use = PinResetRequest(
                    account_id=account.id,
                    expires_at=datetime.now(UTC) + timedelta(minutes=30)
                )
                self.db.add(pin_reset_request_to_use)
                try:
                    self.db.commit()
                    self.db.refresh(pin_reset_request_to_use)
                    logger.info(f"Created new PinResetRequest for unmigrated account: {input_email}")
                except Exception as e:
                    self.db.rollback()
                    logger.error(f"DB error creating PinResetRequest for {input_email}: {e}", exc_info=True)
                    raise ServiceError(message="Failed to process request.", error_code=AppErrorCode.DATABASE_ERROR)


            background_tasks.add_task(
                self.auth_mailer_service.send_parent_pin_reset_email,
                to=input_email,
                reset_token=pin_reset_request_to_use.request_token,
                lang=account.language or 'lu'
            )
            logger.info(f"Sent PIN reset email for unmigrated account: {input_email}")

            return VerificationStatusResponse(
                is_auth_migrated=False,
                is_verified=account.is_verified
            )
        else:
            logger.info(f"Account {input_email} is auth migrated.")
            if account.is_verified:
                logger.info(f"Migrated account {input_email} is already verified.")
                return VerificationStatusResponse(
                    is_auth_migrated=True,
                    is_verified=True
                )
            else:
                if not account.verification_code:
                    account.verification_code = str(random.randint(100000, 999999))
                    try:
                        self.db.commit()
                        logger.info(f"Generated new verification code for migrated, unverified account: {input_email}")
                    except Exception as e:
                        self.db.rollback()
                        logger.error(f"DB error saving new verification code for {input_email}: {e}", exc_info=True)
                        raise ServiceError(message="Failed to process request.", error_code=AppErrorCode.DATABASE_ERROR)


                background_tasks.add_task(
                    self.auth_mailer_service.send_parent_verification_email,
                    to=input_email,
                    verification_code=account.verification_code,
                    lang=account.language or 'lu'
                )
                logger.info(f"Sent verification email for migrated, unverified account: {input_email}")

                return VerificationStatusResponse(
                    is_auth_migrated=True,
                    is_verified=False
                )