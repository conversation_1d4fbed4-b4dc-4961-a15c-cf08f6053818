from sqlalchemy.orm import Session
from sqlalchemy import select
from fastapi import BackgroundTasks
from datetime import datetime, timedelta, UTC
from loguru import logger

from db.models import Account, PinResetRequest, AccountAuthSecurity
from ..schemas.request import TriggerPinResetRequest
from ..schemas.response import TriggerPinResetResponse
from services.mailer.auth_mailer_service import AuthMailerService
from core.exception_handling.exceptions.custom_exceptions import (
    TooManyRequestsError, AuthenticationError, ServiceError
)
from api.v1.common.schemas import AppErrorCode
from .email_rate_limiter import EmailRateLimiterService

class TriggerPinResetService:
    def __init__(self, db: Session, auth_mailer_service: AuthMailerService):
        self.db = db
        self.auth_mailer_service = auth_mailer_service

    def trigger_reset(
        self,
        request: TriggerPinResetRequest,
        background_tasks: BackgroundTasks
    ) -> TriggerPinResetResponse:
        email = request.email.strip().lower()
        logger.info(f"Triggering PIN reset for: {email}")

        # Check rate limiting before processing
        rate_limiter = EmailRateLimiterService(self.db)
        rate_limiter.check_and_update_rate_limit(email, "pin_reset")

        stmt_account = select(Account).where(Account.email == email)
        account = self.db.execute(stmt_account).scalar_one_or_none()

        if not account:
            logger.info(f"PIN reset triggered for non-existent account: {email}")
            return TriggerPinResetResponse(
                message="If the account exists, a reset code will be sent"
            )

        auth_security_stmt = select(AccountAuthSecurity).filter(AccountAuthSecurity.account_id == account.id)
        auth_security = self.db.execute(auth_security_stmt).scalar_one_or_none()

        if auth_security and auth_security.locked_until and auth_security.locked_until > datetime.now(UTC):
            logger.warning(f"Attempt to trigger PIN reset for locked account: {email}")
            raise AuthenticationError(
                message="Account locked. Try again later.",
                error_code=AppErrorCode.ACCOUNT_LOCKED,
                log_message=f"Account {email} is locked, PIN reset attempt blocked."
            )

        # Check for existing valid reset request
        stmt_last_req = select(PinResetRequest).filter(
            PinResetRequest.account_id == account.id,
            PinResetRequest.used.is_(False),
            PinResetRequest.expires_at > datetime.now(UTC)
        ).order_by(PinResetRequest.created_at.desc())

        last_reset_request_row = self.db.execute(stmt_last_req).first()
        last_reset_request = last_reset_request_row[0] if last_reset_request_row else None

        reset_token_to_send = None
        if last_reset_request:
            logger.info(f"Resending existing valid PIN reset token for {email}")
            reset_token_to_send = last_reset_request.request_token
        else:
            logger.info(f"Generating new PIN reset token for {email}")
            reset_request_obj = PinResetRequest(account_id=account.id)
            self.db.add(reset_request_obj)
            try:
                self.db.commit()
                self.db.refresh(reset_request_obj)
                reset_token_to_send = reset_request_obj.request_token
            except Exception as e:
                self.db.rollback()
                logger.error(f"DB error creating PinResetRequest for {email}: {e}", exc_info=True)
                raise ServiceError(message="Failed to process PIN reset request.",
                                   error_code=AppErrorCode.DATABASE_ERROR)

        if reset_token_to_send:
            lang_code = request.language 
            if not lang_code and account.language:
                lang_code = account.language.value
            if not lang_code:
                lang_code = 'lu'

            background_tasks.add_task(
                self.auth_mailer_service.send_parent_pin_reset_email,
                to=email,
                reset_token=reset_token_to_send,
                lang=lang_code
            )
        else:
            logger.error(f"Failed to obtain a reset token for {email} after processing.")

        return TriggerPinResetResponse(message="If the account exists, a reset code will be sent")