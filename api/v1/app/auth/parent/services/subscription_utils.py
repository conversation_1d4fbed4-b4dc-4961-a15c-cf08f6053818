from sqlalchemy.orm import Session
from sqlalchemy import select
from typing import List, Dict
from pydantic import BaseModel # Import BaseModel
from db.models import Year, Subject
from db.models.subscription import (
    ActiveSubscription,
    ActiveSubscriptionPlanLink,
    SubscriptionOption,
    PriceVersion,
    PlanSelectedSubject,
    PlanTypeEnum,
    SubscriptionStatusType # Ensure this is imported
)
# Removed: from api.v1.app.auth.schemas.shared_schemas import RawSubscription - define it here or ensure it's correctly structured
from api.v1.app.auth.schemas.shared_schemas import YearSubscription, SubscribedSubject
from loguru import logger

# Define RawSubscription Pydantic model here to match frontend's SubscriptionApiSchema
class RawSubscription(BaseModel):
    subject_public_id: str
    subject_name: str
    subscription_public_id: str


def get_formatted_raw_subscriptions(db: Session, account_id: int) -> List[RawSubscription]:
    """
    Fetches active subscription options, associated year names, and selected subject public IDs,
    then formats them into a list of RawSubscription Pydantic models.
    """
    stmt = (
        select(
            SubscriptionOption.public_id.label("option_public_id"),
            Year.name.label("year_name"), # Year name from SubscriptionOption's year
            Year.public_id.label("year_public_id"), # Year public_id from SubscriptionOption's year
            Subject.public_id.label("subject_public_id"),
            Subject.name.label("subject_name"),
            ActiveSubscription.public_id.label("subscription_public_id"),
            ActiveSubscriptionPlanLink.chosen_plan_type,
            SubscriptionOption.year_id.label("option_year_id") # Explicitly select year_id from SubscriptionOption
        )
        .join(
            ActiveSubscriptionPlanLink,
            ActiveSubscriptionPlanLink.subscription_option_id == SubscriptionOption.id
        )
        .join(
            ActiveSubscription,
            ActiveSubscription.id == ActiveSubscriptionPlanLink.active_subscription_id
        )
        .join(
            PriceVersion,
            PriceVersion.id == SubscriptionOption.price_version_id
        )
        .join( # Join SubscriptionOption to Year to get year details for the option
            Year,
            Year.id == SubscriptionOption.year_id
        )
        .outerjoin( # For SC: join plan link to selected subjects
            PlanSelectedSubject,
            PlanSelectedSubject.plan_link_id == ActiveSubscriptionPlanLink.id
        )
        .outerjoin( # For SC: join selected subjects to Subject table
            Subject,
            Subject.id == PlanSelectedSubject.subject_id
        )
        .where(
            ActiveSubscription.parent_account_id == account_id,
            ActiveSubscription.status.in_([SubscriptionStatusType.ACTIVE, SubscriptionStatusType.TRIALING]), # type: ignore
            PriceVersion.is_active, # Ensure the price version of the option is active
            SubscriptionOption.is_active # Ensure the subscription option itself is active
        )
    )
    results = db.execute(stmt).mappings().all()

    raw_subscriptions_list: List[RawSubscription] = []
    # Use a set of tuples to track unique (subscription_public_id, subject_public_id) pairs to avoid duplicates
    processed_subscription_subject_pairs = set()


    for row in results:
        subscription_pid_str = str(row["subscription_public_id"])

        if row["chosen_plan_type"] == PlanTypeEnum.SC:
            if row["subject_public_id"] and row["subject_name"]:
                subject_pid_str = str(row["subject_public_id"])
                if (subscription_pid_str, subject_pid_str) not in processed_subscription_subject_pairs:
                    raw_subscriptions_list.append(
                        RawSubscription(
                            subject_public_id=subject_pid_str,
                            subject_name=str(row["subject_name"]),
                            subscription_public_id=subscription_pid_str
                        )
                    )
                    processed_subscription_subject_pairs.add((subscription_pid_str, subject_pid_str))
            else:
                # This case might occur if the outer join for Subject didn't find a match for an SC plan link
                # which could happen if an SC plan link exists but no subjects are selected yet,
                # or if the row is primarily for a YF plan but included due to the outer join structure.
                # Log as debug as it might be expected in some scenarios.
                logger.debug(f"SC plan link {row.get('option_public_id')} for subscription {subscription_pid_str} has no specific subject selected/returned in this row.")


        elif row["chosen_plan_type"] == PlanTypeEnum.YF:
            # For YF, all subjects of the SubscriptionOption's year are granted.
            # The 'option_year_id' was selected directly from SubscriptionOption.
            year_id_for_yf = row["option_year_id"]
            if year_id_for_yf:
                subjects_for_yf_year_stmt = select(Subject.public_id, Subject.name).where(
                    Subject.year_id == year_id_for_yf, Subject.is_active == True) # Ensure subject is active
                subjects_for_yf_year = db.execute(subjects_for_yf_year_stmt).mappings().all()

                for yf_subj in subjects_for_yf_year:
                    subject_pid_str = str(yf_subj["public_id"])
                    if (subscription_pid_str, subject_pid_str) not in processed_subscription_subject_pairs:
                        raw_subscriptions_list.append(
                            RawSubscription(
                                subject_public_id=subject_pid_str,
                                subject_name=str(yf_subj["name"]),
                                subscription_public_id=subscription_pid_str
                            )
                        )
                        processed_subscription_subject_pairs.add((subscription_pid_str, subject_pid_str))
            else:
                logger.warning(f"Could not determine year_id for YF SubscriptionOption public_id: {row.get('option_public_id')} linked to subscription {subscription_pid_str}")
    
    # The processed_subscription_subject_pairs set already handles deduplication during the loop.
    return raw_subscriptions_list


def get_formatted_subscriptions_by_year(db: Session, account_id: int) -> List[YearSubscription]:
    """
    Fetches active subscriptions and formats them grouped by year.
    Returns a list of YearSubscription objects, each containing the year info and subscribed subjects.
    """
    stmt = (
        select(
            Year.public_id.label("year_public_id"),
            Year.name.label("year_name"),
            Subject.public_id.label("subject_public_id"),
            Subject.name.label("subject_name"),
            ActiveSubscriptionPlanLink.chosen_plan_type,
            SubscriptionOption.year_id.label("option_year_id")
        )
        .join(
            ActiveSubscriptionPlanLink,
            ActiveSubscriptionPlanLink.subscription_option_id == SubscriptionOption.id
        )
        .join(
            ActiveSubscription,
            ActiveSubscription.id == ActiveSubscriptionPlanLink.active_subscription_id
        )
        .join(
            PriceVersion,
            PriceVersion.id == SubscriptionOption.price_version_id
        )
        .join(
            Year,
            Year.id == SubscriptionOption.year_id
        )
        .outerjoin(
            PlanSelectedSubject,
            PlanSelectedSubject.plan_link_id == ActiveSubscriptionPlanLink.id
        )
        .outerjoin(
            Subject,
            Subject.id == PlanSelectedSubject.subject_id
        )
        .where(
            ActiveSubscription.parent_account_id == account_id,
            ActiveSubscription.status.in_([SubscriptionStatusType.ACTIVE, SubscriptionStatusType.TRIALING]),
            PriceVersion.is_active,
            SubscriptionOption.is_active
        )
    )
    results = db.execute(stmt).mappings().all()
    
    # Dictionary to group subjects by year
    year_subscriptions_dict: Dict[str, Dict[str, any]] = {}
    
    for row in results:
        year_public_id = str(row["year_public_id"])
        year_name = str(row["year_name"])
        
        # Initialize year entry if not exists
        if year_public_id not in year_subscriptions_dict:
            year_subscriptions_dict[year_public_id] = {
                "year_public_id": year_public_id,
                "year_name": year_name,
                "subjects": {},  # Use dict to track unique subjects
            }
        
        if row["chosen_plan_type"] == PlanTypeEnum.SC:
            # Subject Choice plan - add only selected subjects
            if row["subject_public_id"] and row["subject_name"]:
                subject_public_id = str(row["subject_public_id"])
                year_subscriptions_dict[year_public_id]["subjects"][subject_public_id] = {
                    "subject_public_id": subject_public_id,
                    "subject_name": str(row["subject_name"])
                }
            else:
                logger.debug(f"SC plan for year {year_public_id} has no specific subject selected in this row.")
                
        elif row["chosen_plan_type"] == PlanTypeEnum.YF:
            # Year Full plan - add all subjects for the year
            year_id_for_yf = row["option_year_id"]
            if year_id_for_yf:
                subjects_stmt = select(Subject.public_id, Subject.name).where(
                    Subject.year_id == year_id_for_yf,
                    Subject.is_active == True
                )
                subjects = db.execute(subjects_stmt).mappings().all()
                
                for subj in subjects:
                    subject_public_id = str(subj["public_id"])
                    year_subscriptions_dict[year_public_id]["subjects"][subject_public_id] = {
                        "subject_public_id": subject_public_id,
                        "subject_name": str(subj["name"])
                    }
            else:
                logger.warning(f"Could not determine year_id for YF SubscriptionOption for year {year_public_id}")
    
    # Convert to list of YearSubscription objects
    year_subscriptions_list: List[YearSubscription] = []
    for year_data in year_subscriptions_dict.values():
        subscribed_subjects = [
            SubscribedSubject(**subject_data)
            for subject_data in year_data["subjects"].values()
        ]
        
        # Only add years that have at least one subject
        if subscribed_subjects:
            year_subscriptions_list.append(
                YearSubscription(
                    year_public_id=year_data["year_public_id"],
                    year_name=year_data["year_name"],
                    subscribed_subjects=subscribed_subjects
                )
            )
    
    # Sort by year name for consistent ordering
    year_subscriptions_list.sort(key=lambda x: x.year_name)
    
    return year_subscriptions_list