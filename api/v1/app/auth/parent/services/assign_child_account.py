from sqlalchemy.orm import Session
from sqlalchemy import select
from loguru import logger

from db.models import Account, ChildAccount, ParentChildAssignment, Trial
from ..schemas.request import AssignChildAccountRequest
from ..schemas.response import AssignChildAccountResponse
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, BadRequestError, AuthenticationError, ServiceError
)
from api.v1.common.schemas import AppErrorCode

class ParentAssignChildAccountService:
    def __init__(self, db: Session):
        self.db = db

    def assign_child(
        self,
        link_data: AssignChildAccountRequest,
        parent_public_id: str
    ) -> AssignChildAccountResponse:
        logger.info(f"Parent {parent_public_id} attempting to assign child {link_data.child_email}")

        stmt_parent = select(Account).filter(Account.public_id == parent_public_id)
        relevant_account = self.db.execute(stmt_parent).scalar_one_or_none()

        if not relevant_account:
            logger.warning(f"Parent account not found for public_id: {parent_public_id}")
            raise AuthenticationError(
                message="Not authorized! No corresponding Account found",
                error_code=AppErrorCode.USER_NOT_FOUND
            )

        stmt_child = select(ChildAccount).filter(ChildAccount.email == link_data.child_email)
        relevant_child_account = self.db.execute(stmt_child).scalar_one_or_none()

        if not relevant_child_account:
            logger.warning(f"Child account not found with email: {link_data.child_email}")
            raise NotFoundError(
                message="Child account not found!",
                error_code=AppErrorCode.CHILD_ACCOUNT_NOT_FOUND,
                entity_name="ChildAccount",
                identifier=link_data.child_email
            )

        if relevant_child_account.parent_account_id:
            logger.warning(f"Child account {link_data.child_email} already linked to parent ID\n"
                           f"{relevant_child_account.parent_account_id}")
            raise BadRequestError(
                message="Child account already linked!",
                error_code=AppErrorCode.CHILD_ALREADY_LINKED
            )

        stmt_assignment = select(ParentChildAssignment).filter(
            ParentChildAssignment.parent_email == relevant_account.email,
            ParentChildAssignment.child_account_id == relevant_child_account.id
        )
        relevant_assignment = self.db.execute(stmt_assignment).scalar_one_or_none()

        if not relevant_assignment:
            logger.warning(f"No pending link request found for parent\n"
                           f"{relevant_account.email} and child {link_data.child_email}")
            raise NotFoundError(
                message="No pending link request found for this child",
                error_code=AppErrorCode.ASSIGNMENT_NOT_FOUND_OR_INVALID_CODE,
                entity_name="ParentChildAssignment",
                identifier=f"parent_email: {relevant_account.email}, child_id: {relevant_child_account.id}"
            )

        if relevant_assignment.verification_code != link_data.verification_code:
            logger.warning(f"Invalid verification code for parent\n"
                           f"{relevant_account.email} and child\n"
                           f"{link_data.child_email}")
            raise BadRequestError(
                message="Invalid verification code!",
                error_code=AppErrorCode.INVALID_VERIFICATION_CODE
            )

        relevant_child_account.parent_account_id = relevant_account.id
        relevant_child_account.is_verified = True

        stmt_trials = select(Trial).filter(Trial.child_account_id == relevant_child_account.id)
        trials_to_update = self.db.scalars(stmt_trials).all()

        for trial_row in trials_to_update:
            trial_row.account_id = relevant_account.id

        self.db.delete(relevant_assignment)

        try:
            self.db.commit()
            logger.info(
                f"Child account {relevant_child_account.public_id}\n"
                f"successfully linked to parent {relevant_account.public_id}")
        except Exception as e:
            self.db.rollback()
            logger.error(f"Database commit failed during child assignment: {str(e)}", exc_info=True)
            raise ServiceError(
                message="Could not link child account due to a database issue.",
                error_code=AppErrorCode.DATABASE_ERROR)

        return AssignChildAccountResponse(
            message="Child account linked successfully!",
            child_account_public_id=str(relevant_child_account.public_id)
        )