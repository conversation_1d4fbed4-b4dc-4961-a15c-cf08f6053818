from sqlalchemy.orm import Session
from sqlalchemy import select
from datetime import <PERSON><PERSON><PERSON>
from loguru import logger
from typing import List

from db.models import Account
from db.models.account import LanguageEnum as DbLanguageEnum
# Import get_formatted_raw_subscriptions and its response type
from api.v1.app.auth.parent.services.subscription_utils import get_formatted_raw_subscriptions, RawSubscription, get_formatted_subscriptions_by_year
from api.v1.app.auth.utils import create_access_token
from ..schemas.response import ValidateSessionResponse
# Updated imports for new response structure
from api.v1.app.auth.schemas.shared_schemas import UserTypeEnum, ParentUser, SubscriptionDetails
from core.config.settings import settings

class ParentValidateSessionService:
    def __init__(self, db: Session):
        self.db = db

    def validate_session(self, authenticated_account: Account) -> ValidateSessionResponse:
        logger.info(
            f"Validating session for parent: {authenticated_account.email}"
            f" public_id: {authenticated_account.public_id}") # Corrected log format

        db_language = authenticated_account.language
        api_language = DbLanguageEnum(db_language.value if db_language else 'lu')

        user_payload = ParentUser(
            public_id=str(authenticated_account.public_id),
            email=authenticated_account.email,
            first_name=None, # Parent account model does not have first_name
            language=api_language,
            user_type=UserTypeEnum.PARENT
        )

        token_data = {
            "account_public_id": str(authenticated_account.public_id),
            "user_type": "parent",
            "email": authenticated_account.email,
            "language": api_language.value,
        }
        access_token = create_access_token(
            data=token_data,
            expires_delta=timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
        )

        subscriptions_api = get_formatted_subscriptions_by_year(self.db, authenticated_account.id)

        logger.info(f"Session validated and token re-issued for parent: {authenticated_account.email}")
        return ValidateSessionResponse(
            access_token=access_token,
            user=user_payload,
            subscriptions=subscriptions_api
        )