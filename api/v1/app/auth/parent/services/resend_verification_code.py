from sqlalchemy.orm import Session
from sqlalchemy import select
from fastapi import BackgroundTasks
import random
from loguru import logger

from db.models import Account
from ..schemas.request import ResendVerificationCodeRequest
from ..schemas.response import ResendVerificationCodeResponse
from services.mailer.auth_mailer_service import AuthMailerService
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ConflictError, ServiceError, BadRequestError
)
from api.v1.common.schemas import AppErrorCode
from .email_rate_limiter import EmailRateLimiterService

class ParentResendVerificationService:
    def __init__(self, db: Session, auth_mailer_service: AuthMailerService):
        self.db = db
        self.auth_mailer_service = auth_mailer_service

    def resend(
        self,
        req: ResendVerificationCodeRequest,
        background_tasks: BackgroundTasks
    ) -> ResendVerificationCodeResponse:
        logger.info(f"Parent resend verification code request for: {req.email}")
        if not req.email:
            raise BadRequestError(
                message="Email is required",
                error_code=AppErrorCode.INVALID_REQUEST
            )

        # Check rate limiting before processing
        rate_limiter = EmailRateLimiterService(self.db)
        rate_limiter.check_and_update_rate_limit(req.email, "verification")

        stmt = select(Account).where(Account.email == req.email)
        account = self.db.execute(stmt).scalar_one_or_none()

        if not account:
            logger.warning(f"Account not found for resend verification: {req.email}")
            raise NotFoundError(
                message="Account not found",
                error_code=AppErrorCode.USER_NOT_FOUND,
                entity_name="Account",
                identifier=req.email
            )

        if account.is_verified:
            logger.info(f"Account {req.email} is already verified. No code resent.")
            raise ConflictError(
                message="Account already verified",
                error_code=AppErrorCode.ACCOUNT_ALREADY_VERIFIED
            )

        if not account.verification_code:
            account.verification_code = str(random.randint(100000, 999999))
            try:
                self.db.commit()
                logger.info(f"Generated new verification code for parent {req.email} during resend.")
            except Exception as e:
                self.db.rollback()
                logger.error(f"DB error saving new verification code for {req.email}: {e}", exc_info=True)
                raise ServiceError(
                    message="Failed to process request.",
                    error_code=AppErrorCode.DATABASE_ERROR
                )

        # Ensure language is a string value, not an enum
        language = req.language or (account.language.value if account.language else None) or 'lu'

        background_tasks.add_task(
            self.auth_mailer_service.send_parent_verification_email,
            to=req.email,
            verification_code=account.verification_code,
            lang=language
        )
        logger.info(f"Parent verification code resent to {req.email}")

        return ResendVerificationCodeResponse(
            message="Verification code resent"
        )