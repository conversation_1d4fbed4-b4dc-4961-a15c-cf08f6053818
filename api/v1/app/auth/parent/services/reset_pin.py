from sqlalchemy.orm import Session
from sqlalchemy import select
from datetime import datetime, UTC
from loguru import logger

from db.models import Account, PinResetRequest, AccountAuthSecurity
from ..schemas.request import ResetPinRequest
from ..schemas.response import ResetPinResponse
from api.v1.app.auth.utils import hash_parent_pin
from core.exception_handling.exceptions.custom_exceptions import (
    BadRequestError, NotFoundError, ValidationError, ServiceError
)
from api.v1.common.schemas import AppErrorCode

class ParentResetPinService:
    def __init__(self, db: Session):
        self.db = db

    def reset_pin(self, request: ResetPinRequest) -> ResetPinResponse:
        logger.info(f"Attempting to reset PIN with token: {request.reset_token[:10]}...")
        # ADDED LOGGING AND VARIABLE
        current_time_in_service = datetime.now(UTC)
        logger.info(f"Service check: Current UTC time in service for query = {current_time_in_service}")
        logger.info(f"Service check: Request reset_token for query = '{request.reset_token}'")
        # END ADDED

        if len(request.new_pin) != 6 or not request.new_pin.isdigit():
            logger.warning("Invalid PIN format during reset attempt.")
            raise ValidationError(
                message="PIN must be 6 digits.",
                error_code=AppErrorCode.INVALID_PIN_FORMAT
            )

        stmt = (
            select(PinResetRequest)
            .filter(PinResetRequest.request_token == request.reset_token)
            .filter(PinResetRequest.expires_at > current_time_in_service)
            .filter(PinResetRequest.used == False)  # noqa E712
        )
        reset_request_obj = self.db.execute(stmt).scalar_one_or_none()

        if not reset_request_obj:
            logger.warning(f"Invalid or expired PIN reset token used: {request.reset_token[:10]}...")
            raise BadRequestError(
                message="Invalid or expired reset request.",
                error_code=AppErrorCode.INVALID_OR_EXPIRED_TOKEN
            )

        stmt_account = select(Account).filter(Account.id == reset_request_obj.account_id)
        account = self.db.execute(stmt_account).scalar_one_or_none()

        if not account:
            logger.error(f"Associated account not found for \n"
                         f"valid PinResetRequest ID: {reset_request_obj.id}.\n"
                         f"This indicates a data integrity issue.")
            raise NotFoundError(
                message="Associated account not found.",
                log_message=f"Account not found for PinResetRequest ID {reset_request_obj.id} during PIN reset.",
                error_code=AppErrorCode.USER_NOT_FOUND,
                entity_name="Account",
                identifier=str(reset_request_obj.account_id)
            )

        account.pin_hash = hash_parent_pin(request.new_pin)
        logger.info(f"PIN hashed for account: {account.email}")

        stmt_auth_sec = select(AccountAuthSecurity).filter(
            AccountAuthSecurity.account_id == reset_request_obj.account_id)
        auth_security = self.db.execute(stmt_auth_sec).scalar_one_or_none()

        if not auth_security:
            auth_security = AccountAuthSecurity(account_id=reset_request_obj.account_id)
            self.db.add(auth_security)

        auth_security.failed_attempts = 0
        auth_security.locked_until = None
        reset_request_obj.used = True

        if not account.is_auth_migrated:
            account.is_auth_migrated = True

        account.is_verified = True

        try:
            self.db.commit()
            logger.info(f"PIN successfully reset and account {account.email} status updated.")
        except Exception as e:
            self.db.rollback()
            logger.error(f"Database commit failed during PIN reset for {account.email}: {str(e)}", exc_info=True)
            raise ServiceError(message="Could not reset PIN due to a database issue.",
                               error_code=AppErrorCode.SERVICE_ERROR)

        return ResetPinResponse(message="PIN successfully reset")