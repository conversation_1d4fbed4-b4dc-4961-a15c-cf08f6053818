from sqlalchemy.orm import Session
from sqlalchemy import select
from fastapi import BackgroundTasks
from datetime import timedelta, datetime, UTC
from loguru import logger
from typing import List, Optional

from db.models import Account, AccountAuthSecurity, PinResetRequest, ChildAccount
from db.models.account import LanguageEnum as DbLanguageEnum  # Import DB LanguageEnum
from api.v1.app.auth.utils import (
    check_auth_lock,
    update_failed_attempt,
    verify_parent_pin,
    create_access_token
)
from ..schemas.request import LoginRequest
from ..schemas.response import LoginResponse
# Updated imports for new response structure
from api.v1.app.auth.schemas.shared_schemas import UserTypeEnum, ParentUser, ChildUser, SubscriptionDetails, SchoolYearResponse
# Import the utility for fetching subscriptions
from api.v1.app.auth.parent.services.subscription_utils import get_formatted_raw_subscriptions, RawSubscription, get_formatted_subscriptions_by_year

from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError,
    AuthenticationError,
    ServiceError
)
from api.v1.common.schemas import AppErrorCode
from services.mailer.auth_mailer_service import AuthMailerService
from api.v1.app.auth.parent.schemas.response import LoginChildResponse

from core.config.settings import settings

ACCESS_TOKEN_EXPIRE_MINUTES = int(settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)

class ParentLoginService:
    def __init__(self, db: Session, auth_mailer_service: AuthMailerService):
        self.db = db
        self.auth_mailer_service = auth_mailer_service

    def login_parent(
        self,
        login_attempt: LoginRequest,
        background_tasks: BackgroundTasks
    ) -> LoginResponse:
        input_email = login_attempt.email.strip().lower()
        login_pin = login_attempt.login_pin
        logger.info(f"Parent login attempt for: {input_email}")

        stmt = select(Account).where(Account.email == input_email)
        existing_parent_account: Optional[Account] = self.db.execute(stmt).scalar_one_or_none()

        if existing_parent_account is None:
            logger.warning(f"Parent login attempt for non-existent account: {input_email}")
            raise NotFoundError(
                message="Account does not exist",
                log_message=f"Parent account not found with email {input_email} during login.",
                error_code=AppErrorCode.USER_NOT_FOUND,
                entity_name="Account",
                identifier=input_email
            )

        if existing_parent_account.is_auth_migrated:
            logger.info(f"Account {input_email} is auth migrated. Proceeding with PIN check.")
            auth_security_stmt = select(AccountAuthSecurity).filter(
                AccountAuthSecurity.account_id == existing_parent_account.id
            )
            auth_security: Optional[AccountAuthSecurity] = self.db.execute(auth_security_stmt).scalar_one_or_none()

            if not auth_security:
                auth_security = AccountAuthSecurity(account_id=existing_parent_account.id)
                self.db.add(auth_security)
                try:
                    self.db.commit()
                    self.db.refresh(auth_security)
                except Exception as e:
                    self.db.rollback()
                    logger.error(f"DB error creating AuthSecurity for {input_email}: {e}", exc_info=True)
                    raise ServiceError(message="Login failed due to an internal issue.",
                                       error_code=AppErrorCode.DATABASE_ERROR)

            auth_lock_status = check_auth_lock(auth_security)
            if auth_lock_status["status"] == "locked":
                logger.warning(f"Login attempt for locked account: {input_email}")
                raise AuthenticationError(
                    message=f"Account locked. Try again in {auth_lock_status['retry_after_minutes']} minutes.",
                    error_code=AppErrorCode.ACCOUNT_LOCKED,
                    log_message=f"Account {input_email} is locked. Login attempt blocked."
                )

            if not existing_parent_account.is_verified:
                logger.warning(f"Login attempt for unverified account: {input_email}")
                raise AuthenticationError(
                    message="Account not verified. Please check your email.",
                    error_code=AppErrorCode.ACCOUNT_NOT_VERIFIED,
                    log_message=f"Account {input_email} is not verified. Login attempt blocked."
                )

            if not verify_parent_pin(login_pin, existing_parent_account.pin_hash):
                logger.warning(f"Invalid PIN for parent login: {input_email}")

                auth_security = update_failed_attempt(auth_security)
                try:
                    self.db.commit()
                except Exception as e:
                    self.db.rollback()
                    logger.error(f"DB error updating failed attempts for {input_email}: {e}", exc_info=True)
                    raise ServiceError(message="Login failed due to an internal issue.",
                                       error_code=AppErrorCode.SERVICE_ERROR)

                max_pin_attempts = int(settings.PIN_ATTEMPT_LIMITS[-1][0]) if settings.PIN_ATTEMPT_LIMITS else 5
                attempts_remaining_val = max_pin_attempts - auth_security.failed_attempts
                if attempts_remaining_val < 0:
                    attempts_remaining_val = 0

                raise AuthenticationError(
                    message=f"Invalid PIN. {attempts_remaining_val} attempts remaining before lockout.",
                    error_code=AppErrorCode.INVALID_CREDENTIALS,
                    log_message=f"Invalid PIN for account {input_email}."
                )

            auth_security.failed_attempts = 0
            auth_security.locked_until = None
            try:
                self.db.commit()
            except Exception as e:
                self.db.rollback()
                logger.error(f"DB error resetting failed attempts for {input_email}: {e}", exc_info=True)
                raise ServiceError(message="Login failed due to an internal issue.",
                                   error_code=AppErrorCode.SERVICE_ERROR)

            db_language = existing_parent_account.language
            api_language = DbLanguageEnum(db_language.value if db_language else 'lu')


            user_payload = ParentUser(
                public_id=str(existing_parent_account.public_id),
                email=existing_parent_account.email,
                first_name=None,  # Parent account model does not have first_name
                language=api_language,
                user_type=UserTypeEnum.PARENT
            )

            token_data = {
                "account_public_id": str(existing_parent_account.public_id),
                "user_type": "parent",
                "email": input_email,
                "language": api_language.value,
            }
            access_token = create_access_token(
                data=token_data, expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES))

            subscriptions_api = get_formatted_subscriptions_by_year(
                self.db, existing_parent_account.id)


            max_pin_attempts = int(settings.PIN_ATTEMPT_LIMITS[-1][0]) if settings.PIN_ATTEMPT_LIMITS else 5
            attempts_remaining_val = max_pin_attempts - (auth_security.failed_attempts if auth_security else 0)
            if attempts_remaining_val < 0:
                attempts_remaining_val = 0

            locked_until_str = str(auth_security.locked_until) if auth_security and auth_security.locked_until else None

            logger.info(f"Parent login successful for: {input_email}")
            return LoginResponse(
                access_token=access_token,
                user=user_payload,
                subscriptions=subscriptions_api,
                is_auth_migrated=existing_parent_account.is_auth_migrated,
                attempts_remaining=attempts_remaining_val,
                locked_until=locked_until_str
            )
        else:  # Account not migrated
            logger.info(f"Account {input_email} not migrated. Initiating PIN reset for migration.")
            # ... (rest of the migration logic remains the same)
            stmt_last_req = select(PinResetRequest).filter(
                PinResetRequest.account_id == existing_parent_account.id,
                PinResetRequest.used is False
            ).order_by(PinResetRequest.created_at.desc())
            last_reset_request_row = self.db.execute(stmt_last_req).first()
            last_reset_request = last_reset_request_row[0] if last_reset_request_row else None

            reset_token_to_send = None
            if last_reset_request and last_reset_request.expires_at > datetime.now(UTC):
                if last_reset_request.created_at <= datetime.now(UTC) - timedelta(minutes=1):  # Rate limit check
                    reset_token_to_send = last_reset_request.request_token

            if not reset_token_to_send:
                new_reset_request = PinResetRequest(account_id=existing_parent_account.id)
                self.db.add(new_reset_request)
                try:
                    self.db.commit()
                    self.db.refresh(new_reset_request)
                    reset_token_to_send = new_reset_request.request_token
                except Exception as e:
                    self.db.rollback()
                    logger.error(f"DB error creating PinResetRequest for unmigrated {input_email}: {e}", exc_info=True)
                    # For unmigrated accounts, we still want to guide them to reset, so we don't return the full auth payload yet.
                    # The frontend expects a specific response for migration.
                    # The LoginResponse schema now expects 'user', which we don't fully form here.
                    # This part of the logic might need to return a different response type or the frontend needs to handle an error + message.
                    # For now, raising AuthenticationError as before, which implies the frontend handles this state.
                    raise AuthenticationError(
                        message="Account requires migration. A PIN reset email has been sent to complete setup.",
                        error_code=AppErrorCode.ACCOUNT_MIGRATION_REQUIRED,
                        log_message=f"Account {input_email} requires migration. PIN reset email sent."
                    )


            if reset_token_to_send:
                db_language = existing_parent_account.language
                api_language_value = db_language.value if db_language else 'lu'
                background_tasks.add_task(
                    self.auth_mailer_service.send_parent_pin_reset_email,
                    to=input_email,
                    reset_token=reset_token_to_send,
                    lang=api_language_value
                )
                logger.info(f"Sent PIN reset email for unmigrated account {input_email} during login flow.")
            else:
                logger.error(f"Could not obtain a reset token for unmigrated account {input_email} during login flow.")

            raise AuthenticationError(
                message="Account requires migration. A PIN reset email has been sent to complete setup.",
                error_code=AppErrorCode.ACCOUNT_MIGRATION_REQUIRED,
                log_message=f"Account {input_email} requires migration. PIN reset email sent."
            )


    def login_child(self, child_public_id: str, parent_account_orm: Account) -> LoginChildResponse:
        logger.info(f"Parent {parent_account_orm.email} attempting to log in as child {child_public_id}")
        stmt_child = select(ChildAccount).where(ChildAccount.public_id == child_public_id).where(
            ChildAccount.parent_account_id == parent_account_orm.id)
        relevant_child_account: Optional[ChildAccount] = self.db.execute(stmt_child).scalar_one_or_none()

        if not relevant_child_account:
            logger.warning(
                f"Child account {child_public_id} not found or not linked to parent {parent_account_orm.email}")
            raise NotFoundError(
                message="Child account not found or does not belong to this parent.",
                error_code=AppErrorCode.CHILD_ACCOUNT_NOT_FOUND,
                entity_name="ChildAccount",
                identifier=child_public_id
            )

        db_language = relevant_child_account.language
        api_language = DbLanguageEnum(db_language.value if db_language else 'lu')
        relevant_child_year = relevant_child_account.year

        user_payload = ChildUser(
            public_id=str(relevant_child_account.public_id),
            email=relevant_child_account.email,
            name=relevant_child_account.name,
            language=api_language,
            parent_public_id=str(parent_account_orm.public_id),
            user_type=UserTypeEnum.CHILD,
            school_year=SchoolYearResponse(public_id=str(relevant_child_year.public_id), name=relevant_child_year.name)
        )

        token_data = {
            "account_public_id": str(relevant_child_account.public_id), 
            "user_type": "child",
            "parent_public_id": str(parent_account_orm.public_id),
            "name": relevant_child_account.name,
            "language": api_language.value
        }
        access_token = create_access_token(
            data=token_data,
            expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        )


        # Subscriptions are tied to the parent account
        subscriptions_api = get_formatted_subscriptions_by_year(self.db, parent_account_orm.id)

        logger.info(
            f"Parent {parent_account_orm.email} successfully logged in as child: {relevant_child_account.email}, public_id: {relevant_child_account.public_id}")
        return LoginChildResponse(access_token=access_token, user=user_payload, subscriptions=subscriptions_api)