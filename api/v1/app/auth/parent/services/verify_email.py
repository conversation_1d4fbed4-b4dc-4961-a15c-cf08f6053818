from sqlalchemy.orm import Session
from sqlalchemy import select
from loguru import logger

from db.models import Account
from ..schemas.request import VerifyAccountRequest
from ..schemas.response import VerifyAccountResponse
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ConflictError, BadRequestError, ServiceError
)
from api.v1.common.schemas import AppErrorCode

class ParentEmailVerificationService:
    def __init__(self, db: Session):
        self.db = db

    def verify(self, verification: VerifyAccountRequest) -> VerifyAccountResponse:
        logger.info("Parent email verification attempt for {}.", verification.email)

        stmt = select(Account).filter(Account.email == verification.email)
        account = self.db.execute(stmt).scalar_one_or_none()

        if not account:
            logger.warning("Account not found for email verification: {}.",
                           verification.email)
            raise NotFoundError(
                message="Account not found.",
                error_code=AppErrorCode.USER_NOT_FOUND,
                entity_name="Account",
                identifier=verification.email
            )

        if account.is_verified:
            logger.info("Account {} (public_id: {}) is already verified.", 
                        verification.email, account.public_id)
            raise ConflictError(
                message="Account already verified.",
                error_code=AppErrorCode.ACCOUNT_ALREADY_VERIFIED
            )

        if account.verification_code != verification.verification_code:
            logger.warning("Invalid verification code for {} (public_id: {}). Submitted: {}, Expected: {}",
                           verification.email,
                           account.public_id,
                           verification.verification_code,
                           account.verification_code)
            raise BadRequestError(
                message="Invalid verification code.",
                error_code=AppErrorCode.INVALID_VERIFICATION_CODE
            )

        account.is_verified = True
        try:
            self.db.commit()
            logger.info("Account {} (public_id: {}) successfully verified.",
                        verification.email, account.public_id)
        except Exception as e:
            self.db.rollback()
            logger.error("Database commit failed during email verification for {} (public_id: {}): {}",
                         verification.email, account.public_id, str(e), exc_info=True)
            raise ServiceError(message="Could not verify email due to a database issue.",
                               error_code=AppErrorCode.DATABASE_ERROR)

        return VerifyAccountResponse(
            message="Account verified successfully",
            is_verified=True
        )