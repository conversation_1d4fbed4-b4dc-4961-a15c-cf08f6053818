from sqlalchemy.orm import Session
from sqlalchemy import select
from datetime import datetime, timedelta, UTC
from loguru import logger

from db.models import EmailRateLimit
from core.exception_handling.exceptions.custom_exceptions import TooManyRequestsError
from api.v1.common.schemas import AppErrorCode

class EmailRateLimiterService:
    """
    Rate limiting service for email sending to prevent abuse.
    
    Limits:
    - 2 emails per minute per email/type combination
    - 10 emails per hour per email/type combination  
    - 50 emails per day per email/type combination
    """
    
    # Rate limiting thresholds
    MAX_ATTEMPTS_PER_MINUTE = 3
    MAX_ATTEMPTS_PER_HOUR = 10
    MAX_ATTEMPTS_PER_DAY = 50
    
    def __init__(self, db: Session):
        self.db = db
    
    def check_and_update_rate_limit(self, email: str, email_type: str) -> None:
        """
        Check if the email/type combination is within rate limits and update counters.
        
        Args:
            email: The email address
            email_type: Type of email ('verification', 'pin_reset', etc.)
            
        Raises:
            TooManyRequestsError: If rate limit is exceeded
        """
        email = email.strip().lower()
        now = datetime.now(UTC)
        
        # Get or create rate limit record
        stmt = select(EmailRateLimit).where(
            EmailRateLimit.email == email,
            EmailRateLimit.email_type == email_type
        )
        rate_limit = self.db.execute(stmt).scalar_one_or_none()
        
        if not rate_limit:
            # Create new record
            rate_limit = EmailRateLimit(
                email=email,
                email_type=email_type,
                attempts_last_minute=1,
                attempts_last_hour=1,
                attempts_last_day=1,
                minute_window_start=now,
                hour_window_start=now,
                day_window_start=now,
                last_attempt=now
            )
            self.db.add(rate_limit)
            try:
                self.db.commit()
                logger.info(f"Created new rate limit record for {email} ({email_type})")
                return
            except Exception as e:
                self.db.rollback()
                logger.error(f"Failed to create rate limit record: {e}")
                raise
        
        # Reset windows if they've expired
        self._reset_expired_windows(rate_limit, now)
        
        # Check rate limits
        if rate_limit.attempts_last_minute >= self.MAX_ATTEMPTS_PER_MINUTE:
            logger.warning(f"Rate limit exceeded (minute) for {email} ({email_type}): {rate_limit.attempts_last_minute} attempts")
            raise TooManyRequestsError(
                message="Too many requests. Please wait a moment before trying again.",
                error_code=AppErrorCode.TOO_MANY_REQUESTS
            )
        
        if rate_limit.attempts_last_hour >= self.MAX_ATTEMPTS_PER_HOUR:
            logger.warning(f"Rate limit exceeded (hour) for {email} ({email_type}): {rate_limit.attempts_last_hour} attempts")
            raise TooManyRequestsError(
                message="Too many requests. Please wait before trying again.",
                error_code=AppErrorCode.TOO_MANY_REQUESTS
            )
        
        if rate_limit.attempts_last_day >= self.MAX_ATTEMPTS_PER_DAY:
            logger.warning(f"Rate limit exceeded (day) for {email} ({email_type}): {rate_limit.attempts_last_day} attempts")
            raise TooManyRequestsError(
                message="Daily limit reached. Please try again tomorrow.",
                error_code=AppErrorCode.TOO_MANY_REQUESTS
            )
        
        # Update counters
        rate_limit.attempts_last_minute += 1
        rate_limit.attempts_last_hour += 1
        rate_limit.attempts_last_day += 1
        rate_limit.last_attempt = now
        
        try:
            self.db.commit()
            logger.info(f"Updated rate limit for {email} ({email_type}): "
                       f"minute={rate_limit.attempts_last_minute}, "
                       f"hour={rate_limit.attempts_last_hour}, "
                       f"day={rate_limit.attempts_last_day}")
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to update rate limit record: {e}")
            raise
    
    def _reset_expired_windows(self, rate_limit: EmailRateLimit, now: datetime) -> None:
        """Reset rate limit windows that have expired."""
        
        # Reset minute window (60 seconds)
        if now - rate_limit.minute_window_start >= timedelta(minutes=1):
            rate_limit.attempts_last_minute = 0
            rate_limit.minute_window_start = now
        
        # Reset hour window (60 minutes)
        if now - rate_limit.hour_window_start >= timedelta(hours=1):
            rate_limit.attempts_last_hour = 0
            rate_limit.hour_window_start = now
        
        # Reset day window (24 hours)
        if now - rate_limit.day_window_start >= timedelta(days=1):
            rate_limit.attempts_last_day = 0
            rate_limit.day_window_start = now
    
    def get_rate_limit_status(self, email: str, email_type: str) -> dict:
        """
        Get current rate limit status for debugging/monitoring.
        
        Returns:
            dict: Current attempt counts and window start times
        """
        email = email.strip().lower()
        now = datetime.now(UTC)
        
        stmt = select(EmailRateLimit).where(
            EmailRateLimit.email == email,
            EmailRateLimit.email_type == email_type
        )
        rate_limit = self.db.execute(stmt).scalar_one_or_none()
        
        if not rate_limit:
            return {
                "attempts_last_minute": 0,
                "attempts_last_hour": 0,
                "attempts_last_day": 0,
                "limits": {
                    "minute": self.MAX_ATTEMPTS_PER_MINUTE,
                    "hour": self.MAX_ATTEMPTS_PER_HOUR,
                    "day": self.MAX_ATTEMPTS_PER_DAY
                }
            }
        
        # Reset expired windows for accurate status
        self._reset_expired_windows(rate_limit, now)
        
        return {
            "attempts_last_minute": rate_limit.attempts_last_minute,
            "attempts_last_hour": rate_limit.attempts_last_hour,
            "attempts_last_day": rate_limit.attempts_last_day,
            "minute_window_start": rate_limit.minute_window_start,
            "hour_window_start": rate_limit.hour_window_start,
            "day_window_start": rate_limit.day_window_start,
            "last_attempt": rate_limit.last_attempt,
            "limits": {
                "minute": self.MAX_ATTEMPTS_PER_MINUTE,
                "hour": self.MAX_ATTEMPTS_PER_HOUR,
                "day": self.MAX_ATTEMPTS_PER_DAY
            }
        } 