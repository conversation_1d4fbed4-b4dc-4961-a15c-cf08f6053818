import uuid
import json
import random
from datetime import datetime, timedelta, UTC
from typing import Op<PERSON>, Dict, <PERSON>, <PERSON><PERSON>, List

import stripe
from fastapi import BackgroundTasks
from loguru import logger
from sqlalchemy.orm import Session
from sqlalchemy import select
from core.config.settings import settings

from db.models import (
    Account, 
    SignUp, 
    Trial, 
    MarketingConsent, 
    PriceVersion, 
    PriceEligibility,
    AccountAuthSecurity,
    ChildAccount
)
from db.models.account import LanguageEnum as DbLanguageEnum # Import DB LanguageEnum

from core.exception_handling.exceptions.custom_exceptions import (
    ConflictError, ServiceError, ExternalServiceError
)
from api.v1.common.schemas import AppErrorCode
from api.v1.app.auth.utils import create_access_token, hash_parent_pin
from services.mailer.auth_mailer_service import AuthMailerService
from services.email_list.service import ListService, ListContext

stripe.api_key = settings.STRIPE_API_KEY


def add_to_mailing_lists_task(
    list_service: ListService, 
    email: str, 
    language: str, 
    ip_address: Optional[str] = None, 
    privacy_policy_version: Optional[str] = None, 
    optin_text: Optional[str] = None,
    signup_source: Optional[str] = "unknown_source"
):
    """Background task to add user to mailing lists"""
    try:
        list_ctx = ListContext(
            email=email,
            language=language,
            fields={"source": signup_source}
        )
        list_service.add_to_all_lists(list_ctx)

        if ip_address and privacy_policy_version and optin_text:
            marketing_ctx = ListContext(
                email=email,
                language=language,
                ip_address=ip_address,
                privacy_policy_version=privacy_policy_version,
                optin_text=optin_text,
                fields={"source": signup_source}
            )
            list_service.add_to_marketing_list(marketing_ctx)

        logger.info("User {} (source: {}) added to mailing lists.", email, signup_source)
    except Exception as e:
        logger.error("Failed to add {} (source: {}) to mailing lists: {}",
                     email, signup_source, str(e), exc_info=True)

def create_parent_account(
    db: Session,
    email: str,
    language: str, # Expects string 'lu', 'en', etc.
    pin: Optional[str] = None,
    is_verified: bool = False,
    background_tasks: Optional[BackgroundTasks] = None,
    auth_mailer_service: Optional[AuthMailerService] = None,
    list_service: Optional[ListService] = None,
    marketing_data: Optional[Dict[str, Any]] = None,
    signup_source: str = "direct",
    signup_searchparams: Optional[str] = None,
    year_options: Optional[List[str]] = None,
    system_options: Optional[List[str]] = None,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None,
    is_auth_migrated: bool = True,
    child_account_id: Optional[int] = None,
) -> Tuple[Account, str]: # Returns ORM Account object and access_token
    """
    Create a parent account with common functionality for both direct signup
    and child account parent assignment.
    """
    input_email = email.strip().lower()
    logger.info(f"Attempting to create parent account for email: {input_email}, source: {signup_source}")
    account_public_id = str(uuid.uuid4())
    verification_code = ''.join([str(random.randint(0, 9)) for _ in range(6)])
    stripe_response = None

    # Check for existing accounts with this email
    stmt_existing_parent = select(Account).where(Account.email == input_email)
    existing_account = db.execute(stmt_existing_parent).scalar_one_or_none()
    if existing_account is not None:
        logger.warning("Parent account creation attempt for existing email: {}", input_email)
        raise ConflictError(
            message="An account with this email already exists.",
            error_code=AppErrorCode.PARENT_ACCOUNT_ALREADY_EXISTS,
            log_message=f"Attempt to create parent account with existing email: {input_email}"
        )

    # Check if there is a child account with the same email (only for direct signups)
    stmt_existing_child = select(ChildAccount).where(ChildAccount.email == input_email)
    existing_child_account = db.execute(stmt_existing_child).scalar_one_or_none()
    if existing_child_account is not None:
        logger.warning("Parent account creation attempt for email already used by a child: {}", input_email)
        raise ConflictError(
            message="This email is already associated with a child account. Please use a different email.",
            error_code=AppErrorCode.CHILD_ACCOUNT_ALREADY_EXISTS,
            log_message=f"Attempt to create parent account with email already used by child: {input_email}"
        )

    db_language_enum_val = DbLanguageEnum(language) # Convert string to DB Enum

    try:
        # Create Stripe customer
        logger.info("Creating Stripe customer for {}, public_id: {}", input_email, account_public_id)
        stripe_response = stripe.Customer.create(
            description=f"Parent Account: {account_public_id}",
            email=input_email
        )
        logger.info("Stripe customer created: {} for {}", stripe_response.id, input_email)

        # Create account record
        new_account_orm = Account( # Renamed to new_account_orm
            email=input_email,
            language=db_language_enum_val, # Use DB Enum
            verification_code=verification_code,
            pin_hash=hash_parent_pin(pin) if pin else None,
            public_id=account_public_id,
            stripe_customer_id=stripe_response.id,
            is_verified=is_verified,
            is_auth_migrated=is_auth_migrated
        )
        db.add(new_account_orm)
        db.flush()  # Get new_account_orm.id
        logger.info("Parent account DB object created for {}, ID: {}", input_email, new_account_orm.id)

        if pin:
            new_auth_security = AccountAuthSecurity(
                account_id=new_account_orm.id,
                failed_attempts=0,
                locked_until=None
            )
            db.add(new_auth_security)
            logger.info("AuthSecurity record created for account ID: {}, public_id: {}",
                        new_account_orm.id, account_public_id)

        current_price_version = db.query(PriceVersion).filter_by(
            is_current=True, is_active=True
        ).first()

        if current_price_version:
            new_price_eligibility = PriceEligibility(
                account_id=new_account_orm.id,
                price_version_id=current_price_version.id
            )
            db.add(new_price_eligibility)
            logger.info("PriceEligibility record created for account ID: {}, PriceVersion ID: {}",
                        new_account_orm.id, current_price_version.id)
        else:
            logger.warning("No current and active PriceVersion found. PriceEligibility not created.")

        if marketing_data and list_service and background_tasks:
            new_marketing_consent = MarketingConsent(
                email=input_email,
                account_id=new_account_orm.id,
                user_agent=user_agent,
                ip_address=ip_address,
                text=marketing_data.get('marketing_consent_text'),
                privacy_policy_version=marketing_data.get('privacy_policy_version')
            )
            db.add(new_marketing_consent)
            logger.info("MarketingConsent record created for account ID: {}, public_id: {}",
                        new_account_orm.id, account_public_id)

            if settings.ENVIRONMENT != 'dev':
                background_tasks.add_task(
                    add_to_mailing_lists_task,
                    list_service,
                    input_email,
                    language, # Pass original string language
                    ip_address,
                    marketing_data.get('privacy_policy_version'),
                    marketing_data.get('marketing_consent_text'),
                    signup_source
                )
            logger.info("Mailing list task enqueued for {}", input_email)

        new_sign_up = SignUp(
            account_id=new_account_orm.id,
            email=input_email,
            language=language, # Pass original string language
            source=signup_source,
            year_options=json.dumps(year_options or []),
            system_options=json.dumps(system_options or []),
            searchparams=signup_searchparams,
        )
        db.add(new_sign_up)
        logger.info("SignUp record created for account ID: {}, public_id: {}", new_account_orm.id, account_public_id)

        new_trial = Trial(
            account_id=new_account_orm.id,
            status='active',
            start_date=datetime.now(UTC),
            end_date=datetime.now(UTC) + timedelta(days=settings.TRIAL_DURATION_DAYS)
        )
        db.add(new_trial)
        logger.info("Trial record created for account ID: {}, public_id: {}", new_account_orm.id, account_public_id)

        if child_account_id:
            child_account = db.query(ChildAccount).filter_by(
                id=child_account_id
            ).first()

            if child_account:
                child_account.parent_account_id = new_account_orm.id # Corrected attribute name
                child_account.is_verified = True
                logger.info("Linked child account ID {} to new parent account ID {}", child_account_id, new_account_orm.id)
            else:
                logger.warning("Child account ID {} provided for linking but not found.", child_account_id)
                # This might warrant an error depending on strictness
                # For now, just logging. If critical, raise NotFoundError.

        if background_tasks and auth_mailer_service and not is_verified:
            background_tasks.add_task(
                auth_mailer_service.send_parent_verification_email, 
                to=input_email, 
                verification_code=verification_code,  
                lang=language # Pass original string language
            )
            logger.info("Parent verification email task added for {}", input_email)

        token_data = {
            "account_public_id": account_public_id,  # Use "sub" for subject
            "user_type": "parent",  # Add user_type
            "email": input_email,
            "language": language, # Use original string language for token
            "active_subscriptions": [] # Keep this if needed for token data
        }

        access_token = create_access_token(
            data=token_data,
            expires_delta=timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
        )

        # db.commit() is handled by the calling route/service
        logger.info("Parent account {} (public_id: {}) ORM object prepared.", input_email, account_public_id)
        return new_account_orm, access_token # Return ORM object

    except stripe.error.StripeError as se:
        db.rollback()
        logger.error("Stripe error during parent account creation for {} (public_id: {}): {}",
                     input_email, account_public_id, str(se), exc_info=True)
        raise ExternalServiceError(
            message="Could not create your account due to a payment processing issue.",
            log_message=f"Stripe error for {input_email}: {str(se)}",
            error_code=AppErrorCode.STRIPE_ERROR,
            original_exception=se
        )
    except Exception as e:
        db.rollback()
        # Attempt to delete Stripe customer if one was created before this subsequent error
        if stripe_response and stripe_response.id: 
            try:
                logger.info("Attempting to delete Stripe customer {} due to error.", stripe_response.id)
                stripe.Customer.delete(stripe_response.id)
                logger.info("Successfully deleted Stripe customer {} during rollback.", stripe_response.id)
            except stripe.error.StripeError as se_del:
                logger.error("Failed to delete Stripe customer {} during rollback: {}",
                             stripe_response.id, se_del, exc_info=True)
            except Exception as ex_del:  # Catch any other exception during delete
                logger.error("Unexpected error deleting Stripe customer {} during rollback: {}",
                             stripe_response.id, ex_del, exc_info=True)

        logger.error("Unexpected error in create_parent_account for {} (public_id: {}): {}",
                     input_email, account_public_id, str(e), exc_info=True)

        if isinstance(e, (ServiceError, ConflictError, ExternalServiceError)): # Re-raise if it's already one of our specific errors
            raise e

        # If Stripe was configured and stripe_response is still None, it means stripe.Customer.create() likely failed.
        # This covers the mocked "Stripe API Error" case, as well as other non-StripeError exceptions from that call.
        if settings.STRIPE_API_KEY and stripe_response is None:
            raise ExternalServiceError(
                message="Could not create your account due to an issue with the payment service.",
                log_message="Payment service interaction error for {} (public_id: {}): {}".format(
                    input_email, account_public_id, str(e)),
                error_code=AppErrorCode.STRIPE_ERROR, 
                original_exception=e
            )

        # Fallback for other unexpected errors not related to initial Stripe customer creation.
        raise ServiceError(
            message="Could not register account due to an internal server issue.",
            log_message="Unexpected internal error creating parent account for {} (public_id: {}): {}".format(
                input_email, account_public_id, str(e)),
            error_code=AppErrorCode.SERVICE_ERROR # Changed from STRIPE_ERROR to generic SERVICE_ERROR
        )