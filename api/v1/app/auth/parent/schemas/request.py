from pydantic import BaseModel, Field
from typing import Optional, List

class SignupRequest(BaseModel):
    email: str
    language: str
    system_options: List[str]
    year_options: List[str]
    marketing_consent: bool
    user_agent: str
    marketing_consent_version: Optional[str] = None
    privacy_policy_version: Optional[str] = None
    signup_source: Optional[str]
    signup_searchparams: Optional[str]
    login_pin: str = Field(..., min_length=6, max_length=6, pattern=r'^\d+$')

class LoginRequest(BaseModel):
    email: str
    login_pin: str = Field(..., min_length=6, max_length=6, pattern=r'^\d+$')


class ParentLoginChildRequest(BaseModel):
    child_public_id: str

class VerifyAccountRequest(BaseModel):
    email: str
    verification_code: str

class ResendVerificationCodeRequest(BaseModel):
    email: str
    language: str = 'en'

class ResetPinRequest(BaseModel):
    reset_token: str
    new_pin: str = Field(..., min_length=6, max_length=6, pattern=r'^\d+$')

class TriggerPinResetRequest(BaseModel):
    email: str
    language: str = 'en'


class VerificationStatusRequest(BaseModel):
    email: str

class AssignChildAccountRequest(BaseModel):
    child_email: str
    verification_code: str