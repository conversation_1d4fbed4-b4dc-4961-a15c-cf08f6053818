from pydantic import BaseModel
from typing import Optional, List
from api.v1.app.auth.schemas.shared_schemas import  User, SubscriptionDetails, YearSubscription # Updated imports

class SignupResponse(BaseModel):
    message: str
    access_token: str # Made non-optional as successful signup should provide a token
    user: User
    subscriptions: List[YearSubscription] = []

class AccessTokenResponse(BaseModel):
    access_token: str

class VerifyAccountResponse(BaseModel):
    message: str
    is_verified: bool

class ResendVerificationCodeResponse(BaseModel):
    message: str

class TriggerPinResetResponse(BaseModel):
    message: str

class ResetPinResponse(BaseModel):
    message: str

class VerificationStatusResponse(BaseModel):
    is_auth_migrated: bool
    is_verified: Optional[bool] = None


class AssignChildAccountResponse(BaseModel):
    message: str
    child_account_public_id: str


class LoginResponse(BaseModel):
    access_token: str 
    user: User # Replaced account_info
    subscriptions: List[YearSubscription] = []
    is_auth_migrated: Optional[bool] = None
    attempts_remaining: Optional[int] = None
    locked_until: Optional[str] = None

# Added ValidateSessionResponse
class ValidateSessionResponse(BaseModel):
    access_token: str
    user: User # Replaced account_info
    subscriptions: List[YearSubscription] = []

class LoginChildResponse(BaseModel):
    access_token: str
    user: User # Replaced account_info
    subscriptions: List[YearSubscription] = []
