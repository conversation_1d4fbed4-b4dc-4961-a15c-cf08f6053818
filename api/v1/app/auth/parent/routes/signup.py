from fastapi import API<PERSON><PERSON><PERSON>, Depends, Header, BackgroundTasks
from sqlalchemy.orm import Session
from db.database import get_db
from ..schemas.response import SignupResponse
from ..schemas.request import SignupRequest
from services.mailer.auth_mailer_service import AuthMailerService
from dependencies.email_dependency import get_auth_mailer_service
from dependencies.list_dependency import get_list_service
from services.email_list.service import ListService
from ..services.signup import ParentSignupService

router = APIRouter()

@router.post('/signup', status_code=201, response_model=SignupResponse)
def signup(
    new_account_request: SignupRequest,
    background_tasks: BackgroundTasks,
    ip_address: str = Header(None, alias='x-forwarded-for'), 
    db: Session = Depends(get_db), 
    auth_mailer_service: AuthMailerService = Depends(get_auth_mailer_service),
    list_service: ListService = Depends(get_list_service)
):
    service = ParentSignupService(
        db=db,
        auth_mailer_service=auth_mailer_service,
        list_service=list_service
    )
    return service.signup_parent(
        new_account_request=new_account_request,
        ip_address=ip_address,
        background_tasks=background_tasks
    )