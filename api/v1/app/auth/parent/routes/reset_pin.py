from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from db.database import get_db
from ..schemas.request import ResetPinRequest
from ..schemas.response import ResetPinResponse
from ..services.reset_pin import ParentResetPinService

router = APIRouter()

@router.post('/reset-pin', response_model=ResetPinResponse)
def reset_pin(
    request: ResetPinRequest,
    db: Session = Depends(get_db)
):
    service = ParentResetPinService(db=db)
    return service.reset_pin(request)