from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.auth_dependency import AuthDependency, AuthDependencyResponse
from ..services.validate_session_service import ParentValidateSessionService
from ..schemas.response import ValidateSessionResponse
from db.models import Account
from dependencies.auth_dependencies.base_auth_dependency import UserType 
from core.exception_handling.exceptions.custom_exceptions import AuthenticationError, ServiceError 
from api.v1.common.schemas import AppErrorCode 

router = APIRouter()

# Configure AuthDependency for parent users - no required_user_type here
require_parent_auth = AuthDependency(required=True)

@router.post('/validate-session', response_model=ValidateSessionResponse)
def validate_parent_session(
    db: Session = Depends(get_db),
    auth_response: AuthDependencyResponse = Depends(require_parent_auth)
):
    if auth_response.user_type != UserType.parent:
        raise AuthenticationError(
            message="Access denied. Parent account required.",
            log_message=f"Permission denied for validate_parent_session.\n"
            f"User type '{auth_response.user_type}' is not PARENT.",
            error_code=AppErrorCode.PERMISSION_DENIED
        )

    # Ensure account object is of the correct type (Account for parent)
    if not isinstance(auth_response.account, Account):
        raise ServiceError(
            message="Internal server error: Invalid account data for parent session.",
            log_message=f"Validate_parent_session: Expected Account model\n"
            f"got {type(auth_response.account)} for user_type {auth_response.user_type}\n"
            f"Account ID: {auth_response.account_id}",
            error_code=AppErrorCode.SERVICE_ERROR
        )

    service = ParentValidateSessionService(db=db)
    return service.validate_session(authenticated_account=auth_response.account)