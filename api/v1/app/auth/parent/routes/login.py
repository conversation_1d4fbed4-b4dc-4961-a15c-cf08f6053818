from fastapi import APIRouter, Depends, BackgroundTasks
from sqlalchemy.orm import Session
from db.database import get_db
from ..schemas.request import LoginRequest
from ..schemas.response import LoginResponse
from ..services.login import ParentLoginService
from services.mailer.auth_mailer_service import AuthMailerService
from dependencies.email_dependency import get_auth_mailer_service

router = APIRouter()

@router.post('/login', response_model=LoginResponse)
def login(
    login_attempt: LoginRequest,
    db: Session = Depends(get_db),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    auth_mailer_service: AuthMailerService = Depends(get_auth_mailer_service)
):

    login_service = ParentLoginService(db=db, auth_mailer_service=auth_mailer_service)
    response = login_service.login_parent(
        login_attempt,
        background_tasks
    )
    return response