from fastapi import APIRouter, Depends, BackgroundTasks
from sqlalchemy.orm import Session
from db.database import get_db
from ..schemas.request import ResendVerificationCodeRequest
from ..schemas.response import ResendVerificationCodeResponse
from services.mailer.auth_mailer_service import AuthMailerService
from dependencies.email_dependency import get_auth_mailer_service
from ..services.resend_verification_code import ParentResendVerificationService

router = APIRouter()

@router.post('/resend-verification-code', response_model=ResendVerificationCodeResponse)
def resend_verification_code(
    req: ResendVerificationCodeRequest,
    db: Session = Depends(get_db),
    auth_mailer_service: AuthMailerService = Depends(get_auth_mailer_service),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    service = ParentResendVerificationService(db=db, auth_mailer_service=auth_mailer_service)
    return service.resend(req, background_tasks)