from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from db.database import get_db
from ..schemas.request import AssignChildAccountRequest
from ..schemas.response import AssignChildAccountResponse
from dependencies.auth_dependencies.auth_dependency import AuthDependency
from dependencies.auth_dependencies.base_auth_dependency import AuthDependencyResponse

from ..services.assign_child_account import ParentAssignChildAccountService

router = APIRouter()

require_auth = AuthDependency()

@router.post('/assign-child-account', status_code=200, response_model=AssignChildAccountResponse)
def assign_child_account(
    link_data: AssignChildAccountRequest,
    db: Session = Depends(get_db),
    auth: AuthDependencyResponse = Depends(require_auth)
):

    parent_public_id = auth.account_public_id

    service = ParentAssignChildAccountService(db=db)
    response = service.assign_child(
        link_data=link_data,
        parent_public_id=str(parent_public_id)
    )
    return response