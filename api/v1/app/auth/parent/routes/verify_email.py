from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from db.database import get_db
from ..schemas.request import VerifyAccountRequest
from ..schemas.response import VerifyAccountResponse
from ..services.verify_email import ParentEmailVerificationService

router = APIRouter()

@router.post('/verify-email', status_code=200, response_model=VerifyAccountResponse)
def verify_email(
    verification: VerifyAccountRequest,
    db: Session = Depends(get_db)
):
    service = ParentEmailVerificationService(db=db)
    return service.verify(verification)