from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from db.database import get_db
from ..schemas.request import ParentLoginChildRequest
from ..schemas.response import LoginChildResponse
from ..services.login import ParentLoginService
from services.mailer.auth_mailer_service import AuthMailerService
from dependencies.email_dependency import get_auth_mailer_service
from dependencies.auth_dependencies.auth_dependency import AuthDependency, AuthDependencyResponse
from core.exception_handling.exceptions.custom_exceptions import AuthorizationError
from core.exception_handling.exceptions.custom_exceptions import AppErrorCode
from dependencies.auth_dependencies.base_auth_dependency import UserType

router = APIRouter()
require_auth = AuthDependency(required=True)

@router.post('/login-child', response_model=LoginChildResponse)
def login(
    login_attempt: ParentLoginChildRequest,
    db: Session = Depends(get_db),
    auth_mailer_service: AuthMailerService = Depends(get_auth_mailer_service),
    auth_dependency: AuthDependencyResponse = Depends(require_auth),
):

    if not auth_dependency.user_type == UserType.parent:
        raise AuthorizationError(
            message="Unauthorized access to parent account settings.",
            error_code=AppErrorCode.PERMISSION_DENIED,
        )

    login_service = ParentLoginService(db=db, auth_mailer_service=auth_mailer_service)
    response = login_service.login_child(
        login_attempt.child_public_id,
        auth_dependency.account
    )
    return response


