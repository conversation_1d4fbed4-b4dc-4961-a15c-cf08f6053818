from fastapi import APIRouter, Depends, BackgroundTasks
from sqlalchemy.orm import Session
from ..schemas.response import VerificationStatusResponse
from db.database import get_db
from services.mailer.auth_mailer_service import AuthMailerService  
from dependencies.email_dependency import get_auth_mailer_service
from ..services.check_verification_status import ParentVerificationStatusService
from ..schemas.request import VerificationStatusRequest

router = APIRouter()

@router.post('/check-verification-status', response_model=VerificationStatusResponse)
def check_verification_status(
    request: VerificationStatusRequest,
    db: Session = Depends(get_db),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    auth_mailer_service: AuthMailerService = Depends(get_auth_mailer_service)
):
    service = ParentVerificationStatusService(db=db, auth_mailer_service=auth_mailer_service)
    return service.check_status(request, background_tasks)