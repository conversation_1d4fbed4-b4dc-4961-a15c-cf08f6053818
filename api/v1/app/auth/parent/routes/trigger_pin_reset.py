from fastapi import APIRouter, Depends, BackgroundTasks
from sqlalchemy.orm import Session
from db.database import get_db
from ..schemas.request import TriggerPinResetRequest
from ..schemas.response import TriggerPinResetResponse
from services.mailer.auth_mailer_service import AuthMailerService 
from dependencies.email_dependency import get_auth_mailer_service
from ..services.trigger_pin_reset import TriggerPinResetService

router = APIRouter()

@router.post('/trigger-pin-reset', response_model=TriggerPinResetResponse)
def trigger_pin_reset(
    request: TriggerPinResetRequest,
    db: Session = Depends(get_db),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    auth_mailer_service: AuthMailerService = Depends(get_auth_mailer_service),
):
    service = TriggerPinResetService(db=db, auth_mailer_service=auth_mailer_service)
    return service.trigger_reset(request, background_tasks)