from .routes import (
    check_verification_status,
    login,
    resend_verification_code,
    reset_pin,
    signup,
    trigger_pin_reset,
    verify_email,
    assign_child_account,
    validate_session,
    login_child
)

from fastapi import APIRouter


router = APIRouter(prefix="/api/v1/auth/parent", tags=["Parent Authentication"])

router.include_router(check_verification_status.router)
router.include_router(login.router)
router.include_router(resend_verification_code.router)
router.include_router(reset_pin.router)
router.include_router(signup.router)
router.include_router(trigger_pin_reset.router)
router.include_router(verify_email.router)
router.include_router(assign_child_account.router)
router.include_router(validate_session.router)
router.include_router(login_child.router)