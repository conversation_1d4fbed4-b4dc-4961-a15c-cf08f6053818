import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { InternalAuthState, EditorUser, InternalLoginRequest } from '@/types/internal/authTypes';
import * as authService from '@/services/internal/authService';
import { ServiceResult } from '@/types/services/serviceResultType';

interface InternalAuthActions {
  // Actions
  login: (credentials: InternalLoginRequest) => Promise<ServiceResult<any>>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  initAuth: () => Promise<void>;
  
  // State setters
  _setToken: (token: string | null, expiresAt: string | null) => void;
  _setUser: (user: EditorUser | null) => void;
  _setLoading: (isLoading: boolean) => void;
  _setInitialized: (initialized: boolean) => void;
}

export type InternalAuthStore = InternalAuthState & InternalAuthActions;

// Private variable to prevent concurrent initialization
let _initPromise: Promise<void> | null = null;

// Storage helper for managing token in localStorage
const internalStorage = {
  get: (key: string): string | null => {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(`internal_${key}`);
  },
  set: (key: string, value: string | null) => {
    if (typeof window === 'undefined') return;
    if (value === null) {
      localStorage.removeItem(`internal_${key}`);
    } else {
      localStorage.setItem(`internal_${key}`, value);
    }
  },
};

export const useInternalAuthStore = create<InternalAuthStore>()(
  persist(
    (set, get) => {
      const initialToken = internalStorage.get('token');
      const initialExpiresAt = internalStorage.get('expiresAt');
      
      return {
      // Initial state
      user: null,
      token: initialToken,
      expiresAt: initialExpiresAt,
      initialized: false,
      isLoading: false,

      // State setters
      _setToken: (token, expiresAt) => {
        set({ token, expiresAt });
        internalStorage.set('token', token);
        internalStorage.set('expiresAt', expiresAt);
      },

      _setUser: (user) => {
        set({ user });
      },

      _setLoading: (isLoading) => {
        set({ isLoading });
      },

      _setInitialized: (initialized) => {
        set({ initialized });
      },

      // Initialize auth state
      initAuth: async () => {
        // Prevent concurrent initialization calls
        if (_initPromise) return _initPromise;
        if (get().initialized || get().isLoading) return;
        
        _initPromise = (async () => {
          set({ isLoading: true });
          
          const token = get().token;
          const expiresAt = get().expiresAt;
          
          try {
            if (!token || !expiresAt) {
              get()._setToken(null, null);
              set({ user: null });
              return;
            }

            // Check if token is expired
            const expiryDate = new Date(expiresAt);
            if (expiryDate <= new Date()) {
              get()._setToken(null, null);
              set({ user: null });
              return;
            }

            // Validate session with backend
            const resp = await authService.validateInternalSession(token);

            if (resp.status === 'success' && resp.data.isValid) {
              // Session is valid, keep the existing token and update user info
              set({
                user: resp.data.editor,
                token: token, // Keep existing token
                expiresAt: expiresAt, // Keep existing expiry
              });
            } else {
              // Session is invalid, clear auth state
              get()._setToken(null, null);
              set({ user: null });
            }
          } catch (error) {
            console.error('initAuth failed:', error);
            get()._setToken(null, null);
            set({ user: null });
          } finally {
            set({ isLoading: false, initialized: true });
            _initPromise = null; // Reset promise after completion
          }
        })();
        
        return _initPromise;
      },

      // Login action
      login: async (credentials) => {
        set({ isLoading: true });
        
        try {
          const resp = await authService.loginInternal(credentials);
          
          if (resp.status === 'success') {
            const { accessToken, editor, expiresIn } = resp.data;
            
            // Calculate expiresAt from expiresIn (seconds)
            const expiresAt = new Date(Date.now() + expiresIn * 1000).toISOString();
            
            get()._setToken(accessToken, expiresAt);
            set({
              user: editor,
              initialized: true,
            });
            
            // Return data in the format expected by the login page
            return { 
              status: 'success', 
              data: {
                ...resp.data,
                token: accessToken,
                user: editor,
                expiresAt
              }
            };
          } else {
            return resp;
          }
        } catch (error) {
          console.error('Login failed:', error);
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },

      // Logout action
      logout: () => {
        get()._setToken(null, null);
        set({
          user: null,
          initialized: true,
        });

        // Note: Redirect is handled by the layout or calling component
        // to avoid hard navigation and allow for proper Next.js routing
      },

      // Refresh token action
      refreshToken: async () => {
        const currentToken = get().token;
        if (!currentToken) return;

        try {
          const resp = await authService.refreshInternalToken(currentToken);
          
          if (resp.status === 'success') {
            const { accessToken, expiresIn } = resp.data;
            const expiresAt = new Date(Date.now() + expiresIn * 1000).toISOString();
            get()._setToken(accessToken, expiresAt);
          } else {
            get().logout();
          }
        } catch (error) {
          console.error('Token refresh failed:', error);
          get().logout();
        }
      },
    };
    },
    {
      name: 'internal-auth-storage',
      partialize: (state) => ({
        token: state.token,
        expiresAt: state.expiresAt,
        user: state.user,
      }),
      storage: createJSONStorage(() => localStorage),
    }
  )
);

// Selector helpers
export const selectInternalUser = (state: InternalAuthStore) => state.user;
export const selectIsInternalAuthenticated = (state: InternalAuthStore) => Boolean(state.token && state.user);
export const selectInternalUserRole = (state: InternalAuthStore) => state.user?.role;
export const selectIsAdmin = (state: InternalAuthStore) => state.user?.role === 'ADMIN';
export const selectIsEditor = (state: InternalAuthStore) => state.user?.role === 'EDITOR';