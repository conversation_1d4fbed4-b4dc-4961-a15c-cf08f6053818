import { useState, useEffect, useCallback } from 'react';

const PREVIEW_TIME = 10; // seconds
const PREVIEW_PERCENTAGE = 0.1; // 10% of total video
const READY_TIMEOUT = 6000; // 6 seconds timeout

interface UseVideoSectionProps {
  previewOnly?: boolean;
}

interface UseVideoSectionReturn {
  showPlayer: boolean;
  onTimeUpdate: (currentTime: number, duration: number) => void;
  onReady: () => void;
  refreshKey: number;
  handleRefresh: () => void;
  shouldRefresh: boolean;
  currentTime: number;
  duration: number;
  isReady: boolean;
}

export const useVideoSection = (previewOnly: boolean = false): UseVideoSectionReturn => {
  const [showPlayer, setShowPlayer] = useState(true);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isReady, setIsReady] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [shouldRefresh, setShouldRefresh] = useState(false);

  // Ready timeout effect
  useEffect(() => {
    if (isReady) return;

    const timeout = setTimeout(() => {
      if (!isReady) {
        setShouldRefresh(true);
      }
    }, READY_TIMEOUT);

    return () => clearTimeout(timeout);
  }, [isReady, refreshKey]);

  // Effect to handle changes in previewOnly status
  useEffect(() => {
    if (!previewOnly && !showPlayer) {
      // If user is no longer in preview mode (e.g., subscription status changed)
      // and player was hidden, show it again
      setShowPlayer(true);
    }
  }, [previewOnly, showPlayer]);

  const handleRefresh = useCallback(() => {
    setShouldRefresh(false);
    setIsReady(false);
    setCurrentTime(0);
    setDuration(0);
    setShowPlayer(true);
    setRefreshKey(prev => prev + 1);
  }, []);

  const onTimeUpdate = useCallback((newCurrentTime: number, newDuration: number) => {
    setCurrentTime(newCurrentTime);
    
    // Only update duration if it's valid and different
    if (newDuration > 0 && newDuration !== duration) {
      setDuration(newDuration);
    }

    if (previewOnly) {
      // Calculate preview limits
      const timeLimit = PREVIEW_TIME;
      const percentageLimit = newDuration > 0 ? newDuration * PREVIEW_PERCENTAGE : timeLimit + 1;
      
      // Log preview cutoff logic
      console.log('[useVideoSection] Preview check:', {
        previewOnly,
        currentTime: newCurrentTime,
        duration: newDuration,
        timeLimit,
        percentageLimit,
        willHidePlayer: newCurrentTime > timeLimit || newCurrentTime > percentageLimit
      });
      
      // Hide player if either limit is exceeded
      if (newCurrentTime > timeLimit || newCurrentTime > percentageLimit) {
        console.log('[useVideoSection] Hiding player due to preview limit exceeded');
        setShowPlayer(false);
      }
    }
  }, [previewOnly, duration]);

  const onReady = useCallback(() => {
    setIsReady(true);
  }, []);

  return {
    showPlayer,
    onTimeUpdate,
    onReady,
    refreshKey,
    handleRefresh,
    shouldRefresh,
    currentTime,
    duration,
    isReady,
  };
};