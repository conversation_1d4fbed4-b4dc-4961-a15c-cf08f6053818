'use client';

import { useEffect, useRef } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useInternalAuthStore, selectIsInternalAuthenticated } from '@/zustand/internal/internalAuthStore/internalAuthStore';

/**
 * Hook to handle internal authentication redirects without causing page refreshes.
 * Prevents redirect loops and ensures smooth navigation.
 */
export function useInternalAuthRedirect(redirectTo: string = '/internal/login') {
  const router = useRouter();
  const pathname = usePathname();
  const { initialized } = useInternalAuthStore();
  const isAuthenticated = useInternalAuthStore(selectIsInternalAuthenticated);
  const lastAuthStateRef = useRef<boolean | null>(null);
  const isRedirectingRef = useRef(false);

  useEffect(() => {
    // Only run after initialization
    if (!initialized) return;

    // Check if auth state has actually changed
    const authStateChanged = lastAuthStateRef.current !== null && 
                           lastAuthStateRef.current !== isAuthenticated;
    
    lastAuthStateRef.current = isAuthenticated;

    // Handle unauthenticated state
    if (!isAuthenticated && pathname !== redirectTo && !isRedirectingRef.current) {
      isRedirectingRef.current = true;
      
      // Use replace instead of push to avoid adding to history
      router.replace(redirectTo);
      
      // Reset redirect flag after a delay
      setTimeout(() => {
        isRedirectingRef.current = false;
      }, 1000);
    }
    
    // Handle logout scenario (was authenticated, now not)
    if (authStateChanged && !isAuthenticated) {
      router.replace(redirectTo);
    }
  }, [initialized, isAuthenticated, pathname, redirectTo, router]);

  return { isAuthenticated, initialized };
}