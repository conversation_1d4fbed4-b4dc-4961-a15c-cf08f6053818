'use client';

import { useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { 
  useDraftEditorStore, 
  selectDraft, 
  selectIsLoading, 
  selectIsSaving, 
  selectIsSubmitting, 
  selectIsDirty, 
  selectError,
  selectCanSubmit,
  selectAutoSaveStatus
} from '@/zustand/internal/draftEditorStore';
import { useToast } from '@/hooks/use-toast';

export function useDraftEditor(draftId: number, learningNodeId?: string) {
  const router = useRouter();
  const { toast } = useToast();
  const isUnloadingRef = useRef(false);
  
  const {
    loadDraft,
    updateDraftData,
    saveDraft,
    submitDraft,
    enableAutoSave,
    disableAutoSave,
    reset,
    checkForUnsavedChanges,
  } = useDraftEditorStore();
  
  const draft = useDraftEditorStore(selectDraft);
  const isLoading = useDraftEditorStore(selectIsLoading);
  const isSaving = useDraftEditorStore(selectIsSaving);
  const isSubmitting = useDraftEditorStore(selectIsSubmitting);
  const isDirty = useDraftEditorStore(selectIsDirty);
  const error = useDraftEditorStore(selectError);
  const canSubmit = useDraftEditorStore(selectCanSubmit);
  const autoSaveStatus = useDraftEditorStore(selectAutoSaveStatus);

  // Load draft on mount
  useEffect(() => {
    if (draftId) {
      loadDraft(draftId);
    }
    return () => {
      reset();
    };
  }, [draftId, loadDraft, reset]);

  // Handle save with toast
  const handleSave = useCallback(async () => {
    // Check if draft is rejected by editor before attempting to save
    if (draft && draft.status === 'REJECTED_BY_EDITOR') {
      toast({
        title: 'Cannot save draft',
        description: 'This draft has been rejected by an editor and cannot be edited.',
        variant: 'destructive',
      });
      return false;
    }
    
    const success = await saveDraft(true);
    if (success) {
      toast({
        title: 'Draft saved',
        description: 'Your changes have been saved successfully.',
      });
    } else {
      toast({
        title: 'Save failed',
        description: error || 'Failed to save draft. Please try again.',
        variant: 'destructive',
      });
    }
    return success;
  }, [saveDraft, toast, error, draft]);

  // Handle submit with toast
  const handleSubmit = useCallback(async () => {
    const success = await submitDraft();
    if (success) {
      toast({
        title: 'Draft submitted',
        description: 'Your draft has been submitted for admin review.',
      });
      // Navigate back to the learning node detail page
      router.push(learningNodeId ? `/internal/editor/${learningNodeId}` : '/internal/editor');
    } else {
      toast({
        title: 'Submit failed',
        description: error || 'Failed to submit draft. Please try again.',
        variant: 'destructive',
      });
    }
    return success;
  }, [submitDraft, toast, error, router, learningNodeId]);



  // Update exercise data
  const updateExerciseData = useCallback((dataJson: Record<string, any>) => {
    updateDraftData({ dataJson });
  }, [updateDraftData]);

  // Update solution data
  const updateSolutionData = useCallback((solutionJson: Record<string, any>) => {
    updateDraftData({ solutionJson });
  }, [updateDraftData]);

  // Update difficulty
  const updateDifficulty = useCallback((difficulty: 'low' | 'medium' | 'high') => {
    updateDraftData({ difficulty });
  }, [updateDraftData]);


  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd/Ctrl + S to save
      if ((e.metaKey || e.ctrlKey) && e.key === 's') {
        e.preventDefault();
        if (isDirty && !isSaving) {
          handleSave();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isDirty, isSaving, handleSave]);

  // Warn about unsaved changes before leaving
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (checkForUnsavedChanges() && !isUnloadingRef.current) {
        e.preventDefault();
        e.returnValue = '';
        return '';
      }
    };

    const handleRouteChange = () => {
      if (checkForUnsavedChanges() && !isUnloadingRef.current) {
        const confirmLeave = confirm(
          'You have unsaved changes. Are you sure you want to leave? Your changes will be lost.'
        );
        if (!confirmLeave) {
          throw 'Route change aborted';
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    
    // Note: Next.js 13+ doesn't have a built-in way to intercept route changes
    // This would need to be implemented with a custom solution if needed

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      isUnloadingRef.current = true;
    };
  }, [checkForUnsavedChanges]);

  // Auto-save indicator
  const getSaveStatus = useCallback(() => {
    if (isSaving) return 'saving';
    if (isDirty) return 'unsaved';
    return 'saved';
  }, [isSaving, isDirty]);

  return {
    // State
    draft,
    isLoading,
    isSaving,
    isSubmitting,
    isDirty,
    error,
    canSubmit,
    saveStatus: getSaveStatus(),
    autoSaveStatus,

    // Actions
    save: handleSave,
    submit: handleSubmit,
    updateExerciseData,
    updateSolutionData,
    updateDifficulty,
    enableAutoSave,
    disableAutoSave,

    // Utilities
    checkForUnsavedChanges,
  };
}