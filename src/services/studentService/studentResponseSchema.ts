import { z } from 'zod';
// Imports are already correct if the exported pair names from shared/content schemas are XxxSchema
import { LearningProfileSchema } from '@/schemas/shared/learningProfileSchema';
import { ChapterSchema } from '@/schemas/content/chapterSchema';
import { SubjectSchema as ContentSubjectSchema, SubjectSchema } from '@/schemas/content/subjectSchema'; // Renamed import alias for clarity
import { createSchemaPair, SchemaPair } from '@/schemas/schemaUtils';
import { LearningNodeSchema } from '@/schemas/content/learningNodeSchema';

// ============================================
// 1. Student Account Information
// ============================================
export const StudentAccountAppSchema = z.object({
  publicId: z.string(),
  email: z.string().email(),
  firstName: z.string(),
  lastName: z.string(),
  schoolYear: z.number().int().min(1).max(6), // Assuming this is the frontend representation
  hasPin: z.boolean(),
});

export const StudentAccountApiSchema = z.object({
  public_id: z.string(),
  email: z.string().email(),
  first_name: z.string(),
  last_name: z.string(),
  school_year: z.number().int().min(1).max(6), // Assuming this is the API representation
  has_pin: z.boolean(),
});

export type StudentAccountApp = z.infer<typeof StudentAccountAppSchema>;
export type StudentAccountApi = z.infer<typeof StudentAccountApiSchema>;

export const StudentAccountSchema = createSchemaPair(StudentAccountAppSchema, StudentAccountApiSchema);

// ============================================
// 2. School Year Management
// ============================================
export const UpdateSchoolYearAppResponseSchema = z.object({
  success: z.boolean(),
  schoolYear: z.number().int().min(1).max(6),
});
export const UpdateSchoolYearApiResponseSchema = z.object({
  success: z.boolean(),
  school_year: z.number().int().min(1).max(6),
});

export type UpdateSchoolYearAppResponse = z.infer<typeof UpdateSchoolYearAppResponseSchema>;
export type UpdateSchoolYearApiResponse = z.infer<typeof UpdateSchoolYearApiResponseSchema>;

export const UpdateSchoolYearResponseSchema = createSchemaPair(
  UpdateSchoolYearAppResponseSchema,
  UpdateSchoolYearApiResponseSchema
);

// ============================================
// 3. PIN Management
// ============================================
export const VerifyPinAppResponseSchema = z.object({ success: z.boolean() });
export const VerifyPinApiResponseSchema = z.object({ success: z.boolean() });

export type VerifyPinAppResponse = z.infer<typeof VerifyPinAppResponseSchema>;
export type VerifyPinApiResponse = z.infer<typeof VerifyPinApiResponseSchema>;
export const VerifyPinResponseSchema = createSchemaPair(VerifyPinAppResponseSchema, VerifyPinApiResponseSchema);

export const ResetPinAppResponseSchema = z.object({ success: z.boolean() });
export const ResetPinApiResponseSchema = z.object({ success: z.boolean() });

export type ResetPinAppResponse = z.infer<typeof ResetPinAppResponseSchema>;
export type ResetPinApiResponse = z.infer<typeof ResetPinApiResponseSchema>;
export const ResetPinResponseSchema = createSchemaPair(ResetPinAppResponseSchema, ResetPinApiResponseSchema);

// ============================================
// 4. Student Profile Management Schemas
// ============================================

// ----------------------------
// 4.1 Update Student Profile
// ----------------------------
export const UpdateStudentProfileAppResponseSchema = LearningProfileSchema.frontend;
export const UpdateStudentProfileApiResponseSchema = LearningProfileSchema.api;

export type UpdateStudentProfileAppResponse = z.infer<typeof UpdateStudentProfileAppResponseSchema>;
export type UpdateStudentProfileApiResponse = z.infer<typeof UpdateStudentProfileApiResponseSchema>;

export const UpdateStudentProfileResponseSchema = createSchemaPair(
  UpdateStudentProfileAppResponseSchema,
  UpdateStudentProfileApiResponseSchema
);

// ----------------------------
// 4.2 Delete Student Profile
// ----------------------------
export const DeleteStudentProfileAppResponseSchema = z.object({
  message: z.string(),
  success: z.boolean().optional(),
});

export const DeleteStudentProfileApiResponseSchema = z.object({
  message: z.string(),
  success: z.boolean().optional(),
});

export type DeleteStudentProfileAppResponse = z.infer<typeof DeleteStudentProfileAppResponseSchema>;
export type DeleteStudentProfileApiResponse = z.infer<typeof DeleteStudentProfileApiResponseSchema>;

export const DeleteStudentProfileResponseSchema = createSchemaPair(
  DeleteStudentProfileAppResponseSchema,
  DeleteStudentProfileApiResponseSchema
);

// ============================================
// 5. Learning Content Schemas
// ============================================

export const LessonAppSchema = z.object({
  publicId: z.string(),
  name: z.string(),
  exerciseCount: z.number().optional().nullable(),
  videoLength: z.number().optional().nullable(),
});
export const LessonApiSchema = z.object({
  public_id: z.string(),
  name: z.string(),
  exercise_count: z.number().optional().nullable(),
  video_length: z.number().optional().nullable(),
});
export const LessonSchema = createSchemaPair(LessonAppSchema, LessonApiSchema);
export type LessonApp = z.infer<typeof LessonSchema.frontend>;

export const LanguageConceptAppSchema = z.object({
  name: z.string(),
  publicId: z.string(),
  conceptType: z.enum(['vocabulary', 'grammar', 'verbs']).or(z.string()),
  lessonCount: z.number(),
  lessons: z.array(LessonSchema.frontend),
});
export const LanguageConceptApiSchema = z.object({
  name: z.string(),
  public_id: z.string(),
  concept_type: z.enum(['vocabulary', 'grammar', 'verbs']).or(z.string()),
  lesson_count: z.number(),
  lessons: z.array(LessonSchema.api),
});
export const LanguageConceptSchema = createSchemaPair(LanguageConceptAppSchema, LanguageConceptApiSchema);
export type LanguageConceptApp = z.infer<typeof LanguageConceptSchema.frontend>;


export const GetSubjectAppResponseSchema = z.object({
  subject: SubjectSchema.frontend,
  chapters: z.array(ChapterSchema.frontend).nullish(),
  isSubscribed: z.boolean(),
  subscriptionStatus: z.enum(['active', 'trial', 'none']),
});
export const GetSubjectApiResponseSchema = z.object({
  subject: SubjectSchema.api,
  chapters: z.array(ChapterSchema.api).nullish(),
  is_subscribed: z.boolean(),
  subscription_status: z.enum(['active', 'trial', 'none']),
});

export const GetSubjectResponseSchema = createSchemaPair(
  GetSubjectAppResponseSchema,
  GetSubjectApiResponseSchema
);

export type GetSubjectAppResponse = z.infer<typeof GetSubjectResponseSchema.frontend>;

// StudentSubject uses ContentSubjectSchema (which is SubjectSchema from content/subjectSchema.ts)
export const StudentSubjectAppSchema = ContentSubjectSchema.frontend;
export const StudentSubjectApiSchema = ContentSubjectSchema.api;
export const StudentSubjectSchema = ContentSubjectSchema; // The pair is already ContentSubjectSchema
export type StudentSubjectApp = z.infer<typeof StudentSubjectSchema.frontend>;


// ============================================
// 6. Progress Tracking Schemas
// ============================================
export const ProgressItemAppSchema = z.object({
  subjectName: z.string(),
  publicId: z.string(),
  completionPercentage: z.number().min(0).max(100),
});
export const ProgressItemApiSchema = z.object({
  subject_name: z.string(),
  public_id: z.string(),
  completion_percentage: z.number().min(0).max(100),
});
export const ProgressItemSchema = createSchemaPair(ProgressItemAppSchema, ProgressItemApiSchema);
export type ProgressItemApp = z.infer<typeof ProgressItemSchema.frontend>;

export const DailyProgressAppSchema = z.object({
  day: z.string(),
  correct: z.number().int(),
  total: z.number().int(),
  percentage: z.number().min(0).max(100),
});
export const DailyProgressApiSchema = z.object({
  day: z.string(),
  correct: z.number().int(),
  total: z.number().int(),
  percentage: z.number().min(0).max(100),
});
export const DailyProgressSchema = createSchemaPair(DailyProgressAppSchema, DailyProgressApiSchema);
export type DailyProgressApp = z.infer<typeof DailyProgressSchema.frontend>;

export const DetailedSubjectProgressAppSchema = z.object({
  subject: z.string(),
  publicId: z.string(),
  imageUrl: z.string().optional().nullable(),
  totalExercises: z.number().int(),
  correctPercentage: z.number().min(0).max(100),
  weeklyTrend: z.string(),
  dailyProgress: z.array(DailyProgressSchema.frontend),
});
export const DetailedSubjectProgressApiSchema = z.object({
  subject: z.string(),
  public_id: z.string(),
  image_url: z.string().optional().nullable(),
  total_exercises: z.number().int(),
  correct_percentage: z.number().min(0).max(100),
  weekly_trend: z.string(),
  daily_progress: z.array(DailyProgressSchema.api),
});
export const DetailedSubjectProgressSchema = createSchemaPair(
  DetailedSubjectProgressAppSchema,
  DetailedSubjectProgressApiSchema
);
export type DetailedSubjectProgressApp = z.infer<typeof DetailedSubjectProgressSchema.frontend>;

export const GetProgressOverviewAppDataSchema = z.object({
  subjects: z.array(DetailedSubjectProgressSchema.frontend),
});
export const GetProgressOverviewApiDataSchema = z.object({
  subjects: z.array(DetailedSubjectProgressSchema.api),
});
export const GetProgressOverviewDataSchema = createSchemaPair(
  GetProgressOverviewAppDataSchema,
  GetProgressOverviewApiDataSchema
);
export type GetProgressOverviewAppData = z.infer<typeof GetProgressOverviewDataSchema.frontend>;

// ============================================
// 7. API Response Data Schemas
// ============================================
export const GetStudentDashboardAppResponseSchema = z.object({
  profile: LearningProfileSchema.frontend,
  subjects: z.array(StudentSubjectSchema.frontend), // StudentSubjectSchema is already a pair
});
export const GetStudentDashboardApiResponseSchema = z.object({
  profile: LearningProfileSchema.api,
  subjects: z.array(StudentSubjectSchema.api), // StudentSubjectSchema is already a pair
});
export const GetStudentDashboardResponseSchema = createSchemaPair(
  GetStudentDashboardAppResponseSchema,
  GetStudentDashboardApiResponseSchema
);


export type GetStudentDashboardAppResponse = z.infer<typeof GetStudentDashboardResponseSchema.frontend>;

export const GetStudentSubjectsAppResponseSchema = z.object({
  subjects: z.array(StudentSubjectSchema.frontend),
});
export const GetStudentSubjectsApiResponseSchema = z.object({
  subjects: z.array(StudentSubjectSchema.api),
});
export const GetStudentSubjectsResponseSchema = createSchemaPair(
  GetStudentSubjectsAppResponseSchema,
  GetStudentSubjectsApiResponseSchema
);
export type GetStudentSubjectsAppResponse = z.infer<typeof GetStudentSubjectsResponseSchema.frontend>;



// Get learning node response
export const GetLearningNodeAppResponseSchema = z.intersection(
  LearningNodeSchema.frontend,
  z.object({
    isSubscribed: z.boolean(),
    totalExercises: z.number(),
    shownExercises: z.number(),
  })
);
export const GetLearningNodeApiResponseSchema = z.intersection(
  LearningNodeSchema.api,
  z.object({
    is_subscribed: z.boolean(),
    total_exercises: z.number(),
    shown_exercises: z.number(),
  })
);
export type GetLearningNodeAppResponse = z.infer<typeof GetLearningNodeAppResponseSchema>;
export type GetLearningNodeApiResponse = z.infer<typeof GetLearningNodeApiResponseSchema>;
export const GetLearningNodeResponseSchema = createSchemaPair(
  GetLearningNodeAppResponseSchema,
  GetLearningNodeApiResponseSchema
);

// ============================================
// 8. Exercise Submission Response Schemas
// ============================================

// ----------------------------
// 8.1 Solution Step Schema (for exercise feedback)
// ----------------------------
export const SolutionStepAppSchema = z.object({
  text: z.string().nullable().optional(),
  math: z.string().nullable().optional(),
  imageUrl: z.string().nullable().optional(),
});
export const SolutionStepApiSchema = z.object({
  text: z.string().nullable().optional(),
  math: z.string().nullable().optional(),
  image_url: z.string().nullable().optional(),
});

export const SolutionStepSchema = createSchemaPair(SolutionStepAppSchema, SolutionStepApiSchema);
export type SolutionStepApp = z.infer<typeof SolutionStepSchema.frontend>;

// ----------------------------
// 8.1 Error Correction Response
// ----------------------------
export const ErrorCorrectionAppResponseSchema = z.object({
  index: z.number(),
  text: z.string(),
});

export const ErrorCorrectionApiResponseSchema = z.object({
  index: z.number(),
  text: z.string(),
});

export const ErrorCorrectionResponseSchema = createSchemaPair(ErrorCorrectionAppResponseSchema, ErrorCorrectionApiResponseSchema);
export type ErrorCorrectionAppResponse = z.infer<typeof ErrorCorrectionResponseSchema.frontend>;
export type ErrorCorrectionApiResponse = z.infer<typeof ErrorCorrectionResponseSchema.api>;

// ----------------------------
// 8.2 Exercise Submission Response
// ----------------------------
export const ExerciseSubmissionAppResponseSchema = z.object({
  isCorrect: z.boolean(),
  correctAnswer: z.union([
    z.string(),
    z.array(z.string()),
    z.boolean(),
    z.record(z.string()),
    z.array(z.object({ a: z.string(), b: z.string(), isCorrect: z.boolean().optional() })),
    z.number().int(),
    z.array(z.number().int()),
    z.array(ErrorCorrectionAppResponseSchema), // For error correction structured responses
  ]),
  solutionSteps: z.array(SolutionStepSchema.frontend.nullable()).nullable().optional(),
  videoUrl: z.string().nullable().optional(),
  recentlySubmitted: z.boolean(),
});

export const ExerciseSubmissionApiResponseSchema = z.object({
  is_correct: z.boolean(),
  correct_answer: z.union([
    z.string(),
    z.array(z.string()),
    z.object({ is_true: z.boolean() }), // True/false format from backend
    z.record(z.string()),
    z.array(z.object({ a: z.string(), b: z.string(), is_correct: z.boolean().optional() })),
    z.number().int(),
    z.array(z.number().int()),
    z.array(ErrorCorrectionApiResponseSchema), // For error correction structured responses
  ]),
  solution_steps: z.array(SolutionStepSchema.api.nullable()).nullable().optional(),
  video_url: z.string().nullable().optional(),
  recently_submitted: z.boolean(),
});

export type ExerciseSubmissionAppResponse = z.infer<typeof ExerciseSubmissionAppResponseSchema>;
export type ExerciseSubmissionApiResponse = z.infer<typeof ExerciseSubmissionApiResponseSchema>;

// Custom transformation for ExerciseSubmissionResponse to handle true/false format
export const ExerciseSubmissionResponseSchema: SchemaPair<
  ExerciseSubmissionAppResponse,
  ExerciseSubmissionApiResponse
> = {
  frontend: ExerciseSubmissionAppResponseSchema,
  api: ExerciseSubmissionApiResponseSchema,
  
  toApi: (data: ExerciseSubmissionAppResponse): ExerciseSubmissionApiResponse => {
    // For now, we only handle frontend to API for consistency
    // True/false exercises would need to convert boolean to { is_true: boolean }
    throw new Error('toApi transformation not implemented for ExerciseSubmissionResponse');
  },
  
  toFrontend: (data: ExerciseSubmissionApiResponse): ExerciseSubmissionAppResponse => {
    const parsedApi = ExerciseSubmissionApiResponseSchema.parse(data);
    
    // Transform the data from snake_case to camelCase
    const transformed: any = {
      isCorrect: parsedApi.is_correct,
      correctAnswer: parsedApi.correct_answer,
      solutionSteps: parsedApi.solution_steps,
      videoUrl: parsedApi.video_url,
      recentlySubmitted: parsedApi.recently_submitted,
    };
    
    // Special handling for true/false exercises
    // If correct_answer is an object with is_true property, convert to boolean
    if (
      typeof transformed.correctAnswer === 'object' &&
      transformed.correctAnswer !== null &&
      'is_true' in transformed.correctAnswer &&
      typeof transformed.correctAnswer.is_true === 'boolean'
    ) {
      transformed.correctAnswer = transformed.correctAnswer.is_true;
    }
    
    return ExerciseSubmissionAppResponseSchema.parse(transformed);
  }
};