/* --------------------------------------------------------------------------
 *  serviceUtils.ts — Utility functions for service layer communication
 * --------------------------------------------------------------------------*/

import { z } from 'zod';
import { useAuthStore } from "@/zustand/authStore/authStore";
import { useInternalAuthStore } from '@/zustand/internal/internalAuthStore/internalAuthStore';
import {
  AppErrorCodeEnum,
  AppErrorCode,
  AppErrorResponse,
  ErrorDetail,
} from './appErrorSchema';

import { SuccessResponse, ServiceResult } from '@/types/services/serviceResultType';

import { SchemaPair } from '@/schemas/schemaUtils'; // Import SchemaPair
import { AUTH_HEADERS } from '@/config/constants';

/**
 * Result of token selection for a given route
 */
export interface TokenSelectionResult {
  token: string | null;
  isCorrectTokenType: boolean;
  shouldRedirect: boolean;
}

/**
 * Selects the appropriate authentication token based on the current route.
 * This helps handle browser navigation (back/forward) where the route changes
 * faster than the auth state can update.
 * 
 * @param pathname - The current route pathname
 * @returns TokenSelectionResult with token and validation info
 */
export function getTokenForRoute(pathname: string): TokenSelectionResult {
  const store = useAuthStore.getState();
  const tokens = store.tokens;
  const user = store.user;
  
  // Check if we're on a parent route
  if (pathname.includes('/parent/') || pathname.endsWith('/parent')) {
    if (tokens.parent) {
      // We have the correct token
      return { 
        token: tokens.parent, 
        isCorrectTokenType: true, 
        shouldRedirect: false 
      };
    } else if (user?.userType === 'child' && user.parentPublicId) {
      // Child user with linked parent but no parent token - need to login as parent
      return { 
        token: null, 
        isCorrectTokenType: false, 
        shouldRedirect: true 
      };
    } else {
      // No parent token available and can't get one
      return { 
        token: null, 
        isCorrectTokenType: false, 
        shouldRedirect: true 
      };
    }
  }
  
  // Check if we're on a student route
  if (pathname.includes('/student/') || pathname.endsWith('/student')) {
    if (tokens.child) {
      // We have the correct token
      return { 
        token: tokens.child, 
        isCorrectTokenType: true, 
        shouldRedirect: false 
      };
    } else if (user?.userType === 'parent') {
      // Parent user on student route without child token
      // They need to select a child from parent dashboard
      return { 
        token: null, 
        isCorrectTokenType: false, 
        shouldRedirect: true 
      };
    } else if (user?.userType === 'child') {
      // Child user, use current token (might be a direct child login)
      return { 
        token: tokens.current, 
        isCorrectTokenType: true, 
        shouldRedirect: false 
      };
    } else {
      // Not authenticated
      return { 
        token: null, 
        isCorrectTokenType: false, 
        shouldRedirect: true 
      };
    }
  }
  
  // For other routes, use current token
  return { 
    token: tokens.current, 
    isCorrectTokenType: true, 
    shouldRedirect: false 
  };
}

/**
 * Maps a generic error to our standard AppErrorResponse format.
 * This ensures consistent error structures throughout the application.
 */
export function mapToAppError(error: unknown): AppErrorResponse {
  // 1. Handle Zod validation errors (from client-side validation or request helper)
  if (error instanceof z.ZodError) {
    return {
      status: 'error',
      error_code: AppErrorCodeEnum.enum.INVALID_REQUEST,
      message: 'Client-side validation failed or API response did not match schema.',
      details: error.errors.map((e) => ({
        field: e.path.join('.'),
        code: e.code,
        message: e.message,
      })),
    };
  }

  // 2. Handle API-like error objects (those with a 'data' property that might contain API error details)
  //    This also catches errors thrown as { data: responseData, status: response.status } by makeRequest
  if (typeof error === 'object' && error !== null && 'data' in error) {
    const errorData = (error as any).data as Record<string, any> | undefined;
    const status = (error as any).status as number | undefined;

    if (errorData && typeof errorData === 'object') {
      let apiErrorCode: AppErrorCode = AppErrorCodeEnum.enum.UNKNOWN_ERROR;
      const backendErrorField = errorData.error_code || errorData.error;

      if (typeof backendErrorField === 'string') {
        const parsedCode = AppErrorCodeEnum.safeParse(backendErrorField);
        if (parsedCode.success) {
          apiErrorCode = parsedCode.data;
        } else {
          // Log the unknown code for debugging if necessary
          // console.warn(`Unknown API error code received: '${backendErrorField}'`);
        }
      }

      // If it's a 401, ensure the error code reflects authentication failure
      if (status === 401 && apiErrorCode === AppErrorCodeEnum.enum.UNKNOWN_ERROR) {
        apiErrorCode = AppErrorCodeEnum.enum.AUTHENTICATION_REQUIRED;
      }

      return {
        status: 'error',
        error_code: apiErrorCode,
        message: (errorData.message as string) || `API request failed with status ${status || 'unknown'}.`,
        details: errorData.details as ErrorDetail[] | undefined,
        request_id: errorData.request_id as string | undefined,
      };
    }
  }

  // 3. Handle generic JavaScript Error instances (e.g., network errors, other runtime errors)
  if (error instanceof Error) {
    const isNetworkError = error.message.toLowerCase().includes('network') || error.message.toLowerCase().includes('failed to fetch');
    return {
      status: 'error',
      error_code: isNetworkError ? AppErrorCodeEnum.enum.SERVICE_UNAVAILABLE : AppErrorCodeEnum.enum.SERVICE_ERROR,
      message: error.message || 'A client-side error occurred.',
    };
  }

  // 4. Fallback for truly unknown error formats or non-Error throwables
  return {
    status: 'error',
    error_code: AppErrorCodeEnum.enum.UNKNOWN_ERROR,
    message: 'An unexpected and unknown error occurred.',
  };
}



/**
 * Enhanced customFetch with SchemaPair for typed request/response handling and case conversion.
 */

export async function customFetch<TReqFrontend, TResFrontend>(
  fullUrl: string,
  options: Omit<RequestInit, 'body'> & {
    responseSchemaPair: SchemaPair<TResFrontend, any>; // Response data is TResFrontend
    requestSchemaPair?: SchemaPair<TReqFrontend, any>; // Request data is TReqFrontend
    body?: TReqFrontend; // Body is expected in frontend (camelCase) format
  },
  authenticated: boolean = false,
  overrideToken?: string
): Promise<ServiceResult<TResFrontend>> {
  try {
    const headers = new Headers(options.headers || {});
    if (options.body && !headers.has('Content-Type')) {
      headers.set('Content-Type', 'application/json');
    }

    if (authenticated) {
      const store = useAuthStore.getState();
      const currentToken = overrideToken || store.tokens.current;
      if (currentToken) {
        headers.set('x-auth-token', currentToken);
      } else if (!overrideToken) {
        console.warn("Authenticated request initiated without a token.");
        // Optionally, could throw an error here or let the API handle it
      }
    }

    let processedBody: string | undefined = undefined;
    if (options.body !== undefined) {
      if (options.requestSchemaPair) {
        // .toApi will handle validation of frontend input, conversion, and validation of API output
        const apiRequestBody = options.requestSchemaPair.toApi(options.body);
        processedBody = JSON.stringify(apiRequestBody);
      } else {
        // Sticking to the plan: throw an error if body is present but no requestSchemaPair
        throw new Error("customFetch: 'requestSchemaPair' is required when 'body' is provided.");
      }
    }

    const fetchOptions: RequestInit = {
      method: options.method || (options.body ? 'POST' : 'GET'),
      ...options,
      headers,
      body: processedBody,
      next: { revalidate: 0 }, // This is the key for Next.js 13+ fetch
      cache: 'no-store', // This is standard fetch API

    };

    // Remove custom properties not part of RequestInit
    delete (fetchOptions as any).responseSchemaPair;
    delete (fetchOptions as any).requestSchemaPair;

    const response = await fetch(fullUrl, fetchOptions);

    const responseText = await response.text();
    let responseData: any;
    try {
      responseData = responseText ? JSON.parse(responseText) : {};
    } catch (e) {
      if (!response.ok) {
        throw { data: { message: `API error response was not valid JSON. Status: ${response.status}. Response: ${responseText.substring(0, 100)}...`, error_code: AppErrorCodeEnum.enum.SERVICE_ERROR }, status: response.status };
      }
      throw new z.ZodError([{
        path: ['response'],
        code: 'custom',
        message: `API returned malformed JSON for a successful response. Status: ${response.status}. Response: ${responseText.substring(0, 100)}...`
      }]);
    }

    if (!response.ok) {
      if (response.status === 401 && authenticated) {
        const store = useAuthStore.getState();
        const usedToken = overrideToken || store.tokens.current;
        
        // Determine which token failed and only clear that specific token
        if (usedToken === store.tokens.parent) {
          // Parent token failed - clear parent token but preserve child if exists
          console.warn('[Auth] Parent token validation failed, clearing parent session');
          store._setTokens({ parent: null });
          
          // If currently using parent token, switch to child if available
          if (store.tokens.current === usedToken && store.tokens.child) {
            store._setTokens({ current: store.tokens.child });
            // Redirect to student dashboard
            if (typeof window !== 'undefined') {
              const language = window.location.pathname.split('/')[1] || 'lu';
              window.location.href = `/${language}/student`;
            }
          } else if (store.tokens.current === usedToken) {
            // No child token available, full logout
            store.signOut();
          }
        } else if (usedToken === store.tokens.child) {
          // Child token failed - clear child token but preserve parent if exists
          console.warn('[Auth] Child token validation failed, clearing child session');
          store._setTokens({ child: null });
          
          // If currently using child token, switch to parent if available
          if (store.tokens.current === usedToken && store.tokens.parent) {
            store._setTokens({ current: store.tokens.parent });
            // Redirect to parent dashboard
            if (typeof window !== 'undefined') {
              const language = window.location.pathname.split('/')[1] || 'lu';
              window.location.href = `/${language}/parent`;
            }
          } else if (store.tokens.current === usedToken) {
            // No parent token available, full logout
            store.signOut();
          }
        } else {
          // Unknown token or direct login token failed - full logout
          console.warn('[Auth] Authentication failed, logging out');
          store.signOut();
        }
      }
      throw { data: responseData, status: response.status };
    }

    const dataToParseForFrontend = responseData.data !== undefined && responseData.status === 'success'
      ? responseData.data
      : responseData;

    // Debug logging for schema validation issues
    if (process.env.NODE_ENV === 'development') {
      console.log('API Response for schema validation:', {
        url: fullUrl,
        fullResponse: responseData,
        dataToParseForFrontend: dataToParseForFrontend
      });
    }

    // .toFrontend will handle validation of API input, conversion, and validation of frontend output
    const frontendData = options.responseSchemaPair.toFrontend(dataToParseForFrontend);

    const successResult: SuccessResponse<TResFrontend> = {
      status: 'success',
      data: frontendData,
      message: responseData.message,
    };

    return successResult;

  } catch (error) {
    console.log("error", error)
    return mapToAppError(error);
  }
}

/**
 * Enhanced customFetch wrapper for internal/editorial endpoints.
 * This extends the base customFetch to handle editorial authentication headers.
 */
export async function customFetchInternal<TReqFrontend, TResFrontend>(
  fullUrl: string,
  options: Omit<RequestInit, 'body'> & {
    responseSchemaPair: SchemaPair<TResFrontend, any>;
    requestSchemaPair?: SchemaPair<TReqFrontend, any>;
    body?: TReqFrontend;
  },
  authenticated: boolean = true,
  overrideToken?: string
): Promise<ServiceResult<TResFrontend>> {
  try {
    // Prepare headers with editorial auth token
    const headers = new Headers(options.headers || {});

    if (authenticated) {
      const store = useInternalAuthStore.getState();
      const token = overrideToken || store.token;

      if (token) {
        headers.set(AUTH_HEADERS.EDITORIAL_AUTH, token);
      } else if (!overrideToken) {
        console.warn("Authenticated internal request initiated without a token.");
      }
    }

    // Call the base customFetch without authentication (we handle it ourselves)
    const result = await customFetch(fullUrl, { ...options, headers }, false);
    
    // Check if we got a 401 error
    if (result.status === 'error' && 
        result.error_code === AppErrorCodeEnum.enum.AUTHENTICATION_REQUIRED && 
        authenticated) {
      // Clear internal auth state
      const store = useInternalAuthStore.getState();
      console.warn('[Internal Auth] Authentication failed, clearing session');
      store.logout();
    }
    
    return result;
  } catch (error: any) {
    // Handle 401 errors that might be thrown as exceptions
    if (error?.status === 401 && authenticated) {
      const store = useInternalAuthStore.getState();
      console.warn('[Internal Auth] Authentication failed, clearing session');
      store.logout();
    }
    throw error;
  }
}