import { z } from 'zod';
import { createSchemaPair } from '@/schemas/schemaUtils';
import { EditorRoleEnum } from '@/types/internal/authTypes';

// Current Editor Response Schema (for /editor/me endpoint)
const CurrentEditorAppSchema = z.object({
  id: z.number(),
  publicId: z.string(),
  email: z.string().email(),
  role: Editor<PERSON>ole<PERSON><PERSON>,
  isActive: z.boolean(),
  createdAt: z.string(),
  scopes: z.array(z.object({
    id: z.number(),
    type: z.enum(['SUBJECT', 'CHAPTER', 'LEARNING_NODE']),
    name: z.string(),
    publicId: z.string(),
    subjectName: z.string(),
    subjectId: z.string(),
    chapterName: z.string().nullable(),
    chapterId: z.string().nullable(),
  })).default([]),
});

const CurrentEditorApiSchema = z.object({
  id: z.number(),
  public_id: z.string(),
  email: z.string().email(),
  role: Editor<PERSON><PERSON><PERSON><PERSON>,
  is_active: z.boolean(),
  created_at: z.string(),
  scopes: z.array(z.object({
    id: z.number(),
    type: z.enum(['SUBJECT', 'CHAPTER', 'LEARNING_NODE']),
    name: z.string(),
    public_id: z.string(),
    subject_name: z.string(),
    subject_id: z.string(),
    chapter_name: z.string().nullable(),
    chapter_id: z.string().nullable(),
  })).default([]),
});

export const CurrentEditorSchema = createSchemaPair(
  CurrentEditorAppSchema,
  CurrentEditorApiSchema
);

// Editor Learning Nodes Response Schema (for /editor/learning-nodes endpoint)
const EditorLearningNodeAppSchema = z.object({
  id: z.string(),
  title: z.string(),
  order: z.number().nullable().optional(),
  exercisesCount: z.number(),
  draftCount: z.number(),
  newDrafts: z.number().default(0),
  inReviewDrafts: z.number().default(0),
});

const EditorLearningNodeApiSchema = z.object({
  id: z.string(),
  title: z.string(),
  order: z.number().nullable().optional(),
  exercises_count: z.number(),
  draft_count: z.number(),
  new_drafts: z.number().optional().default(0),
  in_review_drafts: z.number().optional().default(0),
});

const EditorChapterAppSchema = z.object({
  id: z.string(),
  title: z.string(),
  order: z.number().nullable().optional(),
  learningNodes: z.array(EditorLearningNodeAppSchema),
});

const EditorChapterApiSchema = z.object({
  id: z.string(),
  title: z.string(),
  order: z.number().nullable().optional(),
  learning_nodes: z.array(EditorLearningNodeApiSchema),
});

const EditorSubjectAppSchema = z.object({
  id: z.string(),
  name: z.string(),
  order: z.number().nullable().optional(),
  chapters: z.array(EditorChapterAppSchema),
});

const EditorSubjectApiSchema = z.object({
  id: z.string(),
  name: z.string(),
  order: z.number().nullable().optional(),
  chapters: z.array(EditorChapterApiSchema),
});

const EditorLearningNodesResponseAppSchema = z.object({
  totalNodes: z.number(),
  subjects: z.array(EditorSubjectAppSchema),
});

const EditorLearningNodesResponseApiSchema = z.object({
  total_nodes: z.number(),
  subjects: z.array(EditorSubjectApiSchema),
});

export const EditorLearningNodesResponseSchema = createSchemaPair(
  EditorLearningNodesResponseAppSchema,
  EditorLearningNodesResponseApiSchema
);

// Learning Node Detail Schema
const LearningNodeSubjectInfoAppSchema = z.object({
  id: z.string(),
  name: z.string(),
});

const LearningNodeSubjectInfoApiSchema = z.object({
  id: z.string(),
  name: z.string(),
});

const LearningNodeChapterInfoAppSchema = z.object({
  id: z.string(),
  title: z.string(),
  subject: LearningNodeSubjectInfoAppSchema,
});

const LearningNodeChapterInfoApiSchema = z.object({
  id: z.string(),
  title: z.string(),
  subject: LearningNodeSubjectInfoApiSchema,
});

const LearningNodeDetailAppSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().nullable().optional(),
  order: z.number().nullable().optional(),
  exercisesCount: z.number(),
  draftCount: z.number(),
  newDrafts: z.number().default(0),
  inReviewDrafts: z.number().default(0),
  chapter: LearningNodeChapterInfoAppSchema,
});

const LearningNodeDetailApiSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().nullable().optional(),
  order: z.number().nullable().optional(),
  exercises_count: z.number(),
  draft_count: z.number(),
  new_drafts: z.number().optional().default(0),
  in_review_drafts: z.number().optional().default(0),
  chapter: LearningNodeChapterInfoApiSchema,
});

export const LearningNodeDetailSchema = createSchemaPair(
  LearningNodeDetailAppSchema,
  LearningNodeDetailApiSchema
);

// Learning Node Content Schema
const LearningNodeContentAppSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().nullable().optional(),
  nodeType: z.string(),
  notes: z.string().nullable().optional(),
  chapter: LearningNodeChapterInfoAppSchema,
});

const LearningNodeContentApiSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().nullable().optional(),
  node_type: z.string(),
  notes: z.string().nullable().optional(),
  chapter: LearningNodeChapterInfoApiSchema,
});

export const LearningNodeContentSchema = createSchemaPair(
  LearningNodeContentAppSchema,
  LearningNodeContentApiSchema
);

// Export types
export type CurrentEditorApp = z.infer<typeof CurrentEditorSchema.frontend>;
export type EditorLearningNodesResponseApp = z.infer<typeof EditorLearningNodesResponseSchema.frontend>;
export type LearningNodeDetailApp = z.infer<typeof LearningNodeDetailSchema.frontend>;
export type LearningNodeContentApp = z.infer<typeof LearningNodeContentSchema.frontend>;