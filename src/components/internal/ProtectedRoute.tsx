'use client';

import { useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useInternalAuthStore, selectIsInternalAuthenticated, selectInternalUserRole } from '@/zustand/internal/internalAuthStore/internalAuthStore';
import LoadingSpinner from '@/components/custom/LoadingSpinner';
import { EditorRole } from '@/types/internal/authTypes';
import { useInternalAuthRedirect } from '@/hooks/useInternalAuthRedirect';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: EditorRole;
  redirectTo?: string;
}

export default function ProtectedRoute({ 
  children, 
  requiredRole, 
  redirectTo = '/internal/login' 
}: ProtectedRouteProps) {
  const router = useRouter();
  const { isLoading } = useInternalAuthStore();
  const userRole = useInternalAuthStore(selectInternalUserRole);
  const { isAuthenticated, initialized } = useInternalAuthRedirect(redirectTo);
  const hasRedirected = useRef(false);

  useEffect(() => {
    if (initialized && isAuthenticated && requiredRole && userRole !== requiredRole && !hasRedirected.current) {
      // If user doesn't have required role, redirect to their dashboard
      const dashboardPath = userRole === 'ADMIN' ? '/internal/admin' : '/internal/editor';
      hasRedirected.current = true;
      router.replace(dashboardPath);
    }
  }, [initialized, isAuthenticated, userRole, requiredRole, router]);

  // Show loading while checking auth
  if (!initialized || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner className="w-8 h-8" />
      </div>
    );
  }

  // Don't render content if not authenticated or wrong role
  if (!isAuthenticated || (requiredRole && userRole !== requiredRole)) {
    return null;
  }

  return <>{children}</>;
}