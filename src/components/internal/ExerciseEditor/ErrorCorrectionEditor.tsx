'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { 
  Plus, 
  Trash2, 
  AlertTriangle,
  RefreshCw,
  Type,
  Shuffle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { ImageUploadButton } from './ImageUploadButton';

interface ErrorCorrectionEditorProps {
  exerciseData: Record<string, any>;
  onChange: (data: Record<string, any>) => void;
}

export function ErrorCorrectionEditor({ exerciseData, onChange }: ErrorCorrectionEditorProps) {
  const [prompt, setPrompt] = useState(exerciseData.prompt || '');
  const [promptImageUrl, setPromptImageUrl] = useState(exerciseData.promptImageUrl || exerciseData.prompt_image_url || '');
  const [fullText, setFullText] = useState('');
  const [parts, setParts] = useState<string[]>(exerciseData.parts || []);
  const [autoSplit, setAutoSplit] = useState(true);

  // Helper function to join parts with proper spacing
  const joinParts = (partsArray: string[]) => {
    return partsArray.reduce((acc, part, index) => {
      if (index === 0) return part;
      
      // Don't add space before punctuation
      if (/^[.!?,;:''"""')\]}]/.test(part)) {
        return acc + part;
      }
      
      // Don't add space after opening punctuation
      if (/[''"""'(\[{]$/.test(partsArray[index - 1])) {
        return acc + part;
      }
      
      return acc + ' ' + part;
    }, '');
  };

  // Initialize full text from parts
  useEffect(() => {
    if (parts.length > 0 && !fullText) {
      setFullText(joinParts(parts));
    }
  }, []);

  const updateData = (updates: Partial<Record<string, any>>) => {
    onChange({
      ...exerciseData,
      ...updates,
      prompt,
      promptImageUrl
    });
  };

  const handlePromptChange = (value: string) => {
    setPrompt(value);
    updateData({ prompt: value });
  };

  const handleFullTextChange = (value: string) => {
    setFullText(value);
    
    if (autoSplit) {
      // Auto-split by words, keeping punctuation attached
      const newParts = value.match(/\S+\s*/g) || [];
      setParts(newParts.map(p => p.trim()));
      updateData({ parts: newParts.map(p => p.trim()) });
    }
  };


  const splitByWords = () => {
    const newParts = fullText.match(/\S+/g) || [];
    setParts(newParts);
    updateData({ parts: newParts });
  };

  const splitBySentences = () => {
    const newParts = fullText.match(/[^.!?]+[.!?]+/g) || [];
    setParts(newParts.map(p => p.trim()));
    updateData({ parts: newParts.map(p => p.trim()) });
  };


  return (
    <div className="space-y-6">
      {/* Prompt */}
      <div className="space-y-2">
        <Label htmlFor="prompt">Exercise Prompt</Label>
        <Textarea
          id="prompt"
          value={prompt}
          onChange={(e) => handlePromptChange(e.target.value)}
          placeholder="e.g., Correct the grammatical errors in the following sentence."
          className="min-h-[80px]"
        />
        <p className="text-sm text-gray-500">
          Explain what the student needs to do
        </p>
      </div>

      {/* Full Text Input */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label htmlFor="fullText">Text with Errors</Label>
          <div className="flex items-center gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={splitByWords}
            >
              <Type className="h-3 w-3 mr-1" />
              Split by Words
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={splitBySentences}
            >
              <Shuffle className="h-3 w-3 mr-1" />
              Split by Sentences
            </Button>
          </div>
        </div>
        <Textarea
          id="fullText"
          value={fullText}
          onChange={(e) => handleFullTextChange(e.target.value)}
          placeholder="Type or paste the text containing errors..."
          className="min-h-[100px] font-mono"
        />
        <p className="text-sm text-gray-500">
          Type the complete text. It will be automatically split into parts for the exercise.
        </p>
        
        <div className="flex items-center gap-3 mt-3">
          <ImageUploadButton
            currentImageUrl={promptImageUrl}
            onImageChange={(imageUrl) => setPromptImageUrl(imageUrl || '')}
          />
          {promptImageUrl && (
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <div className="relative w-16 h-16 bg-gray-100 rounded overflow-hidden">
                <img
                  src={promptImageUrl}
                  alt="Prompt image"
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.src = '/placeholder-image.svg';
                  }}
                />
              </div>
              <span>Image attached to prompt</span>
            </div>
          )}
        </div>
      </div>

      {/* Parts Display */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label>Text Parts Breakdown</Label>
          <span className="text-sm text-gray-500">{parts.length} parts</span>
        </div>
        
        {parts.length === 0 ? (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Enter text above to create parts for the exercise
            </AlertDescription>
          </Alert>
        ) : (
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-wrap gap-2">
                {parts.map((part, index) => (
                  <div
                    key={index}
                    className="group relative inline-flex items-center gap-1 bg-gray-100 rounded-md px-3 py-2 hover:bg-gray-200 transition-colors"
                  >
                    <span className="text-sm font-mono">
                      {part || <span className="text-gray-400">empty</span>}
                    </span>
                    <span className="text-xs text-gray-400 ml-1">#{index + 1}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

    </div>
  );
}