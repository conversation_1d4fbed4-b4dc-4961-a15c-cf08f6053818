'use client';

import { useState, useEffect } from 'react';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertTriangle } from 'lucide-react';

interface TrueFalseSolutionEditorProps {
  exerciseData: Record<string, any>;
  correctAnswer: any;
  onChange: (correctAnswer: any) => void;
}

export function TrueFalseSolutionEditor({
  exerciseData,
  correctAnswer,
  onChange
}: TrueFalseSolutionEditorProps) {
  const [selectedAnswer, setSelectedAnswer] = useState<string>('');

  // Initialize selected answer from correct answer
  useEffect(() => {
    if (typeof correctAnswer?.is_true === 'boolean') {
      setSelectedAnswer(correctAnswer.is_true.toString());
    }
  }, [correctAnswer]);

  const handleAnswerSelect = (value: string) => {
    setSelectedAnswer(value);
    onChange({
      is_true: value === 'true'
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 text-base font-semibold">
        <CheckCircle className="h-4 w-4" />
        Select Correct Answer
      </div>
          <p className="text-sm text-gray-600">
            Choose whether the statement is true or false:
          </p>
          
          <RadioGroup value={selectedAnswer} onValueChange={handleAnswerSelect}>
            <div className="space-y-3">
              <div
                className={`flex items-center space-x-3 p-3 rounded-lg border transition-colors ${
                  selectedAnswer === 'true' 
                    ? 'border-green-500 bg-green-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <RadioGroupItem value="true" id="true" />
                <Label htmlFor="true" className="flex-1 cursor-pointer">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">True</span>
                    {selectedAnswer === 'true' && (
                      <Badge variant="secondary">Correct Answer</Badge>
                    )}
                  </div>
                </Label>
              </div>

              <div
                className={`flex items-center space-x-3 p-3 rounded-lg border transition-colors ${
                  selectedAnswer === 'false' 
                    ? 'border-green-500 bg-green-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <RadioGroupItem value="false" id="false" />
                <Label htmlFor="false" className="flex-1 cursor-pointer">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">False</span>
                    {selectedAnswer === 'false' && (
                      <Badge variant="secondary">Correct Answer</Badge>
                    )}
                  </div>
                </Label>
              </div>
            </div>
          </RadioGroup>

          {selectedAnswer && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                The statement will be marked as <strong>{selectedAnswer === 'true' ? 'True' : 'False'}</strong>.
              </AlertDescription>
            </Alert>
          )}

          {!selectedAnswer && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Please select whether the statement is true or false.
              </AlertDescription>
            </Alert>
          )}

          <div className="text-xs text-gray-500">
            <p><strong>Tip:</strong> Add solution steps to explain why the statement is true or false.</p>
          </div>
    </div>
  );
}
