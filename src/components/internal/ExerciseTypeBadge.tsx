import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { ExerciseType } from '@/types/internal/editorial';
import { formatExerciseType, getExerciseTypeIcon } from '@/utils/internal/exerciseTypeLabels';

interface ExerciseTypeBadgeProps {
  exerciseType: ExerciseType;
  variant?: 'default' | 'secondary' | 'outline' | 'destructive';
  showIcon?: boolean;
  className?: string;
}

export default function ExerciseTypeBadge({
  exerciseType,
  variant = 'outline',
  showIcon = true,
  className
}: ExerciseTypeBadgeProps) {
  const label = formatExerciseType(exerciseType);
  const icon = getExerciseTypeIcon(exerciseType);

  return (
    <Badge variant={variant} className={cn('text-xs', className)}>
      {showIcon && <span className="mr-1">{icon}</span>}
      {label}
    </Badge>
  );
}