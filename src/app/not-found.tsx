'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

// Root level not-found page that redirects to language-specific not-found
export default function RootNotFound() {
  const router = useRouter();

  useEffect(() => {
    // Detect language from various sources
    let detectedLanguage = 'lu'; // Default language
    
    if (typeof window !== 'undefined') {
      // Try to get language from URL path
      const pathSegments = window.location.pathname.split('/');
      const possibleLang = pathSegments[1];
      
      if (['en', 'lu', 'de', 'fr'].includes(possibleLang)) {
        detectedLanguage = possibleLang;
      } else {
        // Check if there's a saved language preference
        const savedLanguage = localStorage.getItem('preferred_language');
        if (savedLanguage && ['en', 'lu', 'de', 'fr'].includes(savedLanguage)) {
          detectedLanguage = savedLanguage;
        }
        // Otherwise, keep default 'lu'
      }
    }

    // Redirect to login page as a safe fallback
    router.replace(`/${detectedLanguage}/auth/login`);
  }, [router]);

  // Show a minimal loading state while redirecting
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="animate-pulse text-gray-500">Redirecting...</div>
    </div>
  );
}