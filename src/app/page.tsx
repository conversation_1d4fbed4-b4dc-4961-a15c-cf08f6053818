'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/zustand/authStore/authStore';

export default function RootPage() {
  const router = useRouter();
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);
  const user = useAuthStore(state => state.user);
  const initialized = useAuthStore(state => state.initialized);
  const initAuth = useAuthStore(state => state.initAuth);

  useEffect(() => {
    // Initialize auth if not already done
    if (!initialized) {
      initAuth();
    }
  }, [initialized, initAuth]);

  useEffect(() => {
    // Only redirect after auth is initialized
    if (!initialized) return;

    // Detect language from various sources
    let detectedLanguage = 'lu'; // Default language
    
    if (typeof window !== 'undefined') {
      // Try to get language from localStorage first (user preference)
      const savedLanguage = localStorage.getItem('preferred_language');
      if (savedLanguage && ['en', 'lu', 'de', 'fr'].includes(savedLanguage)) {
        detectedLanguage = savedLanguage;
      }
      // Always default to 'lu' if no saved preference, regardless of browser language
    }

    // Determine redirect path based on authentication status
    let redirectPath = `/${detectedLanguage}/auth/login`;
    
    if (isAuthenticated && user) {
      redirectPath = user.userType === 'parent'
        ? `/${detectedLanguage}/parent`
        : `/${detectedLanguage}/student`;
    }

    // Redirect to the appropriate path
    router.replace(redirectPath);
  }, [router, isAuthenticated, user, initialized, initAuth]);

  // Show a minimal loading state while determining where to redirect
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-500">Loading...</p>
      </div>
    </div>
  );
}