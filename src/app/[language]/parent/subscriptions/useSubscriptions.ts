import { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';
import { useAuthStore } from '@/zustand/authStore/authStore';
import {
  getYearOfferings,
  createCheckoutSession,
  verifyDiscountCode,
  getCurrentSubscription,
} from '@/services/subscriptionService/subscriptionService';
import type {
  CreateCheckoutSessionAppRequest,
  VerifyDiscountCodeAppRequest,
} from '@/services/subscriptionService/subscriptionRequestSchema';
import type {
  YearOfferingsListAppResponse,
  PricingTierAppResponse,
} from '@/services/subscriptionService/subscriptionResponseSchema';
import type { BillingPeriod } from '@/services/subscriptionService/subscriptionRequestSchema';
import { SubjectOption, SystemOption, YearOption } from './types';
import { createTranslations, Language } from '@/i18n/types';

// Translations for toast messages
const translations = createTranslations({
  en: {
    discountSuccess: 'Discount code applied successfully!',
    discountSuccessTitle: 'Success',
    checkoutError: 'Failed to create checkout session',
    authError: 'Authentication required',
    selectError: 'Please select a year and subjects'
  },
  fr: {
    discountSuccess: 'Code promo appliqué avec succès!',
    discountSuccessTitle: 'Succès',
    checkoutError: 'Échec de la création de la session de paiement',
    authError: 'Authentification requise',
    selectError: 'Veuillez sélectionner une année et des matières'
  },
  de: {
    discountSuccess: 'Rabattcode erfolgreich angewendet!',
    discountSuccessTitle: 'Erfolg',
    checkoutError: 'Checkout-Sitzung konnte nicht erstellt werden',
    authError: 'Authentifizierung erforderlich',
    selectError: 'Bitte wählen Sie ein Jahr und Fächer aus'
  },
  lu: {
    discountSuccess: 'Rabattcode erfollegräich ugewand!',
    discountSuccessTitle: 'Erfolleg',
    checkoutError: 'Checkout-Sessioun konnt net erstallt ginn',
    authError: 'Authentifiéierung erfuerderlech',
    selectError: 'Wielt w.e.g. e Joer an Fächer aus'
  }
});

// Constants
const FULL_ACCESS_THRESHOLD = 2; // After 2 subjects, use YF pricing

// Types
interface CouponState {
  code: string;
  status: 'idle' | 'verifying' | 'valid' | 'invalid' | null;
  message: string;
  stripeMonthlyId?: string | null;
  stripeYearlyId?: string | null;
  applicableTo?: string | null;
}

interface PricingData {
  monthly: number;
  yearly: number;
  savings: {
    percentage: number;
    annualSavings: number;
  };
}

// Helper function to calculate volume discount pricing
const calculateTieredPrice = (tiers: PricingTierAppResponse[], quantity: number): number => {
  if (!tiers || tiers.length === 0 || quantity <= 0) {
    return 0;
  }

  // Find the applicable tier based on quantity (volume discount model)
  // The tier with the highest "up_to" that still accommodates our quantity
  let applicableTier: PricingTierAppResponse | null = null;
  
  for (const tier of tiers) {
    const tierLimit = tier.upTo === 'inf' ? Infinity : parseInt(tier.upTo, 10);
    
    if (quantity <= tierLimit) {
      applicableTier = tier;
      break; // Take the first tier that can accommodate the quantity
    }
  }
  
  // If no tier found, use the last tier (should be the "inf" tier)
  if (!applicableTier && tiers.length > 0) {
    applicableTier = tiers[tiers.length - 1];
  }
  
  if (!applicableTier) {
    return 0;
  }

  // Apply the unit price to ALL subjects (volume discount)
  return quantity * applicableTier.unitPriceDisplay;
};

// Hook
export const useSubscriptions = (language: string) => {
  const router = useRouter();
  const { toast } = useToast();
  const user = useAuthStore(state => state.user);
  const t = translations[language as Language] || translations.en;
  
  // Loading states
  const [loading, setLoading] = useState(true);
  const [checkoutLoading, setCheckoutLoading] = useState(false);
  const [error, setError] = useState<string>('');
  
  // Selection state
  const [selectedPlan, setSelectedPlan] = useState<BillingPeriod>('yearly');
  const [selectedSystemValue, setSelectedSystemValue] = useState<string>('');
  const [selectedYearValue, setSelectedYearValue] = useState<string>('');
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([]);
  
  // Data state
  const [yearOfferings, setYearOfferings] = useState<YearOfferingsListAppResponse | null>(null);
  const [currentSubscription, setCurrentSubscription] = useState<any>(null);
  const [subjectOptions, setSubjectOptions] = useState<SubjectOption[]>([]);
  const [coupon, setCoupon] = useState<CouponState>({
    code: '',
    status: null,
    message: '',
    stripeMonthlyId: null,
    stripeYearlyId: null,
    applicableTo: null
  });
  
  // System options derived from API data
  const systemOptions: SystemOption[] = useMemo(() => {
    if (!yearOfferings || !yearOfferings.yearOfferings) return [];
    
    const systems = [...new Set(yearOfferings.yearOfferings.map(offering => {
      const yearName = offering.yearName;
      if (yearName.toLowerCase().includes('général')) return 'general';
      if (yearName.toLowerCase().includes('classique')) return 'classique';
      return 'general'; // default
    }))];
    
    return systems.map(system => ({
      value: system,
      label: system === 'classique' ? 'Classique' : 'Général'
    }));
  }, [yearOfferings]);

  // Year options - filtered by selected system and preserving backend order
  const yearOptions: YearOption[] = useMemo(() => {
    if (!selectedSystemValue || !yearOfferings || !yearOfferings.yearOfferings) return [];
    
    const systemLabel = selectedSystemValue === 'classique' ? 'Classique' : 'Général';
    
    // Filter years by system (backend will handle subscription logic)
    return yearOfferings.yearOfferings
      .filter(offering => {
        const yearName = offering.yearName;
        // Special case for '4e' which doesn't have a system suffix
        if (!yearName.includes('Classique') && !yearName.includes('Général')) {
          return true; // Include years without system (like '4e')
        }
        return yearName.includes(systemLabel);
      })
      .map(offering => ({
        value: offering.yearPublicId,
        label: offering.yearName
      }));
  }, [selectedSystemValue, yearOfferings]);
  
  // Calculate pricing tiers for display
  const pricingTiers = useMemo(() => {
    if (!yearOfferings || !yearOfferings.yearOfferings || !selectedYearValue) {
      return { one: 0, two: 0, full: 0 };
    }
    
    const matchingOffering = yearOfferings.yearOfferings.find(offering => 
      offering.yearPublicId === selectedYearValue
    );
    
    if (!matchingOffering) {
      return { one: 0, two: 0, full: 0 };
    }
    
    // Find monthly pricing options
    const monthlyScPricing = matchingOffering.pricingOptions.find(option => 
      option.billingPeriod === 'monthly' && option.planType === 'SC'
    );
    const monthlyYfPricing = matchingOffering.pricingOptions.find(option => 
      option.billingPeriod === 'monthly' && option.planType === 'YF'
    );
    
    let oneSubjectPrice = 0;
    let twoSubjectsPrice = 0;
    let fullAccessPrice = 0;
    
    // Calculate SC pricing for 1 and 2 subjects
    if (monthlyScPricing && monthlyScPricing.tiers) {
      oneSubjectPrice = calculateTieredPrice(monthlyScPricing.tiers, 1);
      twoSubjectsPrice = calculateTieredPrice(monthlyScPricing.tiers, 2);
    }
    
    // Full access price (YF)
    if (monthlyYfPricing && monthlyYfPricing.displayPrice !== null && monthlyYfPricing.displayPrice !== undefined) {
      fullAccessPrice = monthlyYfPricing.displayPrice;
    }
    
    return {
      one: oneSubjectPrice,
      two: twoSubjectsPrice,
      full: fullAccessPrice
    };
  }, [yearOfferings, selectedYearValue]);

  // Current pricing based on selected subjects
  const currentPricing: PricingData = useMemo(() => {
    if (!yearOfferings || !yearOfferings.yearOfferings || !selectedYearValue || selectedSubjects.length === 0) {
      return {
        monthly: 0,
        yearly: 0,
        savings: { percentage: 0, annualSavings: 0 }
      };
    }
    
    const matchingOffering = yearOfferings.yearOfferings.find(offering => 
      offering.yearPublicId === selectedYearValue
    );
    
    if (!matchingOffering) {
      return {
        monthly: 0,
        yearly: 0,
        savings: { percentage: 0, annualSavings: 0 }
      };
    }
    
    let monthlyPrice = 0;
    let yearlyPrice = 0;
    
    const subjectCount = selectedSubjects.length;
    const totalSubjects = matchingOffering.availableSubjects.length;
    
    // Determine whether to use SC or YF pricing based on threshold
    const useFullYearPricing = subjectCount > FULL_ACCESS_THRESHOLD;
    
    if (useFullYearPricing) {
      // For 3+ subjects, use YF (full year) pricing
      const monthlyYfPricing = matchingOffering.pricingOptions.find(option => 
        option.billingPeriod === 'monthly' && option.planType === 'YF'
      );
      const yearlyYfPricing = matchingOffering.pricingOptions.find(option => 
        option.billingPeriod === 'yearly' && option.planType === 'YF'
      );
      
      if (monthlyYfPricing && monthlyYfPricing.displayPrice !== null && monthlyYfPricing.displayPrice !== undefined) {
        monthlyPrice = monthlyYfPricing.displayPrice;
      }
      
      if (yearlyYfPricing && yearlyYfPricing.displayPrice !== null && yearlyYfPricing.displayPrice !== undefined) {
        yearlyPrice = yearlyYfPricing.displayPrice;
      }
    } else {
      // For 1-2 subjects, use SC (per-subject) pricing
      const monthlyScPricing = matchingOffering.pricingOptions.find(option => 
        option.billingPeriod === 'monthly' && option.planType === 'SC'
      );
      const yearlyScPricing = matchingOffering.pricingOptions.find(option => 
        option.billingPeriod === 'yearly' && option.planType === 'SC'
      );
      
      // Calculate tiered pricing for SC plans
      if (monthlyScPricing && monthlyScPricing.tiers) {
        monthlyPrice = calculateTieredPrice(monthlyScPricing.tiers, subjectCount);
      }
      
      if (yearlyScPricing && yearlyScPricing.tiers) {
        yearlyPrice = calculateTieredPrice(yearlyScPricing.tiers, subjectCount);
      }
    }
    
    // Calculate savings
    const annualSavings = (monthlyPrice * 12) - yearlyPrice;
    const percentage = monthlyPrice > 0 ? Math.round((annualSavings / (monthlyPrice * 12)) * 100) : 0;
    
    return {
      monthly: monthlyPrice,
      yearly: yearlyPrice,
      savings: {
        percentage: Math.max(0, percentage),
        annualSavings: Math.max(0, annualSavings)
      }
    };
  }, [yearOfferings, selectedYearValue, selectedSubjects]);
  
  // Find existing subscription for the selected year to enforce billing period
  // Use currentSubscription from backend call, not auth store
  const existingSubscriptionForYear = useMemo(() => {
    if (!selectedYearValue || !currentSubscription) return null;
    
    // Check if currentSubscription has subscriptions array (multiple subscriptions)
    const subscriptions = currentSubscription.subscriptions || [currentSubscription];
    
    for (const subscription of subscriptions) {
      const planItem = subscription.planItems?.find((item: any) => {
        return item.yearPublicId === selectedYearValue;
      });
      
      if (planItem) {
        return {
          billingPeriod: planItem.billingPeriod as BillingPeriod,
          yearName: planItem.yearName,
          subscribedSubjects: planItem.selectedSubjects || []
        };
      }
    }
    
    return null;
  }, [selectedYearValue, currentSubscription]);
  
  // Get already subscribed subject IDs for the selected year
  const subscribedSubjectIds = useMemo(() => {
    return existingSubscriptionForYear?.subscribedSubjects.map((subject: any) => 
      subject.publicId
    ) || [];
  }, [existingSubscriptionForYear]);
  
  // Check if all selected subjects are already subscribed
  const allSelectedSubjectsSubscribed = useMemo(() => {
    if (selectedSubjects.length === 0) return false;
    return selectedSubjects.every(subjectId => subscribedSubjectIds.includes(subjectId));
  }, [selectedSubjects, subscribedSubjectIds]);
  
  // Get subjects that are selected but not yet subscribed (new subjects to add)
  const newSubjectsToAdd = useMemo(() => {
    return selectedSubjects.filter(subjectId => !subscribedSubjectIds.includes(subjectId));
  }, [selectedSubjects, subscribedSubjectIds]);
  
  // Check if user has any active subscriptions
  const hasActiveSubscriptions = Boolean(currentSubscription && (
    currentSubscription.hasSubscription || 
    (currentSubscription.subscriptions && currentSubscription.subscriptions.length > 0)
  ));
  
  // Derived state
  const isFormEnabled = Boolean(
    selectedYearValue && 
    selectedSubjects.length > 0 && 
    selectedPlan &&
    !allSelectedSubjectsSubscribed &&
    newSubjectsToAdd.length > 0
  );
  const isCurrentSubscriptionActive = false; // Let backend handle subscription detection automatically
  
  // Fetch subjects when year is selected
  const loadSubjectsForYear = useCallback(async (yearId: string) => {
    try {
      setError('');
      
      if (!yearOfferings || !yearOfferings.yearOfferings) {
        setSubjectOptions([]);
        setSelectedSubjects([]);
        return;
      }
      
      // Find the matching year offering
      const matchingOffering = yearOfferings.yearOfferings.find(offering => 
        offering.yearPublicId === yearId
      );
      
      if (!matchingOffering) {
        setSubjectOptions([]);
        setSelectedSubjects([]);
        return;
      }
      
      // Convert available subjects to SubjectOption format
      const subjects: SubjectOption[] = matchingOffering.availableSubjects.map(subject => ({
        publicId: subject.publicId,
        name: subject.name,
        yearPublicId: yearId
      }));
      
      setSubjectOptions(subjects);
      // Auto-select all subjects by default
      setSelectedSubjects(subjects.map(s => s.publicId));
    } catch (err) {
      console.error('Error loading subjects:', err);
      setError('Failed to load subjects');
      setSubjectOptions([]);
      setSelectedSubjects([]);
    }
  }, [yearOfferings]);
  
  // Load subjects when year changes
  useEffect(() => {
    if (selectedYearValue) {
      loadSubjectsForYear(selectedYearValue);
      // Don't clear selectedSubjects here - they will be auto-selected in loadSubjectsForYear
    } else {
      setSubjectOptions([]);
      setSelectedSubjects([]);
    }
  }, [selectedYearValue, loadSubjectsForYear]);
  
  // Auto-select billing period when year changes and there's an existing subscription
  useEffect(() => {
    if (existingSubscriptionForYear && selectedSubjects.length > 0 && !selectedPlan) {
      setSelectedPlan(existingSubscriptionForYear.billingPeriod);
    } else if (!existingSubscriptionForYear && selectedSubjects.length > 0 && !selectedPlan) {
      // Default to yearly if no existing subscription
      setSelectedPlan('yearly');
    }
  }, [existingSubscriptionForYear, selectedSubjects.length, selectedPlan]);
  
  // Load current subscription
  const loadCurrentSubscription = useCallback(async () => {
    try {
      const result = await getCurrentSubscription();
      if (result.status === 'success') {
        setCurrentSubscription(result.data);
      }
    } catch (err) {
      console.error('Error loading current subscription:', err);
      // Don't set error here as this is not critical for the page
    }
  }, []);

  // Load year offerings
  const loadYearOfferings = useCallback(async () => {
    try {
      setLoading(true);
      setError('');
      
      const result = await getYearOfferings();
      
      if (result.status === 'success') {
        setYearOfferings(result.data);
      } else {
        setError(result.message || 'Failed to load year offerings');
      }
    } catch (err) {
      console.error('Error loading year offerings:', err);
      setError('Failed to load year offerings');
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Initialize data
  useEffect(() => {
    loadYearOfferings();
    loadCurrentSubscription();
  }, [loadYearOfferings, loadCurrentSubscription]);
  
  // Handlers
  const handlePlanSelect = (plan: BillingPeriod) => {
    // If there's an existing subscription for this year, enforce the same billing period
    if (existingSubscriptionForYear && existingSubscriptionForYear.billingPeriod !== plan) {
      return; // Don't allow selection of different billing period
    }
    setSelectedPlan(plan);
    clearError();
  };
  
  const handleSystemSelect = (system: string) => {
    setSelectedSystemValue(system);
    setSelectedYearValue(''); // Clear year when system changes
    setSelectedSubjects([]); // Clear subjects when system changes
    clearError();
  };

  const handleYearSelect = (year: string) => {
    setSelectedYearValue(year);
    clearError();
  };
  
  const handleSubjectSelect = (subjectId: string) => {
    setSelectedSubjects(prev => {
      if (prev.includes(subjectId)) {
        return prev.filter(id => id !== subjectId);
      } else {
        return [...prev, subjectId];
      }
    });
    clearError();
  };
  
  const handleSelectAllSubjects = () => {
    const allSubjectIds = subjectOptions.map(s => s.publicId);
    const allSelected = allSubjectIds.length > 0 && allSubjectIds.every(id => selectedSubjects.includes(id));
    
    if (allSelected) {
      setSelectedSubjects([]);
    } else {
      setSelectedSubjects(allSubjectIds);
    }
    clearError();
  };
  
  const handleClearAllSubjects = () => {
    setSelectedSubjects([]);
    clearError();
  };
  
  const handleVerifyDiscountCode = useCallback(async () => {
    if (!coupon.code.trim()) return;
    
    try {
      setCoupon(prev => ({ ...prev, status: 'verifying', message: '' }));
      
      const request: VerifyDiscountCodeAppRequest = {
        discountCode: coupon.code.trim().toUpperCase(),
        subscriptionType: selectedPlan
      };
      
      const result = await verifyDiscountCode(request);
      
      if (result.status === 'success' && result.data.isValid) {
        // Check if the discount code is applicable to the selected billing period
        const isApplicableToBillingPeriod = 
          !result.data.applicableTo || 
          result.data.applicableTo === 'both' || 
          result.data.applicableTo === selectedPlan;

        if (isApplicableToBillingPeriod) {
          setCoupon(prev => ({
            ...prev,
            status: 'valid',
            message: '', // Don't use backend message, let frontend handle internationalization
            stripeMonthlyId: result.data.stripeMonthlyId,
            stripeYearlyId: result.data.stripeYearlyId,
            applicableTo: result.data.applicableTo
          }));
          toast({
            title: t.discountSuccessTitle,
            description: t.discountSuccess
          });
        } else {
          setCoupon(prev => ({
            ...prev,
            status: 'invalid',
            message: '', // Don't use backend message, let frontend handle internationalization
            stripeMonthlyId: null,
            stripeYearlyId: null,
            applicableTo: null
          }));
        }
      } else {
        setCoupon(prev => ({
          ...prev,
          status: 'invalid',
          message: '', // Don't use backend message, let frontend handle internationalization
          stripeMonthlyId: null,
          stripeYearlyId: null,
          applicableTo: null
        }));
      }
    } catch (err) {
      console.error('Error verifying discount code:', err);
      setCoupon(prev => ({
        ...prev,
        status: 'invalid',
        message: '', // Don't use backend message, let frontend handle internationalization
        stripeMonthlyId: null,
        stripeYearlyId: null,
        applicableTo: null
      }));
    }
  }, [coupon.code, selectedPlan, toast, t]);
  
  const handleSubmit = useCallback(async () => {
    if (!isFormEnabled) {
      setError(t.selectError);
      return;
    }
    
    if (!user) {
      setError(t.authError);
      return;
    }
    
    try {
      setCheckoutLoading(true);
      setError('');
      
      // Build checkout request based on NEW subjects only (filter out already subscribed subjects)
      const newSubjectsForCheckout = newSubjectsToAdd;
      const totalSelectedCount = selectedSubjects.length; // Total selected including already subscribed
      const isFullYearPlan = totalSelectedCount > FULL_ACCESS_THRESHOLD; // Use YF pricing for 3+ subjects
      
      const request: CreateCheckoutSessionAppRequest = {
        selections: [{
          yearPublicId: selectedYearValue,
          subjectPublicIds: isFullYearPlan ? [] : newSubjectsForCheckout, // Empty array for YF plan, specific subjects for SC plan
          fullYearPlan: isFullYearPlan,
          chosenBillingPeriod: selectedPlan
        }],
        language: language,
        discountCode: coupon.status === 'valid' ? coupon.code : undefined
      };
      
      const result = await createCheckoutSession(request);
      
      if (result.status === 'success') {
        const { flowType, checkoutSessionUrl, message, itemsAdded, discountApplied } = result.data;
        
        if (flowType === 'direct_update') {
          // Direct update - show success message and refresh subscription data
          toast({
            title: 'Success',
            description: message || `Successfully added ${itemsAdded || 1} year(s) to your subscription!`,
            variant: 'default',
            duration: 5000
          });
          
          // Refresh auth data to get updated subscription info
          const authStore = useAuthStore.getState();
          await authStore.initAuth();
          
          // Redirect to subscription management with success indicator
          setTimeout(() => {
            router.push(`/${language}/parent/manage-subscriptions?success=true&direct_update=true`);
          }, 2000);
        } else {
          // Check if this is a separate subscription due to billing period mismatch
          if (message?.includes('different billing period')) {
            toast({
              title: 'Note',
              description: message,
              variant: 'default',
              duration: 6000
            });
            // Give user time to read the message before redirecting
            setTimeout(() => {
              window.location.href = checkoutSessionUrl;
            }, 2000);
          } else {
            // Traditional checkout flow - redirect to Stripe immediately
            window.location.href = checkoutSessionUrl;
          }
        }
      } else {
        const errorMessage = result.message || t.checkoutError;
        setError(errorMessage);
        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive'
        });
      }
    } catch (err) {
      console.error('Error creating checkout session:', err);
      const errorMessage = t.checkoutError;
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setCheckoutLoading(false);
    }
  }, [isFormEnabled, selectedYearValue, selectedSubjects, selectedPlan, coupon, toast, user, subjectOptions.length, newSubjectsToAdd, subscribedSubjectIds, t]);
  
  const clearError = () => {
    setError('');
  };
  
  const navigateToManageSubscriptions = () => {
    router.push(`/${language}/parent/manage-subscriptions`);
  };
  
  return {
    // Status
    loading,
    error,
    checkoutLoading,
    
    // Selection State (read-only)
    selectedPlan,
    selectedSystemValue,
    selectedYearValue,
    selectedSubjects,
    
    // Data & Options
    systemOptions,
    yearOptions,
    subjectOptions,
    currentPricing,
    pricingTiers,
    
    // Coupon
    coupon,
    
    // Existing subscription info
    existingSubscriptionForYear,
    subscribedSubjectIds,
    allSelectedSubjectsSubscribed,
    newSubjectsToAdd,
    hasActiveSubscriptions,
    
    // Derived Status
    isFormEnabled,
    isCurrentSubscriptionActive,
    
    // Methods
    handlePlanSelect,
    handleSystemSelect,
    handleYearSelect,
    handleSubjectSelect,
    handleSelectAllSubjects,
    handleClearAllSubjects,
    setCoupon,
    handleVerifyDiscountCode,
    handleSubmit,
    clearError,
    navigateToManageSubscriptions
  };
};

export default useSubscriptions;