'use client';
import { motion, AnimatePresence } from 'framer-motion';
import { Book, Check, Plus, Sparkles, Users, X, Info, Percent } from 'lucide-react';

interface SubjectSelectorProps {
  language?: string;
  subjectOptions: Array<{ publicId: string; name: string }>;
  selectedSubjects: string[];
  handleSubjectSelect: (subjectId: string) => void;
  handleSelectAll: () => void;
  handleClearAll: () => void;
  currentPricing: { monthly: number; yearly: number; savings: { annualSavings: number } };
  maxSubjects?: number;
  subscribedSubjectIds?: string[];
  pricingTiers?: { one: number; two: number; full: number }; // Pricing for 1, 2, and 3+ subjects
}

// Threshold for full access pricing
const FULL_ACCESS_THRESHOLD = 2;

/**
 * Component for selecting subjects with improved design
 */
const SubjectSelector = ({
  language = 'en',
  subjectOptions = [],
  selectedSubjects = [],
  handleSubjectSelect,
  handleSelectAll,
  handleClearAll,
  currentPricing,
  maxSubjects,
  subscribedSubjectIds = [],
  pricingTiers = { one: 0, two: 0, full: 0 },
}: SubjectSelectorProps) => {

  const texts = {
    en: {
      selectSubjects: "Select subjects",
      chooseSubjects: "Choose the subjects you want to subscribe to",
      selectAll: "Select All",
      clearAll: "Clear All",
      selected: "selected",
      subject: "subject",
      subjects: "subjects",
      pricingInfo: "Pricing: First subject {firstPrice}€, additional subjects at discount",
      allSubjectsPrice: "All subjects: {price}€",
      discountNotification: "💡 Great choice! Additional subjects come with a discount",
      discountDetails: "Save money by adding more subjects to your subscription",
      allSubjectsOption: "All Subjects",
      allSubjectsDiscount: "Best Value!",
      allSubjectsDescription: "Get everything with maximum savings",
      saveAmount: "Save {amount}€",
      bestDeal: "Best Deal",
      comingSoon: "Coming Soon",
      alreadySubscribed: "Already Subscribed",
      subscribed: "Subscribed",
      fullAccessNotification: "🎉 Access to all subjects!",
      fullAccessDescription: "All our content at the best price",
      perSubjectPrice: "per subject",
      fullYearAccess: "All Subjects",
      month: "month",
      save: "Save"
    },
    fr: {
      selectSubjects: "Sélectionner les matières",
      chooseSubjects: "Choisissez les matières auxquelles vous voulez vous abonner",
      selectAll: "Tout sélectionner",
      clearAll: "Tout effacer",
      selected: "sélectionné",
      subject: "matière",
      subjects: "matières",
      pricingInfo: "Prix : Première matière {firstPrice}€, matières supplémentaires avec réduction",
      allSubjectsPrice: "Toutes les matières : {price}€",
      discountNotification: "💡 Excellent choix ! Les matières supplémentaires bénéficient d'une réduction",
      discountDetails: "Économisez en ajoutant plus de matières à votre abonnement",
      allSubjectsOption: "Toutes les Matières",
      allSubjectsDiscount: "Meilleure Offre!",
      allSubjectsDescription: "Obtenez tout avec des économies maximales",
      saveAmount: "Économisez {amount}€",
      bestDeal: "Meilleur Deal",
      comingSoon: "Bientôt disponible",
      alreadySubscribed: "Déjà abonné",
      subscribed: "Abonné",
      fullAccessNotification: "🎉 Accès complet à toutes les matières !",
      fullAccessDescription: "Tout notre contenu au meilleur prix",
      perSubjectPrice: "par matière",
      fullYearAccess: "Toutes les matières",
      month: "mois",
      save: "Économisez"
    },
    de: {
      selectSubjects: "Fächer auswählen",
      chooseSubjects: "Wählen Sie die Fächer aus, die Sie abonnieren möchten",
      selectAll: "Alle auswählen",
      clearAll: "Alle löschen",
      selected: "ausgewählt",
      subject: "Fach",
      subjects: "Fächer",
      pricingInfo: "Preise: Erstes Fach {firstPrice}€, zusätzliche Fächer mit Rabatt",
      allSubjectsPrice: "Alle Fächer: {price}€",
      discountNotification: "💡 Tolle Wahl! Zusätzliche Fächer kommen mit Rabatt",
      discountDetails: "Sparen Sie Geld, indem Sie weitere Fächer hinzufügen",
      allSubjectsOption: "Alle Fächer",
      allSubjectsDiscount: "Bester Wert!",
      allSubjectsDescription: "Alles mit maximalen Ersparnissen",
      saveAmount: "Sparen Sie {amount}€",
      bestDeal: "Bestes Angebot",
      comingSoon: "Demnächst verfügbar",
      alreadySubscribed: "Bereits abonniert",
      subscribed: "Abonniert",
      fullAccessNotification: "🎉 Zugriff auf alle Fächer!",
      fullAccessDescription: "Alle unsere Inhalte zum besten Preis",
      perSubjectPrice: "pro Fach",
      fullYearAccess: "Alle Fächer",
      month: "Monat",
      save: "Sparen Sie"
    },
    lu: {
      selectSubjects: "Fächer auswielen",
      chooseSubjects: "Wielt d'Fächer aus déi Dir wëllt abonnéieren",
      selectAll: "All auswielen",
      clearAll: "All läschen",
      selected: "ausgewielt",
      subject: "Fach",
      subjects: "Fächer",
      pricingInfo: "Präisser: Éischt Fach {firstPrice}€, zousätzlech Fächer mat Rabatt",
      allSubjectsPrice: "All Fächer: {price}€",
      discountNotification: "💡 Reduktioun fir all weidert Fach",
      discountDetails: "Spart Sue andeems Dir méi Fächer derbäi setzt",
      allSubjectsOption: "All Fächer",
      allSubjectsDiscount: "Bescht Offer!",
      allSubjectsDescription: "Kritt alles mat maximalen Ersparnisser",
      saveAmount: "Spart {amount}€",
      bestDeal: "Bescht Offer",
      comingSoon: "Demnächst disponibel",
      alreadySubscribed: "Scho abonnéiert",
      subscribed: "Abonnéiert",
      fullAccessNotification: "🎉 Accès op all Fächer!",
      fullAccessDescription: "All eisen Contenu zum beschte Präis",
      perSubjectPrice: "pro Fach",
      fullYearAccess: "All Fächer",
      month: "Mount",
      save: "Spart"
    }
  };

  const languageData = texts[language as keyof typeof texts] || texts.en;

  const isAllSelected = selectedSubjects.length === subjectOptions.length && subjectOptions.length > 0;
  const selectedCount = selectedSubjects.length;

  // Helper function to format price without unnecessary decimals
  const formatPrice = (price: number): string => {
    return price % 1 === 0 ? price.toString() : price.toFixed(2);
  };

  // Calculate potential savings for "All subjects" option
  const calculateSavings = () => {
    // This is a placeholder calculation - adjust based on your actual pricing logic
    // Assuming first subject is full price, additional subjects have discount
    if (subjectOptions.length > 1) {
      const firstSubjectPrice = currentPricing.monthly;
      const allSubjectsPrice = currentPricing.monthly; // Your actual "all subjects" price
      const individualTotal = firstSubjectPrice * subjectOptions.length;
      return Math.max(0, individualTotal - allSubjectsPrice);
    }
    return 0;
  };

  const potentialSavings = calculateSavings();

  // Enhanced subject icon mapping
  const getSubjectIcon = (subjectName: string) => {
    const name = subjectName.toLowerCase();
    
    // Language subjects with flags
    if (name.includes('french') || name.includes('français') || name.includes('franzéisch')) return '🇫🇷';
    if (name.includes('german') || name.includes('deutsch') || name.includes('däitsch')) return '🇩🇪';
    if (name.includes('english') || name.includes('englisch') || name.includes('englesch')) return '🇬🇧';
    if (name.includes('spanish') || name.includes('espagnol') || name.includes('spanisch')) return '🇪🇸';
    if (name.includes('italian') || name.includes('italien') || name.includes('italienisch')) return '🇮🇹';
    if (name.includes('portuguese') || name.includes('portugais') || name.includes('portugiesisch')) return '🇵🇹';
    if (name.includes('luxembourgish') || name.includes('luxembourgeois') || name.includes('lëtzebuergesch')) return '🇱🇺';
    
    // Other subjects
    if (name.includes('math') || name.includes('mathematik') || name.includes('mathématiques')) return '📐';
    if (name.includes('science') || name.includes('chemistry') || name.includes('physics') || 
        name.includes('biologie') || name.includes('chimie') || name.includes('physique')) return '🔬';
    if (name.includes('history') || name.includes('histoire') || name.includes('geschichte')) return '📚';
    if (name.includes('geography') || name.includes('géographie') || name.includes('geographie')) return '🌍';
    if (name.includes('art') || name.includes('kunst')) return '🎨';
    if (name.includes('music') || name.includes('musique') || name.includes('musik')) return '🎵';
    if (name.includes('sport') || name.includes('physical') || name.includes('éducation physique')) return '⚽';
    if (name.includes('computer') || name.includes('informatique') || name.includes('informatik')) return '💻';
    
    // Default book icon
    return '📖';
  };

  // Helper function to check if a language subject already exists
  const isLanguageAvailable = (languageName: string): boolean => {
    const languageVariants: Record<string, string[]> = {
      'french': ['french', 'français', 'franzéisch'],
      'german': ['german', 'deutsch', 'däitsch', 'allemand'],
      'english': ['english', 'englisch', 'englesch', 'anglais']
    };
    
    const variants = languageVariants[languageName.toLowerCase()] || [languageName.toLowerCase()];
    
    return subjectOptions.some(subject => {
      const subjectNameLower = subject.name.toLowerCase();
      return variants.some(variant => subjectNameLower.includes(variant));
    });
  };

  // Define coming soon subjects - filter out ones that are already available
  const allComingSoonSubjects = [
    { id: 'french-coming-soon', name: 'French', icon: '🇫🇷' },
    { id: 'german-coming-soon', name: 'German', icon: '🇩🇪' },
    { id: 'english-coming-soon', name: 'English', icon: '🇬🇧' }
  ];
  
  const comingSoonSubjects = allComingSoonSubjects.filter(subject => 
    !isLanguageAvailable(subject.name)
  );

  return (
    <div className="space-y-4">
      {/* Compact pricing overview - only show when there are multiple subjects */}
      {subjectOptions.length > 1 && pricingTiers && (
        <div className="bg-gray-50 rounded-lg p-3 text-xs text-gray-600 border border-gray-200">
          <div className="flex items-center justify-center gap-3 flex-wrap">
            <span className="flex items-center gap-1">
              <span className="font-medium">1 {languageData.subject}:</span>
              <span className="text-gray-800">{pricingTiers.one > 0 ? `${formatPrice(pricingTiers.one)}€/${languageData.month}` : ''}</span>
            </span>
            {subjectOptions.length >= 2 && (
              <>
                <span className="text-gray-400">•</span>
                <span className="flex items-center gap-1">
                  <span className="font-medium">2 {languageData.subjects}:</span>
                  <span className="text-gray-800">{pricingTiers.two > 0 ? `${formatPrice(pricingTiers.two)}€/${languageData.month}` : ''}</span>
                </span>
              </>
            )}
            {subjectOptions.length > 2 && (
              <>
                <span className="text-gray-400">•</span>
                <span className="flex items-center gap-1 text-primary-700 font-bold">
                  <Sparkles className="w-3 h-3" />
                  <span>3+ = {languageData.allSubjectsOption}:</span>
                  <span>{pricingTiers.full > 0 ? `${formatPrice(pricingTiers.full)}€/${languageData.month}` : ''}</span>
                </span>
              </>
            )}
          </div>
        </div>
      )}

      {/* Compact selection controls - Hidden when only one subject */}
      {/* {subjectOptions.length > 1 && (
        <div className="flex items-center justify-between">
     
          <div className="flex gap-2">
            <button
              onClick={handleSelectAll}
              disabled={isAllSelected}
              className="px-3 py-1 text-xs bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {languageData.selectAll}
            </button>
            <button
              onClick={handleClearAll}
              disabled={selectedCount === 0}
              className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {languageData.clearAll}
            </button>
          </div>
        </div>
      )} */}

      {/* Discount notification for compact mode - appears when first subject is selected */}
      {/* <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, y: -10, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -10, scale: 0.95 }}
          transition={{ type: "spring", duration: 0.4, bounce: 0.3 }}
          className="bg-gradient-to-r from-secondary/10 to-primary/10 border border-secondary/30 rounded-lg p-3 shadow-md"
        >
          <div className="flex items-center space-x-2">
            <div className="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-secondary to-primary rounded-full flex items-center justify-center">
              <Percent className="w-3 h-3 text-white" />
            </div>
            <div className="text-left">
              <div className="text-primary-800 font-bold text-xs mb-0.5">
                {languageData.discountNotification}
              </div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence> */}

      {/* Compact subject grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
        {subjectOptions.map((subject, index) => {
          const isSelected = selectedSubjects.includes(subject.publicId);
          const isSubscribed = subscribedSubjectIds.includes(subject.publicId);
          // Prevent deselection if only one subject is available and it's selected
          const isOnlySubject = subjectOptions.length === 1 && isSelected;
          const canSelect = (!maxSubjects || selectedSubjects.length < maxSubjects || isSelected) && !isOnlySubject && !isSubscribed;

          return (
            <motion.button
              key={subject.publicId}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.2, delay: index * 0.02 }}
              whileTap={canSelect ? { scale: 0.98 } : {}}
              onClick={() => {
                canSelect && handleSubjectSelect(subject.publicId);
              }}
              disabled={!canSelect}
              className={`
                  relative p-3 rounded-lg text-center transition-all duration-200 border-2 text-xs
                  ${isSubscribed
                    ? 'bg-green-50 border-green-400 shadow-md cursor-default'
                    : isSelected
                      ? isOnlySubject 
                        ? 'bg-primary-50 border-primary-400 shadow-md cursor-default'
                        : 'bg-primary-50 border-primary-400 shadow-md cursor-pointer'
                      : canSelect
                        ? 'bg-white border-gray-200 hover:border-primary-300 hover:bg-primary-50/50 cursor-pointer'
                        : 'bg-gray-50 border-gray-200 opacity-50 cursor-not-allowed'
                  }
                `}
            >
              <div className="flex items-center justify-center space-y-1 flex-col">
                <div className="text-lg">
                  {getSubjectIcon(subject.name)}
                </div>
                <span className={`font-medium leading-tight ${
                  isSubscribed 
                    ? 'text-green-700' 
                    : isSelected 
                      ? 'text-primary-700' 
                      : canSelect 
                        ? 'text-gray-700' 
                        : 'text-gray-500'
                }`}>
                  {subject.name}
                </span>
                {isSubscribed && (
                  <span className="text-[10px] text-green-600 font-medium mt-1">
                    {languageData.subscribed}
                  </span>
                )}
                {isSubscribed && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute top-1 right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center"
                  >
                    <Check className="w-2.5 h-2.5 text-white" />
                  </motion.div>
                )}
                {isSelected && !isSubscribed && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute top-1 right-1 w-4 h-4 bg-primary-500 rounded-full flex items-center justify-center"
                  >
                    <Check className="w-2.5 h-2.5 text-white" />
                  </motion.div>
                )}
              </div>
            </motion.button>
          );
        })}

        {/* Coming Soon Subjects */}
        {comingSoonSubjects.map((subject, index) => (
          <motion.div
            key={subject.id}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.2, delay: (subjectOptions.length + index) * 0.02 }}
            className="relative p-3 rounded-lg text-center border-2 text-xs bg-gray-50 border-gray-200 opacity-60 cursor-not-allowed"
          >
            <div className="flex items-center justify-center space-y-1 flex-col">
              <div className="text-lg grayscale opacity-70">
                {subject.icon}
              </div>
              <span className="font-medium leading-tight text-gray-500">
                {subject.name}
              </span>
              <span className="text-[10px] text-gray-400 font-medium mt-1">
                {languageData.comingSoon}
              </span>
            </div>
            {/* Coming Soon badge */}
            <div className="absolute top-1 right-1">
              <motion.div
                animate={{ 
                  scale: [1, 1.1, 1],
                }}
                transition={{ 
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="w-4 h-4 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full flex items-center justify-center shadow-sm"
              >
                <Sparkles className="w-2.5 h-2.5 text-white" />
              </motion.div>
            </div>
          </motion.div>
        ))}

        {/* All subjects option for compact mode */}
        {subjectOptions.length > 1 && (
          <motion.button
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.2, delay: (subjectOptions.length + comingSoonSubjects.length) * 0.02 }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleSelectAll}
            className={`
                relative p-3 rounded-lg text-center transition-all duration-200 border-2 text-xs
                ${isAllSelected
                ? 'bg-gradient-to-br from-secondary/20 to-secondary/10 border-secondary shadow-md'
                : 'bg-white border-gray-200 hover:border-secondary/50 hover:bg-secondary/5'
              }
              `}
          >
            {/* "Best Deal" badge */}
            <div className="absolute -top-1 left-1 bg-secondary text-white text-xs px-1.5 py-0.5 rounded-full shadow-sm border-2 border-white">
              {languageData.bestDeal}
            </div>

            <div className="flex items-center justify-center space-y-1 flex-col">
              <div className="text-lg">🎓</div>
              <span className={`font-bold leading-tight ${isAllSelected ? 'text-secondary-darker' : 'text-gray-700'}`}>
                {languageData.allSubjectsOption}
              </span>
              {isAllSelected && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute top-1 right-1 w-4 h-4 bg-secondary rounded-full flex items-center justify-center"
                >
                  <Check className="w-2.5 h-2.5 text-white" />
                </motion.div>
              )}
            </div>
          </motion.button>
        )}
      </div>

      {/* Combined pricing display */}
      {selectedCount > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className={`border rounded-lg p-3 text-center text-sm ${
            selectedCount > FULL_ACCESS_THRESHOLD
              ? 'bg-gradient-to-r from-secondary/20 to-primary/20 border-secondary/30'
              : 'bg-gradient-to-r from-primary-50 to-secondary/10 border-primary-200'
          }`}
        >
          {selectedCount > FULL_ACCESS_THRESHOLD && (
            <div className="flex items-center justify-center gap-2 mb-1">
              <span className="text-lg">🎉</span>
              <span className="text-primary-800 font-bold">{languageData.fullAccessNotification.replace('🎉 ', '')}</span>
            </div>
          )}
          <div className="font-semibold text-primary-800">
            {selectedCount > FULL_ACCESS_THRESHOLD
              ? `${languageData.fullYearAccess}: ${formatPrice(currentPricing.monthly)}€/${languageData.month}`
              : `${selectedCount} ${selectedCount === 1 ? languageData.subject : languageData.subjects}: ${formatPrice(currentPricing.monthly)}€/${languageData.month}`
            }
            {selectedCount > 1 && selectedCount <= FULL_ACCESS_THRESHOLD && (
              <span className="text-sm font-normal text-gray-600 ml-1">
                ({formatPrice(currentPricing.monthly / selectedCount)}€ {languageData.perSubjectPrice})
              </span>
            )}
          </div>
          {selectedCount > FULL_ACCESS_THRESHOLD && (
            <div className="text-xs text-gray-700 mt-1">
              {languageData.fullAccessDescription}
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
};

export default SubjectSelector;