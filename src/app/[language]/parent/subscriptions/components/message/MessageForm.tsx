'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageCircle, AlertTriangle, CheckCircle } from 'lucide-react';
import { useAuthStore } from '@/zustand/authStore/authStore';
import { createTranslations, Language } from '@/i18n/types';
import { contactService } from '@/services/contactService/contactService';
import { SendContactMessageAppRequest } from '@/services/contactService/contactRequestSchema';

// Define translations
const translations = createTranslations({
  en: {
    askUs: "Any questions?",
    messageUs: "Message Us",
    hideForm: "Hide Form",
    messagePlaceholder: "Your message...",
    sending: "Sending...",
    sendMessage: "Send Message",
    error: "There was an error sending your message. Please try again.",
    messageSent: "Your message has been sent! We'll get back to you as soon as possible."
  },
  fr: {
    askUs: "Vous avez des questions ?",
    messageUs: "Nous contacter",
    hideForm: "Masquer le formulaire",
    messagePlaceholder: "Votre message...",
    sending: "Envoi en cours...",
    sendMessage: "Envoyer le message",
    error: "Une erreur s'est produite lors de l'envoi de votre message. Veuillez réessayer.",
    messageSent: "Votre message a été envoyé ! Nous vous répondrons aussi vite que possible."
  },
  de: {
    askUs: "Sie haben Fragen?",
    messageUs: "Nachricht senden",
    hideForm: "Formular ausblenden",
    messagePlaceholder: "Ihre Nachricht...",
    sending: "Wird gesendet...",
    sendMessage: "Nachricht senden",
    error: "Beim Senden Ihrer Nachricht ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.",
    messageSent: "Ihre Nachricht wurde gesendet! Wir werden uns so schnell wie möglich bei Ihnen melden."
  },
  lu: {
    askUs: "Dir hutt Froen?",
    messageUs: "Schreift eis",
    hideForm: "Formulaire verstoppen",
    messagePlaceholder: "Ären Message...",
    sending: "Gëtt geschéckt...",
    sendMessage: "Message schécken",
    error: "Et gouf e Problem beim schécke vun Ärem Message. Probéiert w.e.g. nach eng Kéier.",
    messageSent: "Äre Message gouf geschéckt! Mir mellen eis esou schnell ewéi méiglech."
  }
});

// Button spinner component
const ButtonSpinner = () => (
    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
);

// Component props interface
interface MessageFormProps {
    language?: Language;
}

/**
 * Message form component for contacting support
 */
const MessageForm: React.FC<MessageFormProps> = ({ language = 'en' }) => {
    // Get language-specific data (fallback to English if language not available)
    const t = translations[language as Language] || translations.en;

    // State management
    const [showMessageForm, setShowMessageForm] = useState(false);
    const [message, setMessage] = useState('');
    const [hasSubmitted, setHasSubmitted] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState(false);

    // Get current user from auth store
    const currentUser = useAuthStore(state => state.user);

    /**
     * Handle message form submission
     */
    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        // Validation
        if (!message.trim()) return;

        // Update UI state
        setIsSubmitting(true);
        setError(false);
        setHasSubmitted(false);

        try {
            // Prepare user identifier from available data
            const userIdentifier = currentUser?.publicId 
                ? `User ${currentUser.publicId}` 
                : 'Anonymous user';
            
            // Get user email or use identifier as fallback
            const userEmail = currentUser?.email || `${userIdentifier.replace(/\s+/g, '_').toLowerCase()}@subscription.luxedu.lu`;
            
            // Format the message with context
            const formattedMessage = `Message from subscription page: ${message}`;
            
            // Prepare contact request
            const contactRequest: SendContactMessageAppRequest = {
                email: userEmail,
                name: userIdentifier,
                message: formattedMessage
            };

            // Submit message using the contact service
            const response = await contactService.sendContactMessage(contactRequest);

            if (response.status === 'success') {
                setHasSubmitted(true);
                setMessage('');
            } else {
                setError(true);
            }
        } catch (error) {
            console.error('Error submitting form:', error);
            setError(true);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className='flex flex-col mt-2'>
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 bg-blue-50/50 p-4 rounded-lg border border-blue-100/80 shadow-sm">
                <div className="flex items-center gap-3 w-full sm:w-auto">
                    <div className="bg-primary/10 rounded-full p-2 flex-shrink-0">
                        <MessageCircle className='size-[20px] text-primary' />
                    </div>
                    <div className="flex-1">
                        <p className='text-gray-700 font-medium text-sm'>
                            {t.askUs}
                        </p>
                    </div>
                </div>
                <div className="mt-3 sm:mt-0 w-full sm:w-auto sm:ml-auto">
                    <button
                        onClick={() => setShowMessageForm(!showMessageForm)}
                        className={`flex items-center justify-center w-full sm:w-auto ${showMessageForm ? 'bg-gray-200 text-gray-700 hover:bg-gray-300' : 'bg-primary hover:bg-primary-dark text-white'} rounded-lg px-4 py-2 cursor-pointer text-sm transition duration-200 shadow-sm`}
                    >
                        <span>{showMessageForm ? t.hideForm : t.messageUs}</span>
                    </button>
                </div>
            </div>

            <AnimatePresence>
                {showMessageForm && !hasSubmitted && (
                    <motion.form
                        onSubmit={handleSubmit}
                        initial={{ opacity: 0, scaleY: 0, originY: 0 }}
                        animate={{ opacity: 1, scaleY: 1, originY: 0 }}
                        exit={{ opacity: 0, scaleY: 0, originY: 0 }}
                        transition={{ duration: 0.2, ease: "easeOut" }}
                        className="w-full mt-3 bg-white p-4 rounded-lg border border-gray-200 shadow-sm"
                    >
                        <div className="flex flex-col space-y-3">
                            <textarea
                                name="message"
                                value={message}
                                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setMessage(e.target.value)}
                                placeholder={t.messagePlaceholder}
                                className="w-full p-3 border border-blue-200 rounded-lg resize-none h-28 focus:ring-2 focus:ring-primary/30 focus:border-primary transition duration-200 outline-none"
                                required
                                disabled={isSubmitting}
                            />
                            <div className="flex justify-end">
                                <button
                                    type="submit"
                                    className={`flex items-center justify-center w-full sm:w-auto bg-primary hover:bg-primary-dark text-white rounded-lg px-5 py-2.5 cursor-pointer text-sm font-medium transition duration-200 shadow-sm ${isSubmitting ? 'opacity-75 cursor-not-allowed' : ''}`}
                                    disabled={isSubmitting}
                                >
                                    {isSubmitting ? (
                                        <>
                                            <ButtonSpinner />
                                            <span className="ml-2">{t.sending}</span>
                                        </>
                                    ) : (
                                        t.sendMessage
                                    )}
                                </button>
                            </div>
                        </div>
                    </motion.form>
                )}
            </AnimatePresence>

            {error && !isSubmitting && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.2 }}
                    className='text-yellow-700 mt-3 bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-sm shadow-sm flex items-start gap-2'
                >
                    <div className="flex items-center gap-2">
                        <AlertTriangle className="h-5 w-5 text-yellow-500 flex-shrink-0" />
                        <span>{t.error}</span>
                    </div>
                </motion.div>
            )}

            {hasSubmitted && !isSubmitting && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.2 }}
                    className='text-primary mt-3 border-primary/30 border bg-primary/5 rounded-lg p-4 text-sm shadow-sm flex items-start gap-2'
                >
                    <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                        <span>{t.messageSent}</span>
                    </div>
                </motion.div>
            )}
        </div>
    );
};

export default MessageForm;
