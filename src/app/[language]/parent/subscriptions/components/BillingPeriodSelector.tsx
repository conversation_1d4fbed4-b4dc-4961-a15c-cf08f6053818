'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, CreditCard, Info } from 'lucide-react';
import PlanOption from './shared/PlanOption';
import { Language } from '@/i18n/types';
import { BillingPeriod } from '@/services/subscriptionService/subscriptionRequestSchema';

// Translations for billing period enforcement
const billingPeriodTranslations = {
  en: {
    enforcementNotice: "You already have a {billingPeriod} subscription for {yearName}. To add subjects to the same year, you must use the same billing period.",
    monthlyBilling: "monthly",
    yearlyBilling: "yearly"
  },
  fr: {
    enforcementNotice: "Vous avez déjà un abonnement {billingPeriod} pour {yearName}. Pour ajouter des matières à la même année, vous devez utiliser la même période de facturation.",
    monthlyBilling: "mensuel",
    yearlyBilling: "annuel"
  },
  de: {
    enforcementNotice: "Sie haben bereits ein {billingPeriod} Abonnement für {yearName}. Um Fächer zum gleichen Jahr hinzuzufügen, müssen Sie die gleiche Abrechnungsperiode verwenden.",
    monthlyBilling: "monatliches",
    yearlyBilling: "jährliches"
  },
  lu: {
    enforcementNotice: "Dir hutt schonn en {billingPeriod} Abonnement fir {yearName}. Fir Fächer bei d'selwecht Joer dobäi ze setzen, musst Dir déi selwecht Abrechnungsperiod benotzen.",
    monthlyBilling: "monatlechen",
    yearlyBilling: "jährlechen"
  }
};

interface BillingPeriodSelectorProps {
  language: Language;
  selectedPlan: BillingPeriod | null;
  handlePlanSelect: (plan: BillingPeriod) => void;
  currentPricing: {
    monthly: number;
    yearly: number;
    savings: {
      percentage: number;
      annualSavings: number;
    };
  };
  existingSubscriptionForYear?: {
    billingPeriod: BillingPeriod;
    yearName: string;
  } | null;
}

const BillingPeriodSelector: React.FC<BillingPeriodSelectorProps> = ({
  language,
  selectedPlan,
  handlePlanSelect,
  currentPricing,
  existingSubscriptionForYear
}) => {
  const t = billingPeriodTranslations[language] || billingPeriodTranslations.en;
  
  // Helper function to format price without unnecessary decimals
  const formatPrice = (price: number): string => {
    return price % 1 === 0 ? price.toString() : price.toFixed(2);
  };
  
  const monthlyPriceFormatted = `${formatPrice(currentPricing.monthly)}€`;
  const yearlyPriceFormatted = `${formatPrice(currentPricing.yearly)}€`;
  const savingsAmountFormatted = currentPricing.savings.annualSavings;

  // Determine which options should be disabled
  const monthlyDisabled = existingSubscriptionForYear?.billingPeriod === 'yearly';
  const yearlyDisabled = existingSubscriptionForYear?.billingPeriod === 'monthly';

  return (
    <div className="space-y-4">
      {/* Billing Period Enforcement Notice */}
      {existingSubscriptionForYear && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 bg-blue-50 border border-blue-200 rounded-lg"
        >
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <p className="text-sm text-blue-800">
              {t.enforcementNotice
                .replace('{billingPeriod}', existingSubscriptionForYear.billingPeriod === 'monthly' ? t.monthlyBilling : t.yearlyBilling)
                .replace('{yearName}', existingSubscriptionForYear.yearName)
              }
            </p>
          </div>
        </motion.div>
      )}

      {/* Plan Options */}
      <div className="grid md:grid-cols-2 gap-4">
        <PlanOption
          title="12 Months Access"
          price={yearlyPriceFormatted}
          originalPrice={`${formatPrice(currentPricing.monthly * 12)}€`}
          isSelected={selectedPlan === 'yearly'}
          onClick={() => handlePlanSelect('yearly')}
          icon={<Calendar className="h-5 w-5" />}
          discountPercentage={currentPricing.savings.percentage}
          savingsAmount={savingsAmountFormatted > 0
            ? Math.round(savingsAmountFormatted)
            : null
          }
          language={language}
          planType="yearly"
          disabled={yearlyDisabled}
        />
        <PlanOption
          title="Monthly Plan"
          price={monthlyPriceFormatted}
          originalPrice={null}
          isSelected={selectedPlan === 'monthly'}
          onClick={() => handlePlanSelect('monthly')}
          icon={<CreditCard className="h-5 w-5" />}
          discountPercentage={0}
          savingsAmount={null}
          language={language}
          planType="monthly"
          disabled={monthlyDisabled}
        />
      </div>
    </div>
  );
};

export default BillingPeriodSelector;