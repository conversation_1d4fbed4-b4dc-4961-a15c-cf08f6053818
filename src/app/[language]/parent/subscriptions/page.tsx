'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { createTranslations, Language } from '@/i18n/types';
import { getCommonTranslations } from '@/i18n/common';
import SubscriptionCard from './components/shared/Card';
import PlanComparison from './components/shared/PlanComparison/PlanComparison';
import SystemSelector from './components/SystemSelector';
import YearSelector from './components/YearSelector';
import SubjectSelector from './components/SubjectSelector';
import BillingPeriodSelector from './components/BillingPeriodSelector';
import SubscriptionStatusNotice from './components/SubscriptionStatusNotice';
import CheckoutSection from './components/checkout/CheckoutSection';
import MessageForm from './components/message/MessageForm';
import { Calendar, CreditCard, CheckCircle, ArrowLeft, Sparkles, Shield } from 'lucide-react';
import { Button } from '@/components/custom/Button';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSubscriptions } from './useSubscriptions';
import { Toaster } from '@/components/ui/toaster';
import { Loader2 } from 'lucide-react';
import BackgroundElements from '@/components/custom/BackgroundElements';
import { StatusDialog } from './components/dialogs/StatusDialog';
import { useAuthStore } from '@/zustand/authStore/authStore';
import { getCheckoutSessionStatus } from '@/services/subscriptionService/subscriptionService';
import { SystemOption, YearOption, SubjectOption } from './types';
import { motion } from 'framer-motion';

// Define translations
const translations = createTranslations({
  en: {
    title: "Because your child is worth it",
    subtitle: "Join 753+ parents supporting their children with LuxEdu",
    loading: "Loading subscription plans...",
    error: {
      title: "Oops! Something went wrong",
      goBack: "Go Back"
    },
    navigation: {
      backToSettings: "Back"
    },
    subscriptionBanner: {
      title: "You already have active subscriptions",
      subtitle: "Manage your existing subscriptions or add new ones below",
      buttonText: "Manage Subscriptions"
    },
    steps: {
      configureSub: "Configure your subscription",
      system: "1. School System",
      year: "2. School Year",
      subjects: "3. Subjects",
      chooseYourPlan: "Choose Your Plan",
      secureCheckout: "Secure checkout"
    },
    subjectsPlaceholder: {
      loading: "Loading subjects...",
      selectYear: "Please select a school year first"
    },
    plans: {
      yearly: {
        title: "Yearly Plan"
      },
      monthly: {
        title: "Monthly Plan"
      }
    }
  },
  fr: {
    title: "Parce que votre enfant le mérite",
    subtitle: "Rejoignez 753+ parents qui soutiennent leurs enfants avec LuxEdu",
    loading: "Chargement des abonnements...",
    error: {
      title: "Oups ! Quelque chose s'est mal passé",
      goBack: "Retour"
    },
    navigation: {
      backToSettings: "Retour"
    },
    subscriptionBanner: {
      title: "Vous avez déjà des abonnements actifs",
      subtitle: "Gérez vos abonnements existants ou ajoutez-en de nouveaux ci-dessous",
      buttonText: "Gérer les abonnements"
    },
    steps: {
      configureSub: "Configurez votre abonnement",
      system: "1. Système scolaire",
      year: "2. Année scolaire",
      subjects: "3. Matières",
      chooseYourPlan: "Choisissez votre forfait",
      secureCheckout: "Paiement sécurisé"
    },
    subjectsPlaceholder: {
      loading: "Chargement des matières...",
      selectYear: "Veuillez d'abord sélectionner une année scolaire"
    },
    plans: {
      yearly: {
        title: "Plan Annuel"
      },
      monthly: {
        title: "Plan Mensuel"
      }
    }
  },
  de: {
    title: "Weil ihr Kind es wert ist",
    subtitle: "Schließen Sie sich 753+ Eltern an, die ihre Kinder mit LuxEdu unterstützen",
    loading: "Abonnements werden geladen...",
    error: {
      title: "Hoppla! Etwas ist schiefgelaufen",
      goBack: "Zurück"
    },
    navigation: {
      backToSettings: "Zurück"
    },
    subscriptionBanner: {
      title: "Sie haben bereits aktive Abonnements",
      subtitle: "Verwalten Sie Ihre bestehenden Abonnements oder fügen Sie unten neue hinzu",
      buttonText: "Abonnements verwalten"
    },
    steps: {
      configureSub: "Konfigurieren Sie Ihr Abonnement",
      system: "1. Schulsystem",
      year: "2. Schuljahr",
      subjects: "3. Fächer",
      chooseYourPlan: "Wählen Sie Ihren Plan",
      secureCheckout: "Sicherer Checkout"
    },
    subjectsPlaceholder: {
      loading: "Fächer werden geladen...",
      selectYear: "Bitte wählen Sie zuerst ein Schuljahr"
    },
    plans: {
      yearly: {
        title: "Jahresplan"
      },
      monthly: {
        title: "Monatsplan"
      }
    }
  },
  lu: {
    title: "Well Äert Kand et wäert ass",
    subtitle: "Schléisst Iech den 753+ Elteren un déi hier Kanner mat LuxEdu ënnerstëtzen",
    loading: "Abonnementer gi gelueden...",
    error: {
      title: "Hoppla! Et ass eppes schief gelaf",
      goBack: "Zeréck"
    },
    navigation: {
      backToSettings: "Zeréck"
    },
    subscriptionBanner: {
      title: "Dir hutt schonn aktiv Abonnementer",
      subtitle: "Verwalt Är Abonnementer",
      buttonText: "Abonnementer verwalten"
    },
    steps: {
      configureSub: "Konfiguréiert Äert Abonnement",
      system: "1. Schoulsystem",
      year: "2. Schouljoer",
      subjects: "3. Fächer",
      chooseYourPlan: "Wielt Äre Plang",
      secureCheckout: "Sécheren Checkout"
    },
    subjectsPlaceholder: {
      loading: "Fächer gi gelueden...",
      selectYear: "Wielt w.e.g. fir d'éischt e Schouljoer aus"
    },
    plans: {
      yearly: {
        title: "Jährlechen Plan"
      },
      monthly: {
        title: "Monatlechen Plan"
      }
    }
  }
});

function SubscriptionsPage() {
    const language = useLanguage();
    const t = translations[language as Language];
    const commonActions = getCommonTranslations('actions', language);
    const router = useRouter();
    const searchParams = useSearchParams();
    const initAuth = useAuthStore(state => state.initAuth);

    const {
        loading,
        error: hookError,
        checkoutLoading,
        selectedPlan,
        selectedSystemValue,
        selectedYearValue,
        selectedSubjects,
        systemOptions,
        yearOptions,
        subjectOptions,
        currentPricing,
        pricingTiers,
        coupon,
        isFormEnabled,
        isCurrentSubscriptionActive,
        existingSubscriptionForYear,
        subscribedSubjectIds,
        allSelectedSubjectsSubscribed,
        newSubjectsToAdd,
        hasActiveSubscriptions,
        handlePlanSelect,
        handleSystemSelect,
        handleYearSelect,
        handleSubjectSelect,
        handleSelectAllSubjects,
        handleClearAllSubjects,
        setCoupon,
        handleVerifyDiscountCode,
        handleSubmit,
        clearError: clearHookError,
    } = useSubscriptions(language);

    // Check if user has active subscriptions from the currentSubscription data

    const [uiError, setUiError] = useState('');
    const [dialogState, setDialogState] = useState({ success: false, canceled: false });

    // Set default selections when data loads
    useEffect(() => {
        if (systemOptions.length > 0 && !selectedSystemValue) {
            handleSystemSelect(systemOptions[0].value);
        }
    }, [systemOptions, selectedSystemValue, handleSystemSelect]);

    useEffect(() => {
        if (yearOptions.length > 0 && !selectedYearValue && selectedSystemValue) {
            // Select the first year option (respecting backend order)
            handleYearSelect(yearOptions[0].value);
        }
    }, [yearOptions, selectedYearValue, selectedSystemValue, handleYearSelect]);

    // Auto-select single subject when only one is available
    useEffect(() => {
        if (subjectOptions.length === 1 && selectedSubjects.length === 0) {
            handleSubjectSelect(subjectOptions[0].publicId);
        }
    }, [subjectOptions, selectedSubjects.length, handleSubjectSelect]);

    // Set billing plan as default when subjects are selected (handled in useSubscriptions hook)

    // Use year options directly from the hook (preserving backend order)
    const sortedYearOptions = yearOptions;

    // Handle success/cancel from Stripe redirect
    useEffect(() => {
        const success = searchParams.get('success');
        const canceled = searchParams.get('canceled');
        const sessionId = searchParams.get('session_id');

        if (success === 'true' && sessionId) {
            const checkStatus = async () => {
                try {
                    const result = await getCheckoutSessionStatus(sessionId);
                    if (result.status === 'success') {
                        const status = result.data.status;
                        if (status === 'completed_active' || status === 'processing') {
                            setDialogState({ success: true, canceled: false });
                            initAuth();
                            router.replace(`/${language}/parent/subscriptions`);
                        } else if (status === 'failed' || status === 'completed_issue_contact_support') {
                            setUiError(result.data.message || 'There was an issue with your subscription. Please contact support.');
                        }
                    }
                } catch (error) {
                    console.error('Error checking checkout status:', error);
                }
            };

            checkStatus();
            const intervalId = setInterval(checkStatus, 2000);
            const timeoutId = setTimeout(() => clearInterval(intervalId), 20000);

            return () => {
                clearInterval(intervalId);
                clearTimeout(timeoutId);
            };
        } else if (success === 'true') {
            setDialogState({ success: true, canceled: false });
            initAuth();
        } else if (canceled === 'true') {
            setDialogState({ success: false, canceled: true });
        }
    }, [searchParams, initAuth, language, router]);

    // Computed values for pricing display
    const monthlyPriceFormatted = `${currentPricing.monthly.toFixed(2)}€`;
    const yearlyPriceFormatted = `${currentPricing.yearly.toFixed(2)}€`;
    const savingsAmountFormatted = currentPricing.savings.annualSavings;

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex justify-center items-center">
                <div className="text-center">
                    <Loader2 className="h-12 w-12 animate-spin text-indigo-500 mx-auto mb-4" />
                    <p className="text-gray-600 text-lg">{t.loading}</p>
                </div>
            </div>
        );
    }

    if (hookError) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex flex-col justify-center items-center p-4">
                <div className="text-center max-w-md">
                    <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <CheckCircle className="w-8 h-8 text-red-500" />
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">{t.error.title}</h2>
                    <p className="text-red-600 text-center mb-6">{hookError}</p>
                    <Button onClick={() => router.back()} className="bg-indigo-500 hover:bg-indigo-600">
                        {t.error.goBack}
                    </Button>
                </div>
            </div>
        );
    }

    return (
        <div className="w-[98%] mx-auto max-w-7xl">
            <Toaster />
            {/* Header section */}
            <Button
                variant="outline"
                size="sm"
                onClick={() => {
                    // Use router.back() to return to previous page instead of hardcoded navigation
                    // This handles both parent and student origin cases properly
                    router.back();
                }}
                className="mb-6 group bg-white/80 backdrop-blur-sm border-gray-200 hover:border-primary hover:bg-white shadow-md"
            >
                <ArrowLeft className="mr-2 h-4 w-4 transition-transform group-hover:-translate-x-0.5" />
                {t.navigation.backToSettings}
            </Button>
            
            {/* Manage Subscriptions Banner for Existing Customers */}
            {hasActiveSubscriptions && (
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="mb-6 p-6 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg border border-indigo-200 shadow-sm"
                >
                    <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                        <div className="flex items-center gap-3">
                            <div className="p-2 bg-indigo-100 rounded-full">
                                <Shield className="h-6 w-6 text-indigo-600" />
                            </div>
                            <div className="text-center sm:text-left">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    {t.subscriptionBanner.title}
                                </h3>
                                <p className="text-sm text-gray-600">
                                    {t.subscriptionBanner.subtitle}
                                </p>
                            </div>
                        </div>
                        <Button
                            onClick={() => router.push(`/${language}/parent/manage-subscriptions`)}
                            className="bg-indigo-600 hover:bg-indigo-700 text-white shadow-md transition-all hover:shadow-lg"
                        >
                            <CreditCard className="mr-2 h-4 w-4" />
                            {t.subscriptionBanner.buttonText}
                        </Button>
                    </div>
                </motion.div>
            )}
            <div className="max-w-4xl mx-auto p-6 bg-gradient-to-r from-primary/5 via-secondary/5 to-tertiary/5">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    className="text-center"
                >

                    <h1 className="text-3xl md:text-4xl font-black mb-3 leading-tight">
                        {t.title}
                    </h1>

                    <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed ">
                        {t.subtitle}
                    </p>


                </motion.div>
            </div>

            <div className="max-w-4xl mx-auto sm:px-4 pb-8 bg-white/60 pt-4 space-y-4">
                {/* Plan Comparison */}
                <SubscriptionCard className="mb-6">
                    <PlanComparison language={language} />
                    <div className="my-4"/>
                    {/* Message Form for Questions */}
                    <MessageForm language={language} />
                </SubscriptionCard>

                {/* Streamlined Selection Steps - All three substeps shown simultaneously */}
                <SubscriptionCard
                    title={t.steps.configureSub}
                    className="mb-6"
                >

                    <div className="space-y-8">
                        {/* Step 1: System Selection - Always visible */}
                        <div>
                            <h3 className="text-lg font-semibold text-primary mb-3">
                                {t.steps.system}
                            </h3>
                            <SystemSelector
                                language={language}
                                systemOptions={systemOptions}
                                selectedSystemValue={selectedSystemValue}
                                handleSystemSelect={handleSystemSelect}
                            />
                        </div>

                        {/* Step 2: Year Selection - Always visible */}
                        <div className={`transition-opacity duration-300 ${!selectedSystemValue ? 'opacity-50 pointer-events-none' : 'opacity-100'}`}>
                            <h3 className="text-lg font-semibold text-primary mb-3">
                                {t.steps.year}
                            </h3>
                            <YearSelector
                                language={language}
                                yearOptions={sortedYearOptions}
                                selectedYearValue={selectedYearValue}
                                handleYearSelect={handleYearSelect}
                            />
                        </div>

                        {/* Step 3: Subject Selection - Always visible */}
                        <div className={`transition-opacity duration-300 ${!selectedYearValue ? 'opacity-50 pointer-events-none' : 'opacity-100'}`}>
                            <h3 className="text-lg font-semibold text-primary mb-3">
                                {t.steps.subjects}
                            </h3>
                            {subjectOptions.length > 0 ? (
                                <>
                                    <SubjectSelector
                                        language={language}
                                        subjectOptions={subjectOptions}
                                        selectedSubjects={selectedSubjects}
                                        handleSubjectSelect={handleSubjectSelect}
                                        handleSelectAll={handleSelectAllSubjects}
                                        handleClearAll={handleClearAllSubjects}
                                        currentPricing={currentPricing}
                                        subscribedSubjectIds={subscribedSubjectIds}
                                        pricingTiers={pricingTiers}
                                    />
                                    {selectedSubjects.length > 0 && (
                                        <div className="mt-4">
                                            <SubscriptionStatusNotice
                                                language={language}
                                                allSelectedSubjectsSubscribed={allSelectedSubjectsSubscribed}
                                                subscribedSubjectIds={subscribedSubjectIds}
                                                selectedSubjects={selectedSubjects}
                                                newSubjectsToAdd={newSubjectsToAdd}
                                                yearName={existingSubscriptionForYear?.yearName}
                                                hasExistingSubscriptionForYear={Boolean(existingSubscriptionForYear)}
                                            />
                                        </div>
                                    )}
                                </>
                            ) : (
                                <div className="text-center py-8 text-gray-500">
                                    {selectedYearValue ? t.subjectsPlaceholder.loading : t.subjectsPlaceholder.selectYear}
                                </div>
                            )}
                        </div>
                    </div>
                </SubscriptionCard>

                {/* Plan Selection - Only show when there are new subjects to add */}
                {selectedSubjects.length > 0 && newSubjectsToAdd.length > 0 && (
                    <SubscriptionCard
                        title={t.steps.chooseYourPlan}
                        className="mb-6"
                    >
                        <BillingPeriodSelector
                            language={language}
                            selectedPlan={selectedPlan}
                            handlePlanSelect={handlePlanSelect}
                            currentPricing={currentPricing}
                            existingSubscriptionForYear={existingSubscriptionForYear}
                        />
                    </SubscriptionCard>
                )}

                {/* Checkout Section - Only show when plan is selected and there are new subjects */}
                {selectedSubjects.length > 0 && selectedPlan && newSubjectsToAdd.length > 0 && (
                    <SubscriptionCard title={t.steps.secureCheckout}>
                        <CheckoutSection
                            language={language}
                            coupon={coupon}
                            setCoupon={setCoupon}
                            handleVerifyDiscountCode={handleVerifyDiscountCode}
                            error={uiError || hookError}
                            clearError={() => { setUiError(''); clearHookError(); }}
                            isFormEnabled={isFormEnabled}
                            isCurrentSubscriptionActive={isCurrentSubscriptionActive}
                            checkoutLoading={checkoutLoading}
                            handleSubmit={handleSubmit}
                            selectedYearValue={selectedYearValue}
                            posthogCaptureEvent={undefined}
                        />
                    </SubscriptionCard>
                )}
            </div>

            {/* Dialogs for success/cancel */}
            <StatusDialog
                isOpen={dialogState.success}
                onClose={() => setDialogState(prev => ({ ...prev, success: false }))}
                type="success"
                language={language}
            />
            <StatusDialog
                isOpen={dialogState.canceled}
                onClose={() => setDialogState(prev => ({ ...prev, canceled: false }))}
                type="canceled"
                language={language}
            />
        </div>
    )
}

export default SubscriptionsPage;