"use client";

import VideoSection from '../components/VideoSection';
import ExerciseList from '../components/Exercises/ExerciseList';
import ContentTabs from '../../components/ContentTabs'; // Assuming ContentTabs is in this location
import LectureContent from '../components/LectureContent';
import { BookOpen, PenLine, Video } from 'lucide-react'; // Example icons
import { MathNode } from '@/types';
import { useLanguage } from '@/hooks/useLanguage';
import { createTranslations, Language } from '@/i18n/types';

const translations = createTranslations({
  en: {
    theory: "Notes",
    video: "Video",
    exercises: "Exercises",
    lectureNotes: "Notes",
    noExercises: "No exercises available.",
    noTheory: "No notes available.",
    noContent: "No content available."
  },
  fr: {
    theory: "Notes",
    video: "Vidéo",
    exercises: "Exercices",
    lectureNotes: "Notes",
    noExercises: "Pas d'exercices disponible.",
    noTheory: "Pas de notes disponible.",
    noContent: "Pas encore de contenu."
  },
  de: {
    theory: "Notizen",
    video: "Video",
    exercises: "Übungen",
    lectureNotes: "Notizen",
    noExercises: "Keine Übungen verfügbar.",
    noTheory: "Keine Notizen verfügbar.",
    noContent: "Noch kein Inhalt."
  },
  lu: {
    theory: "Notizen",
    video: "Video",
    exercises: "Übungen",
    lectureNotes: "Notizen",
    noExercises: "Keng Übungen verfügbar.",
    noTheory: "Keng Notizen verfügbar.",
    noContent: "Nach keen Inhalt."
  }
});

interface MathNodeRendererProps {
  learningNode: MathNode;
  isSubscribed: boolean;
  totalExercises: number;
  shownExercises: number;
}

function MathNodeRenderer({ learningNode, isSubscribed, totalExercises, shownExercises }: MathNodeRendererProps) {
  const language = useLanguage();
  const t = translations[language as Language] || translations.lu;

  const videoContent = learningNode.content.videoUrl ? (
    <VideoSection videoUrl={learningNode.content.videoUrl} isSubscribed={isSubscribed} />
  ) : null;

  const lectureContent = (
    <LectureContent
      content={learningNode.content.notes}
      title={t.lectureNotes}
      icon={<BookOpen className="w-5 h-5 text-blue-600" />}
      emptyMessage={t.noTheory}
      showReadingTime={true}
      structuredSections={true}
      collapsibleSections={true}
      isSubscribed={isSubscribed}
      contentOutline={learningNode.content.contentOutline || undefined}
    />
  );

  const exercises = learningNode.content.exercises;

  const exercisesContent = exercises && exercises.length > 0 ? (
    <div className="mt-2 space-y-4"> {/* Reduced margin top as tabs will have their own spacing */}
      <ExerciseList 
        exercises={exercises} 
        language={language}
        isSubscribed={isSubscribed}
        shownExercises={shownExercises}
        totalExercises={totalExercises}
      />
    </div>
  ) : (
    <div className="my-6 text-center text-gray-500">
      <p>{t.noExercises}</p>
    </div>
  );

  const tabs = [];

  // Add Theory/Lecture tab if we have notes
  if (learningNode.content.notes) {
    tabs.push({
      id: "theory",
      label: t.theory,
      icon: <BookOpen className="w-4 h-4" />,
      content: lectureContent,
    });
  }

  // Add Video tab if we have video content
  if (videoContent) {
    tabs.push({
      id: "video",
      label: t.video,
      icon: <Video className="w-4 h-4" />,
      content: videoContent,
    });
  }

  // Always add exercises tab, it will show "No exercises" message if empty
  tabs.push({
    id: "exercises",
    label: t.exercises,
    icon: <PenLine className="w-4 h-4" />,
    content: exercisesContent,
  });

  // If no video and no exercises, provide a fallback message or structure
  if (tabs.length === 0) {
    return (
      <div className="flex flex-col gap-4 w-full mx-auto max-w-4xl py-6 text-center text-gray-500">
        <p>{t.noContent}</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 w-full mx-auto max-w-4xl py-6">
      {/* Title and Description are handled by NavigationHeader and the page layout */}
      <ContentTabs tabs={tabs} />
    </div>
  );
}

export default MathNodeRenderer;