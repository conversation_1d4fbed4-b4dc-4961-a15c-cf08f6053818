"use client";

import { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { TrueFalseExercise as TrueFalseExerciseType, ExerciseSubmissionResponse } from '../exerciseTypes';
import SubmissionButton from '../components/SubmissionButton';
import { useExercises } from '../useExercises';
import { ExerciseSolution } from "../components/ExerciseSolution";
import SubmissionAnimation from '../components/SubmissionAnimation';
import SubmissionError from '../components/SubmissionError';
import CelebrationEffect from '../components/CelebrationEffect';
import IncorrectAnswerEffect from '../components/IncorrectAnswerEffect';
import { Language } from '@/i18n/types';
import { useLanguage } from '@/hooks/useLanguage';

const languageData = {
  en: {
    instructions: "Select True or False.",
    instructionsMobile: "Tap True or False.",
    trueLabel: "True",
    falseLabel: "False",
  },
  fr: {
    instructions: "Sélectionnez Vrai ou Faux.",
    instructionsMobile: "Appuyez sur Vrai ou Faux.",
    trueLabel: "Vrai",
    falseLabel: "Faux",
  },
  de: {
    instructions: "Wählen Sie Richtig oder Falsch.",
    instructionsMobile: "Tippen Sie auf Richtig oder Falsch.",
    trueLabel: "Richtig",
    falseLabel: "Falsch",
  },
  lu: {
    instructions: "Wielt Richteg oder Falsch.",
    instructionsMobile: "Tippt op Richteg oder Falsch.",
    trueLabel: "Richteg",
    falseLabel: "Falsch",
  }
};

interface TrueFalseExerciseProps {
  exercise: TrueFalseExerciseType;
  isSubmitting: boolean;
  isSubmitted: boolean;
  submissionResult: ExerciseSubmissionResponse | null;
  setIsSubmitting: (isSubmitting: boolean) => void;
  setIsSubmitted: (isSubmitted: boolean) => void;
  setSubmissionResult: (submissionResult: ExerciseSubmissionResponse | null) => void;
}

export const TrueFalseExercise = ({
  exercise,
  isSubmitting,
  isSubmitted,
  submissionResult,
  setIsSubmitting,
  setIsSubmitted,
  setSubmissionResult,
}: TrueFalseExerciseProps) => {
  const language = useLanguage();
  const translations = languageData[language as Language] || languageData.lu;
  const { submitExercise } = useExercises();
  
  const [selectedValue, setSelectedValue] = useState<boolean | null>(null);
  const [submissionError, setSubmissionError] = useState<boolean>(false);
  const [showCelebration, setShowCelebration] = useState<boolean>(false);
  const [showIncorrectEffect, setShowIncorrectEffect] = useState<boolean>(false);

  const handleOptionSelect = useCallback((value: boolean) => {
    if (isSubmitted) return;
    setSelectedValue(value);
  }, [isSubmitted]);

  // Determine if the answer is valid for submission
  const isAnswerValid = selectedValue !== null;
  const submissionEnabled = isAnswerValid && !isSubmitted;

  // Handle submission
  const handleSubmission = async () => {
    if (selectedValue === null) return;
    
    setIsSubmitting(true);
    setSubmissionError(false);
    
    try {
      const result = await submitExercise(exercise.publicId, selectedValue);
      setSubmissionResult(result);
      setIsSubmitted(true);
      
      if (result?.isCorrect) {
        setShowCelebration(true);
      } else {
        setShowIncorrectEffect(true);
      }
    } catch (error) {
      console.error("Submission failed:", error);
      setSubmissionError(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderOption = (value: boolean, label: string) => {
    const isSelected = selectedValue === value;
    let isCorrectOption = false;
    let wasIncorrectlySelected = false;

    if (isSubmitted && submissionResult) {
      // Handle the correct answer structure: { isTrue: boolean }
      const correctAnswerObj = submissionResult.correctAnswer as { isTrue: boolean };
      const correctAnswer = correctAnswerObj.isTrue;
      isCorrectOption = correctAnswer === value;
      wasIncorrectlySelected = isSelected && !isCorrectOption;
    }

    return (
      <motion.button
        key={value.toString()}
        onClick={() => handleOptionSelect(value)}
        disabled={isSubmitted}
        className={`relative p-2 rounded-xl border-2 transition-all duration-200 shadow-sm text-left group min-h-[60px] flex items-center justify-center
          ${isSubmitted
            ? isCorrectOption
              ? 'bg-green-50 border-green-400 text-green-900 cursor-default'
              : wasIncorrectlySelected
                ? 'bg-red-50 border-red-400 text-red-900 cursor-default'
                : 'bg-gray-50 border-gray-200 text-gray-600 cursor-default'
            : isSelected
              ? 'bg-blue-50 border-blue-400 text-blue-900 cursor-pointer hover:bg-blue-100'
              : 'bg-white border-gray-200 text-gray-800 cursor-pointer hover:bg-gray-50 hover:border-gray-300'
          }`}
        whileHover={!isSubmitted ? {
          scale: 1.02,
          y: -2,
          transition: { duration: 0.1, ease: "easeOut" }
        } : {}}
        whileTap={!isSubmitted ? {
          scale: 0.98,
          transition: { duration: 0.1 }
        } : {}}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: value ? 0.1 : 0 }}
      >
        <div className="flex items-center gap-3">
          {/* Radio button indicator */}
          <motion.div
            className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200
              ${isSubmitted
                ? isCorrectOption
                  ? 'border-green-500 bg-green-500'
                  : wasIncorrectlySelected
                    ? 'border-red-500 bg-red-500'
                    : 'border-gray-300 bg-white'
                : isSelected
                  ? 'border-blue-500 bg-blue-500'
                  : 'border-gray-300 bg-white group-hover:border-blue-400'
              }`}
            animate={isSelected || (isSubmitted && isCorrectOption) ? {
              scale: [1, 1.1, 1],
              transition: { duration: 0.3 }
            } : {}}
          >
            {(isSelected || (isSubmitted && isCorrectOption)) && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.2 }}
              >
                {isSubmitted && wasIncorrectlySelected ? (
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </motion.div>
            )}
          </motion.div>
          
          {/* Label */}
          <span className="text-lg font-semibold select-none">{label}</span>
        </div>

        {/* Celebratory sparkle effect for correct answers */}
        {isSubmitted && isCorrectOption && (
          <motion.div
            className="absolute inset-0 pointer-events-none"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-green-400 rounded-full"
                style={{
                  left: `${20 + i * 30}%`,
                  top: `${30 + i * 20}%`,
                }}
                initial={{ scale: 0, rotate: 0 }}
                animate={{ 
                  scale: [0, 1, 0],
                  rotate: 360,
                  opacity: [0, 1, 0]
                }}
                transition={{
                  duration: 1.5,
                  delay: i * 0.2,
                  ease: "easeOut"
                }}
              />
            ))}
          </motion.div>
        )}
      </motion.button>
    );
  };

  return (
    <div className="relative">
      {/* Animate between Exercise Content and Submission Animation */}
      <AnimatePresence mode="wait">
        {isSubmitting ? (
          <SubmissionAnimation key="submission-animation" isSubmitting={isSubmitting} />
        ) : (
          <motion.div
            key="exercise-content"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            {/* Instructions */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-4 p-3 bg-blue-50/50 border border-blue-100 rounded-lg"
            >
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-sm text-blue-700 font-medium">
                  <span className="hidden sm:inline">{translations.instructions}</span>
                  <span className="sm:hidden">{translations.instructionsMobile}</span>
                </span>
              </div>
            </motion.div>

            {/* True/False Options */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.1 }}
              className="grid grid-cols-1 sm:grid-cols-2 gap-2 max-w-lg mx-auto"
            >
              {renderOption(true, translations.trueLabel)}
              {renderOption(false, translations.falseLabel)}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Submission Area - Button */}
      {submissionError && <SubmissionError language={language} />}
      <div className="mt-6">
        <SubmissionButton
          isDisabled={!submissionEnabled}
          onSubmit={handleSubmission}
          isSubmitting={isSubmitting}
        />
      </div>

      {/* Solution/Feedback Area */}
      <AnimatePresence>
        {isSubmitted && !isSubmitting && submissionResult && (
          <motion.div
            key="solution-feedback"
            initial={{ opacity: 0, y: 15 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
            transition={{ delay: 0.1 }}
            className="mt-6"
          >
            <ExerciseSolution
              correctlyAnswered={submissionResult.isCorrect}
              correctAnswer={(submissionResult.correctAnswer as { isTrue: boolean }).isTrue ? translations.trueLabel : translations.falseLabel}
              videoUrl={submissionResult.videoUrl || undefined}
              solutionSteps={submissionResult.solutionSteps?.filter(step => step !== null) || undefined}
              language={language}
              recentlySubmitted={submissionResult.recentlySubmitted}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Celebration Effect */}
      <CelebrationEffect 
        trigger={showCelebration}
        onComplete={() => setShowCelebration(false)}
      />

      {/* Incorrect Answer Effect */}
      <IncorrectAnswerEffect 
        trigger={showIncorrectEffect}
        onComplete={() => setShowIncorrectEffect(false)}
      />
    </div>
  );
};

export default TrueFalseExercise;