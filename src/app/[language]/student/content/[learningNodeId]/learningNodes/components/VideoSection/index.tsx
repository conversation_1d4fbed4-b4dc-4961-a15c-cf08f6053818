'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { useAuthStore } from '@/zustand/authStore/authStore'
import { useLanguage } from '@/hooks/useLanguage'
import { createTranslations, Language } from '@/i18n/types'
import { RotateCcw, AlertTriangle, InfoIcon } from 'lucide-react'
import { useVideoSection } from '@/hooks/useVideoSection'
import SubscriptionPrompt from '@/components/custom/SubscriptionPrompt'
import VideoPlayer from './VideoPlayer'

const translations = createTranslations({
  en: {
    refreshDescription: 'Unable to load video',
    refreshButton: 'Try again',
    noVideo: 'No video available'
  },
  lu: {
    refreshDescription: 'Video konnt net geluden ginn',
    refreshButton: 'Nach eng Kéier probéieren',
    noVideo: 'Keen Video verfügbar'
  },
  de: {
    refreshDescription: 'Video konnte nicht geladen werden',
    refreshButton: 'Nochmal versuchen',
    noVideo: 'Kein Video verfügbar'
  },
  fr: {
    refreshDescription: 'La vidéo n\'a pas pu être chargée',
    refreshButton: 'Réessayer',
    noVideo: 'Aucune vidéo disponible'
  }
})

interface VideoSectionProps {
  videoUrl: string
  isSubscribed?: boolean
}

const VideoSection = ({ videoUrl, isSubscribed: isSubscribedProp }: VideoSectionProps) => {
  const language = useLanguage()
  const t = translations[language as Language]
  const [isVideoReady, setIsVideoReady] = useState(false)
  
  // Use prop if provided, otherwise fall back to auth store
  const authStoreIsSubscribed = useAuthStore((state) => state.isSubscribed)
  const isSubscribed = isSubscribedProp !== undefined ? isSubscribedProp : authStoreIsSubscribed
  
  // Log subscription status for debugging
  useEffect(() => {
    console.log('[VideoSection] Subscription status:', {
      authStoreIsSubscribed,
      isSubscribedProp,
      finalIsSubscribed: isSubscribed,
      videoUrl
    })
  }, [authStoreIsSubscribed, isSubscribedProp, isSubscribed, videoUrl])
  
  // Determine if we should show preview cutoff
  const shouldShowPreview = !isSubscribed

  const { 
    showPlayer, 
    onTimeUpdate, 
    onReady, 
    refreshKey, 
    shouldRefresh, 
    handleRefresh 
  } = useVideoSection(shouldShowPreview)

  const handleVideoReady = () => {
    setIsVideoReady(true)
    if (onReady) onReady()
  }

  // Reset ready state when refresh key changes
  useEffect(() => {
    setIsVideoReady(false)
  }, [refreshKey])

  // Check if subscription prompt should be shown
  const shouldShowSubscriptionPrompt = !showPlayer && !shouldRefresh

  return (
    <div className="w-full">
      <div className="aspect-video rounded-lg shadow-lg overflow-hidden bg-black">
          
          {/* Subscription prompt takes priority - show when needed */}
          {shouldShowSubscriptionPrompt && (
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-center max-w-md">
                <SubscriptionPrompt variant="inline" />
              </div>
            </div>
          )}
          
          {/* Only show other states if subscription prompt is not shown */}
          {!shouldShowSubscriptionPrompt && (
            <>
              {/* Refresh needed state - subtle message */}
              {shouldRefresh && showPlayer && (
                <div className="h-full flex items-center justify-center">
                  <div className="flex max-w-sm flex-col items-center justify-center gap-3 bg-blue-50/95 backdrop-blur-sm border border-blue-200 p-4 rounded-lg text-center mx-4">
                    <InfoIcon className="w-6 h-6 text-blue-500" />
                    <p className="text-blue-600 text-sm">{t.refreshDescription}</p>
                    <Button 
                      onClick={handleRefresh}
                      size="sm"
                    >
                      <RotateCcw className="w-3 h-3 mr-1.5" />
                      {t.refreshButton}
                    </Button>
                  </div>
                </div>
              )}
              
              {/* Video player with loading state */}
              {!shouldRefresh && showPlayer && videoUrl && (
                <div key={refreshKey} className="relative w-full h-full">
                  {/* Loading animation */}
                  {!isVideoReady && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/90 z-10">
                      <div className="flex flex-col items-center gap-3">
                        <div className="relative w-16 h-16">
                          <div className="absolute inset-0 border-4 border-blue-600/20 rounded-full"></div>
                          <div className="absolute inset-0 border-4 border-transparent border-t-blue-600 rounded-full animate-spin"></div>
                        </div>
                        <div className="flex gap-1">
                          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                        </div>
                      </div>
                    </div>
                  )}
                  <VideoPlayer
                    videoUrl={videoUrl}
                    onTimeUpdate={onTimeUpdate}
                    onReady={handleVideoReady}
                  />
                </div>
              )}
              
              {/* No video available */}
              {!shouldRefresh && showPlayer && !videoUrl && (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="flex max-w-md flex-col items-center justify-center gap-4 bg-blue-800/90 backdrop-blur-sm border border-blue-600 p-6 rounded-lg text-center mx-4">
                    <div className="w-16 h-16 bg-blue-700 rounded-full flex items-center justify-center">
                      <AlertTriangle className="w-8 h-8 text-blue-300" />
                    </div>
                    <p className="text-blue-200 font-medium">{t.noVideo}</p>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
    </div>
  )
}

export default VideoSection