"use client"

import React, { useState, use<PERSON><PERSON>back, memo } from "react"
import { Button } from "@/components/custom/Button"
// Card components are not directly used in the final tab structure, but kept if needed for sub-components
// import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Headphones, FileText, ChevronDown, Video, PenLine } from "lucide-react"
import AudioPlayer from "./AudioPlayer"
import ExerciseList from "../components/Exercises/ExerciseList"
import { ListeningNode } from "@/types"
import ContentTabs from "../../components/ContentTabs"
import VideoSection from '../components/VideoSection';
import { useLanguage } from '@/hooks/useLanguage';
import { createTranslations, Language } from '@/i18n/types';

const translations = createTranslations({
  en: {
    listen: "Listen",
    video: "Video",
    exercises: "Exercises",
    audioInstruction: "Listen to the audio carefully.",
    transcript: "View transcript",
    transcriptAvailable: "(available after listening)",
    show: "Show",
    hide: "Hide",
    noContent: "No content available."
  },
  fr: {
    listen: "Écouter",
    video: "Vidéo",
    exercises: "Exercices",
    audioInstruction: "Écoutez attentivement l'audio.",
    transcript: "Voir la transcription",
    transcriptAvailable: "(disponible après écoute)",
    show: "Afficher",
    hide: "Masquer",
    noContent: "Pas encore de contenu."
  },
  de: {
    listen: "Anhören",
    video: "Video",
    exercises: "Übungen",
    audioInstruction: "Hören Sie sich das Audio aufmerksam an.",
    transcript: "Transkript anzeigen",
    transcriptAvailable: "(nach dem Anhören verfügbar)",
    show: "Anzeigen",
    hide: "Ausblenden",
    noContent: "Noch kein Inhalt."
  },
  lu: {
    listen: "Nolauschteren",
    video: "Video",
    exercises: "Übungen",
    audioInstruction: "Lauschtert d'Audiodatei opmierksam.",
    transcript: "Transkript weisen",
    transcriptAvailable: "(no Lauschteren verfügbar)",
    show: "Weisen",
    hide: "Verstoppen",
    noContent: "Nach keen Inhalt."
  }
});

const AudioSection = ({ audioSrc, onComplete, t }: {
  audioSrc: string;
  onComplete: () => void;
  t: any;
}) => (
  <div className="bg-slate-50 p-5 rounded-lg shadow-sm border border-slate-200">
    <div className="mb-4">
      <p className="text-sm text-slate-700 font-medium flex items-center">
        <Headphones className="h-4 w-4 mr-2 text-indigo-600" />
        {t.audioInstruction}
      </p>
    </div>
    <AudioPlayer audioSrc={audioSrc} onComplete={onComplete} />
  </div>
);

const TranscriptToggle = ({ hasListened, showTranscript, onToggle, t }: {
  hasListened: boolean;
  showTranscript: boolean;
  onToggle: () => void;
  t: any;
}) => (
  <div className="mt-3 flex items-center justify-between">
    <div className="flex items-center gap-2">
      <FileText className="h-4 w-4 text-slate-500" />
      <span className="text-sm font-medium text-slate-600">
        {t.transcript} {!hasListened && t.transcriptAvailable}
      </span>
    </div>
    <Button
      variant="outline"
      size="sm"
      className={`flex items-center gap-1 h-8 px-3 border-slate-200 transition-all duration-200
                ${!hasListened ? 'opacity-60 cursor-not-allowed' : ''}
                ${showTranscript && hasListened ? 'bg-slate-100' : ''}`}
      onClick={onToggle}
      disabled={!hasListened}
    >
      <span className="text-xs">{showTranscript ? t.hide : t.show}</span>
      <div className="transition-transform duration-200" style={{ transform: showTranscript ? 'rotate(180deg)' : 'rotate(0deg)' }}>
        <ChevronDown className="h-3 w-3" />
      </div>
    </Button>
  </div>
);

const TranscriptContent = ({ transcript }: { transcript: string; }) => (
  <div className="overflow-hidden transition-all duration-300 ease-in-out mt-2">
    <div className="p-4 border border-slate-200 rounded-md bg-slate-50">
      <p className="whitespace-pre-line text-slate-600 text-sm">{transcript}</p>
    </div>
  </div>
);

const ListeningTabContent = memo(({
  audioSrc,
  transcript,
  hasListened,
  showTranscript,
  onComplete,
  onToggleTranscript,
  t
}: {
  audioSrc: string;
  transcript: string;
  hasListened: boolean;
  showTranscript: boolean;
  onComplete: () => void;
  onToggleTranscript: () => void;
  t: typeof translations["en"];
}) => {
  return (
    <div>
      <AudioSection audioSrc={audioSrc} onComplete={onComplete} t={t} />
      {transcript && ( // Only show transcript toggle if transcript exists
        <>
          <TranscriptToggle hasListened={hasListened} showTranscript={showTranscript} onToggle={onToggleTranscript} t={t} />
          {showTranscript && hasListened && <TranscriptContent transcript={transcript} />}
        </>
      )}
    </div>
  );
});
ListeningTabContent.displayName = 'ListeningTabContent';


interface ListeningNodeRendererProps {
  learningNode: ListeningNode;
  isSubscribed: boolean;
  totalExercises: number;
  shownExercises: number;
}

const ListeningNodeRenderer = ({ learningNode, isSubscribed, totalExercises, shownExercises }: ListeningNodeRendererProps) => {
  const language = useLanguage();
  const t = translations[language as Language] || translations.lu;
  const [hasListened, setHasListened] = useState(false);
  const [showTranscript, setShowTranscript] = useState(false);

  const audioSrc = learningNode.content.audioUrl || '';
  const transcript = learningNode.content.transcription || '';
  const exercises = learningNode.content.exercises || [];

  const handleAudioComplete = useCallback(() => {
    setHasListened(true);
  }, []);

  const handleTranscriptToggle = useCallback(() => {
    if (hasListened) {
      setShowTranscript(prev => !prev);
    }
  }, [hasListened]);

  const tabs = [];

  if (audioSrc) {
    tabs.push({
      id: "listening",
      label: t.listen,
      icon: <Headphones className="h-4 w-4" />,
      content: (
        <ListeningTabContent
          audioSrc={audioSrc}
          transcript={transcript}
          hasListened={hasListened}
          showTranscript={showTranscript}
          onComplete={handleAudioComplete}
          onToggleTranscript={handleTranscriptToggle}
          t={t}
        />
      )
    });
  }

  if (learningNode.content.videoUrl) {
    tabs.push({
      id: "video",
      label: t.video,
      icon: <Video className="h-4 w-4" />,
      content: <VideoSection videoUrl={learningNode.content.videoUrl} isSubscribed={isSubscribed} />
    });
  }

  if (exercises.length > 0) {
    tabs.push({
      id: "exercises",
      label: t.exercises,
      icon: <PenLine className="h-4 w-4" />,
      content: (
        <div className="space-y-4">
          <ExerciseList 
            exercises={exercises} 
            language={language}
            isSubscribed={isSubscribed}
            shownExercises={shownExercises}
            totalExercises={totalExercises}
          />
        </div>
      )
    });
  }
  
  if (tabs.length === 0) {
     return (
        <div className="flex flex-col gap-4 w-full mx-auto max-w-4xl py-6 text-center text-gray-500">
            <p>{t.noContent}</p>
        </div>
    );
  }

  return (
    <div className="w-full max-w-3xl mx-auto py-6">
      <ContentTabs tabs={tabs} />
    </div>
  );
};

export default ListeningNodeRenderer;