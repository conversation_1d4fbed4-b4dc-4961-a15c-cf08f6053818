"use client"

import { useState } from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Button } from "@/components/custom/Button"
import { Plus, Minus, Book, PencilLine, Video } from "lucide-react"
import InteractiveText from "./InteractiveText"
import WordDetails from "./WordDetails"
import LectureContent from '../components/LectureContent'
import { ReadingExplanation, ReadingNode } from "@/types"
import ContentTabs from "../../components/ContentTabs"
import VideoSection from '../components/VideoSection';
import ExerciseList from '../components/Exercises/ExerciseList';
import { useLanguage } from '@/hooks/useLanguage';
import { createTranslations, Language } from '@/i18n/types';

const translations = createTranslations({
  en: {
    readingText: "Reading Text",
    theory: "Notes",
    video: "Video",
    exercises: "Exercises",
    textSize: "Text Size:",
    decreaseTextSize: "Decrease text size",
    increaseTextSize: "Increase text size",
    help: "Click on highlighted words to see translations and explanations.",
    readingNotes: "Notes",
    noTheory: "No notes available.",
    noExercises: "No exercises available.",
    noContent: "No content available."
  },
  fr: {
    readingText: "Texte à lire",
    theory: "Notes",
    video: "Vidéo",
    exercises: "Exercices",
    textSize: "Taille du texte :",
    decreaseTextSize: "Diminuer la taille du texte",
    increaseTextSize: "Augmenter la taille du texte",
    help: "Cliquez sur les mots surlignés pour voir les traductions et explications.",
    readingNotes: "Notes",
    noTheory: "Pas de notes disponible.",
    noExercises: "Pas d'exercices disponible.",
    noContent: "Pas encore de contenu."
  },
  de: {
    readingText: "Lesetext",
    theory: "Notizen",
    video: "Video",
    exercises: "Übungen",
    textSize: "Textgröße:",
    decreaseTextSize: "Textgröße verringern",
    increaseTextSize: "Textgröße erhöhen",
    help: "Klicken Sie auf hervorgehobene Wörter, um Übersetzungen und Erklärungen zu sehen.",
    readingNotes: "Notizen",
    noTheory: "Keine Notizen verfügbar.",
    noExercises: "Keine Übungen verfügbar.",
    noContent: "Noch kein Inhalt."
  },
  lu: {
    readingText: "Liestext",
    theory: "Notizen",
    video: "Video",
    exercises: "Übungen",
    textSize: "Textgréisst:",
    decreaseTextSize: "Textgréisst verkleineren",
    increaseTextSize: "Textgréisst vergréisseren",
    help: "Klickt op markéiert Wierder fir Iwwersetzungen an Erklärungen ze gesinn.",
    readingNotes: "Notizen",
    noTheory: "Keng Notizen verfügbar.",
    noExercises: "Keng Übungen verfügbar.",
    noContent: "Nach keen Inhalt."
  }
});

interface ReadingNodeRendererProps {
  learningNode: ReadingNode;
  isSubscribed: boolean;
  totalExercises: number;
  shownExercises: number;
}

function ReadingNodeRenderer({
  learningNode,
  isSubscribed,
  totalExercises,
  shownExercises
}: ReadingNodeRendererProps) {
  const language = useLanguage();
  const t = translations[language as Language] || translations.lu;
  const [selectedWord, setSelectedWord] = useState<ReadingExplanation | null>(null)
  const [fontSize, setFontSize] = useState<number>(18)

  const textContent = {
    title: learningNode.title,
    content: learningNode.content.markdownText,
    explanations: learningNode.content.explanations
  };

  const handleWordClick = (word: string) => {
    const cleanWord = word.toLowerCase().replace(/[.,!?;:'"()]/g, "")
    const explanation = textContent.explanations?.find(
      (exp: ReadingExplanation) => exp.matchTerm.toLowerCase() === cleanWord
    )
    if (explanation) {
      setSelectedWord(explanation)
    }
  }

  const closeWordDetails = () => {
    setSelectedWord(null)
  }

  const increaseFontSize = () => {
    setFontSize(prev => Math.min(prev + 2, 32))
  }

  const decreaseFontSize = () => {
    setFontSize(prev => Math.max(prev - 2, 12))
  }

  const vocabularyTerms = textContent.explanations?.map((exp: ReadingExplanation) => exp.matchTerm.toLowerCase())

  const ReadingTabContent = () => (
    <Card>
      <CardContent className="pt-6">
        <div className="flex justify-end items-center gap-2 mb-4">
          <span className="text-sm text-muted-foreground mr-2">{t.textSize}</span>
          <Button
            variant="outline"
            size="icon"
            onClick={decreaseFontSize}
            disabled={fontSize <= 12}
            aria-label={t.decreaseTextSize}
          >
            <Minus className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={increaseFontSize}
            disabled={fontSize >= 32}
            aria-label={t.increaseTextSize}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        <div className="mb-4">
          <InteractiveText
            text={textContent.content ?? ""}
            onWordClick={handleWordClick}
            vocabulary={vocabularyTerms ?? []}
            fontSize={fontSize}
          />
        </div>
        <div className="text-sm text-muted-foreground mt-4">
          <p>{t.help}</p>
        </div>
      </CardContent>
    </Card>
  );

  // Theory content using LectureContent component for notes
  const TheoryTabContent = () => (
    <LectureContent
      content={learningNode.content.notes}
      title={t.readingNotes}
      icon={<Book className="w-5 h-5 text-blue-600" />}
      emptyMessage={t.noTheory}
      showReadingTime={true}
      structuredSections={true}
      collapsibleSections={true}
      isSubscribed={isSubscribed}
      contentOutline={learningNode.content.contentOutline || undefined}
    />
  );

  const VideoTabContent = learningNode.content.videoUrl ? (
    <VideoSection videoUrl={learningNode.content.videoUrl} isSubscribed={isSubscribed} />
  ) : null;

  const ExercisesTabContent = learningNode.content.exercises && learningNode.content.exercises.length > 0 ? (
    <Card>
      <CardContent className="pt-6 space-y-4">
        <ExerciseList 
          exercises={learningNode.content.exercises ?? []} 
          language={language}
          isSubscribed={isSubscribed}
          shownExercises={shownExercises}
          totalExercises={totalExercises}
        />
      </CardContent>
    </Card>
  ) : (
    <div className="my-6 text-center text-gray-500">
      <p>{t.noExercises}</p>
    </div>
  );


  const tabs = [
    {
      id: "reading",
      icon: <Book className="w-3.5 h-3.5" />,
      label: t.readingText,
      content: <ReadingTabContent />
    }
  ];

  // Add theory/notes tab if we have notes
  if (learningNode.content.notes) {
    tabs.push({
      id: "theory",
      icon: <Book className="w-3.5 h-3.5" />,
      label: t.theory,
      content: <TheoryTabContent />
    });
  }

  if (VideoTabContent) {
    tabs.push({
      id: "video",
      icon: <Video className="w-3.5 h-3.5" />,
      label: t.video,
      content: VideoTabContent
    });
  }

  // Always add exercises tab, it will show "No exercises" message if empty
  tabs.push({
    id: "exercises",
    icon: <PencilLine className="w-3.5 h-3.5" />,
    label: t.exercises,
    content: ExercisesTabContent
  });
  
  if (tabs.length === 0) {
     return (
        <div className="flex flex-col gap-4 w-full mx-auto max-w-4xl py-6 text-center text-gray-500">
            <p>{t.noContent}</p>
        </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-4">
      <ContentTabs tabs={tabs} />
      {selectedWord && <WordDetails wordData={selectedWord} onClose={closeWordDetails} language="french" />}
    </div >
  )
}

export default ReadingNodeRenderer