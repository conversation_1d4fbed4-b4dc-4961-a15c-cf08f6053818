// src/app/[language]/(shared)/auth/login/hooks/useChildLogin.ts
import { useState, useEffect, useCallback } from 'react';
import { useSearchParams, useRouter, useParams } from 'next/navigation';
import { useAuthStore } from '@/zustand/authStore/authStore';
import * as authService from '@/services/authService/authService';
import { Language } from '@/types';
import { validateEmail } from '@/utils/validateEmail';
import { AppErrorCodeEnum } from '@/services/appErrorSchema';
// Import specific request/response data types if needed for explicit typing,
// though makeSafeApiCall handles the ServiceResult<TData> structure.
// For example:
// import {
//   CheckChildVerificationStatusRequest, ChildVerificationStatusData,
//   VerifyChildAccountRequest, VerifyChildAccountData,
//   ResendChildVerificationCodeRequest, ResendChildVerificationCodeData,
//   RequestChildPinResetRequest, RequestChildPinResetData
// } from '@/services/authService/authSchema';

// Basic i18n (consolidated from old files)
const translations = {
  en: {
    title: "Student Login",
    emailLabel: "Your email address",
    emailPlaceholder: "Your email address",
    pinLabel: "PIN",
    pinPlaceholder: "Enter 4-digit PIN",
    verifyTitle: "Verify Your Account",
    verifyDesc: "Verification code sent to",
    verifyCodeLabel: "Verification Code",
    verifyBtn: "Verify",
    resendBtn: "Resend Code",
    resendWait: "Resend code in {time} minutes",
    forgotPinTitle: "Forgot PIN?",
    forgotPinDesc: "Enter your email to receive your PIN.",
    sendPinBtn: "Send PIN",
    pinSentSuccess: "Your PIN has been sent to your email!",
    backBtn: "Back",
    nextBtn: "Next",
    submitBtn: "Login",
    noAccount: "No account yet?",
    registerNow: "Register now",
    errorEmailNotFound: "Email not found. Are you registered?",
    errorInvalidEmail: "Please enter a valid email address.",
    errorInvalidPin: "Incorrect PIN.",
    errorAccountLocked: "Account locked. Try again in {time} minutes.",
    errorVerificationCode: "Invalid verification code.",
    errorTooManyRequests: "Too many requests. Try again later.",
    errorGeneric: "An error occurred. Please try again.",
    pinResetError: "Could not send PIN.",
    verificationSuccess: "Email verified successfully!",
    loginSuccess: "Login successful!",
    redirecting: "Redirecting...",
    verificationError: "Could not verify email.",
    resendSuccess: "Code resent!",
    resendError: "Could not resend code.",
    forgotPinLink: "Forgot PIN?",
    forgotPinAction: "Click here",
    tryLoginWithNewPin: "Please try logging in with your PIN now",
    alreadyVerified: "Your account is already verified",
    verificationNoAccount: "No student account found",
    verificationNoAccountDescription: "We couldn't find a student account with this email address. Please check the email address or create a new account.",
  },
  lu: {
    title: "Schüler-Login",
    emailLabel: "Deng E-Mail-Adress",
    emailPlaceholder: "Deng E-Mail-Adress",
    pinLabel: "PIN",
    pinPlaceholder: "Gitt de 4-zifferege PIN an",
    verifyTitle: "Verifizéier däi Kont",
    verifyDesc: "Verifizéierungscode geschéckt un",
    verifyCodeLabel: "Verifizéierungscode",
    verifyBtn: "Verifizéieren",
    resendBtn: "Code nei schécken",
    resendWait: "Code nei schécken an {time} Minutten",
    forgotPinTitle: "PIN vergiess?",
    forgotPinDesc: "Gitt deng Email an fir däi PIN ze kréien.",
    sendPinBtn: "PIN schécken",
    pinSentSuccess: "PIN un deng Email geschéckt!",
    backBtn: "Zeréck",
    nextBtn: "Weider",
    submitBtn: "Login",
    noAccount: "Nach keen Kont?",
    registerNow: "Elo registréieren",
    errorEmailNotFound: "E-Mail net fonnt. Bass du registréiert?",
    errorInvalidEmail: "Gitt w.e.g. eng valabel E-Mail Adress an.",
    errorInvalidPin: "Falschen PIN.",
    errorAccountLocked: "Kont gespaart. Probéiert an {time} Minutten erëm.",
    errorVerificationCode: "Ongültege Verifizéierungscode.",
    errorTooManyRequests: "Ze vill Ufroen. Probéiert méi spéit erëm.",
    errorGeneric: "E Feeler ass opgetrueden. Probéiert w.e.g. erëm.",
    pinResetError: "Konnt PIN net schécken.",
    verificationSuccess: "Email erfollegräich verifizéiert!",
    loginSuccess: "Login erfollegräich!",
    redirecting: "Weiderleeden...",
    verificationError: "Konnt Email net verifizéieren.",
    resendSuccess: "Code erëm geschéckt!",
    resendError: "Konnt Code net erëm schécken.",
    forgotPinLink: "PIN vergiess?",
    forgotPinAction: "Klick hei",
    tryLoginWithNewPin: "Probéier dech elo mat denger PIN unzemellen",
    alreadyVerified: "Däi Konto ass schonn verifizéiert",
    verificationNoAccount: "Keen Schüler-Kont fonnt",
    verificationNoAccountDescription: "Mir konnten keen Schüler-Kont mat dëser E-Mail-Adress fannen. Kuck w.e.g. d'E-Mail-Adress no oder erstellt een néien Kont.",
  },
  de: {
    title: "Schüler-Login",
    emailLabel: "Deine E-Mail-Adresse",
    emailPlaceholder: "Deine E-Mail-Adresse",
    pinLabel: "PIN",
    pinPlaceholder: "Gib den 4-stelligen PIN ein",
    verifyTitle: "Verifiziere deinen Account",
    verifyDesc: "Verifizierungscode gesendet an",
    verifyCodeLabel: "Verifizierungscode",
    verifyBtn: "Verifizieren",
    resendBtn: "Code erneut senden",
    resendWait: "Code erneut senden in {time} Minuten",
    forgotPinTitle: "PIN vergessen?",
    forgotPinDesc: "Gib deine E-Mail ein, um deinen PIN zu erhalten.",
    sendPinBtn: "PIN senden",
    pinSentSuccess: "PIN an deine E-Mail gesendet!",
    backBtn: "Zurück",
    nextBtn: "Weiter",
    submitBtn: "Login",
    noAccount: "Noch kein Konto?",
    registerNow: "Jetzt registrieren",
    errorEmailNotFound: "E-Mail nicht gefunden. Bist du registriert?",
    errorInvalidEmail: "Bitte gib eine gültige E-Mail-Adresse ein.",
    errorInvalidPin: "Falscher PIN.",
    errorAccountLocked: "Konto gesperrt. Versuche es in {time} Minuten erneut.",
    errorVerificationCode: "Ungültiger Verifizierungscode.",
    errorTooManyRequests: "Zu viele Anfragen. Versuche es später erneut.",
    errorGeneric: "Ein Fehler ist aufgetreten. Bitte versuche es erneut.",
    pinResetError: "PIN konnte nicht gesendet werden.",
    verificationSuccess: "Email erfolgreich verifiziert!",
    loginSuccess: "Anmeldung erfolgreich!",
    redirecting: "Weiterleitung...",
    verificationError: "Email konnte nicht verifiziert werden.",
    resendSuccess: "Code erneut gesendet!",
    resendError: "Code konnte nicht erneut gesendet werden.",
    forgotPinLink: "PIN vergessen?",
    forgotPinAction: "Hier klicken",
    tryLoginWithNewPin: "Bitte versuche dich jetzt mit deiner PIN anzumelden",
    alreadyVerified: "Dein Account ist bereits verifiziert",
    verificationNoAccount: "Kein Schüler-Konto gefunden",
    verificationNoAccountDescription: "Wir konnten kein Schüler-Konto mit dieser E-Mail-Adresse finden. Bitte überprüfe die E-Mail-Adresse oder erstelle ein neues Konto.",
  },
  fr: {
    title: "Connexion étudiant",
    emailLabel: "Ton adresse e-mail",
    emailPlaceholder: "Ton adresse e-mail",
    pinLabel: "PIN",
    pinPlaceholder: "Entrez le PIN à 4 chiffres",
    verifyTitle: "Vérifie ton compte",
    verifyDesc: "Code de vérification envoyé à",
    verifyCodeLabel: "Code de vérification",
    verifyBtn: "Vérifier",
    resendBtn: "Renvoyer le code",
    resendWait: "Renvoyer le code dans {time} minutes",
    forgotPinTitle: "PIN oublié?",
    forgotPinDesc: "Entre ton email pour recevoir ton PIN.",
    sendPinBtn: "Envoyer le PIN",
    pinSentSuccess: "PIN envoyé à ton email!",
    backBtn: "Retour",
    nextBtn: "Suivant",
    submitBtn: "Connexion",
    noAccount: "Pas encore de compte?",
    registerNow: "S'inscrire maintenant",
    errorEmailNotFound: "Email non trouvé. Es-tu inscrit?",
    errorInvalidEmail: "Veuillez entrer une adresse e-mail valide.",
    errorInvalidPin: "PIN incorrect.",
    errorAccountLocked: "Compte verrouillé. Réessaie dans {time} minutes.",
    errorVerificationCode: "Code de vérification invalide.",
    errorTooManyRequests: "Trop de demandes. Réessaie plus tard.",
    errorGeneric: "Une erreur s'est produite. Veuillez réessayer.",
    pinResetError: "Impossible d'envoyer le PIN.",
    verificationSuccess: "Email vérifié avec succès!",
    loginSuccess: "Connexion réussie!",
    redirecting: "Redirection...",
    verificationError: "Impossible de vérifier l'email.",
    resendSuccess: "Code renvoyé!",
    resendError: "Impossible de renvoyer le code.",
    forgotPinLink: "PIN oublié?",
    forgotPinAction: "Clique ici",
    tryLoginWithNewPin: "Essaie maintenant de te connecter avec ton PIN",
    alreadyVerified: "Ton compte est déjà vérifié",
    verificationNoAccount: "Aucun compte étudiant trouvé",
    verificationNoAccountDescription: "Nous n'avons pas trouvé de compte étudiant avec cette adresse e-mail. Veuillez vérifier l'adresse e-mail ou créer un nouveau compte.",
  },
};

export type ChildLoginStep = 'email' | 'pin' | 'verification' | 'forgotPin';

interface UseChildLoginProps {
  onBack: () => void;
}

export function useChildLogin({ onBack }: UseChildLoginProps) {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const { loginChild, isInitializingAuth } = useAuthStore();
  const language = params.language as Language || 'lu';
  const text = translations[language];

  const [step, setStep] = useState<ChildLoginStep>('email');
  const [email, setEmail] = useState('');
  const [pin, setPin] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [emailValidationError, setEmailValidationError] = useState<string | null>(null);
  const [accountLockedTime, setAccountLockedTime] = useState<number>(0);
  const [resendCountdown, setResendCountdown] = useState(0);
  const [isStepLoading, setIsStepLoading] = useState(false);

  const isLoading = isInitializingAuth || isStepLoading;

  // Initialize email and step from URL params
  useEffect(() => {
    const initialEmail = searchParams.get('child_email') || '';
    const vCode = searchParams.get('child_verification_code');
    setEmail(initialEmail);
    if (vCode && initialEmail) {
      setVerificationCode(vCode);
      setStep('verification');
    } else if (initialEmail) {
      setStep('email');
    }
  }, [searchParams]);

  // Resend Timer
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    if (resendCountdown > 0) {
      interval = setInterval(() => {
        setResendCountdown((prev) => prev - 1);
      }, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [resendCountdown]);

  const clearErrors = useCallback(() => {
    setError(null);
    setSuccess(null);
    setEmailValidationError(null);
    setAccountLockedTime(0);
  }, []);

  const handleEmailChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newEmail = e.target.value;
    setEmail(newEmail);
    clearErrors();
    if (!validateEmail(newEmail) && newEmail.length > 0) {
      setEmailValidationError(text.errorInvalidEmail);
    } else {
      setEmailValidationError(null);
    }
  }, [clearErrors, text.errorInvalidEmail]);

  const handleSubmitPin = useCallback(async (currentPin: string) => {
    clearErrors();
    if (currentPin.length !== 4) return;
    // No need for setIsStepLoading(true) here, useAuthStore handles it
    try {
      const result = await loginChild(email, currentPin);
      console.log('[useChildLogin] Login result:', result);
      if (result.status === 'success') {
        setSuccess(text.loginSuccess);
        
        // Check for redirect parameter in URL (though less common for child login)
        const redirectParam = searchParams.get('redirect');
        const redirectUrl = redirectParam ? decodeURIComponent(redirectParam) : `/${language}/student`;
        
        console.log('[useChildLogin] Login successful, redirecting to:', redirectUrl);
        // Ensure state is fully updated before redirecting
        // Use window.location for more reliable redirect
        setTimeout(() => {
          window.location.href = redirectUrl;
        }, 100);
      } else {
        // Handle error response from loginChild
        console.error("Login failed with error response:", result);
        // Extract error details from the result object (now contains full error response)
        const errorResult = result as any;
        const errorCode = errorResult?.error_code;
        const errorMessage = errorResult?.message;
        const retryAfterMinutes = errorResult?.details?.retryAfterMinutes;
        
        // Handle specific error codes from the result
        switch (errorCode) {
          case AppErrorCodeEnum.enum.INVALID_CREDENTIALS:
            setError(text.errorInvalidPin);
            // Don't clear the PIN - let user see and correct it
            break;
          case AppErrorCodeEnum.enum.ACCOUNT_LOCKED:
            setError(text.errorAccountLocked.replace('{time}', retryAfterMinutes || 'some'));
            setAccountLockedTime(retryAfterMinutes || 5);
            // Don't clear the PIN for account locked either
            break;
          case AppErrorCodeEnum.enum.CHILD_ACCOUNT_NOT_FOUND:
          case AppErrorCodeEnum.enum.USER_NOT_FOUND:
            setError(text.errorEmailNotFound);
            break;
          case AppErrorCodeEnum.enum.ACCOUNT_NOT_VERIFIED:
          case AppErrorCodeEnum.enum.EMAIL_NOT_VERIFIED:
            setError(text.errorGeneric); // Could add specific message for unverified account
            break;
          case AppErrorCodeEnum.enum.TOO_MANY_REQUESTS:
            setError(text.errorTooManyRequests);
            break;
          default:
            // Use the specific error message if available, otherwise fallback to generic
            setError(errorMessage || text.errorGeneric);
            break;
        }
      }
    } catch (e: any) {
      console.error("Login error:", e);
      
      // Extract error details from multiple possible locations
      // Check both direct error properties and nested data properties
      const errorCode = e?.error_code || e?.data?.error_code || e?.code;
      const errorMessage = e?.message || e?.data?.message;
      const retryAfterMinutes = e?.data?.retryAfterMinutes || e?.retryAfterMinutes;
      
      // Handle specific error codes
      switch (errorCode) {
        case AppErrorCodeEnum.enum.INVALID_CREDENTIALS:
          setError(text.errorInvalidPin);
          // Don't clear the PIN - let user see and correct it
          break;
        case AppErrorCodeEnum.enum.ACCOUNT_LOCKED:
          setError(text.errorAccountLocked.replace('{time}', retryAfterMinutes || 'some'));
          setAccountLockedTime(retryAfterMinutes || 5);
          // Don't clear the PIN for account locked either
          break;
        case AppErrorCodeEnum.enum.CHILD_ACCOUNT_NOT_FOUND:
        case AppErrorCodeEnum.enum.USER_NOT_FOUND:
          setError(text.errorEmailNotFound);
          break;
        case AppErrorCodeEnum.enum.ACCOUNT_NOT_VERIFIED:
        case AppErrorCodeEnum.enum.EMAIL_NOT_VERIFIED:
          setError(text.errorGeneric); // Could add specific message for unverified account
          break;
        case AppErrorCodeEnum.enum.TOO_MANY_REQUESTS:
          setError(text.errorTooManyRequests);
          break;
        default:
          // Use the specific error message if available, otherwise fallback to generic
          setError(errorMessage || text.errorGeneric);
          break;
      }
    }
  }, [clearErrors, email, language, loginChild, text, searchParams]);

  const handlePinChange = useCallback((value: string) => {
    setPin(value);
    // Only clear errors when submitting, not on every keystroke
    if (value.length === 4) {
      clearErrors(); // Clear errors before new submission
      handleSubmitPin(value); // Auto-submit on complete
    }
  }, [clearErrors, handleSubmitPin]);

  const handleVerifySubmit = useCallback(async (code: string) => {
    clearErrors();
    if (code.length !== 6) return;
    setIsStepLoading(true);
    try {
      const result = await authService.verifyChildAccount({ email, verificationCode: code });
      if (result.status === 'success') {
        setSuccess(text.verificationSuccess);
        // Clear verification code from URL
        const newParams = new URLSearchParams(searchParams.toString());
        newParams.delete('child_verification_code');
        router.replace(`/${language}/auth/login?${newParams.toString()}`);
        setStep('pin'); // Proceed to PIN entry after successful verification
      } else {
        if (result.error_code === AppErrorCodeEnum.enum.INVALID_VERIFICATION_CODE) {
          setError(text.errorVerificationCode);
        } else if (result.error_code === AppErrorCodeEnum.enum.ACCOUNT_ALREADY_VERIFIED) {
          setSuccess(text.verificationSuccess); // Treat as success
          setStep('pin');
        } else {
          setError(text.errorGeneric);
        }
        setVerificationCode('');
      }
    } catch (e) {
      setError(text.errorGeneric);
      setVerificationCode('');
    } finally {
      setIsStepLoading(false);
    }
  }, [clearErrors, email, language, router, searchParams, text]);

  const handleVerificationCodeChange = useCallback((value: string) => {
    setVerificationCode(value);
    clearErrors();
    if (value.length === 6) {
      handleVerifySubmit(value); // Auto-submit on complete
    }
  }, [clearErrors, handleVerifySubmit]);

  const handleResendCode = useCallback(async (showSuccess = true) => {
    clearErrors();
    if (resendCountdown > 0) return;
    setIsStepLoading(true);
    try {
      const result = await authService.resendChildVerificationCode({ email, language });
      if (result.status === 'success') {
        if (showSuccess) setSuccess(text.resendSuccess);
        setResendCountdown(30);
      } else {
        setError(text.resendError);
      }
    } catch (e) {
      setError(text.resendError);
    } finally {
      setIsStepLoading(false);
    }
  }, [clearErrors, email, language, resendCountdown, text]);

  const handleNextFromEmail = useCallback(async () => {
    clearErrors();
    if (!validateEmail(email)) {
      setEmailValidationError(text.errorInvalidEmail);
      return;
    }
    setIsStepLoading(true);
    try {
      const status = await authService.checkChildVerificationStatus({ email, language });
      if (status.status === 'error') {
        if (status.error_code === AppErrorCodeEnum.enum.CHILD_ACCOUNT_NOT_FOUND) {
          setError(text.errorEmailNotFound);
        } else {
          setError(text.errorGeneric);
        }
      } else {
        if (status.data.isVerified) {
          setStep('pin');
        } else {
          await handleResendCode(false); // Attempt to send code without success message initially
          setStep('verification');
          setResendCountdown(30); // Start countdown after potentially sending
        }
      }
    } catch (e) {
      setError(text.errorGeneric);
    } finally {
      setIsStepLoading(false);
    }
  }, [clearErrors, email, handleResendCode, text]);

  const handleForgotPinSubmit = useCallback(async () => {
    clearErrors();
    if (!validateEmail(email)) {
      setEmailValidationError(text.errorInvalidEmail);
      return;
    }
    setIsStepLoading(true);
    try {
      const result = await authService.requestChildPinReset({ email, language });
      if (result.status === 'success') {
        setSuccess(text.pinSentSuccess);
        // Optionally stay on this step or go back to email/pin?
        // setStep('pin'); // Example: Go back to pin after sending
      } else {
        if (result.error_code === AppErrorCodeEnum.enum.TOO_MANY_REQUESTS) {
          setError(text.errorTooManyRequests);
        } else if (result.error_code === AppErrorCodeEnum.enum.CHILD_ACCOUNT_NOT_FOUND) {
          setError(text.errorEmailNotFound); // Use email not found error
        } else {
          setError(text.pinResetError);
        }
      }
    } catch (e) {
      setError(text.pinResetError);
    } finally {
      setIsStepLoading(false);
    }
  }, [clearErrors, email, language, text]);

  const handleBack = useCallback(() => {
    clearErrors();
    setPin('');
    setVerificationCode('');
    if (step === 'pin' || step === 'verification' || step === 'forgotPin') {
      setStep('email');
    } else {
      onBack(); // Call the original onBack passed from the parent
    }
  }, [clearErrors, step, onBack]);

  const navigateToSignup = useCallback(() => {
    router.push(`/${language}/auth/register?child_email=${email}`);
  }, [router, language, email]);

  const goToForgotPin = useCallback(() => {
    clearErrors();
    setStep('forgotPin');
  }, [clearErrors]);

  return {
    step,
    email,
    pin,
    verificationCode,
    error,
    success,
    emailValidationError,
    accountLockedTime, // You might not need this in the component, but return it just in case
    resendCountdown,
    isLoading,
    text, // Return translations
    language, // Return language
    handleEmailChange,
    handlePinChange,
    handleVerificationCodeChange,
    handleNextFromEmail,
    handleSubmitPin, // Exposed if needed, e.g., for manual submit button
    handleVerifySubmit, // Exposed if needed
    handleResendCode,
    handleForgotPinSubmit,
    handleBack,
    navigateToSignup,
    goToForgotPin,
  };
}