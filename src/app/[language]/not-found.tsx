'use client';

import { useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, Home, AlertCircle } from 'lucide-react';
import { useAuthStore } from '@/zustand/authStore/authStore';
import { createTranslations } from '@/i18n/types';
import { Language } from '@/types';

// Define translations
const translations = createTranslations({
  en: {
    title: '404 - Page not found',
    description: 'Oops! The page you\'re looking for doesn\'t exist.',
    hint: 'You may have followed an incorrect link or the page may have been moved.',
    backButton: 'Go back',
    homeButton: 'Go to dashboard',
    loginButton: 'Go to login',
  },
  lu: {
    title: '404 - Säit net fonnt',
    description: 'Ups! D\'Säit déi Dir sicht existéiert net.',
    hint: 'Dir hutt vläicht e falschen Link gefollegt oder d\'Säit gouf geréckelt.',
    backButton: 'Zréck',
    homeButton: 'Op den Dashboard',
    loginButton: 'Op d\'Umeldung',
  },
  de: {
    title: '404 - Seite nicht gefunden',
    description: 'Hoppla! Die gesuchte Seite existiert nicht.',
    hint: 'Sie haben möglicherweise einen falschen Link verfolgt oder die Seite wurde verschoben.',
    backButton: 'Zurück',
    homeButton: 'Zum Dashboard',
    loginButton: 'Zur Anmeldung',
  },
  fr: {
    title: '404 - Page introuvable',
    description: 'Oups ! La page que vous recherchez n\'existe pas.',
    hint: 'Vous avez peut-être suivi un lien incorrect ou la page a été déplacée.',
    backButton: 'Retour',
    homeButton: 'Aller au tableau de bord',
    loginButton: 'Aller à la connexion',
  },
});

const NOT_FOUND_ILLUSTRATION_URL = "https://pub-e2b70868e474414392f797cc1f284b26.r2.dev/app_assets%2F404.svg";

export default function NotFound() {
  const router = useRouter();
  const params = useParams();
  const language = (params.language as Language) || 'lu';
  const t = translations[language];
  
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);
  const user = useAuthStore(state => state.user);
  const initialized = useAuthStore(state => state.initialized);
  const initAuth = useAuthStore(state => state.initAuth);

  // Initialize auth if not already done
  useEffect(() => {
    if (!initialized) {
      initAuth();
    }
  }, [initialized, initAuth]);

  const handleGoBack = () => {
    if (typeof window !== 'undefined' && window.history.length > 1) {
      window.history.back();
    } else {
      handleGoHome();
    }
  };

  const handleGoHome = () => {
    if (isAuthenticated && user) {
      const targetDashboard = user.userType === 'parent'
        ? `/${language}/parent`
        : `/${language}/student`;
      router.push(targetDashboard);
    } else {
      router.push(`/${language}/auth/login`);
    }
  };

  const getButtonText = () => {
    if (isAuthenticated && user) {
      return t.homeButton;
    }
    return t.loginButton;
  };

  return (
    <motion.div 
      className="min-h-screen flex items-center justify-center p-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="w-full max-w-2xl">
        <Card className="overflow-hidden">
          <CardHeader className="text-center pb-2">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              <CardTitle className="text-3xl font-bold text-gray-900">
                {t.title}
              </CardTitle>
            </motion.div>
          </CardHeader>
          
          <CardContent className="space-y-6 pt-4">
            {/* Illustration */}
            <motion.div 
              className="flex justify-center"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              <img 
                className="w-full max-w-md h-auto" 
                src={NOT_FOUND_ILLUSTRATION_URL} 
                alt="404 Not found"
              />
            </motion.div>

            {/* Error Message */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              <Alert className="border-orange-200 bg-orange-50">
                <AlertCircle className="h-4 w-4 text-orange-600" />
                <AlertDescription className="text-gray-700">
                  <p className="font-medium">{t.description}</p>
                  <p className="text-sm mt-1 text-gray-600">{t.hint}</p>
                </AlertDescription>
              </Alert>
            </motion.div>

            {/* Action Buttons */}
            <motion.div 
              className="flex flex-col sm:flex-row gap-3 justify-center pt-4"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
            >
              <Button
                variant="outline"
                onClick={handleGoBack}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                {t.backButton}
              </Button>
              
              <Button
                onClick={handleGoHome}
                className="flex items-center gap-2"
              >
                <Home className="h-4 w-4" />
                {getButtonText()}
              </Button>
            </motion.div>
          </CardContent>
        </Card>
      </div>
    </motion.div>
  );
}