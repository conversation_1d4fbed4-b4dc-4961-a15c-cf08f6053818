'use client';

import { useEffect, useState } from 'react';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RefreshCw, ArrowRight, CheckCircle2 } from 'lucide-react';
import { createTranslations } from '@/i18n/types';
import { Language } from '@/types';

const translations = createTranslations({
  en: {
    title: 'Welcome to the New LuxEdu!',
    description: 'Our app has been updated with a fresh new design and improved features.',
    redirecting: 'Redirecting you to the new version...',
    continueButton: 'Continue to New Version',
    migrationComplete: 'Migration complete!',
    clearingData: 'Clearing old data...',
  },
  lu: {
    title: 'Wëllkomm beim neien LuxEdu!',
    description: 'Eis App gouf aktualiséiert.',
    redirecting: 'Dir gitt op déi nei Versioun ëmgeleet...',
    continueButton: 'Weider zur op déi nei Versioun',
    migrationComplete: 'Migratioun ofgeschloss!',
    clearingData: 'Al Daten ginn geläscht...',
  },
  de: {
    title: 'Willkommen beim neuen LuxEdu!',
    description: 'Unsere App wurde aktualisert.',
    redirecting: 'Sie werden zur neuen Version weitergeleitet...',
    continueButton: 'Weiter zur neuen Version',
    migrationComplete: 'Migration abgeschlossen!',
    clearingData: 'Alte Daten werden gelöscht...',
  },
  fr: {
    title: 'Bienvenue sur le nouveau LuxEdu!',
    description: 'Notre application a été mise à jour avec un nouveau design et des fonctionnalités améliorées.',
    redirecting: 'Redirection vers la nouvelle version...',
    continueButton: 'Continuer vers la nouvelle version',
    migrationComplete: 'Migration terminée!',
    clearingData: 'Suppression des anciennes données...',
  },
});

// Old localStorage keys that need to be cleared
const OLD_STORAGE_KEYS = [
  'access_token',
  'parent_access_token',
  'child_access_token',
  'refresh_token',
  'token_type',
  'expires_in',
  'user',
  'userType',
  'selectedChild',
  'childProfiles',
  'parentProfile',
  'studentProfile',
  'authState',
  'auth_state',
];

export default function AppMigrationPage() {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'clearing' | 'complete' | 'redirecting'>('clearing');
  
  const language = (params.language as Language) || 'lu';
  const slug = params.slug as string[] | undefined;
  const t = translations[language];

  useEffect(() => {
    const migrate = async () => {
      // Clear old localStorage data
      setStatus('clearing');
      
      // Small delay to show the clearing message
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Clear all old storage keys
      OLD_STORAGE_KEYS.forEach(key => {
        if (typeof window !== 'undefined' && window.localStorage) {
          localStorage.removeItem(key);
        }
      });
      
      // Also clear any session storage that might exist
      if (typeof window !== 'undefined' && window.sessionStorage) {
        sessionStorage.clear();
      }
      
      setStatus('complete');
      
      // Do NOT auto-redirect - let user read the message and click continue
    };

    migrate();
  }, []);

  const handleContinue = () => {
    setStatus('redirecting');
    
    // Determine the new route based on the old path
    let newPath = `/${language}`;
    
    if (slug && slug.length > 0) {
      const [firstSegment, ...restSegments] = slug;
      
      // Map old routes to new routes
      switch (firstSegment) {
        case 'parent':
          newPath = `/${language}/parent${restSegments.length > 0 ? '/' + restSegments.join('/') : ''}`;
          break;
        case 'student':
          newPath = `/${language}/student${restSegments.length > 0 ? '/' + restSegments.join('/') : ''}`;
          break;
        case 'auth':
          // Auth routes stay similar but without /app
          newPath = `/${language}/auth${restSegments.length > 0 ? '/' + restSegments.join('/') : '/login'}`;
          break;
        case 'shared':
          // Shared routes might need special handling
          if (restSegments[0] === 'contact-us') {
            newPath = `/${language}/contact-us`;
          } else {
            newPath = `/${language}`;
          }
          break;
        default:
          // For any other routes, redirect to home
          newPath = `/${language}`;
      }
    }
    
    // Preserve query parameters
    const queryString = searchParams.toString();
    if (queryString) {
      newPath += `?${queryString}`;
    }
    
    // Redirect to the new path
    router.replace(newPath);
  };

  return (
    <motion.div 
      className="min-h-screen flex items-center justify-center p-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="w-full max-w-2xl">
        <Card className="overflow-hidden">
          <CardHeader className="text-center pb-2">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              <CardTitle className="text-3xl font-bold text-gray-900">
                {t.title}
              </CardTitle>
            </motion.div>
          </CardHeader>
          
          <CardContent className="space-y-6 pt-4">
            {/* Description */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="text-center"
            >
              <p className="text-lg text-gray-700 mb-2">{t.description}</p>
            </motion.div>

            {/* Status */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              <Alert className="border-blue-200 bg-blue-50">
                <div className="flex items-center gap-3">
                  {status === 'clearing' && (
                    <>
                      <RefreshCw className="h-5 w-5 text-blue-600 animate-spin" />
                      <AlertDescription className="text-gray-700 font-medium">
                        {t.clearingData}
                      </AlertDescription>
                    </>
                  )}
                  {status === 'complete' && (
                    <>
                      <CheckCircle2 className="h-5 w-5 text-green-600" />
                      <AlertDescription className="text-gray-700 font-medium">
                        {t.migrationComplete}
                      </AlertDescription>
                    </>
                  )}
                  {status === 'redirecting' && (
                    <>
                      <RefreshCw className="h-5 w-5 text-blue-600 animate-spin" />
                      <AlertDescription className="text-gray-700 font-medium">
                        {t.redirecting}
                      </AlertDescription>
                    </>
                  )}
                </div>
              </Alert>
            </motion.div>

            {/* Continue Button */}
            <motion.div 
              className="flex justify-center pt-4"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
            >
              <Button
                onClick={handleContinue}
                disabled={status === 'redirecting'}
                className="flex items-center gap-2"
                size="lg"
              >
                {status === 'redirecting' ? t.redirecting : t.continueButton}
                <ArrowRight className="h-4 w-4" />
              </Button>
            </motion.div>
          </CardContent>
        </Card>
      </div>
    </motion.div>
  );
}