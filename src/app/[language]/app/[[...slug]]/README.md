# Legacy App Route Redirect

This catch-all route handles redirects from the old app structure to the new one.

## Old → New Route Mappings

- `/[language]/app/parent/*` → `/[language]/parent/*`
- `/[language]/app/student/*` → `/[language]/student/*`
- `/[language]/app/auth/*` → `/[language]/auth/*`
- `/[language]/app/shared/*` → Various new locations
- `/[language]/app/new/*` → `/[language]` (home)
- `/[language]/app/chapters/*` → `/[language]/student`

## Special Route Handling

### Parent Routes
- `/app/parent/login` → `/auth/login?type=parent`
- `/app/parent/customer-support` → `/contact-us`
- `/app/parent/courses/*` → `/parent`

### Student Routes
- `/app/student/courses/[courseId]/*` → `/student/[courseId]/*`
- `/app/student/switch-to-parent` → `/parent`

### Auth Routes
- `/app/auth/signup` → `/auth/register`
- `/app/auth/reset-parent-pin` → `/auth/reset-parent-pin`

### Shared Routes
- `/app/shared/help` → `/contact-us`
- `/app/shared/lesson/*` → `/student`

## Token Migration

The redirect page automatically migrates authentication tokens from the old format to the new format:

- Old: `access_token`, `parent_access_token`, `child_access_token`
- New: `token-current`, `token-parent`, `token-child`

## Features

1. **Automatic Token Migration**: Preserves user authentication state
2. **Query Parameter Preservation**: Maintains all URL parameters during redirect
3. **Analytics Tracking**: Logs redirect events for monitoring
4. **Error Handling**: Falls back to language home page on errors
5. **SEO Protection**: Uses `noindex, nofollow` to prevent old URLs from being indexed