"use client";

import { useEffect, ReactNode, useRef } from 'react';
import { useAuthStore } from '@/zustand/authStore/authStore';
import { useRouter, useParams, usePathname } from 'next/navigation';
import DashboardLoading from './loading';
import NavigationBar from "@/components/custom/NavigationBar";

export default function LanguageLayout({ children }: { children: ReactNode }) {
  const initAuth = useAuthStore(state => state.initAuth);
  const isInitializingAuth = useAuthStore(state => state.isInitializingAuth);
  const initialized = useAuthStore(state => state.initialized);
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);
  const isSwitchingToParent = useAuthStore(state => state.isSwitchingToParent);
  const syncTokenWithRoute = useAuthStore(state => state.syncTokenWithRoute);

  const router = useRouter();
  const params = useParams();
  const pathname = usePathname();
  const language = params.language as string;

  // Track previous pathname to determine navigation direction
  const previousPathnameRef = useRef<string>(pathname);

  useEffect(() => {
    if (!initialized && !isInitializingAuth) {
      initAuth();
    }
  }, [initialized, isInitializingAuth]);

  // Sync token with route when pathname changes
  useEffect(() => {
    if (initialized && isAuthenticated) {
      console.log('[LanguageLayout] Syncing token for route:', pathname);
      syncTokenWithRoute(pathname);
    }
  }, [pathname, initialized, isAuthenticated, syncTokenWithRoute]);

  useEffect(() => {
    // NEVER interfere with autologin flow - let it handle its own logic completely
    if (pathname.includes('/auth/autologin')) {
      console.log('[MainLayout] Skipping auth checks for autologin route');
      return;
    }

    // Only handle redirects for unauthenticated users trying to access protected pages
    // Let auth layout handle its own flow for authenticated users and token logins
    if (initialized && !isInitializingAuth && !isAuthenticated) {

      // Define paths that do NOT require authentication
      const publicPaths = [
        `/${language}/auth/`,  // All auth pages
        `/${language}/contact-us`, // Contact us page
        `/${language}/app`, // Legacy app migration pages
        // Add any other public paths like privacy policy, terms, etc.
        // `/${language}/policies`,
      ];

      // Check if the current path is one of the public paths
      const isPublicPath = publicPaths.some(publicPath => pathname.startsWith(publicPath));

      if (!isPublicPath) {
        // If not on a public path and not authenticated, redirect to login
        const loginUrl = `/${language}/auth/login`;
        console.log('[MainLayout] Redirecting unauthenticated user to login');
        router.replace(loginUrl); // Use replace to avoid adding protected page to history
      }
    }

    // Clear switching state when navigating away from auth pages
    const wasOnAuthPages = previousPathnameRef.current.startsWith(`/${language}/auth/`);
    const isOnAuthPages = pathname.startsWith(`/${language}/auth/`);

    if (isSwitchingToParent && initialized && wasOnAuthPages && !isOnAuthPages) {
      console.log('[MainLayout] Clearing switching to parent state');
      useAuthStore.getState()._setSwitchingToParent(false);
    }

    // Update previous pathname for next effect run
    previousPathnameRef.current = pathname;
  }, [initialized, isInitializingAuth, isAuthenticated, isSwitchingToParent, router, language, pathname]);

  if (isInitializingAuth || !initialized) {
    return <DashboardLoading />;
  }

  // If initialized but not authenticated, and on a public path, children will render.
  // If on a protected path, DashboardLoading will be shown while redirect effect triggers.
  // If authenticated, children will render.
  return (
    <>
      <NavigationBar />
      <div className="mx-auto max-w-3xl w-[95%] mt-12">
        {children}
      </div>
    </>
  );
}