'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import LoadingSpinner from '@/components/custom/LoadingSpinner';

export default function InternalRootPage() {
  const router = useRouter();

  useEffect(() => {
    router.replace('/internal/login');
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <LoadingSpinner className="w-8 h-8" />
    </div>
  );
}