import { DraftExercise, ExerciseType } from '@/types/internal/editorial';
import { Exercise } from '@/types/content/exerciseTypes';

export type StepNumber = 1 | 2 | 3;
export type SolutionEditorMode = 'structured' | 'json';

export interface DraftEditorPageProps {
  params: {
    draftId: string;
    learningNodeId: string;
  };
}

export interface StepIndicatorProps {
  currentStep: StepNumber;
}

export interface StepNavigationProps {
  currentStep: StepNumber;
  canProceedToNextStep: () => boolean;
  onNextStep: () => Promise<void>;
  onPreviousStep: () => void;
  canSubmit: boolean;
  isDirty: boolean;
  isSubmitting: boolean;
  onSubmit: () => void;
}

export interface DraftEditorHeaderProps {
  draft: DraftExercise;
  isDirty: boolean;
  isSaving: boolean;
  canSubmit: boolean;
  isSubmitting: boolean;
  canReject: boolean;
  autoSaveStatus?: 'idle' | 'saving' | 'saved' | 'error';
  onSave: () => Promise<void>;
  onSubmit: () => void;
  onReject: () => void;
  onBack: () => void;
}

export interface SubmitDialogProps {
  isOpen: boolean;
  isSubmitting: boolean;
  onClose: () => void;
  onSubmit: () => Promise<void>;
}

export interface LoadingStateProps {
  // No props needed for loading state
}

export interface ErrorStateProps {
  error?: string;
  learningNodeId: string;
  onBack: () => void;
}

export interface ExerciseStepProps {
  draft: DraftExercise;
  updateExerciseData: (data: any) => void;
}

export interface SolutionStepProps {
  draft: DraftExercise;
  updateSolutionData: (data: any) => void;
  solutionEditorMode: SolutionEditorMode;
  onEditorModeChange: (mode: SolutionEditorMode) => void;
}

export interface ReviewStepProps {
  draft: DraftExercise;
  canSubmit: boolean;
  isDirty: boolean;
  isSubmitting: boolean;
  onSubmit: () => void;
  updateDifficulty: (difficulty: string) => void;
}

export interface ExerciseEditorPanelProps {
  exerciseType: ExerciseType;
  exerciseData: any;
  onChange: (data: any) => void;
}

export interface ExercisePreviewPanelProps {
  draft: DraftExercise;
}

export interface SolutionEditorPanelProps {
  exerciseType: ExerciseType;
  exerciseData: any;
  solutionData: any;
  onChange: (data: any) => void;
  mode: SolutionEditorMode;
  onModeChange: (mode: SolutionEditorMode) => void;
}

export interface SolutionPreviewPanelProps {
  exerciseType: ExerciseType;
  exerciseData: any;
  solutionData: any;
}

export interface UseStepNavigationReturn {
  currentStep: StepNumber;
  canProceedToNextStep: () => boolean;
  handleNextStep: () => Promise<void>;
  handlePreviousStep: () => void;
}

export interface UseSubmitDialogReturn {
  showSubmitDialog: boolean;
  setShowSubmitDialog: (show: boolean) => void;
  handleSubmit: () => Promise<void>;
}

export interface UseSolutionEditorReturn {
  solutionEditorMode: SolutionEditorMode;
  setSolutionEditorMode: (mode: SolutionEditorMode) => void;
  handleSolutionDataChange: (value: string) => void;
  handleStructuredSolutionChange: (solutionData: Record<string, any>) => void;
  ensureSolutionInitialized: () => void;
}

export interface UseDraftEditorPageReturn {
  // Draft data
  draft: DraftExercise | null;
  isLoading: boolean;
  error?: string;
  
  // Save/Submit state
  isSaving: boolean;
  isSubmitting: boolean;
  isDirty: boolean;
  canSubmit: boolean;
  saveStatus: any;
  autoSaveStatus?: 'idle' | 'saving' | 'saved' | 'error';
  
  // Actions
  save: () => Promise<void>;
  submit: () => Promise<boolean>;
  updateExerciseData: (data: any) => void;
  updateSolutionData: (data: any) => void;
  updateDifficulty: (difficulty: string) => void;
  
  // Rejection state and actions
  canReject: boolean;
  showRejectModal: boolean;
  setShowRejectModal: (show: boolean) => void;
  rejectReason: string;
  setRejectReason: (reason: string) => void;
  isRejecting: boolean;
  handleReject: () => Promise<void>;
  
  // Step navigation
  stepNavigation: UseStepNavigationReturn;
  
  // Submit dialog
  submitDialog: UseSubmitDialogReturn;
  
  // Solution editor
  solutionEditor: UseSolutionEditorReturn;
}