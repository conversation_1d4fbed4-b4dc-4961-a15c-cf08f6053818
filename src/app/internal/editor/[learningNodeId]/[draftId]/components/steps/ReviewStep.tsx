import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { FileCheck, AlertTriangle } from 'lucide-react';
import { ExerciseFactory } from '@/app/[language]/student/content/[learningNodeId]/learningNodes/components/Exercises/ExerciseFactory';
import SolutionPreview from '@/components/internal/SolutionPreview';
import { transformDraftToExercise } from '../../utils/transformDraftToExercise';
import { ReviewStepProps } from '../../types';
import { DifficultySelector } from '../../../new/components/DraftSettings/DifficultySelector';
import { Difficulty } from '@/types/internal/editorial';

export function ReviewStep({ draft, updateDifficulty }: ReviewStepProps) {
  const transformedExercise = transformDraftToExercise(draft);
  const hasSolution = draft.solutionJson && Object.keys(draft.solutionJson).length > 0;

  return (
    <div className="h-full min-h-[600px]">
      <div className="">
        <div className="max-w-4xl mx-auto space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileCheck className="h-5 w-5" />
                  <span>Final Review</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">
                    {draft.exerciseType.replace(/[-_]/g, ' ')}
                  </Badge>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Difficulty Section */}
              <div className="max-w-xs">
                <DifficultySelector 
                  value={draft.difficulty || 'medium'} 
                  onChange={(value) => updateDifficulty(value)}
                />
              </div>
              
              {/* Exercise Section */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Exercise</h3>
                <div className="border rounded-lg p-4">
                  {transformedExercise ? (
                    <div className="exercise-preview-container">
                      <ExerciseFactory
                        exercise={transformedExercise}
                        exerciseIndex={0}
                        animationDelay={0}
                        language="en"
                      />
                    </div>
                  ) : (
                    <Alert variant="destructive">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        Unable to display exercise.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </div>
              
              {/* Solution Section */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Solution</h3>
                <div className="border rounded-lg p-4 bg-gray-50">
                  {hasSolution && draft.solutionJson ? (
                    <SolutionPreview
                      exerciseType={draft.exerciseType}
                      exerciseData={draft.dataJson}
                      solutionData={draft.solutionJson as any}
                      className="w-full"
                    />
                  ) : (
                    <Alert>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        No solution configured
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}