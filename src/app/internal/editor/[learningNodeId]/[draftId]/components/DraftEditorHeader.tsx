import { Button } from '@/components/ui/button';
import StatusBadge from '@/components/internal/StatusBadge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, Save, Send, AlertTriangle, XCircle } from 'lucide-react';
import { DraftEditorHeaderProps } from '../types';
import { formatExerciseType } from '@/utils/internal/exerciseTypeLabels';

export function DraftEditorHeader({
  draft,
  isDirty,
  isSaving,
  canSubmit,
  isSubmitting,
  canReject,
  autoSaveStatus,
  onSave,
  onSubmit,
  onReject,
  onBack
}: DraftEditorHeaderProps) {
  return (
    <>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {formatExerciseType(draft.exerciseType)} Exercise
            </h1>
            <div className="flex items-center gap-2 mt-1">
              <StatusBadge status={draft.status} />
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* Save Status Indicator */}
          <span className={`text-sm ${
            isSaving || autoSaveStatus === 'saving' ? 'text-blue-600' : 
            autoSaveStatus === 'saved' && !isDirty ? 'text-gray-500' : 
            autoSaveStatus === 'error' ? 'text-red-600' : 
            isDirty ? 'text-orange-600' : 'text-gray-500'
          }`}>
            {isSaving || autoSaveStatus === 'saving' ? 'Saving...' :
             autoSaveStatus === 'saved' && !isDirty ? 'All changes saved' :
             autoSaveStatus === 'error' ? 'Save failed' :
             isDirty ? 'Unsaved changes' : 
             'All changes saved'}
          </span>
          
          <Button
            variant="outline"
            onClick={onSave}
            disabled={!isDirty || isSaving || autoSaveStatus === 'saving'}
          >
            <Save className="h-4 w-4 mr-2" />
            {autoSaveStatus === 'saving' ? 'Autosaving...' : 'Save'}
          </Button>
          
          {canReject && (
            <Button
              variant="outline"
              onClick={onReject}
              disabled={isDirty}
              className="text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300"
            >
              <XCircle className="h-4 w-4 mr-2" />
              Reject Draft
            </Button>
          )}
          
          {canSubmit && (
            <Button
              onClick={onSubmit}
              disabled={isSubmitting || isDirty}
            >
              <Send className="h-4 w-4 mr-2" />
              Submit for Review
            </Button>
          )}
        </div>
      </div>

      {/* Rejection Banner */}
      {(draft.status === 'REJECTED_BY_ADMIN' || draft.status === 'REJECTED_BY_EDITOR') && draft.rejectReason && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>{draft.status === 'REJECTED_BY_ADMIN' ? 'Admin' : 'Editor'} Feedback:</strong> {draft.rejectReason}
          </AlertDescription>
        </Alert>
      )}
    </>
  );
}