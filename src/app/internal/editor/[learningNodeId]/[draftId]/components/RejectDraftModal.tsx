import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, XCircle } from 'lucide-react';

interface RejectDraftModalProps {
  isOpen: boolean;
  reason: string;
  isRejecting: boolean;
  onReasonChange: (reason: string) => void;
  onConfirm: () => void;
  onClose: () => void;
}

const PREDEFINED_REASONS = [
  'Does not make sense',
  'Too difficult',
  'Low quality',
  'Incorrect answer',
  'Unclear instructions',
  'Grammar/spelling errors'
];

export function RejectDraftModal({
  isOpen,
  reason,
  isRejecting,
  onReasonChange,
  onConfirm,
  onClose,
}: RejectDraftModalProps) {
  if (!isOpen) return null;

  const canSubmit = !isRejecting && reason.trim().length >= 10;

  const handlePredefinedReason = (predefinedReason: string) => {
    const currentReason = reason.trim();
    const newReason = currentReason 
      ? `${currentReason}\n${predefinedReason}` 
      : predefinedReason;
    onReasonChange(newReason);
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-md mx-4">
        <CardHeader>
          <CardTitle>Reject Draft</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-gray-600">
            Please provide feedback explaining why this draft needs revision. 
            This will help the content creator improve the exercise.
          </p>

          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Quick Reasons
            </label>
            <div className="flex flex-wrap gap-2 mb-4">
              {PREDEFINED_REASONS.map((predefinedReason) => (
                <Button
                  key={predefinedReason}
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => handlePredefinedReason(predefinedReason)}
                  className="text-xs"
                  disabled={isRejecting}
                >
                  {predefinedReason}
                </Button>
              ))}
            </div>
          </div>

          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Rejection Reason
            </label>
            <textarea
              placeholder="Explain what needs to be changed or improved..."
              value={reason}
              onChange={(e) => onReasonChange(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md resize-none h-32 text-sm"
              required
            />
            <p className="text-xs text-gray-500 mt-1">
              Minimum 10 characters required
            </p>
          </div>

          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isRejecting}
            >
              Cancel
            </Button>
            <Button
              onClick={onConfirm}
              disabled={!canSubmit}
              className="bg-orange-600 hover:bg-orange-700 text-white"
            >
              {isRejecting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Rejecting...
                </>
              ) : (
                <>
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject Draft
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}