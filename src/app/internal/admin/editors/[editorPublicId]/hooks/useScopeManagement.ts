import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useEditorsStore } from '@/zustand/internal/editorsStore';
import { ScopeForm, UseScopeManagementReturn, UseContentHierarchyReturn } from '../types';

interface UseScopeManagementProps {
  editorPublicId: string;
  contentHierarchy: UseContentHierarchyReturn;
}

export function useScopeManagement({ 
  editorPublicId, 
  contentHierarchy 
}: UseScopeManagementProps): UseScopeManagementReturn {
  const { toast } = useToast();
  const {
    addScopeByPublicId,
    removeScopeByPublicId,
    fetchEditorByPublicId,
  } = useEditorsStore();
  const error = useEditorsStore((state) => state.error);

  const [showScopeDialog, setShowScopeDialog] = useState(false);
  const [scopeForm, setScopeForm] = useState<ScopeForm>({ 
    scopeType: 'SUBJECT', 
    scopeValue: '' 
  });
  const [isAddingScope, setIsAddingScope] = useState(false);

  // Load subjects when dialog opens
  useEffect(() => {
    if (showScopeDialog) {
      contentHierarchy.loadSubjects();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showScopeDialog]);

  // Load chapters when subject is selected
  useEffect(() => {
    if (scopeForm.selectedSubjectId && scopeForm.scopeType !== 'SUBJECT') {
      contentHierarchy.loadChapters(scopeForm.selectedSubjectId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [scopeForm.selectedSubjectId, scopeForm.scopeType]);

  // Load learning nodes when chapter is selected
  useEffect(() => {
    if (scopeForm.selectedChapterId && scopeForm.scopeType === 'LEARNING_NODE') {
      contentHierarchy.loadLearningNodes(scopeForm.selectedChapterId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [scopeForm.selectedChapterId, scopeForm.scopeType]);

  const handleAddScope = async () => {
    setIsAddingScope(true);
    
    try {
      const success = await addScopeByPublicId(editorPublicId, {
        scopeType: scopeForm.scopeType,
        scopeValue: scopeForm.scopeValue,
      });
      
      if (success) {
        toast({
          title: 'Scope added',
          description: 'The scope has been added to the editor.',
        });
        setShowScopeDialog(false);
        setScopeForm({ scopeType: 'SUBJECT', scopeValue: '' });
        contentHierarchy.resetContent();
        
        // Refresh editor data to show the new scope
        await fetchEditorByPublicId(editorPublicId);
      } else {
        toast({
          title: 'Failed to add scope',
          description: error || 'Please try again.',
          variant: 'destructive',
        });
      }
    } finally {
      setIsAddingScope(false);
    }
  };

  const handleRemoveScope = async (scopeId: number) => {
    if (!window.confirm('Are you sure you want to remove this scope?')) {
      return;
    }
    
    const success = await removeScopeByPublicId(editorPublicId, scopeId);
    
    if (success) {
      toast({
        title: 'Scope removed',
        description: 'The scope has been removed from the editor.',
      });
      // Refresh editor data to reflect the removal
      await fetchEditorByPublicId(editorPublicId);
    } else {
      toast({
        title: 'Failed to remove scope',
        description: error || 'Please try again.',
        variant: 'destructive',
      });
    }
  };

  return {
    showScopeDialog,
    setShowScopeDialog,
    scopeForm,
    setScopeForm,
    isAddingScope,
    handleAddScope,
    handleRemoveScope,
  };
}