import { 
  getAvailableSubjects, 
  getSubjectChapters, 
  getChapterLearningNodes 
} from '@/services/internal/adminService';
import { Subject, Chapter, LearningNode } from '../types';

export async function fetchSubjects(): Promise<Subject[]> {
  try {
    const result = await getAvailableSubjects();
    console.log('Raw API response for subjects:', JSON.stringify(result, null, 2));
    if (result.status === 'success') {
      return result.data.subjects;
    }
    return [];
  } catch (error) {
    console.error('Failed to load subjects:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
    return [];
  }
}

export async function fetchChapters(subjectId: string): Promise<Chapter[]> {
  try {
    const result = await getSubjectChapters(subjectId);
    if (result.status === 'success') {
      return result.data.chapters;
    }
    return [];
  } catch (error) {
    console.error('Failed to load chapters:', error);
    return [];
  }
}

export async function fetchLearningNodes(chapterId: string): Promise<LearningNode[]> {
  try {
    const result = await getChapterLearningNodes(chapterId);
    if (result.status === 'success') {
      return result.data.learningNodes;
    }
    return [];
  } catch (error) {
    console.error('Failed to load learning nodes:', error);
    return [];
  }
}