'use client';

import { useEffect, useMemo } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useInternalAuthStore, selectIsInternalAuthenticated, selectIsAdmin } from '@/zustand/internal/internalAuthStore/internalAuthStore';
import LoadingSpinner from '@/components/custom/LoadingSpinner';
import InternalErrorBoundary from '@/components/internal/ErrorBoundary';
import { Button } from '@/components/ui/button';
import { 
  Home,
  FileText,
  Users,
  CheckSquare,
  LogOut,
  Menu
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useState } from 'react';
import { Toaster } from '@/components/ui/toaster';
import { useInternalAuthRedirect } from '@/hooks/useInternalAuthRedirect';

interface NavItem {
  label: string;
  href: string;
  icon: any;
  adminOnly?: boolean;
}

const navItems: NavItem[] = [
  { label: 'My Content Assignments', href: '/internal/editor', icon: FileText },
  { label: 'Admin Dashboard', href: '/internal/admin', icon: Home, adminOnly: true },
  { label: 'Review Queue', href: '/internal/admin/review', icon: CheckSquare, adminOnly: true },
  { label: 'Manage Editors', href: '/internal/admin/editors', icon: Users, adminOnly: true },
];

export default function InternalLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, isLoading, initAuth, logout } = useInternalAuthStore();
  const isAdmin = useInternalAuthStore(selectIsAdmin);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const { isAuthenticated, initialized } = useInternalAuthRedirect('/internal/login');

  useEffect(() => {
    initAuth();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Run once on mount

  // Filter nav items based on role (memoized for performance)
  const visibleNavItems = useMemo(() => 
    navItems.filter(item => {
      if (item.adminOnly && !isAdmin) return false;
      return true;
    }), [isAdmin]
  );

  // Show loading state while initializing
  if (!initialized || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner className="w-8 h-8" />
      </div>
    );
  }

  // Don't show layout on login page
  if (pathname === '/internal/login' || !isAuthenticated) {
    return <>{children}</>;
  }

  return (
    <div className="bg-gray-50 flex w-full min-h-screen">
        {/* Sidebar */}
        <aside className={cn(
          "bg-white border-r border-gray-200 transition-all duration-200 flex-shrink-0",
          sidebarOpen ? "w-64" : "w-16"
        )}>
          <div className="flex flex-col h-full sticky top-0">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              {sidebarOpen && (
                <h2 className="text-xl font-semibold text-gray-800">LuxEdu Editorial</h2>
              )}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="ml-auto"
              >
                <Menu className="h-5 w-5" />
              </Button>
            </div>

            {/* User info */}
            {sidebarOpen && user && (
              <div className="px-4 py-3 border-b border-gray-200">
                <p className="text-sm text-gray-600">{user.email}</p>
                <p className="text-xs text-gray-500 capitalize">{user.role}</p>
              </div>
            )}

            {/* Navigation */}
            <nav className="flex-1 p-4">
              <ul className="space-y-2">
                {visibleNavItems.map((item) => {
                  const Icon = item.icon;
                  const isActive = pathname === item.href || 
                    (item.href !== '/internal/editor' && item.href !== '/internal/admin' && pathname.startsWith(item.href));
                  
                  return (
                    <li key={item.href}>
                      <Button
                        variant={isActive ? "secondary" : "ghost"}
                        className={cn(
                          "w-full justify-start",
                          !sidebarOpen && "justify-center px-2"
                        )}
                        onClick={() => router.push(item.href)}
                      >
                        <Icon className={cn("h-5 w-5", sidebarOpen && "mr-3")} />
                        {sidebarOpen && <span>{item.label}</span>}
                      </Button>
                    </li>
                  );
                })}
                
                {/* Logout button */}
                <li className="pt-4 border-t border-gray-200">
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50",
                      !sidebarOpen && "justify-center px-2"
                    )}
                    onClick={() => {
                      logout();
                      router.push('/internal/login');
                    }}
                  >
                    <LogOut className={cn("h-5 w-5", sidebarOpen && "mr-3")} />
                    {sidebarOpen && <span>Logout</span>}
                  </Button>
                </li>
              </ul>
            </nav>
          </div>
        </aside>
        {/* Main content */}
        <main className="mx-auto w-full flex-1 flex flex-col min-h-0">
          <InternalErrorBoundary
            onError={(error, errorInfo) => {
              console.error('Internal layout error:', error, errorInfo);
              // Here you would typically send to error monitoring service
            }}
          >
            <div className="p-8 flex-1 flex flex-col min-h-0">
              {children}
            </div>
          </InternalErrorBoundary>
        </main>
        <Toaster />
    </div>
  );
}