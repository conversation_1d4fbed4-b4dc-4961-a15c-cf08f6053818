// Test utility to verify data structure compatibility between drafts and published exercises
import { validateDraftData, isDraftDataValid } from '@/schemas/internal/draftSchema';
import { ExerciseType } from '@/types/internal/enums';

// Sample draft data in the correct format for testing
const sampleMCDraftData = {
  prompt: "What is the capital of France?",
  options: [
    { public_id: "opt1", text: "London" },
    { public_id: "opt2", text: "Paris" },
    { public_id: "opt3", text: "Berlin" },
    { public_id: "opt4", text: "Madrid" }
  ]
};

const sampleMCSolution = {
  correct_answer: {
    correct_option_id: ["opt2"]
  },
  solution_steps: [
    { text: "Paris is the capital and most populous city of France." }
  ]
};

const sampleTrueFalseDraftData = {
  prompt: "The Earth is the third planet from the Sun."
};

const sampleTrueFalseSolution = {
  correct_answer: {
    is_true: true
  },
  solution_steps: [
    { text: "The Earth is indeed the third planet from the Sun in our solar system." }
  ]
};

const sampleClozeDraftData = {
  prompt: "Fill in the blanks in the following text.",
  text_parts: ["The ", " of France is known for its ", " tower."],
  hints: ["capital city", "famous landmark"]
};

const sampleClozeSolution = {
  correct_answer: {
    correct_answers: ["Paris", "Eiffel"]
  },
  solution_steps: [
    { text: "Paris is the capital of France, famous for the Eiffel Tower." }
  ]
};

// Test function to validate all exercise types
export function testDataStructureCompatibility() {
  const results: { exerciseType: string; dataValid: boolean; solutionValid: boolean; error?: string }[] = [];

  // Test MC Simple
  try {
    const mcValid = isDraftDataValid('mc-simple', sampleMCDraftData, sampleMCSolution);
    results.push({ exerciseType: 'mc-simple', dataValid: mcValid, solutionValid: mcValid });
  } catch (error) {
    results.push({ 
      exerciseType: 'mc-simple', 
      dataValid: false, 
      solutionValid: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }

  // Test MC Multi (same data structure as simple)
  try {
    const mcMultiValid = isDraftDataValid('mc-multi', sampleMCDraftData, {
      ...sampleMCSolution,
      correct_answer: {
        correct_option_ids: ["opt2", "opt1"] // Multiple correct answers
      }
    });
    results.push({ exerciseType: 'mc-multi', dataValid: mcMultiValid, solutionValid: mcMultiValid });
  } catch (error) {
    results.push({ 
      exerciseType: 'mc-multi', 
      dataValid: false, 
      solutionValid: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }

  // Test True/False
  try {
    const tfValid = isDraftDataValid('true-false', sampleTrueFalseDraftData, sampleTrueFalseSolution);
    results.push({ exerciseType: 'true-false', dataValid: tfValid, solutionValid: tfValid });
  } catch (error) {
    results.push({ 
      exerciseType: 'true-false', 
      dataValid: false, 
      solutionValid: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }

  // Test Cloze
  try {
    const clozeValid = isDraftDataValid('cloze', sampleClozeDraftData, sampleClozeSolution);
    results.push({ exerciseType: 'cloze', dataValid: clozeValid, solutionValid: clozeValid });
  } catch (error) {
    results.push({ 
      exerciseType: 'cloze', 
      dataValid: false, 
      solutionValid: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }

  // Test Input
  try {
    const inputValid = isDraftDataValid('input', { prompt: "Explain photosynthesis." }, {
      correct_answer: {
        correct_answer: "Photosynthesis is the process by which plants convert light energy into chemical energy."
      }
    });
    results.push({ exerciseType: 'input', dataValid: inputValid, solutionValid: inputValid });
  } catch (error) {
    results.push({
      exerciseType: 'input',
      dataValid: false,
      solutionValid: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }

  return results;
}

// Function to log test results
export function logTestResults() {
  console.log('=== Data Structure Compatibility Test Results ===');
  const results = testDataStructureCompatibility();
  
  results.forEach(result => {
    const status = result.dataValid && result.solutionValid ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.exerciseType}`);
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });

  const passCount = results.filter(r => r.dataValid && r.solutionValid).length;
  const totalCount = results.length;
  
  console.log(`\nSummary: ${passCount}/${totalCount} exercise types passed validation`);
  
  return { passCount, totalCount, results };
}

// Export for use in browser console or other testing
if (typeof window !== 'undefined') {
  (window as any).testDataStructureCompatibility = testDataStructureCompatibility;
  (window as any).logTestResults = logTestResults;
}
