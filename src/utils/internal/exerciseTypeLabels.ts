import { ExerciseType } from '@/types/internal/editorial';

// Map of exercise types to user-friendly labels
export const exerciseTypeLabels: Record<ExerciseType, string> = {
  'mc-simple': 'Multiple Choice (Single)',
  'mc-multi': 'Multiple Choice (Multi)',
  'true-false': 'True/False',
  'error-correction': 'Error Correction',
  'cloze': 'Fill in the Blanks',
  'dropdown': 'Dropdown Selection',
  'input': 'Text Input',
  'matching-pairs': 'Matching Pairs',
  'categorize': 'Categorization',
  'highlight': 'Text Highlighting'
};

// Map of exercise types to icons (emoji or icon name)
export const exerciseTypeIcons: Record<ExerciseType, string> = {
  'mc-simple': '🔘',
  'mc-multi': '☑️',
  'true-false': '✓',
  'error-correction': '✏️',
  'cloze': '📝',
  'dropdown': '📋',
  'input': '✏️',
  'matching-pairs': '🔗',
  'categorize': '📁',
  'highlight': '🖍️'
};

/**
 * Format an exercise type into a user-friendly label
 * @param exerciseType - The exercise type code
 * @returns User-friendly label
 */
export function formatExerciseType(exerciseType: ExerciseType): string {
  return exerciseTypeLabels[exerciseType] || exerciseType.replace(/[-_]/g, ' ');
}

/**
 * Get the icon for an exercise type
 * @param exerciseType - The exercise type code
 * @returns Icon string (emoji or icon name)
 */
export function getExerciseTypeIcon(exerciseType: ExerciseType): string {
  return exerciseTypeIcons[exerciseType] || '📄';
}

/**
 * Get both label and icon for an exercise type
 * @param exerciseType - The exercise type code
 * @returns Object with label and icon
 */
export function getExerciseTypeInfo(exerciseType: ExerciseType) {
  return {
    label: formatExerciseType(exerciseType),
    icon: getExerciseTypeIcon(exerciseType)
  };
}