#!/usr/bin/env python3
"""
Utility script to generate password hashes for manual database entries.
Uses the same bcrypt configuration as the application.
"""

import bcrypt
import sys
import getpass


def hash_password(password: str) -> str:
    """
    Hash a password using bcrypt (same as the application).
    
    Args:
        password: Plain text password to hash
        
    Returns:
        Hashed password as string
    """
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')


def validate_password_strength(password: str) -> tuple[bool, str]:
    """
    Validate password meets strength requirements.
    
    Args:
        password: Password to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    
    if not any(c.isupper() for c in password):
        return False, "Password must contain at least one uppercase letter"
    
    if not any(c.islower() for c in password):
        return False, "Password must contain at least one lowercase letter"
    
    if not any(c.isdigit() for c in password):
        return False, "Password must contain at least one digit"
    
    special_chars = "!@#$%^&*(),.?\":{}|<>"
    if not any(c in special_chars for c in password):
        return False, f"Password must contain at least one special character: {special_chars}"
    
    return True, ""


def main():
    print("Password Hash Generator for Editor Accounts")
    print("==========================================")
    print()
    
    # Get password from user (hidden input)
    password = getpass.getpass("Enter password to hash: ")
    
    # Validate password strength
    is_valid, error_msg = validate_password_strength(password)
    if not is_valid:
        print(f"\nError: {error_msg}")
        sys.exit(1)
    
    # Generate hash
    hashed = hash_password(password)
    
    print("\nPassword hash generated successfully!")
    print("\nHashed password:")
    print(hashed)
    print("\nYou can use this hash in your SQL INSERT statement:")
    print(f"INSERT INTO editor_account (email, pwd_hash, ...) VALUES ('<EMAIL>', '{hashed}', ...);")


if __name__ == "__main__":
    main()