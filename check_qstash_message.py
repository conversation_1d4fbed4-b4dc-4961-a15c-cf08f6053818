#!/usr/bin/env python3
"""
Quick script to check if our QStash message exists
"""
import os
import httpx
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

QSTASH_TOKEN = os.getenv("QSTASH_TOKEN")
QSTASH_URL = os.getenv("QSTASH_URL", "https://qstash.upstash.io")
MESSAGE_ID = "msg_7YoJxFpwkEy5zBp3gfyDYeCbJtaE2xLxKrij2qHEufuGQUKM8iqQk"

def check_message():
    """Check if the specific message exists in QStash"""
    headers = {
        "Authorization": f"Bearer {QSTASH_TOKEN}"
    }
    
    # Check specific message
    message_url = f"{QSTASH_URL}/v2/messages/{MESSAGE_ID}"
    
    with httpx.Client() as client:
        try:
            response = client.get(message_url, headers=headers)
            print(f"Message check response: {response.status_code}")
            print(f"Response body: {response.text}")
            
            if response.status_code == 200:
                message_data = response.json()
                print(f"✅ Message found:")
                print(f"   - ID: {message_data.get('messageId')}")
                print(f"   - URL: {message_data.get('url')}")
                print(f"   - Scheduled for: {message_data.get('scheduleAt')}")
                print(f"   - Status: {message_data.get('state')}")
            elif response.status_code == 404:
                print("❌ Message not found in QStash")
            else:
                print(f"❓ Unexpected response: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error checking message: {e}")

def list_messages():
    """List all messages in QStash"""
    headers = {
        "Authorization": f"Bearer {QSTASH_TOKEN}"
    }
    
    messages_url = f"{QSTASH_URL}/v2/messages"
    
    with httpx.Client() as client:
        try:
            response = client.get(messages_url, headers=headers)
            print(f"\nAll messages response: {response.status_code}")
            
            if response.status_code == 200:
                messages = response.json()
                print(f"📝 Found {len(messages)} total messages:")
                for msg in messages[:5]:  # Show first 5
                    print(f"   - {msg.get('messageId')}: {msg.get('url')} (scheduled: {msg.get('scheduleAt')})")
                if len(messages) > 5:
                    print(f"   ... and {len(messages) - 5} more")
            else:
                print(f"❌ Failed to list messages: {response.text}")
                
        except Exception as e:
            print(f"❌ Error listing messages: {e}")

if __name__ == "__main__":
    print("🔍 Checking QStash message...")
    print(f"Token: {QSTASH_TOKEN[:20]}..." if QSTASH_TOKEN else "❌ No token found")
    print(f"QStash URL: {QSTASH_URL}")
    print(f"Message ID: {MESSAGE_ID}")
    
    check_message()
    list_messages()