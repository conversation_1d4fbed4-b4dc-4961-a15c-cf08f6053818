Metadata-Version: 2.1
Name: logtail-python
Version: 0.3.3
Summary: Logtail.com client library
Home-page: https://github.com/logtail/logtail-python
Download-URL: https://github.com/logtail/logtail-python/tarball/0.3.3
Author: Logtail
Author-email: <EMAIL>
License: ISC
Keywords: api,logtail,logging,client
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: ISC License (ISCL)
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Description-Content-Type: text/markdown
License-File: LICENSE.md
Requires-Dist: requests>=2.18.4
Requires-Dist: msgpack>=0.5.6

# [Better Stack](https://betterstack.com/logs) Python client

📣 Logtail is now part of Better Stack. [Learn more ⇗](https://betterstack.com/press/introducing-better-stack/)

[![Better Stack dashboard](https://github.com/logtail/logtail-python/assets/10132717/e2a1196b-7924-4abc-9b85-055e17b5d499)](https://betterstack.com/logs)

[![ISC License](https://img.shields.io/badge/license-ISC-ff69b4.svg)](LICENSE.md)
[![PyPI package](https://badge.fury.io/py/logtail-python.svg)](https://badge.fury.io/py/logtail-python)
![Tests](https://github.com/logtail/logtail-python/actions/workflows/main.yml/badge.svg?branch=master)

Experience SQL-compatible structured log management based on ClickHouse. [Learn more ⇗](https://betterstack.com/logs)

## Documentation

[Getting started ⇗](https://betterstack.com/docs/logs/python/)

## Need help?
Please let us know at [<EMAIL>](mailto:<EMAIL>). We're happy to help!

---

[ISC license](https://github.com/logtail/logtail-python/blob/master/LICENSE.md), [example project](https://github.com/logtail/logtail-python/tree/master/example-project)
