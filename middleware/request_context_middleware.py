# /Users/<USER>/Coding/project_edu/project_edu_backend/middleware/request_context_middleware.py
import uuid
import time
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.requests import Request
from starlette.responses import Response
from loguru import logger

class RequestContextMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        request_id = str(uuid.uuid4())
        
        # Initialize state attributes that might be set by other middleware/routes
        request.state.user_public_id = None
        request.state.user_type = None
        request.state.error_code_val = None
        # request.state.client_ip will be set by ClientIPMiddleware

        with logger.contextualize(request_id=request_id):
            # Get client_ip from state; ClientIPMiddleware should have set this.
            # Use a clear default if, for some reason, it's not set.
            client_ip_for_log = getattr(request.state, "client_ip", "ip_not_determined") 
            
            logger.info(
                f"Incoming request: {request.method} {request.url.path}",
                client_ip=client_ip_for_log 
            )
            start_time = time.time()

            response = await call_next(request)
            
            process_time_ms = (time.time() - start_time) * 1000

            log_details = {
                "method": request.method,
                "path": str(request.url),
                "status_code": response.status_code,
                "process_time_ms": round(process_time_ms, 2),
                # Get client_ip again for the final log, using a clear default.
                "client_ip": getattr(request.state, "client_ip", "ip_not_determined_final"), 
            }

            if request.state.user_public_id:
                log_details["user_id"] = str(request.state.user_public_id)
            if request.state.user_type:
                log_details["user_type"] = request.state.user_type
            if request.state.error_code_val:
                log_details["error_code"] = request.state.error_code_val

            if 200 <= response.status_code < 300:
                logger.info("Request finished successfully.", **log_details)
            elif 300 <= response.status_code < 400:
                logger.info("Request resulted in redirection.", **log_details)
            elif 400 <= response.status_code < 500:
                logger.warning("Request finished with client error.", **log_details)
            else: 
                logger.error("Request finished with server error.", **log_details)

            response.headers["X-Request-ID"] = request_id
            return response