# /Users/<USER>/Coding/project_edu/project_edu_backend/middleware/client_ip_middleware.py
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.requests import Request
from starlette.responses import Response
from typing import Optional
from core.config import settings

class ClientIPMiddleware(BaseHTTPMiddleware):
    """
    Middleware to extract the real client IP address from request headers.
    
    This middleware checks various headers in a configurable order to determine
    the actual client IP address, accounting for different deployment platforms
    and proxy configurations.
    """
    
    @staticmethod
    def is_private_ip(ip: str) -> bool:
        """Check if an IP address is in a private/internal range."""
        if not ip:
            return False
        return (
            ip.startswith("10.") 
            or ip.startswith("192.168.") 
            or any(ip.startswith(f"172.{i}.") for i in range(16, 32)) 
            or ip.startswith("127.")
            or ip == "::1"  # IPv6 localhost
        )
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        client_ip: Optional[str] = None
        
        # Check configured headers in order
        for header_name in settings.CLIENT_IP_HEADERS:
            header_value = request.headers.get(header_name)
            if header_value:
                # Special handling for X-Forwarded-For which can contain multiple IPs
                if header_name.lower() == "x-forwarded-for":
                    # X-Forwarded-For format: client, proxy1, proxy2, ...
                    # We want the leftmost IP based on TRUSTED_PROXY_DEPTH
                    ips = [ip.strip() for ip in header_value.split(",")]
                    # Get the IP at the trusted depth (counting from the right)
                    # If TRUSTED_PROXY_DEPTH is 1, we get the rightmost IP minus 1
                    # which should be the real client IP
                    if len(ips) >= settings.TRUSTED_PROXY_DEPTH:
                        # Get IP from the left side, accounting for proxy depth
                        # If there's 1 trusted proxy, we want the second-to-last IP
                        index = len(ips) - settings.TRUSTED_PROXY_DEPTH
                        if index > 0:
                            client_ip = ips[0]  # Always prefer the leftmost (original client)
                        else:
                            client_ip = ips[0]
                    else:
                        # Not enough IPs in the chain, just use the first one
                        client_ip = ips[0]
                else:
                    # For other headers, just use the value directly
                    client_ip = header_value.strip()
                
                # If we found a non-private IP, use it
                if client_ip and not self.is_private_ip(client_ip):
                    break
                # If it's private, continue checking other headers
                # but keep it as a fallback
        
        # Fallback to request.client.host if no valid header IP found
        if not client_ip or self.is_private_ip(client_ip):
            if request.client and request.client.host:
                # Only use this if we have no other option or if it's not private
                if not client_ip or not self.is_private_ip(request.client.host):
                    client_ip = request.client.host
        
        # Set the client IP in request state
        request.state.client_ip = client_ip if client_ip else "unknown"
        
        response = await call_next(request)
        return response