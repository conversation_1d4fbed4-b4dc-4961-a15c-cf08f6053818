from typing import Optional, Dict, Any
import jwt
from datetime import datetime, timedelta, UTC

from db.models.editorial import EditorAccount, EditorRole
from api.v1.app.auth.utils import create_access_token
from core.config.settings import settings


def create_editor_token(
    editor_id: int,
    editor_public_id: Optional[str] = None,
    email: Optional[str] = None,
    role: Optional[str] = None,
    expires_delta: Optional[timedelta] = None
) -> str:
    """
    Helper to create a JWT token for an editor account.
    If editor_public_id is not provided, it will use a default format.
    """
    if not editor_public_id:
        editor_public_id = f"editor_{editor_id}"
    
    if not email:
        email = f"editor{editor_id}@test.com"
    
    if not role:
        role = EditorRole.EDITOR.value
    
    token_data = {
        "editor_id": editor_id,
        "editor_public_id": editor_public_id,
        "email": email,
        "role": role,
        "user_type": "editor"
    }
    
    # Add expiration
    if expires_delta:
        expire = datetime.now(UTC) + expires_delta
    else:
        expire = datetime.now(UTC) + timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
    
    token_data["exp"] = expire
    
    # Create token using the same secret key and algorithm as EditorAuthDependency
    return jwt.encode(token_data, settings.SECRET_KEY, algorithm=settings.JWT_ALGORITHM)


class JWTTestHelper:
    """Helper class for JWT token testing"""
    
    @staticmethod
    def create_token(payload: Dict[str, Any], secret_key: Optional[str] = None) -> str:
        """Create a JWT token with the given payload"""
        if not secret_key:
            secret_key = settings.SECRET_KEY
        
        # Add default expiration if not present
        if "exp" not in payload:
            payload["exp"] = datetime.now(UTC) + timedelta(hours=1)
        
        return jwt.encode(payload, secret_key, algorithm="HS256")
    
    @staticmethod
    def create_editor_token(
        editor_id: int,
        editor_public_id: Optional[str] = None,
        email: Optional[str] = None,
        role: Optional[EditorRole] = None,
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """Create a JWT token for an editor account"""
        if not editor_public_id:
            editor_public_id = f"editor_{editor_id}"
        
        if not email:
            email = f"editor{editor_id}@test.com"
        
        role_value = role.value if isinstance(role, EditorRole) else (role or EditorRole.EDITOR.value)
        
        token_data = {
            "editor_id": editor_id,
            "editor_public_id": editor_public_id,
            "email": email,
            "role": role_value,
            "type": "editor_access"
        }
        
        # Add expiration
        if expires_delta:
            expire = datetime.now(UTC) + expires_delta
        else:
            expire = datetime.now(UTC) + timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
        
        token_data["exp"] = expire
        
        # Create token using the same secret key and algorithm as EditorAuthDependency
        return jwt.encode(token_data, settings.SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
    
    @staticmethod
    def create_expired_token(editor_id: int) -> str:
        """Create an expired JWT token for an editor"""
        return JWTTestHelper.create_editor_token(
            editor_id=editor_id,
            expires_delta=timedelta(hours=-1)
        )
    
    @staticmethod
    def create_invalid_token() -> str:
        """Create an invalid JWT token"""
        return "invalid.jwt.token"
    
    @staticmethod
    def decode_token(token: str, secret_key: Optional[str] = None) -> Dict[str, Any]:
        """Decode a JWT token"""
        if not secret_key:
            secret_key = settings.SECRET_KEY
        
        return jwt.decode(token, secret_key, algorithms=["HS256"])