from typing import Optional, List

from db.models.account import (
    Account as <PERSON><PERSON>AccountORM,
    ChildAccount as <PERSON>AccountORM,
    LanguageEnum,
)
from api.v1.app.auth.utils import create_access_token


def create_test_parent_auth_token(
    parent: ParentAccountORM, active_subscriptions: Optional[List[str]] = None
) -> str:
    """
    Helper to create a JWT token for a parent account, matching
    BaseAuthDependency expectations. active_subscriptions
    should be a list of ActiveSubscription public_id strings.
    """
    language_value = (
        parent.language.value
        if parent.language and isinstance(parent.language, LanguageEnum)
        else (parent.language if isinstance(parent.language, str) else "lu")
    )
    token_data = {
        "account_public_id": parent.public_id,
        "user_type": "parent",
        "email": parent.email,
        "language": language_value,
        "active_subscriptions": active_subscriptions or [],
    }
    return create_access_token(data=token_data)


def create_test_child_auth_token(
    child: ChildAccountORM, parent_public_id: Optional[str] = None
) -> str:
    """
    Helper to create a JWT token for a child account, matching
    BaseAuthDependency expectations.
    """
    token_data = {
        "account_public_id": child.public_id,
        "user_type": "child",
        "name": child.name,
        "parent_public_id": parent_public_id,
    }
    return create_access_token(data=token_data)
