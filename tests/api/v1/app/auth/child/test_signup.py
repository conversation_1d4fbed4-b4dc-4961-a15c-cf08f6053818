from sqlalchemy.orm import Session
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>
from fastapi import status
from jose import jwt
from core.config.settings import settings
from api.v1.app.auth.child.schemas.request import SignupRequest
from db.models.account import ChildAccount, Account
from db.models.content import Year
from sqlalchemy import select
import uuid
from fastapi import BackgroundTasks
from tests.data import constants

SIGNUP_URL = "/api/v1/auth/child/signup"

def test_child_signup_success(client, mocker: MockerFixture, db_session: Session):
    """Test successful child signup."""
    # Mock EmailService instance and its send method
    bg_spy = mocker.spy(BackgroundTasks, "add_task")


    # Fetch the first year from the database
    stmt = select(Year).limit(1)
    result = db_session.execute(stmt)
    mock_year = result.scalars().first()

    signup_data = SignupRequest(
        name=constants.AUTH_CHILD_NAME,
        pin=constants.AUTH_CHILD_PIN,
        email=constants.AUTH_CHILD_EMAIL,
        language=constants.AUTH_CHILD_LANGUAGE,
        school_year_public_id=mock_year.public_id,
        searchparams="{}"
    )

    response = client.post(SIGNUP_URL, json=signup_data.model_dump())

    assert response.status_code == status.HTTP_201_CREATED
    response_data = response.json()

    assert "access_token" in response_data

    decoded_token = jwt.decode(response_data["access_token"], settings.SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])


    stmt = select(ChildAccount).where(ChildAccount.email == signup_data.email)
    result = db_session.execute(stmt)
    child_account = result.scalar_one()

    assert isinstance(decoded_token["account_public_id"], str) and uuid.UUID(decoded_token["account_public_id"]) 
    assert decoded_token["user_type"] == "child"
    assert decoded_token["name"] == constants.AUTH_CHILD_NAME  # Use constant
    assert decoded_token.get("parent_public_id") is None

    bg_spy.assert_called_with(
        mocker.ANY,  # add_task() is a method, and as such its first argument is `self`
        mocker.ANY,
        to=signup_data.email,
        verification_code=child_account.verification_code,
        lang=signup_data.language,
    )


def test_child_signup_validation_error(client):
    """Test child signup with missing required fields (e.g., name)."""
    invalid_signup_data = {
        "pin": constants.AUTH_CHILD_PIN,  # Use constant
        "email": "<EMAIL>",
        "language": constants.AUTH_CHILD_LANGUAGE,  # Use constant
        "school_year_public_id": "year-1",  # Assuming year-1 exists from content_data
        "searchparams": "{}"
    }
    response = client.post(SIGNUP_URL, json=invalid_signup_data)

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    response_data = response.json()
    assert "detail" in response_data
    assert any(err["type"] == "missing" and "name" in err["loc"] for err in response_data["detail"])

def test_child_signup_existing_child_email(client, db_session: Session):
    """Test child signup with an email that already exists."""
    existing_email = constants.AUTH_CHILD_EMAIL  # Use constant

    # Fetch the first year from the database
    stmt = select(Year).limit(1)
    result = db_session.execute(stmt)
    mock_year = result.scalars().first()


    existing_child = ChildAccount(
        name=constants.AUTH_CHILD_NAME,
        email=existing_email,
        pin=constants.AUTH_CHILD_PIN,
        language=constants.AUTH_CHILD_LANGUAGE,
        is_verified=True,
        verification_code=None,
        year_id=mock_year.id
    )
    db_session.add(existing_child)
    db_session.commit()

    signup_data = SignupRequest(
        name=constants.AUTH_CHILD_NAME,
        pin=constants.AUTH_CHILD_PIN,
        email=existing_email,
        language=constants.AUTH_CHILD_LANGUAGE,
        school_year_public_id=mock_year.public_id,
        searchparams="{}"
    )

    response = client.post(SIGNUP_URL, json=signup_data.model_dump())

    assert response.status_code == status.HTTP_409_CONFLICT
    response_data = response.json()
    assert "error_code" in response_data
    assert "message" in response_data
    assert (
        "email already registered" in response_data["message"].lower() 
        or "e-mail address already exists" in response_data["message"].lower() 
        or "account with this email already exists" in response_data["message"].lower()
    )

def test_child_signup_existing_parent_email(client, db_session: Session):
    """Test child signup with an email that already exists for a parent account."""

    # Fetch the first year from the database
    stmt = select(Year).limit(1)
    result = db_session.execute(stmt)
    mock_year = result.scalars().first()


    existing_parent = Account(
        email=constants.AUTH_PARENT_EMAIL,
        pin_hash=constants.AUTH_PARENT_PIN_HASH,  # Use parent hash
        stripe_customer_id=constants.AUTH_PARENT_STRIPE_CUSTOMER_ID,  # Use parent stripe id
        language=constants.AUTH_PARENT_LANGUAGE,  # Use parent language
        public_id=constants.AUTH_PARENT_PUBLIC_ID  # Use parent public id
    )

    db_session.add(existing_parent)
    db_session.commit()

    signup_data = SignupRequest(
        name=constants.AUTH_CHILD_NAME,
        pin=constants.AUTH_CHILD_PIN,
        email=constants.AUTH_PARENT_EMAIL,
        language=constants.AUTH_CHILD_LANGUAGE,
        school_year_public_id=mock_year.public_id,
        searchparams="{}"
    )

    response = client.post(SIGNUP_URL, json=signup_data.model_dump())

    assert response.status_code == status.HTTP_409_CONFLICT
    response_data = response.json()

    assert "error_code" in response_data and response_data["error_code"] == "PARENT_ACCOUNT_ALREADY_EXISTS"