import pytest
from sqlalchemy.orm import Session
from fastapi import status
from jose import jwt

from core.config.settings import settings
from db.models import Account, ChildAccount, Year 
from api.v1.app.auth.child.schemas.response import ValidateSessionResponse
from tests.utils.auth_utils import create_test_parent_auth_token, create_test_child_auth_token
from tests.data import constants
from api.v1.common.schemas import AppErrorCode
from sqlalchemy import select 
from db.database import get_db 
from api.v1.app.auth.schemas.shared_schemas import UserTypeEnum

VALIDATE_SESSION_URL = "/api/v1/auth/child/validate-session"

@pytest.fixture
def verified_child_for_session_test(db_session: Session) -> ChildAccount:
    # Using existing constants for a verified child
    child = db_session.execute(
        select(ChildAccount).where(ChildAccount.public_id == constants.MOCK_CHILD_PUBLIC_ID)
    ).scalar_one_or_none()

    if not child:  # Should be seeded by mock_data_loader
        year = db_session.execute(select(Year).limit(1)).scalar_one()
        parent = db_session.execute(select(Account).where(
            Account.public_id == constants.MOCK_PARENT_PUBLIC_ID)).scalar_one()

        child = ChildAccount(
            name=constants.MOCK_CHILD_NAME,
            email=constants.MOCK_CHILD_EMAIL.lower(),
            pin=constants.MOCK_CHILD_PIN,
            language=constants.MOCK_CHILD_LANGUAGE,
            year_id=year.id,
            is_verified=True,
            public_id=constants.MOCK_CHILD_PUBLIC_ID,
            parent_account_id=parent.id  # Link to parent
        )
        db_session.add(child)
        db_session.commit()
        db_session.refresh(child)
    elif not child.parent_account_id:  # Ensure parent is linked if child exists
        stmt = select(Account).where(Account.public_id == constants.MOCK_PARENT_PUBLIC_ID)
        parent = db_session.execute(stmt).scalar_one()
        child.parent_account_id = parent.id
        db_session.commit()
        db_session.refresh(child)

    return child


def test_validate_child_session_success(client, verified_child_for_session_test: ChildAccount, db_session: Session):
    """Test successful child session validation."""
    # Ensure parent is loaded for parent_public_id in token
    db_session.refresh(verified_child_for_session_test, ['parent_account'])
    parent_public_id = (
        str(verified_child_for_session_test.parent_account.public_id) 
        if verified_child_for_session_test.parent_account 
        else None
    )

    child_token = create_test_child_auth_token(
        verified_child_for_session_test, 
        parent_public_id=parent_public_id
    )
    headers = {"x-auth-token": child_token}

    response = client.post(VALIDATE_SESSION_URL, headers=headers)

    assert response.status_code == status.HTTP_200_OK
    response_data = ValidateSessionResponse(**response.json())

    assert response_data.access_token is not None
    assert response_data.user is not None
    assert response_data.user.user_type == UserTypeEnum.CHILD  # Updated to use UserTypeEnum
    assert response_data.user.public_id == str(verified_child_for_session_test.public_id)
    # child_account_public_id is now just public_id on the user object
    assert response_data.user.name == verified_child_for_session_test.name  # Changed from first_name to name
    assert response_data.user.email == verified_child_for_session_test.email
    assert response_data.user.parent_public_id == parent_public_id
    assert isinstance(response_data.subscriptions, list)
    decoded_new_token = jwt.decode(
        response_data.access_token, 
        settings.SECRET_KEY, 
        algorithms=[settings.JWT_ALGORITHM]
    )
    assert (decoded_new_token["account_public_id"]
            == str(verified_child_for_session_test.public_id))
    assert decoded_new_token["user_type"] == "child"
    assert decoded_new_token["name"] == verified_child_for_session_test.name
    assert (decoded_new_token["parent_public_id"]
            == parent_public_id)


def test_validate_child_session_unauthorized_no_token(client):
    """Test validate session without auth token."""
    response = client.post(VALIDATE_SESSION_URL)
    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    assert response.json()["error_code"] == AppErrorCode.AUTHENTICATION_REQUIRED

def test_validate_child_session_unauthorized_invalid_token(client):
    """Test validate session with an invalid token."""
    headers = {"x-auth-token": "invalidtoken123"}
    response = client.post(VALIDATE_SESSION_URL, headers=headers)
    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    assert response.json()["error_code"] == AppErrorCode.AUTHENTICATION_ERROR


def test_validate_child_session_unauthorized_parent_token(client, db_session: Session):
    """Test validate session with a parent token."""
    parent_user = db_session.execute(
        select(Account).where(Account.public_id == constants.MOCK_PARENT_PUBLIC_ID)
    ).scalar_one()
    parent_token = create_test_parent_auth_token(parent_user)
    headers = {"x-auth-token": parent_token}

    response = client.post(VALIDATE_SESSION_URL, headers=headers)
    assert response.status_code == status.HTTP_401_UNAUTHORIZED  # AuthDependency user_type check
    assert response.json()["error_code"] == AppErrorCode.PERMISSION_DENIED


def test_validate_child_session_account_not_found_in_db(
    client, 
    verified_child_for_session_test: ChildAccount
):
    """Test validate_session when child account is not found (e.g., deleted after token issuance)."""
    from sqlalchemy import delete
    from tests.utils.auth_utils import create_test_child_auth_token   # Import here

    # Generate token before deleting the account
    child_token = create_test_child_auth_token(verified_child_for_session_test)
    headers = {"x-auth-token": child_token}

    db_gen = client.app.dependency_overrides[get_db]()  # Get the generator
    temp_db = next(db_gen)  # Get the actual session
    try:
        temp_db.execute(delete(ChildAccount).where(ChildAccount.id == verified_child_for_session_test.id))
        temp_db.commit()  # Ensure deletion is committed
    finally:
        # It's important to close the session obtained from the generator
        # to prevent issues with the test client's own session management.
        # However, the main db_session fixture handles rollback, so direct close might be okay.
        # For safety, let's assume the test client's override mechanism handles session lifecycle.
        # If issues persist, this might need adjustment based on how TestClient manages overridden deps.
        pass  # db_session fixture will handle rollback of the outer transaction

    response = client.post(VALIDATE_SESSION_URL, headers=headers)

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json()["error_code"] == AppErrorCode.USER_NOT_FOUND