import uuid

import pytest
from fastapi import BackgroundTasks, status
from jose import jwt
from pytest_mock import MockerFix<PERSON>
from sqlalchemy import select
from sqlalchemy.orm import Session

from api.v1.app.auth.child.schemas.request import VerifyParentAssignmentRequest
from api.v1.app.auth.child.schemas.response import VerifyParentAssignmentResponse
# Import UserTypeEnum for assertions
from api.v1.app.auth.schemas.shared_schemas import UserTypeEnum
from core.config.settings import settings
from db.models.account import Account, ChildAccount, LanguageEnum, ParentChildAssignment
from db.models.content import Year

from tests.data import constants
from api.v1.common.schemas import AppErrorCode

VERIFY_PARENT_ASSIGNMENT_URL = "/api/v1/auth/child/verify-parent-account"


@pytest.fixture
def child_for_assign(db_session: Session) -> ChildAccount:
    """Fixture to create a child account for assignment tests."""
    year = db_session.execute(select(Year).limit(1)).scalar_one()
    child = ChildAccount(
        name="ChildForAssign",
        email=f"child_for_assign_{uuid.uuid4().hex[:4]}@example.com",
        pin="5555",
        language=LanguageEnum.EN,
        year_id=year.id,
        is_verified=False,  # Typically not verified before assignment
        public_id=str(uuid.uuid4())
    )
    db_session.add(child)
    db_session.commit()
    db_session.refresh(child)
    return child


@pytest.fixture
def mock_parent_creation_services(mocker: MockerFixture):
    """Mocks services used by create_parent_account utility."""
    mocker.patch('stripe.Customer.create', return_value=mocker.Mock(id="cus_teststripeid_verifyassign"))
    mocker.patch('stripe.Customer.delete', return_value=mocker.Mock())
    return 


def test_verify_parent_assignment_new_parent_success(client, db_session: Session, mocker: MockerFixture):
    """Test successful parent assignment verification where parent account is newly created."""
    # Spy on BackgroundTasks from the route
    mocker.spy(BackgroundTasks, "add_task")

    year = db_session.execute(select(Year).limit(1)).scalar_one()
    child_user = ChildAccount(
        name=constants.AUTH_CHILD_NAME, 
        email=constants.AUTH_CHILD_EMAIL, 
        pin=constants.AUTH_CHILD_PIN,
        language=constants.AUTH_CHILD_LANGUAGE, 
        year_id=year.id, 
        is_verified=False, 
        public_id=constants.AUTH_CHILD_PUBLIC_ID
    )
    db_session.add(child_user)
    db_session.commit()
    db_session.refresh(child_user)

    assignment = ParentChildAssignment(
        parent_email=constants.AUTH_PARENT_EMAIL,
        child_account_id=child_user.id,
        verification_code=constants.AUTH_PARENT_VERIFICATION_CODE,
        language=constants.AUTH_PARENT_LANGUAGE
    )
    db_session.add(assignment)
    db_session.commit()

    request_data = VerifyParentAssignmentRequest(
        email=constants.AUTH_PARENT_EMAIL,
        verification_code=constants.AUTH_PARENT_VERIFICATION_CODE,
        login_pin=constants.AUTH_PARENT_PIN
    )
    response = client.post(VERIFY_PARENT_ASSIGNMENT_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_200_OK
    response_body = VerifyParentAssignmentResponse(**response.json())

    # Verify parent account created and linked
    parent_account = db_session.execute(
        select(Account).where(Account.email == constants.AUTH_PARENT_EMAIL)
    ).scalar_one_or_none()
    assert parent_account is not None
    assert parent_account.is_verified is True
    
    # Assertions for the new 'user' object in response
    user_info = response_body.user
    assert user_info.public_id == str(parent_account.public_id)
    assert user_info.email == parent_account.email
    assert user_info.user_type == UserTypeEnum.PARENT
    assert user_info.language == parent_account.language # Direct enum comparison
    # Removed first_name assertion - ParentUser doesn't have this field

    # Assertions for 'subscriptions' at the root level
    assert isinstance(response_body.subscriptions, list)
    assert response_body.subscriptions == [] # New parent has no subscriptions

    db_session.refresh(child_user)
    assert child_user.parent_account_id == parent_account.id
    assert child_user.is_verified is True

    # Verify assignment is deleted
    deleted_assignment = db_session.execute(
        select(ParentChildAssignment)
        .where(ParentChildAssignment.id == assignment.id)
    ).scalar_one_or_none()
    assert deleted_assignment is None

    # Verify token
    decoded_token = jwt.decode(
        response_body.access_token,
        settings.SECRET_KEY,
        algorithms=[settings.JWT_ALGORITHM]
    )
    assert decoded_token["account_public_id"] == str(parent_account.public_id)
    assert decoded_token["user_type"] == "parent"
    assert decoded_token["email"] == constants.AUTH_PARENT_EMAIL.lower()
    assert decoded_token["language"] == parent_account.language.value # Token stores string

def test_verify_parent_assignment_existing_parent_success(
    client,
    db_session: Session,
    mocker: MockerFixture
):
    """Test successful parent assignment verification for an existing parent account."""
    mocker.spy(BackgroundTasks, "add_task")

    year = db_session.execute(select(Year).limit(1)).scalar_one()
    child_user = ChildAccount(
        name="VerifyAssign ChildExistingParent", 
        email="<EMAIL>", 
        pin="5678",
        language=LanguageEnum.FR, 
        year_id=year.id, 
        is_verified=False, 
        public_id=str(uuid.uuid4())
    )
    db_session.add(child_user)
    
    parent_email_existing = constants.AUTH_PARENT_EMAIL
    existing_parent = Account(
        email=parent_email_existing,
        language=constants.AUTH_PARENT_LANGUAGE,
        pin_hash=constants.AUTH_PARENT_PIN_HASH,
        is_verified=False,
        public_id=constants.AUTH_PARENT_PUBLIC_ID,
        stripe_customer_id=constants.AUTH_PARENT_STRIPE_CUSTOMER_ID
    )
    db_session.add(existing_parent)
    db_session.commit()
    db_session.refresh(child_user)
    db_session.refresh(existing_parent)

    verification_code = constants.AUTH_PARENT_VERIFICATION_CODE
    assignment = ParentChildAssignment(
        parent_email=parent_email_existing,
        child_account_id=child_user.id,
        verification_code=verification_code,
        language=constants.AUTH_PARENT_LANGUAGE
    )
    db_session.add(assignment)
    db_session.commit()

    request_data = VerifyParentAssignmentRequest(
        email=parent_email_existing,
        verification_code=verification_code,
        login_pin=constants.AUTH_PARENT_PIN
    )
    response = client.post(VERIFY_PARENT_ASSIGNMENT_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_200_OK
    response_body = VerifyParentAssignmentResponse(**response.json())
    assert response_body.message == "Account linked successfully."
    
    user_info = response_body.user
    assert user_info.public_id == str(existing_parent.public_id)
    assert user_info.email == existing_parent.email
    assert user_info.user_type == UserTypeEnum.PARENT
    assert user_info.language == existing_parent.language

    assert isinstance(response_body.subscriptions, list)
    # Assuming existing parent has no subscriptions in this test setup
    assert response_body.subscriptions == []
    
    db_session.refresh(existing_parent)
    assert existing_parent.is_verified is True
    db_session.refresh(child_user)
    assert child_user.parent_account_id == existing_parent.id
    assert child_user.is_verified is True

    deleted_assignment = db_session.execute(
        select(ParentChildAssignment)
        .where(ParentChildAssignment.id == assignment.id)
    ).scalar_one_or_none()
    assert deleted_assignment is None

def test_verify_parent_assignment_invalid_code(
    client,
    db_session: Session,
    child_for_assign: ChildAccount
):
    """Test parent assignment verification with an invalid code."""
    parent_email = constants.AUTH_PARENT_EMAIL
    assignment = ParentChildAssignment(
        parent_email=parent_email,
        child_account_id=child_for_assign.id,
        verification_code=constants.AUTH_PARENT_VERIFICATION_CODE,
        language=constants.AUTH_PARENT_LANGUAGE
    )
    db_session.add(assignment)
    db_session.commit()

    request_data = VerifyParentAssignmentRequest(
        email=parent_email,
        verification_code="invalidcode",
        login_pin=constants.AUTH_PARENT_PIN
    )
    response = client.post(VERIFY_PARENT_ASSIGNMENT_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json()["error_code"] == AppErrorCode.ASSIGNMENT_NOT_FOUND_OR_INVALID_CODE
    assert "Invalid verification code or assignment not found" in response.json()["message"]

def test_verify_parent_assignment_no_assignment_found(client):
    """Test parent assignment verification when no assignment record exists."""
    request_data = VerifyParentAssignmentRequest(
        email=constants.AUTH_PARENT_EMAIL,
        verification_code=constants.AUTH_PARENT_VERIFICATION_CODE,
        login_pin=constants.AUTH_PARENT_PIN
    )
    response = client.post(VERIFY_PARENT_ASSIGNMENT_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json()["error_code"] == AppErrorCode.ASSIGNMENT_NOT_FOUND_OR_INVALID_CODE
    assert "Invalid verification code or assignment not found" in response.json()["message"]