from sqlalchemy.orm import Session
from pytest_mock import Mo<PERSON><PERSON><PERSON><PERSON>
from fastapi import status, BackgroundTasks
from api.v1.app.auth.child.schemas.request import AssignParentRequest
from api.v1.app.auth.child.schemas.response import AssignParentResponse
from db.models.account import ChildAccount, Account, ParentChildAssignment
from db.models.content import Year
from sqlalchemy import select
from tests.data import constants
from api.v1.common.schemas import AppErrorCode

RESEND_PARENT_ASSIGNMENT_CODE_URL = "/api/v1/auth/child/resend-parent-assignment-code"

def test_resend_parent_assignment_code_success(client, db_session: Session, mocker: MockerFixture):
    """Test successful resend_parent_assignment_code. We assume the parent account already exists."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")

    year = db_session.execute(select(Year).limit(1)).scalar_one()
    child_user = ChildAccount(
        name=constants.AUTH_CHILD_NAME,
        email=constants.AUTH_CHILD_EMAIL,
        pin=constants.AUTH_CHILD_PIN,
        language=constants.AUTH_CHILD_LANGUAGE,
        year_id=year.id,
        is_verified=True,
        public_id=constants.AUTH_CHILD_PUBLIC_ID
    )
    db_session.add(child_user)
    db_session.commit()
    db_session.refresh(child_user)

    assignment = ParentChildAssignment(
        parent_email=constants.AUTH_PARENT_EMAIL,
        child_account_id=child_user.id,
        verification_code=constants.AUTH_PARENT_VERIFICATION_CODE,
        language=constants.AUTH_PARENT_LANGUAGE
    )
    db_session.add(assignment)
    db_session.commit()

    parent_account = Account(
        email=constants.AUTH_PARENT_EMAIL,
        language=constants.AUTH_PARENT_LANGUAGE,
        pin_hash=constants.AUTH_PARENT_PIN_HASH,
        is_verified=True,
        public_id=constants.AUTH_PARENT_PUBLIC_ID,
        stripe_customer_id=constants.AUTH_PARENT_STRIPE_CUSTOMER_ID
    )
    db_session.add(parent_account)
    db_session.commit()

    request_data = AssignParentRequest(
        parent_email=constants.AUTH_PARENT_EMAIL,
        language=constants.AUTH_PARENT_LANGUAGE # Pass string value of enum
    )
    response = client.post(
        RESEND_PARENT_ASSIGNMENT_CODE_URL,
        json=request_data.model_dump()
    )

    assert response.status_code == status.HTTP_200_OK
    response_body = AssignParentResponse(**response.json())
    assert response_body.existing_parent_account is True
    assert response_body.message == "Verification code sent"


    bg_spy.assert_called_once_with(
        mocker.ANY,
        mocker.ANY,
        to=constants.AUTH_PARENT_EMAIL,
        verification_code=constants.AUTH_PARENT_VERIFICATION_CODE,
        lang=constants.AUTH_PARENT_LANGUAGE # Pass string value of enum
    )

def test_resend_parent_assignment_code_no_assignment_found(client, mocker: MockerFixture):
    """Test resend_parent_assignment_code when no assignment exists for the parent email."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")
    request_data = AssignParentRequest(
        parent_email=constants.AUTH_PARENT_EMAIL,
        language=constants.AUTH_PARENT_LANGUAGE # Pass string value of enum
    )
    response = client.post(
        RESEND_PARENT_ASSIGNMENT_CODE_URL,
        json=request_data.model_dump()
    )

    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.ASSIGNMENT_NOT_FOUND_OR_INVALID_CODE
    bg_spy.assert_not_called()

def test_resend_parent_assignment_code_parent_does_not_exist(client, db_session: Session, mocker: MockerFixture):
    """Test resend_parent_assignment_code when assignment exists but parent account does not."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")

    year = db_session.execute(select(Year).limit(1)).scalar_one()
    child_user = ChildAccount(
        name=constants.AUTH_CHILD_NAME,
        email=constants.AUTH_CHILD_EMAIL,
        pin=constants.AUTH_CHILD_PIN,
        language=constants.AUTH_CHILD_LANGUAGE,
        year_id=year.id,
        is_verified=True,
        public_id=constants.AUTH_CHILD_PUBLIC_ID
    )
    db_session.add(child_user)
    db_session.commit()
    db_session.refresh(child_user)

    assignment = ParentChildAssignment(
        parent_email=constants.AUTH_PARENT_EMAIL,
        child_account_id=child_user.id,
        verification_code=constants.AUTH_PARENT_VERIFICATION_CODE,
        language=constants.AUTH_PARENT_LANGUAGE
    )
    db_session.add(assignment)
    db_session.commit()

    request_data = AssignParentRequest(
        parent_email=constants.AUTH_PARENT_EMAIL,
        language=constants.AUTH_PARENT_LANGUAGE # Pass string value of enum
    )
    response = client.post(
        RESEND_PARENT_ASSIGNMENT_CODE_URL,
        json=request_data.model_dump()
    )

    assert response.status_code == status.HTTP_200_OK
    response_body = AssignParentResponse(**response.json())
    assert response_body.existing_parent_account is False

    bg_spy.assert_called_once()