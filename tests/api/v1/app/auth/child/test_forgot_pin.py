from sqlalchemy.orm import Session
from pytest_mock import Mo<PERSON><PERSON>ix<PERSON>
from fastapi import status
from api.v1.app.auth.child.schemas.request import ForgotPinRequest
from api.v1.app.auth.child.schemas.response import ForgotPinResponse
from db.models.account import ChildAccount
from db.models.content import Year
from sqlalchemy import select
from fastapi import BackgroundTasks
from tests.data import constants
from api.v1.common.schemas import AppErrorCode

FORGOT_PIN_URL = "/api/v1/auth/child/forgot-pin"

def test_forgot_pin_success_sends_email(client, db_session: Session, mocker: MockerFixture):
    """Test successful forgot_pin request, expects email to be sent."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")

    year = db_session.execute(select(Year).limit(1)).scalar_one()

    child = ChildAccount(
        name=constants.AUTH_CHILD_NAME,
        email=constants.AUTH_CHILD_EMAIL,
        pin=constants.AUTH_CHILD_PIN,
        language=constants.AUTH_CHILD_LANGUAGE,
        year_id=year.id,
        is_verified=True,
        public_id=constants.AUTH_CHILD_PUBLIC_ID,
        verification_code=constants.AUTH_CHILD_VERIFICATION_CODE
    )
    db_session.add(child)
    db_session.commit()
    db_session.refresh(child)

    request_data = ForgotPinRequest(email=constants.AUTH_CHILD_EMAIL)
    response = client.post(FORGOT_PIN_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_200_OK
    # Validate with Pydantic model
    response_body = ForgotPinResponse(**response.json())
    assert response_body.message == "Pin code sent"

    bg_spy.assert_called_with(
        mocker.ANY,
        mocker.ANY,
        to=constants.AUTH_CHILD_EMAIL,
        pin=constants.AUTH_CHILD_PIN,
        lang=constants.AUTH_CHILD_LANGUAGE,
    )


def test_forgot_pin_child_not_found(client):
    """Test forgot_pin for a non-existent child account."""
    request_data = ForgotPinRequest(email="<EMAIL>")
    response = client.post(FORGOT_PIN_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.CHILD_ACCOUNT_NOT_FOUND