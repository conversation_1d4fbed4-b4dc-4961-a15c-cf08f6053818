from sqlalchemy.orm import Session
from pytest_mock import Mo<PERSON><PERSON><PERSON><PERSON>
from fastapi import status, BackgroundTasks
from api.v1.app.auth.child.schemas.request import ResendVerificationCodeRequest
from db.models.account import ChildAccount
from db.models.content import Year
from sqlalchemy import select
from api.v1.common.schemas import AppErrorCode
from tests.data import constants

RESEND_VERIFICATION_CODE_URL = "/api/v1/auth/child/resend-verification-code"

def test_resend_verification_code_success(client, db_session: Session, mocker: MockerFixture):
    """Test successful resend_verification_code for an unverified child."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")


    year = db_session.execute(select(Year).limit(1)).scalar_one()

    child = ChildAccount(
        name=constants.AUTH_CHILD_NAME,
        email=constants.AUTH_CHILD_EMAIL,
        pin=constants.AUTH_CHILD_PIN,
        language=constants.AUTH_CHILD_LANGUAGE,
        year_id=year.id,
        is_verified=False,
        verification_code=constants.AUTH_CHILD_VERIFICATION_CODE,
        public_id=constants.AUTH_CHILD_PUBLIC_ID
    )
    db_session.add(child)
    db_session.commit()

    request_data = ResendVerificationCodeRequest(
        email=constants.AUTH_CHILD_EMAIL,
        language=constants.AUTH_CHILD_LANGUAGE
    )
    response = client.post(RESEND_VERIFICATION_CODE_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_200_OK
    
    bg_spy.assert_called_once_with(
        mocker.ANY,
        mocker.ANY,
        to=constants.AUTH_CHILD_EMAIL,
        verification_code=constants.AUTH_CHILD_VERIFICATION_CODE,
        lang=constants.AUTH_CHILD_LANGUAGE
    )

def test_resend_verification_code_child_not_found(client, mocker: MockerFixture):
    """Test resend_verification_code for a non-existent child account."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")
    request_data = ResendVerificationCodeRequest(
        email="<EMAIL>",
        language=constants.AUTH_CHILD_LANGUAGE
    )
    response = client.post(RESEND_VERIFICATION_CODE_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.CHILD_ACCOUNT_NOT_FOUND
    bg_spy.assert_not_called()

def test_resend_verification_code_already_verified(client, db_session: Session, mocker: MockerFixture):
    """Test resend_verification_code for an already verified child account."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")
    year = db_session.execute(select(Year).limit(1)).scalar_one()

    child = ChildAccount(
        name=constants.AUTH_CHILD_NAME,
        email=constants.AUTH_CHILD_EMAIL,
        pin=constants.AUTH_CHILD_PIN,
        language=constants.AUTH_CHILD_LANGUAGE,
        year_id=year.id,
        is_verified=True,  # Already verified
        verification_code=constants.AUTH_CHILD_VERIFICATION_CODE,
        public_id=constants.AUTH_CHILD_PUBLIC_ID
    )
    db_session.add(child)
    db_session.commit()

    request_data = ResendVerificationCodeRequest(
        email=constants.AUTH_CHILD_EMAIL,
        language=constants.AUTH_CHILD_LANGUAGE
    )
    response = client.post(RESEND_VERIFICATION_CODE_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_409_CONFLICT
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.ACCOUNT_ALREADY_VERIFIED
    bg_spy.assert_not_called()

def test_resend_verification_code_no_code_on_record_error(
    client,
    db_session: Session,
    mocker: MockerFixture
):
    """Test resend_verification_code when child is unverified but has no code (should be service error)."""
    # This scenario tests if the service correctly handles an unexpected state.
    bg_spy = mocker.spy(BackgroundTasks, "add_task")
    year = db_session.execute(select(Year).limit(1)).scalar_one()

    child = ChildAccount(
        name=constants.AUTH_CHILD_NAME,
        email=constants.AUTH_CHILD_EMAIL,
        pin=constants.AUTH_CHILD_PIN,
        language=constants.AUTH_CHILD_LANGUAGE,
        year_id=year.id,
        is_verified=False,
        verification_code=None,  # No code on record
        public_id=constants.AUTH_CHILD_PUBLIC_ID
    )
    db_session.add(child)
    db_session.commit()

    request_data = ResendVerificationCodeRequest(
        email=constants.AUTH_CHILD_EMAIL,
        language=constants.AUTH_CHILD_LANGUAGE
    )
    response = client.post(RESEND_VERIFICATION_CODE_URL, json=request_data.model_dump())
    
    # The service ResendVerificationService raises ServiceError in this case.
    # The default exception handler for ServiceError might return 500.
    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.SERVICE_ERROR
    bg_spy.assert_not_called()  # Email should not be sent if service errors out before