from sqlalchemy.orm import Session
from pytest_mock import Mo<PERSON><PERSON><PERSON><PERSON>
from fastapi import status, BackgroundTasks
from api.v1.app.auth.child.schemas.request import CheckVerificationStatusRequest
from api.v1.app.auth.child.schemas.response import CheckVerificationStatusResponse
from db.models.account import ChildAccount
from db.models.content import Year
from sqlalchemy import select
from api.v1.common.schemas import AppErrorCode
from tests.data import constants

CHECK_VERIFICATION_STATUS_URL = "/api/v1/auth/child/check-verification-status"

def test_check_verification_status_child_verified(client, db_session: Session, mocker: MockerFixture):
    """Test check_verification_status for an already verified child."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")
    year = db_session.execute(select(Year).limit(1)).scalar_one()

    child = ChildAccount(
        name=constants.AUTH_CHILD_NAME,
        email=constants.AUTH_CHILD_EMAIL,
        pin=constants.AUTH_CHILD_PIN,
        language=constants.AUTH_CHILD_LANGUAGE,
        year_id=year.id,
        is_verified=True,
        public_id=constants.AUTH_CHILD_PUBLIC_ID
    )
    db_session.add(child)
    db_session.commit()

    request_data = CheckVerificationStatusRequest(
        email=constants.AUTH_CHILD_EMAIL,
        language=constants.AUTH_CHILD_LANGUAGE
    )
    response = client.post(CHECK_VERIFICATION_STATUS_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_200_OK
    response_body = CheckVerificationStatusResponse(**response.json())
    assert response_body.is_verified is True
    bg_spy.assert_not_called()

def test_check_verification_status_child_not_verified_sends_email(
    client,
    db_session: Session,
    mocker: MockerFixture
):
    """Test check_verification_status for a non-verified child, expects email to be sent."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")
    year = db_session.execute(select(Year).limit(1)).scalar_one()

    child = ChildAccount(
        name=constants.AUTH_CHILD_NAME,
        email=constants.AUTH_CHILD_EMAIL,
        pin=constants.AUTH_CHILD_PIN,
        language=constants.AUTH_CHILD_LANGUAGE,
        year_id=year.id,
        is_verified=False,
        verification_code=constants.AUTH_CHILD_VERIFICATION_CODE,
        public_id=constants.AUTH_CHILD_PUBLIC_ID
    )
    db_session.add(child)
    db_session.commit()

    request_data = CheckVerificationStatusRequest(
        email=constants.AUTH_CHILD_EMAIL,
        language=constants.AUTH_CHILD_LANGUAGE
    )
    response = client.post(CHECK_VERIFICATION_STATUS_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_200_OK
    response_body = CheckVerificationStatusResponse(**response.json())
    assert response_body.is_verified is False
    
    bg_spy.assert_called_with(
        mocker.ANY,
        mocker.ANY,
        to=constants.AUTH_CHILD_EMAIL,
        verification_code=constants.AUTH_CHILD_VERIFICATION_CODE,
        lang=constants.AUTH_CHILD_LANGUAGE,
    )

def test_check_verification_status_child_not_verified_generates_code(
    client,
    db_session: Session,
    mocker: MockerFixture
):
    """Test check_verification_status for a non-verified child without a code, expects code generation and email."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")
    year = db_session.execute(select(Year).limit(1)).scalar_one()

    child = ChildAccount(
        name=constants.AUTH_CHILD_NAME,
        email=constants.AUTH_CHILD_EMAIL,
        pin=constants.AUTH_CHILD_PIN,
        language=constants.AUTH_CHILD_LANGUAGE,
        year_id=year.id,
        is_verified=False,
        verification_code=None,
        public_id=constants.AUTH_CHILD_PUBLIC_ID
    )
    db_session.add(child)
    db_session.commit()

    request_data = CheckVerificationStatusRequest(
        email=constants.AUTH_CHILD_EMAIL,
        language=constants.AUTH_CHILD_LANGUAGE
    )
    response = client.post(CHECK_VERIFICATION_STATUS_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_200_OK
    response_body = CheckVerificationStatusResponse(**response.json())
    assert response_body.is_verified is False
    
    db_session.refresh(child)
    assert child.verification_code is not None
    assert len(child.verification_code) == 6

    bg_spy.assert_called_with(
        mocker.ANY,
        mocker.ANY,
        to=constants.AUTH_CHILD_EMAIL,
        verification_code=mocker.ANY,
        lang=constants.AUTH_CHILD_LANGUAGE,
    )

def test_check_verification_status_child_not_found(
    client,
    mocker: MockerFixture
):
    """Test check_verification_status for a non-existent child account."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")
    request_data = CheckVerificationStatusRequest(
        email="<EMAIL>",
        language=constants.AUTH_CHILD_LANGUAGE
    )
    response = client.post(CHECK_VERIFICATION_STATUS_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.CHILD_ACCOUNT_NOT_FOUND
    bg_spy.assert_not_called()