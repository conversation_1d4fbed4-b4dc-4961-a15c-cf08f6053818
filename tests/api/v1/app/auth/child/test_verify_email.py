import uuid

import pytest
from fastapi import status
from sqlalchemy.orm import Session
from sqlalchemy import select

from api.v1.app.auth.parent.schemas.request import VerifyAccountRequest
# Updated import for VerifyAccountResponse
from api.v1.app.auth.child.schemas.response import VerifyAccountResponse
from api.v1.common.schemas import AppErrorCode
from db.models import Account, ChildAccount, Year
from tests.data import constants

VERIFY_EMAIL_URL = "/api/v1/auth/child/verify-email"

@pytest.fixture
def unverified_child(db_session: Session) -> ChildAccount:
    child = ChildAccount(
        email=constants.AUTH_CHILD_EMAIL.lower(),
        language=constants.AUTH_CHILD_LANGUAGE,
        name=constants.AUTH_CHILD_NAME,
        pin=constants.AUTH_CHILD_PIN,
        is_verified=False,
        verification_code=constants.AUTH_CHILD_VERIFICATION_CODE,
        public_id=constants.AUTH_CHILD_PUBLIC_ID,
    )
    db_session.add(child)
    db_session.commit()
    db_session.refresh(child)
    return child

def test_child_verify_email_success(client, db_session: Session):
    """Test successful child email verification."""
  
    first_year = db_session.execute(select(Year).limit(1)).scalar_one_or_none()

    new_child_account = ChildAccount(
        email=constants.AUTH_CHILD_EMAIL.lower(),
        name=constants.AUTH_CHILD_NAME,
        pin=constants.AUTH_CHILD_PIN,
        language=constants.AUTH_CHILD_LANGUAGE,
        is_verified=False,
        verification_code=constants.AUTH_CHILD_VERIFICATION_CODE,
        public_id=constants.AUTH_CHILD_PUBLIC_ID,
        year_id=first_year.id
    )
     
    db_session.add(new_child_account)
    db_session.commit()
    db_session.refresh(new_child_account)

    verify_data = VerifyAccountRequest(
        email=new_child_account.email,
        verification_code=new_child_account.verification_code
    )
    response = client.post(VERIFY_EMAIL_URL, json=verify_data.model_dump())

    assert response.status_code == status.HTTP_200_OK

    db_session.refresh(new_child_account)
    assert new_child_account.is_verified is True

def test_child_verify_email_account_not_found(client):
    """Test email verification for a non-existent child account."""
    verify_data = VerifyAccountRequest(
        email="<EMAIL>", 
        verification_code=constants.AUTH_CHILD_VERIFICATION_CODE 
    )
    response = client.post(VERIFY_EMAIL_URL, json=verify_data.model_dump())

    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.CHILD_ACCOUNT_NOT_FOUND

def test_child_verify_email_already_verified(client, db_session: Session):
    """Test email verification for an already verified child account."""
    child = ChildAccount(
        email="<EMAIL>", 
        name=constants.AUTH_CHILD_NAME,
        pin=constants.AUTH_CHILD_PIN,
        language=constants.AUTH_CHILD_LANGUAGE,  
        is_verified=True,  
        verification_code=constants.AUTH_CHILD_VERIFICATION_CODE,  
        public_id=str(uuid.uuid4()),
    )
    # Need a year for the child account
    year = db_session.execute(select(Year).limit(1)).scalar_one_or_none()
    if year:
        child.year_id = year.id
    else: # Create a dummy year if none exists
        dummy_year = Year(name="Dummy Year for Test", public_id=str(uuid.uuid4()))
        db_session.add(dummy_year)
        db_session.flush()
        child.year_id = dummy_year.id

    db_session.add(child)
    db_session.commit()

    verify_data = VerifyAccountRequest(
        email="<EMAIL>",
        verification_code=constants.AUTH_CHILD_VERIFICATION_CODE
    )
    response = client.post(VERIFY_EMAIL_URL, json=verify_data.model_dump())

    assert response.status_code == status.HTTP_409_CONFLICT
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.ACCOUNT_ALREADY_VERIFIED

def test_child_verify_email_incorrect_code(client, unverified_child: ChildAccount):
    """Test email verification with an incorrect verification code."""
    verify_data = VerifyAccountRequest(
        email=unverified_child.email,
        verification_code="777777"  # Incorrect code
    )
    response = client.post(VERIFY_EMAIL_URL, json=verify_data.model_dump())

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.INVALID_VERIFICATION_CODE