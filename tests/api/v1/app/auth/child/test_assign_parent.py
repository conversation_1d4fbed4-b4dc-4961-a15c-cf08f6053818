from sqlalchemy.orm import Session
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>
from fastapi import status, BackgroundTasks
import uuid
from api.v1.app.auth.child.schemas.request import AssignParentRequest
# Updated import for AssignParentResponse
from api.v1.app.auth.child.schemas.response import AssignParentResponse
from db.models.account import ChildAccount, Account, ParentChildAssignment
from db.models.content import Year
from sqlalchemy import select
from api.v1.app.auth.utils import create_access_token
from tests.utils.auth_utils import create_test_child_auth_token
from tests.data import constants
from api.v1.common.schemas import AppErrorCode 

ASSIGN_PARENT_URL = "/api/v1/auth/child/assign-parent"


def test_assign_parent_success_new_assignment(client, db_session: Session, mocker: MockerFixture):
    """Test successful parent assignment creating a new assignment."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")

    # Setup: Create a child account
    year = db_session.execute(select(Year).limit(1)).scalar_one()
    child_user = ChildAccount(
        name=constants.AUTH_CHILD_NAME,
        email=constants.AUTH_CHILD_EMAIL,
        pin=constants.AUTH_CHILD_PIN,
        language=constants.AUTH_CHILD_LANGUAGE,
        year_id=year.id,
        is_verified=True,
        public_id=constants.AUTH_CHILD_PUBLIC_ID
    )
    db_session.add(child_user)
    db_session.commit()

    db_session.refresh(child_user)

    child_token = create_test_child_auth_token(child_user)
    headers = {"x-auth-token": child_token}

    assign_data = AssignParentRequest(
        parent_email=constants.AUTH_PARENT_EMAIL, language=constants.AUTH_PARENT_LANGUAGE)

    response = client.post(
        ASSIGN_PARENT_URL, headers=headers, json=assign_data.model_dump())

    assert response.status_code == status.HTTP_200_OK
    response_data_obj = AssignParentResponse(**response.json()) # Use Pydantic model
    assert response_data_obj.existing_parent_account is False
    assert response_data_obj.message == "Verification email sent"


    # Verify ParentChildAssignment was created
    assignment = db_session.execute(
        select(ParentChildAssignment).where(
            ParentChildAssignment.child_account_id == child_user.id,
            ParentChildAssignment.parent_email == constants.AUTH_PARENT_EMAIL
        )
    ).scalar_one_or_none()
    assert assignment is not None
    assert assignment.language.value == constants.AUTH_PARENT_LANGUAGE

    bg_spy.assert_called_with(
        mocker.ANY,
        mocker.ANY, # This is the auth_mailer_service instance
        to=constants.AUTH_PARENT_EMAIL,
        verification_code=assignment.verification_code, # Corrected from code to verification_code
        lang=constants.AUTH_PARENT_LANGUAGE,
    )


def test_assign_parent_unauthorized_no_token(client):
    """Test assign parent without auth token."""
    assign_data = AssignParentRequest(
        parent_email=constants.AUTH_PARENT_EMAIL, language=constants.AUTH_PARENT_LANGUAGE)
    response = client.post(ASSIGN_PARENT_URL, json=assign_data.model_dump())
    assert response.status_code == status.HTTP_401_UNAUTHORIZED


def test_assign_parent_with_parent_token(client, db_session: Session, mocker: MockerFixture):
    """Test assign parent with a parent token (should be unauthorized)."""
    parent_user = Account(
        email=constants.AUTH_PARENT_EMAIL,
        language=constants.AUTH_PARENT_LANGUAGE,
        pin_hash=constants.AUTH_PARENT_PIN_HASH,
        is_verified=True,
        public_id=constants.AUTH_PARENT_PUBLIC_ID,
        stripe_customer_id=constants.AUTH_PARENT_STRIPE_CUSTOMER_ID
    )
    db_session.add(parent_user)
    db_session.commit()
    db_session.refresh(parent_user)

    token_data = {"account_public_id": str(parent_user.public_id),
                  "user_type": "parent"}
    parent_token = create_access_token(data=token_data)
    headers = {"x-auth-token": parent_token}

    assign_data = AssignParentRequest(
        parent_email=constants.AUTH_PARENT_EMAIL, language=constants.AUTH_PARENT_LANGUAGE)
    response = client.post(
        ASSIGN_PARENT_URL, headers=headers, json=assign_data.model_dump())

    assert response.status_code == status.HTTP_401_UNAUTHORIZED


def test_assign_parent_child_not_found_from_token(client, mocker: MockerFixture):
    """Test assign parent when child from token does not exist in DB."""
    non_existent_child_public_id = str(uuid.uuid4())
    token_data = {"account_public_id": non_existent_child_public_id,
                  "user_type": "child", "name": "Ghost Child"}
    ghost_child_token = create_access_token(data=token_data)
    headers = {"x-auth-token": ghost_child_token}

    assign_data = AssignParentRequest(
        parent_email=constants.AUTH_PARENT_EMAIL, language=constants.AUTH_PARENT_LANGUAGE)

    response = client.post(
        ASSIGN_PARENT_URL, headers=headers, json=assign_data.model_dump())
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json()["error_code"] == AppErrorCode.USER_NOT_FOUND