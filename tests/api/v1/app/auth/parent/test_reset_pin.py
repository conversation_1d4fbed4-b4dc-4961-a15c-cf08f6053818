import pytest
from sqlalchemy.orm import Session
from fastapi import status
from datetime import datetime, timedelta, UTC

from api.v1.app.auth.parent.schemas.request import ResetPinRequest
# Updated import for ResetPinResponse
from api.v1.app.auth.parent.schemas.response import ResetPinResponse
from db.models import Account, PinResetRequest, AccountAuthSecurity
from sqlalchemy import select
from tests.data import constants
from api.v1.common.schemas import AppErrorCode
from api.v1.app.auth.utils import verify_parent_pin

RESET_PIN_URL = "/api/v1/auth/parent/reset-pin"


@pytest.fixture
def parent_with_reset_token(
    db_session: Session, request
) -> tuple[Account, PinResetRequest]:
    params = request.param
    parent = Account(
        email=params.get("email", constants.AUTH_PARENT_EMAIL.lower()),
        language=params.get("language", constants.AUTH_PARENT_LANGUAGE),
        public_id=params.get("public_id", constants.AUTH_PARENT_PUBLIC_ID),
        stripe_customer_id=params.get(
            "stripe_customer_id", constants.AUTH_PARENT_STRIPE_CUSTOMER_ID
        ),
        is_auth_migrated=params.get("is_auth_migrated", False),  # Test migration
        is_verified=params.get("is_verified", False)  # Test verification
    )
    db_session.add(parent)
    db_session.flush()  # Get parent.id

    reset_token_value = params.get("token_value", "validresettoken123")
    reset_req = PinResetRequest(
        account_id=parent.id,
        request_token=reset_token_value,
        expires_at=datetime.now(UTC) + timedelta(
            minutes=params.get("token_expiry_minutes", 10)
        ),
        used=params.get("token_used", False)
    )
    db_session.add(reset_req)

    # Create AccountAuthSecurity if it doesn't exist
    # to avoid FK constraint violation on parent.auth_security_id
    auth_sec = db_session.execute(
        select(AccountAuthSecurity).filter_by(account_id=parent.id)
    ).scalar_one_or_none()
    if not auth_sec:
        auth_sec = AccountAuthSecurity(account_id=parent.id)
        db_session.add(auth_sec)

    db_session.commit()
    db_session.refresh(parent)
    db_session.refresh(reset_req)
    return parent, reset_req


@pytest.mark.parametrize("parent_with_reset_token", [{}], indirect=True)
def test_reset_pin_success(
    client, parent_with_reset_token: tuple[Account, PinResetRequest], db_session: Session
):
    """Test successful PIN reset."""
    parent, reset_req = parent_with_reset_token
    
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"Test Debug: reset_req.request_token = {reset_req.request_token}")
    logger.info(f"Test Debug: reset_req.expires_at = {reset_req.expires_at}")
    logger.info(f"Test Debug: reset_req.used = {reset_req.used}")
    logger.info(f"Test Debug: Current UTC time = {datetime.now(UTC)}")

    new_pin = constants.AUTH_PARENT_PIN
    request_data = ResetPinRequest(
        reset_token=reset_req.request_token,
        new_pin=new_pin
    )
    response = client.post(RESET_PIN_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_200_OK
    response_data_obj = ResetPinResponse(**response.json()) # Use Pydantic model
    assert response_data_obj.message == "PIN successfully reset"

    db_session.refresh(parent)
    db_session.refresh(reset_req)
    
    assert verify_parent_pin(new_pin, parent.pin_hash)
    assert parent.is_auth_migrated is True
    assert parent.is_verified is True
    assert reset_req.used is True

    auth_security = db_session.execute(
        select(AccountAuthSecurity)
        .where(AccountAuthSecurity.account_id == parent.id)
    ).scalar_one()
    assert auth_security.failed_attempts == 0
    assert auth_security.locked_until is None


def test_reset_pin_invalid_token(client):
    """Test PIN reset with an invalid or non-existent token."""
    request_data = ResetPinRequest(
        reset_token="invalidtoken",
        new_pin=constants.AUTH_PARENT_PIN
    )
    response = client.post(RESET_PIN_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.INVALID_OR_EXPIRED_TOKEN


# Expired token
@pytest.mark.parametrize(
    "parent_with_reset_token", [{"token_expiry_minutes": -5}], indirect=True
)
def test_reset_pin_expired_token(
    client, parent_with_reset_token: tuple[Account, PinResetRequest]
):
    """Test PIN reset with an expired token."""
    parent, reset_req = parent_with_reset_token
    request_data = ResetPinRequest(
        reset_token=reset_req.request_token,
        new_pin=constants.AUTH_PARENT_PIN
    )
    response = client.post(RESET_PIN_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.INVALID_OR_EXPIRED_TOKEN


# Used token
@pytest.mark.parametrize("parent_with_reset_token", [{"token_used": True}], indirect=True)
def test_reset_pin_used_token(
    client, parent_with_reset_token: tuple[Account, PinResetRequest]
):
    """Test PIN reset with an already used token."""
    parent, reset_req = parent_with_reset_token
    request_data = ResetPinRequest(
        reset_token=reset_req.request_token,
        new_pin=constants.AUTH_PARENT_PIN
    )
    response = client.post(RESET_PIN_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.INVALID_OR_EXPIRED_TOKEN