import pytest
from sqlalchemy.orm import Session
from fastapi import status
from jose import jwt

from core.config.settings import settings
from db.models import ChildAccount, Account
from api.v1.app.auth.child.schemas.response import ValidateSessionResponse
# Import UserTypeEnum for assertions
from api.v1.app.auth.schemas.shared_schemas import UserTypeEnum
from tests.utils.auth_utils import create_test_parent_auth_token, create_test_child_auth_token
from tests.data import constants
from api.v1.common.schemas import AppErrorCode
from sqlalchemy import select, delete
from db.database import get_db
from db.models import Year
VALIDATE_PARENT_SESSION_URL = "/api/v1/auth/parent/validate-session"  # Defined URL
VALIDATE_CHILD_SESSION_URL = "/api/v1/auth/child/validate-session"  # Defined URL


@pytest.fixture
def verified_parent_for_session_test(db_session: Session) -> Account:  # Renamed fixture
    # Using existing constants for a verified parent
    parent = db_session.execute(
        select(Account).where(Account.public_id == constants.MOCK_PARENT_PUBLIC_ID)
    ).scalar_one_or_none()
    if not parent:  # Should be seeded by mock_data_loader
        parent = Account(
            email=constants.MOCK_PARENT_EMAIL.lower(),
            language=constants.MOCK_PARENT_LANGUAGE,  # This should be an enum member if model expects it
            pin_hash=constants.MOCK_PARENT_PIN_HASH,
            public_id=constants.MOCK_PARENT_PUBLIC_ID,
            stripe_customer_id=constants.MOCK_STRIPE_CUSTOMER_ID
        )
        db_session.add(parent)
        db_session.commit()
        db_session.refresh(parent)
    return parent

def test_validate_parent_session_success(client, verified_parent_for_session_test: Account, db_session: Session):  # Renamed test
    """Test successful parent session validation."""
    parent_token = create_test_parent_auth_token(verified_parent_for_session_test)
    headers = {"x-auth-token": parent_token}

    response = client.post(VALIDATE_PARENT_SESSION_URL, headers=headers)  # Use defined URL

    assert response.status_code == status.HTTP_200_OK
    # Assuming ValidateSessionResponse is imported from parent schemas
    from api.v1.app.auth.parent.schemas.response import ValidateSessionResponse as ParentValidateSessionResponse
    response_data_obj = ParentValidateSessionResponse(**response.json())

    assert response_data_obj.access_token is not None

    # Assertions for 'subscriptions' at the root level
    user_info = response_data_obj.user
    # Removed first_name assertion - ParentUser doesn't have this field

    assert isinstance(response_data_obj.subscriptions, list)



    new_token = response_data_obj.access_token
    decoded_new_token = jwt.decode(
        new_token, 
        settings.SECRET_KEY,
        algorithms=[settings.JWT_ALGORITHM]
    )
    assert decoded_new_token["user_type"] == "parent"
    assert decoded_new_token["email"] == verified_parent_for_session_test.email.lower()
    assert decoded_new_token["language"] == verified_parent_for_session_test.language.value


def test_validate_parent_session_unauthorized_no_token(client):  
    """Test validate parent session without auth token."""
    response = client.post(VALIDATE_PARENT_SESSION_URL)
    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    assert response.json()["error_code"] == AppErrorCode.AUTHENTICATION_REQUIRED

def test_validate_parent_session_unauthorized_invalid_token(client):  
    """Test validate parent session with an invalid token."""
    headers = {"x-auth-token": "invalidtoken123"}
    response = client.post(VALIDATE_PARENT_SESSION_URL, headers=headers)
    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    assert response.json()["error_code"] == AppErrorCode.AUTHENTICATION_ERROR




def test_validate_parent_session_unauthorized_when_child_token_used(client, db_session: Session):
    """Test validate parent session with a child token."""
    # Fetch or create a mock child for token generation
    child_stmt = select(ChildAccount).where(
        ChildAccount.public_id == constants.MOCK_CHILD_PUBLIC_ID
    )
    child_user = db_session.execute(child_stmt).scalar_one_or_none()
    # Removed unused: verified_parent_for_session_test: Account
    if not child_user:
        # If the mock child doesn't exist, create a basic one
        # 'Year' is globally imported.
        year_stmt = select(Year).limit(1)
        year = db_session.execute(year_stmt).scalar_one_or_none()
        child_user = ChildAccount(
            name=constants.MOCK_CHILD_NAME,
            email=f"unique_child_sess_{constants.MOCK_CHILD_EMAIL}",  # Ensure unique email
            pin=constants.MOCK_CHILD_PIN,
            language=constants.MOCK_CHILD_LANGUAGE,
            year_id=year.id if year else None,
            public_id=constants.MOCK_CHILD_PUBLIC_ID
        )
        db_session.add(child_user)
        db_session.commit()
        db_session.refresh(child_user) # Refresh to load all attributes

    # create_test_child_auth_token is globally imported.
    child_token = create_test_child_auth_token(child_user)
    headers = {"x-auth-token": child_token}

    response = client.post(VALIDATE_PARENT_SESSION_URL, headers=headers)
    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    # The error code PERMISSION_DENIED is appropriate as a child token is used for a parent endpoint.
    assert response.json()["error_code"] == AppErrorCode.PERMISSION_DENIED.value

def test_validate_parent_session_account_not_found_in_db(   # Renamed test
    client, 
    verified_parent_for_session_test: Account,  # Changed to parent fixture
    db_session: Session  # Added db_session fixture
):
    """Test validate_session when parent account is not found (e.g., deleted after token issuance)."""
    # create_test_parent_auth_token is imported globally.
    # db_session fixture will be used directly.
    try:
        # Token created using the globally imported function
        parent_token = create_test_parent_auth_token(verified_parent_for_session_test)
        headers = {"x-auth-token": parent_token}

        # Delete the account using the test's db_session
        db_session.execute(delete(Account).where(Account.id == verified_parent_for_session_test.id))
        db_session.commit() # Commit the deletion
    finally:
        # Note: Test transactions are typically rolled back by pytest-fastapi-sqlalchemy.
        # For now, assume standard test transaction behavior handles cleanup.
        pass

    response = client.post(VALIDATE_PARENT_SESSION_URL, headers=headers)

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json()["error_code"] == AppErrorCode.USER_NOT_FOUND.value