import pytest
from sqlalchemy.orm import Session
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>
from fastapi import status, BackgroundTasks

from api.v1.app.auth.parent.schemas.request import ResendVerificationCodeRequest
# Updated import for ResendVerificationCodeResponse
from api.v1.app.auth.parent.schemas.response import ResendVerificationCodeResponse
from db.models import Account
from tests.data import constants
from api.v1.common.schemas import AppErrorCode

RESEND_VERIFICATION_CODE_URL = "/api/v1/auth/parent/resend-verification-code"


@pytest.fixture
def parent_for_resend(db_session: Session, request) -> Account:
    params = request.param
    parent = Account(
        email=params.get("email", constants.AUTH_PARENT_EMAIL.lower()),
        language=params.get(
            "language", constants.AUTH_PARENT_LANGUAGE),
        is_verified=params.get("is_verified", False),
        verification_code=params.get(
            "verification_code", constants.AUTH_PARENT_VERIFICATION_CODE),
        public_id=params.get("public_id", constants.AUTH_PARENT_PUBLIC_ID),
        stripe_customer_id=params.get(
            "stripe_customer_id", constants.AUTH_PARENT_STRIPE_CUSTOMER_ID)
    )
    db_session.add(parent)
    db_session.commit()
    db_session.refresh(parent)
    return parent


@pytest.mark.parametrize("parent_for_resend", [{
    "is_verified": False
}], indirect=True)
def test_resend_verification_code_success(client, parent_for_resend: Account, mocker: MockerFixture):
    """Test successful resend of verification code for an unverified parent."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")
    request_data = ResendVerificationCodeRequest(
        email=parent_for_resend.email, language=parent_for_resend.language.value)
    response = client.post(RESEND_VERIFICATION_CODE_URL,
                           json=request_data.model_dump())

    assert response.status_code == status.HTTP_200_OK
    # Validate with Pydantic model
    response_body = ResendVerificationCodeResponse(**response.json())
    assert response_body.message == "Verification code resent"

    bg_spy.assert_called_once_with(
        mocker.ANY,
        mocker.ANY,
        to=parent_for_resend.email,
        verification_code=parent_for_resend.verification_code,
        lang=parent_for_resend.language.value
    )


@pytest.mark.parametrize("parent_for_resend", [{
    "is_verified": False, "verification_code": None
}], indirect=True)
def test_resend_verification_code_generates_code_if_none(
    client,
    parent_for_resend: Account,
    mocker: MockerFixture,
    db_session: Session
):
    """Test resend generates a code if none exists and sends email."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")
    request_data = ResendVerificationCodeRequest(
        email=parent_for_resend.email, language=parent_for_resend.language.value)
    response = client.post(RESEND_VERIFICATION_CODE_URL,
                           json=request_data.model_dump())

    assert response.status_code == status.HTTP_200_OK
    db_session.refresh(parent_for_resend)
    assert parent_for_resend.verification_code is not None
    assert len(parent_for_resend.verification_code) == 6

    bg_spy.assert_called_once_with(
        mocker.ANY,
        mocker.ANY,
        to=parent_for_resend.email,
        verification_code=parent_for_resend.verification_code,
        lang=parent_for_resend.language.value
    )


def test_resend_verification_code_account_not_found(client, mocker: MockerFixture):
    """Test resend for a non-existent parent account."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")
    request_data = ResendVerificationCodeRequest(
        email="<EMAIL>", language=constants.AUTH_PARENT_LANGUAGE)
    response = client.post(RESEND_VERIFICATION_CODE_URL,
                           json=request_data.model_dump())

    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.USER_NOT_FOUND
    bg_spy.assert_not_called()


@pytest.mark.parametrize("parent_for_resend", [{"is_verified": True}], indirect=True)
def test_resend_verification_code_already_verified(
    client, parent_for_resend: Account, mocker: MockerFixture
):
    """Test resend for an already verified parent account."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")
    request_data = ResendVerificationCodeRequest(
        email=parent_for_resend.email, language=parent_for_resend.language.value)
    response = client.post(RESEND_VERIFICATION_CODE_URL,
                           json=request_data.model_dump())

    assert response.status_code == status.HTTP_409_CONFLICT
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.ACCOUNT_ALREADY_VERIFIED
    bg_spy.assert_not_called()