from datetime import datetime, timedelta, UTC

import pytest
from fastapi import status, BackgroundTasks
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session

from api.v1.app.auth.parent.schemas.request import TriggerPinResetRequest
# Updated import for TriggerPinResetResponse
from api.v1.app.auth.parent.schemas.response import TriggerPinResetResponse
from db.models import Account, PinResetRequest, AccountAuthSecurity
from sqlalchemy import select
from tests.data import constants
from api.v1.common.schemas import AppErrorCode

TRIGGER_PIN_RESET_URL = "/api/v1/auth/parent/trigger-pin-reset"


@pytest.fixture
def parent_for_pin_reset(db_session: Session, request) -> Account:
    params = request.param
    parent = Account(
        email=params.get("email", constants.AUTH_PARENT_EMAIL.lower()),
        language=params.get("language", constants.AUTH_PARENT_LANGUAGE),
        public_id=params.get("public_id", constants.AUTH_PARENT_PUBLIC_ID),
        stripe_customer_id=params.get("stripe_customer_id", constants.AUTH_PARENT_STRIPE_CUSTOMER_ID)
    )
    db_session.add(parent)
    if params.get("is_locked"):
        db_session.flush()  # Ensure parent.id is populated before use
        auth_security = AccountAuthSecurity(
            account_id=parent.id,
            locked_until=datetime.now(UTC) + timedelta(minutes=10)
        )
        db_session.add(auth_security)
    db_session.commit()
    db_session.refresh(parent)
    return parent


@pytest.mark.parametrize("parent_for_pin_reset", [{}], indirect=True)
def test_trigger_pin_reset_success_new_token(
    client,
    parent_for_pin_reset: Account,
    mocker: MockerFixture,
    db_session: Session
):
    """Test successful PIN reset trigger, creating a new token."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")
    request_data = TriggerPinResetRequest(
        email=parent_for_pin_reset.email,
        language="fr"
    )
    response = client.post(TRIGGER_PIN_RESET_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_200_OK
    # Validate with Pydantic model
    response_body = TriggerPinResetResponse(**response.json())
    assert response_body.message == "If the account exists, a reset code will be sent"

    pin_reset_req = db_session.execute(
        select(PinResetRequest).where(
            PinResetRequest.account_id == parent_for_pin_reset.id
        )
    ).scalar_one_or_none()
    assert pin_reset_req is not None

    bg_spy.assert_called_once()
    call_args = bg_spy.call_args_list[0][1]
    assert call_args['to'] == parent_for_pin_reset.email
    assert call_args['reset_token'] == pin_reset_req.request_token
    assert call_args['lang'] == "fr"

@pytest.mark.parametrize("parent_for_pin_reset", [{}], indirect=True)
def test_trigger_pin_reset_success_resend_existing_token(
    client,
    parent_for_pin_reset: Account,
    db_session: Session,
    mocker: MockerFixture
):
    """Test PIN reset trigger resending an existing valid token."""
    existing_token = constants.AUTH_PARENT_VERIFICATION_CODE
    existing_req = PinResetRequest(
        account_id=parent_for_pin_reset.id,
        request_token=existing_token,
        expires_at=datetime.now(UTC) + timedelta(minutes=10),
        created_at=datetime.now(UTC) - timedelta(minutes=5)
    )
    db_session.add(existing_req)
    db_session.commit()

    bg_spy = mocker.spy(BackgroundTasks, "add_task")
    request_data = TriggerPinResetRequest(email=parent_for_pin_reset.email)
    response = client.post(TRIGGER_PIN_RESET_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_200_OK
    bg_spy.assert_called_once()
    call_args = bg_spy.call_args_list[0][1]
    assert call_args['reset_token'] == existing_token

def test_trigger_pin_reset_account_not_found(
    client,
    mocker: MockerFixture
):
    """Test PIN reset trigger for a non-existent account (should still return generic success)."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")
    request_data = TriggerPinResetRequest(email="<EMAIL>")
    response = client.post(TRIGGER_PIN_RESET_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_200_OK
    bg_spy.assert_not_called()

@pytest.mark.parametrize("parent_for_pin_reset", [{"is_locked": True}], indirect=True)
def test_trigger_pin_reset_account_locked(
    client,
    parent_for_pin_reset: Account,
    mocker: MockerFixture
):
    """Test PIN reset trigger for a locked account."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")
    request_data = TriggerPinResetRequest(email=parent_for_pin_reset.email)
    response = client.post(TRIGGER_PIN_RESET_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.ACCOUNT_LOCKED
    bg_spy.assert_not_called()

@pytest.mark.parametrize("parent_for_pin_reset", [{}], indirect=True)
def test_trigger_pin_reset_too_many_requests(
    client,
    parent_for_pin_reset: Account,
    db_session: Session,
    mocker: MockerFixture
):
    """Test rate limiting for PIN reset trigger."""
    # Create an EmailRateLimit entry that's already at the limit
    from db.models import EmailRateLimit
    rate_limit = EmailRateLimit(
        email=parent_for_pin_reset.email.lower(),
        email_type="pin_reset",
        attempts_last_minute=3,  # Already at the limit (MAX_ATTEMPTS_PER_MINUTE = 3)
        attempts_last_hour=3,
        attempts_last_day=3,
        minute_window_start=datetime.now(UTC) - timedelta(seconds=30),
        hour_window_start=datetime.now(UTC) - timedelta(minutes=30),
        day_window_start=datetime.now(UTC) - timedelta(hours=12),
        last_attempt=datetime.now(UTC) - timedelta(seconds=5)
    )
    db_session.add(rate_limit)
    db_session.commit()

    bg_spy = mocker.spy(BackgroundTasks, "add_task")
    request_data = TriggerPinResetRequest(email=parent_for_pin_reset.email)
    response = client.post(TRIGGER_PIN_RESET_URL, json=request_data.model_dump())

    assert response.status_code == status.HTTP_429_TOO_MANY_REQUESTS
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.TOO_MANY_REQUESTS
    bg_spy.assert_not_called()