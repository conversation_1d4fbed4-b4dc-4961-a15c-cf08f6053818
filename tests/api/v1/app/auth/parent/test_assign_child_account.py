import pytest
from sqlalchemy.orm import Session
from fastapi import status
import uuid

from api.v1.app.auth.parent.schemas.request import AssignChildAccountRequest
# Updated import for AssignChildAccountResponse
from api.v1.app.auth.parent.schemas.response import AssignChildAccountResponse
from db.models import Account, ChildAccount, ParentChildAssignment, Year
from sqlalchemy import select
from tests.data import constants
from api.v1.common.schemas import AppErrorCode
from tests.utils.auth_utils import create_test_parent_auth_token

ASSIGN_CHILD_URL = "/api/v1/auth/parent/assign-child-account"

@pytest.fixture
def parent_for_assign(db_session: Session) -> Account:
    parent = Account(
        email=constants.AUTH_PARENT_EMAIL.lower(),
        language=constants.AUTH_PARENT_LANGUAGE,
        pin_hash=constants.AUTH_PARENT_PIN_HASH,
        is_verified=True,
        is_auth_migrated=True,
        public_id=constants.AUTH_PARENT_PUBLIC_ID,
        stripe_customer_id=constants.AUTH_PARENT_STRIPE_CUSTOMER_ID
    )
    db_session.add(parent)
    db_session.commit()
    db_session.refresh(parent)
    return parent

@pytest.fixture
def child_for_assign(db_session: Session) -> ChildAccount:
    year = db_session.execute(select(Year).limit(1)).scalar_one()
    child = ChildAccount(
        email=constants.AUTH_CHILD_EMAIL.lower(),
        name=constants.AUTH_CHILD_NAME,
        pin=constants.AUTH_CHILD_PIN,
        language=constants.AUTH_CHILD_LANGUAGE,
        year_id=year.id,
        public_id=constants.AUTH_CHILD_PUBLIC_ID,
        is_verified=False
    )
    db_session.add(child)
    db_session.commit()
    db_session.refresh(child)
    return child

@pytest.fixture
def parent_child_assignment_record(
    db_session: Session,
    parent_for_assign: Account,
    child_for_assign: ChildAccount
) -> ParentChildAssignment:
    assignment = ParentChildAssignment(
        parent_email=parent_for_assign.email,
        child_account_id=child_for_assign.id,
        verification_code=constants.AUTH_PARENT_VERIFICATION_CODE,
        language=parent_for_assign.language.value
    )
    db_session.add(assignment)
    db_session.commit()
    db_session.refresh(assignment)
    return assignment

def test_assign_child_account_success(
    client,
    db_session: Session,
    parent_for_assign: Account,
    child_for_assign: ChildAccount,
    parent_child_assignment_record: ParentChildAssignment
):
    """Test successful assignment of a child to a parent."""
    parent_token = create_test_parent_auth_token(parent_for_assign)
    headers = {"x-auth-token": parent_token}

    request_data = AssignChildAccountRequest(
        child_email=child_for_assign.email,
        verification_code=parent_child_assignment_record.verification_code
    )
    response = client.post(ASSIGN_CHILD_URL, headers=headers, json=request_data.model_dump())

    assert response.status_code == status.HTTP_200_OK
    response_data_obj = AssignChildAccountResponse(**response.json()) # Use Pydantic model
    assert response_data_obj.child_account_public_id == str(child_for_assign.public_id)
    assert response_data_obj.message == "Child account linked successfully!"


    db_session.refresh(child_for_assign)
    assert child_for_assign.parent_account_id == parent_for_assign.id
    assert child_for_assign.is_verified is True

    deleted_assignment = db_session.get(ParentChildAssignment, parent_child_assignment_record.id)
    assert deleted_assignment is None

def test_assign_child_account_unauthorized(client):
    """Test assign child without auth token."""
    request_data = AssignChildAccountRequest(
        child_email=constants.AUTH_CHILD_EMAIL,
        verification_code=constants.AUTH_PARENT_VERIFICATION_CODE
    )
    response = client.post(ASSIGN_CHILD_URL, json=request_data.model_dump())
    assert response.status_code == status.HTTP_401_UNAUTHORIZED

def test_assign_child_account_child_not_found(client, parent_for_assign: Account):
    """Test assign child when child email does not exist."""
    parent_token = create_test_parent_auth_token(parent_for_assign)
    headers = {"x-auth-token": parent_token}
    request_data = AssignChildAccountRequest(
        child_email="<EMAIL>",
        verification_code=constants.AUTH_PARENT_VERIFICATION_CODE
    )
    response = client.post(ASSIGN_CHILD_URL, headers=headers, json=request_data.model_dump())

    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.CHILD_ACCOUNT_NOT_FOUND

def test_assign_child_account_already_linked(
    client,
    db_session: Session,
    parent_for_assign: Account,
    child_for_assign: ChildAccount
):
    """Test assign child when child is already linked to another parent."""
    other_parent = Account(
        email="<EMAIL>",
        language="en",
        public_id=str(uuid.uuid4()),
        stripe_customer_id="cus_other"
    )
    db_session.add(other_parent)
    db_session.commit()
    child_for_assign.parent_account_id = other_parent.id
    db_session.commit()

    parent_token = create_test_parent_auth_token(parent_for_assign)
    headers = {"x-auth-token": parent_token}
    request_data = AssignChildAccountRequest(
        child_email=child_for_assign.email,
        verification_code=constants.AUTH_PARENT_VERIFICATION_CODE
    )
    response = client.post(ASSIGN_CHILD_URL, headers=headers, json=request_data.model_dump())

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.CHILD_ALREADY_LINKED

def test_assign_child_account_no_pending_assignment(
    client,
    parent_for_assign: Account,
    child_for_assign: ChildAccount
):
    """Test assign child when no ParentChildAssignment record exists."""
    parent_token = create_test_parent_auth_token(parent_for_assign)
    headers = {"x-auth-token": parent_token}
    request_data = AssignChildAccountRequest(
        child_email=child_for_assign.email,
        verification_code=constants.AUTH_PARENT_VERIFICATION_CODE
    )
    response = client.post(ASSIGN_CHILD_URL, headers=headers, json=request_data.model_dump())

    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.ASSIGNMENT_NOT_FOUND_OR_INVALID_CODE

def test_assign_child_account_invalid_verification_code(
    client,
    parent_for_assign: Account,
    child_for_assign: ChildAccount,
    parent_child_assignment_record: ParentChildAssignment
):
    """Test assign child with an invalid verification code."""
    parent_token = create_test_parent_auth_token(parent_for_assign)
    headers = {"x-auth-token": parent_token}
    request_data = AssignChildAccountRequest(
        child_email=child_for_assign.email,
        verification_code="000000"
    )
    response = client.post(ASSIGN_CHILD_URL, headers=headers, json=request_data.model_dump())

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.INVALID_VERIFICATION_CODE