import pytest
from sqlalchemy.orm import Session
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>
from fastapi import status, BackgroundTasks
from jose import jwt
import uuid
from datetime import datetime, timedelta, UTC

from core.config.settings import settings
from api.v1.app.auth.parent.schemas.request import LoginRequest
from db.models import (
    Account,
    AccountAuthSecurity,
    PinResetRequest,
    ActiveSubscription,
    SubscriptionStatusType
)
from sqlalchemy import select
from tests.data import constants
from api.v1.common.schemas import AppErrorCode
from api.v1.app.auth.utils import hash_parent_pin

from api.v1.app.auth.schemas.shared_schemas import UserTypeEnum

LOGIN_URL = "/api/v1/auth/parent/login"

@pytest.fixture
def verified_migrated_parent(db_session: Session) -> Account:
    """Creates a verified, migrated parent account."""
    email = constants.AUTH_PARENT_EMAIL.lower()
    parent = Account(
        email=email,
        language=constants.AUTH_PARENT_LANGUAGE,
        pin_hash=hash_parent_pin(constants.AUTH_PARENT_PIN),
        is_verified=True,
        is_auth_migrated=True,
        public_id=constants.AUTH_PARENT_PUBLIC_ID,
        stripe_customer_id=constants.AUTH_PARENT_STRIPE_CUSTOMER_ID,
    )
    db_session.add(parent)
    db_session.commit()
    db_session.refresh(parent)
    
    # Now create the auth security record with the parent's ID
    auth_security = AccountAuthSecurity(account_id=parent.id)
    db_session.add(auth_security)
    db_session.commit()
    
    return parent

def test_parent_login_success_migrated_verified(
    client,
    verified_migrated_parent: Account,
    db_session: Session
):
    """Test successful login for a migrated and verified parent."""
    # First, ensure the auth_security record exists and has some failed attempts
    # This will let us verify that successful login resets the counter
    auth_security = db_session.execute(
        select(AccountAuthSecurity).where(AccountAuthSecurity.account_id == verified_migrated_parent.id)
    ).scalar_one()
    auth_security.failed_attempts = 2  # Set some failed attempts
    db_session.commit()
    
    # Attempt login with correct credentials
    login_data = LoginRequest(email=constants.AUTH_PARENT_EMAIL, login_pin=constants.AUTH_PARENT_PIN)
    response = client.post(LOGIN_URL, json=login_data.model_dump())

    # Verify successful login
    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert "access_token" in response_data
    # assert response_data["is_auth_migrated"] is True 
    # This is now part of account_info or not directly in LoginResponse

    # Validate the new LoginResponse structure
    from api.v1.app.auth.parent.schemas.response import (
        LoginResponse as ParentLoginResponsePydantic
    )
    login_response_obj = ParentLoginResponsePydantic(**response_data)

    assert login_response_obj.access_token is not None
    assert login_response_obj.user.user_type == UserTypeEnum.PARENT
    assert login_response_obj.user.public_id == str(verified_migrated_parent.public_id) # Ensure comparison with string
    assert login_response_obj.user.email == verified_migrated_parent.email.lower()
    assert login_response_obj.is_auth_migrated is True # Check is_auth_migrated at root

    # Verify attempts_remaining is still part of the response if needed by frontend
    # This was kept in the Pydantic model for LoginResponse
    max_attempts = int(settings.PIN_ATTEMPT_LIMITS[-1][0]) if settings.PIN_ATTEMPT_LIMITS else 5
    assert login_response_obj.attempts_remaining == max_attempts

    # Verify token contents
    decoded_token = jwt.decode(
        login_response_obj.access_token,
        settings.SECRET_KEY,
        algorithms=[settings.JWT_ALGORITHM]
    )
    assert decoded_token["account_public_id"] == verified_migrated_parent.public_id
    assert decoded_token["user_type"] == "parent"
    assert decoded_token["email"] == constants.AUTH_PARENT_EMAIL.lower()

    # Verify active_subscriptions in token payload
    active_subs_from_db_stmt = (
        select(ActiveSubscription.public_id)
        .where(ActiveSubscription.parent_account_id == verified_migrated_parent.id)
        .where(ActiveSubscription.status.in_(
            [SubscriptionStatusType.ACTIVE, SubscriptionStatusType.TRIALING]
        ))
    )
    expected_token_subscription_public_ids = sorted(
        [str(pid) for pid in db_session.execute(active_subs_from_db_stmt).scalars().all()]
    )

    token_subs_payload_sorted = sorted(decoded_token.get("active_subscriptions", []))
    assert token_subs_payload_sorted == expected_token_subscription_public_ids


    # Verify subscriptions in account_info response (should be empty for parent login)
    assert login_response_obj.subscriptions == []

    # Verify that failed_attempts was reset to 0
    db_session.refresh(auth_security)
    assert auth_security.failed_attempts == 0
    assert auth_security.locked_until is None

def test_parent_login_account_not_found(client):
    """Test login with a non-existent parent email."""
    login_data = LoginRequest(email="<EMAIL>", login_pin="123456")
    response = client.post(LOGIN_URL, json=login_data.model_dump())

    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.USER_NOT_FOUND

def test_parent_login_incorrect_pin(client, verified_migrated_parent: Account, db_session: Session):
    """Test login with an incorrect PIN for a migrated account."""
    # Attempt login with incorrect PIN - use a different PIN than the one in constants
    incorrect_pin = "000000"  # Different from AUTH_PARENT_PIN
    login_data = LoginRequest(email=constants.AUTH_PARENT_EMAIL, login_pin=incorrect_pin)
    response = client.post(LOGIN_URL, json=login_data.model_dump())

    # Verify authentication failure
    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.INVALID_CREDENTIALS
    
    # Verify the message includes information about attempts remaining
    assert "attempts remaining" in response_data["message"]

    # Verify that failed_attempts was incremented in the database
    auth_security = db_session.execute(
        select(AccountAuthSecurity).where(AccountAuthSecurity.account_id == verified_migrated_parent.id)
    ).scalar_one_or_none()
    assert auth_security is not None
    assert auth_security.failed_attempts == 1
    
    # Verify last_failed_attempt was updated
    assert auth_security.last_failed_attempt is not None

def test_parent_login_unverified_account(client, db_session: Session):
    """Test login for a migrated but unverified parent account."""
    # Use a consistent email for the unverified account
    unverified_email = constants.AUTH_PARENT_EMAIL  # Use constant

    # First create and commit the parent account
    parent = Account(
        email=unverified_email,
        language=constants.AUTH_PARENT_LANGUAGE,
        pin_hash=hash_parent_pin(constants.AUTH_PARENT_PIN),
        is_verified=False,  # Not verified  
        is_auth_migrated=True,
        public_id=str(uuid.uuid4()),
        stripe_customer_id="cus_unverified",
    )
    db_session.add(parent)
    db_session.commit()
    db_session.refresh(parent)
    
    # Now create the auth security record with the parent's ID
    auth_security = AccountAuthSecurity(account_id=parent.id)
    db_session.add(auth_security)
    db_session.commit()

    login_data = LoginRequest(email=unverified_email, login_pin=constants.AUTH_PARENT_PIN)
    response = client.post(LOGIN_URL, json=login_data.model_dump())

    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.ACCOUNT_NOT_VERIFIED

def test_parent_login_account_locked(client, verified_migrated_parent: Account, db_session: Session):
    """Test login for a locked account."""
    # Get the auth security record for the parent account
    auth_security = db_session.execute(
        select(AccountAuthSecurity).where(AccountAuthSecurity.account_id == verified_migrated_parent.id)
    ).scalar_one_or_none()
    
    # If auth_security doesn't exist, create it
    if not auth_security:
        auth_security = AccountAuthSecurity(account_id=verified_migrated_parent.id)
        db_session.add(auth_security)
        db_session.commit()
        db_session.refresh(auth_security)
    
    # Set a future locked_until time to simulate a locked account
    auth_security.locked_until = datetime.now(UTC) + timedelta(minutes=5)
    db_session.commit()

    # Attempt to login with correct credentials, but account is locked
    login_data = LoginRequest(email=constants.AUTH_PARENT_EMAIL, login_pin=constants.AUTH_PARENT_PIN)
    response = client.post(LOGIN_URL, json=login_data.model_dump())

    # Verify the response indicates the account is locked
    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.ACCOUNT_LOCKED
    # Verify the message contains information about retry time
    assert "minutes" in response_data["message"]

def test_parent_login_progressive_lockout(client, verified_migrated_parent: Account, db_session: Session):
    """Test the progressive lockout mechanism with multiple failed attempts."""
    # Get the auth security record for the parent account
    auth_security = db_session.execute(
        select(AccountAuthSecurity).where(AccountAuthSecurity.account_id == verified_migrated_parent.id)
    ).scalar_one_or_none()
    
    # If auth_security doesn't exist, create it
    if not auth_security:
        auth_security = AccountAuthSecurity(account_id=verified_migrated_parent.id)
        db_session.add(auth_security)
        db_session.commit()
        db_session.refresh(auth_security)
    
    # Reset any existing failed attempts
    auth_security.failed_attempts = 0
    auth_security.locked_until = None
    db_session.commit()
    
    # Make multiple failed login attempts and check the lockout behavior
    # Use a different PIN than the one in constants
    incorrect_pin = "000000"  # Different from AUTH_PARENT_PIN

    # Test first threshold (typically 6 attempts -> 1 minute lockout)
    for i in range(int(settings.PIN_ATTEMPT_LIMITS[0][0])):
        login_data = LoginRequest(email=constants.AUTH_PARENT_EMAIL, login_pin=incorrect_pin)
        response = client.post(LOGIN_URL, json=login_data.model_dump())
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    # Verify the account is now locked
    db_session.refresh(auth_security)
    assert auth_security.locked_until is not None
    
    # The lockout duration should match the first threshold in PIN_ATTEMPT_LIMITS
    expected_lockout_minutes = settings.PIN_ATTEMPT_LIMITS[0][1]
    lockout_duration = (auth_security.locked_until - datetime.now(UTC)).total_seconds() / 60
    assert abs(lockout_duration - expected_lockout_minutes) < 1  # Allow for slight timing differences
    
    # Attempt login while locked
    login_data = LoginRequest(email=constants.AUTH_PARENT_EMAIL, login_pin=constants.AUTH_PARENT_PIN)
    response = client.post(LOGIN_URL, json=login_data.model_dump())
    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    assert response.json()["error_code"] == AppErrorCode.ACCOUNT_LOCKED


def test_parent_login_not_migrated_triggers_pin_reset(client, db_session: Session, mocker: MockerFixture):
    """Test login for a non-migrated account, expecting PIN reset flow."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")
    parent_email_not_migrated = constants.AUTH_PARENT_EMAIL  # Use constant

    # First create and commit the parent account
    parent = Account(
        email=parent_email_not_migrated,
        language=constants.AUTH_PARENT_LANGUAGE,  # Use constant for language
        pin_hash=None,  # Old system might not have pin_hash or it's different
        is_verified=True,
        is_auth_migrated=False,  # Not migrated
        public_id=str(uuid.uuid4()),
        stripe_customer_id="cus_notmigrated",
    )
    db_session.add(parent)
    db_session.commit()
    db_session.refresh(parent)
    
    # Now create the auth security record with the parent's ID
    auth_security = AccountAuthSecurity(account_id=parent.id)
    db_session.add(auth_security)
    db_session.commit()

    # PIN doesn't matter for non-migrated accounts, but use constant for consistency
    login_data = LoginRequest(email=parent_email_not_migrated, login_pin=constants.AUTH_PARENT_PIN)
    response = client.post(LOGIN_URL, json=login_data.model_dump())

    # Service now raises AuthenticationError for migration required
    assert response.status_code == status.HTTP_401_UNAUTHORIZED 
    response_data = response.json()
    # assert response_data["access_token"] is None # No longer part of this error response structure
    # assert response_data["is_auth_migrated"] is False # This info is in the error code/message
    assert response_data["error_code"] == AppErrorCode.ACCOUNT_MIGRATION_REQUIRED


    # Verify PinResetRequest created and email sent
    pin_reset_request = db_session.execute(
        select(PinResetRequest).where(PinResetRequest.account_id == parent.id)
    ).scalar_one_or_none()
    assert pin_reset_request is not None

    bg_spy.assert_called_once()
    call_args = bg_spy.call_args_list[0][1]  # Get kwargs of the first call
    assert call_args['to'] == parent_email_not_migrated
    assert call_args['reset_token'] == pin_reset_request.request_token
    assert call_args['lang'] == constants.AUTH_PARENT_LANGUAGE  # Use the constant instead of hardcoded value