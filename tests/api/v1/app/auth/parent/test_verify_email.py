import uuid

import pytest
from fastapi import status
from sqlalchemy.orm import Session

from api.v1.app.auth.parent.schemas.request import VerifyAccountRequest
from api.v1.common.schemas import AppErrorCode
from db.models import Account
from tests.data import constants

VERIFY_EMAIL_URL = "/api/v1/auth/parent/verify-email"

@pytest.fixture
def unverified_parent(db_session: Session) -> Account:
    parent = Account(
        email=constants.AUTH_PARENT_EMAIL.lower(),
        language=constants.AUTH_PARENT_LANGUAGE,
        pin_hash="somehash",  # Not relevant for verification itself
        is_verified=False,
        verification_code=constants.AUTH_PARENT_VERIFICATION_CODE,
        public_id=constants.AUTH_PARENT_PUBLIC_ID,
        stripe_customer_id=constants.AUTH_PARENT_STRIPE_CUSTOMER_ID
    )
    db_session.add(parent)
    db_session.commit()
    db_session.refresh(parent)
    return parent

def test_parent_verify_email_success(client, unverified_parent: Account, db_session: Session):
    """Test successful parent email verification."""
    verify_data = VerifyAccountRequest(
        email=constants.AUTH_PARENT_EMAIL,
        verification_code=constants.AUTH_PARENT_VERIFICATION_CODE
    )
    response = client.post(VERIFY_EMAIL_URL, json=verify_data.model_dump())

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert response_data["message"] == "Account verified successfully"
    assert response_data["is_verified"] is True

    db_session.refresh(unverified_parent)
    assert unverified_parent.is_verified is True

def test_parent_verify_email_account_not_found(client):
    """Test email verification for a non-existent parent account."""
    verify_data = VerifyAccountRequest(
        email="<EMAIL>",
        verification_code=constants.AUTH_PARENT_VERIFICATION_CODE
    )
    response = client.post(VERIFY_EMAIL_URL, json=verify_data.model_dump())

    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.USER_NOT_FOUND

def test_parent_verify_email_already_verified(client, db_session: Session):
    """Test email verification for an already verified parent account."""
    parent = Account(
        email="<EMAIL>",
        language=constants.AUTH_PARENT_LANGUAGE,  # Use constant
        is_verified=True,  # Already verified
        verification_code=constants.AUTH_PARENT_VERIFICATION_CODE,  # Use constant
        public_id=str(uuid.uuid4()),
        stripe_customer_id="cus_alreadyverified"
    )
    db_session.add(parent)
    db_session.commit()

    verify_data = VerifyAccountRequest(
        email="<EMAIL>",
        verification_code=constants.AUTH_PARENT_VERIFICATION_CODE
    )
    response = client.post(VERIFY_EMAIL_URL, json=verify_data.model_dump())

    assert response.status_code == status.HTTP_409_CONFLICT
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.ACCOUNT_ALREADY_VERIFIED

def test_parent_verify_email_incorrect_code(client, unverified_parent: Account):
    """Test email verification with an incorrect verification code."""
    verify_data = VerifyAccountRequest(
        email=constants.AUTH_PARENT_EMAIL,
        verification_code="xxxxxx"  # Incorrect code
    )
    response = client.post(VERIFY_EMAIL_URL, json=verify_data.model_dump())

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.INVALID_VERIFICATION_CODE