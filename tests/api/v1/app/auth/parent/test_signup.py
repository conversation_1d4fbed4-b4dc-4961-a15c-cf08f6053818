from sqlalchemy.orm import Session
from pytest_mock import Mo<PERSON><PERSON><PERSON><PERSON>
from fastapi import status, BackgroundTasks
from jose import jwt
import json
from loguru import logger

from core.config.settings import settings
from api.v1.app.auth.parent.schemas.request import SignupRequest
# Corrected import for SignupResponse from parent's perspective
from api.v1.app.auth.parent.schemas.response import SignupResponse
from api.v1.app.auth.schemas.shared_schemas import UserTypeEnum
from db.models import (
    Account, ChildAccount, SignUp, Trial, MarketingConsent,
    PriceVersion, PriceEligibility, AccountAuthSecurity
)
from sqlalchemy import select
from tests.data import constants
from api.v1.common.schemas import AppErrorCode

SIGNUP_URL = "/api/v1/auth/parent/signup"


def test_parent_signup_success(client, db_session: Session, mocker: MockerFixture):
    """Test successful parent signup."""
    bg_spy = mocker.spy(BackgroundTasks, "add_task")

    price_versions = db_session.execute(
        select(PriceVersion)
    ).scalars().all()
    price_version_overview = "\n".join(
        f"{pv.public_id}: {pv.name}" for pv in price_versions
    )
    logger.info("Price versions:\n{}", price_version_overview)

    signup_data = SignupRequest(
        email=constants.AUTH_PARENT_EMAIL,
        language=constants.AUTH_PARENT_LANGUAGE, # Pass string value of enum
        system_options=["luxembourgish"],
        year_options=["7e"],
        marketing_consent=True,
        user_agent="TestAgent/1.0",
        marketing_consent_version="v1.0",
        privacy_policy_version="v1.2",
        signup_source="pytest",
        signup_searchparams=json.dumps({"utm_source": "test"}),
        login_pin=constants.AUTH_PARENT_PIN
    )

    response = client.post(
        SIGNUP_URL,
        json=signup_data.model_dump(),
        headers={"x-forwarded-for": "127.0.0.1"}
    )

    assert response.status_code == status.HTTP_201_CREATED
    response_data_obj = SignupResponse(**response.json())

    assert response_data_obj.access_token is not None
    user_info = response_data_obj.user
    assert user_info.email == signup_data.email.lower()
    assert user_info.user_type == UserTypeEnum.PARENT
    assert user_info.language.value == signup_data.language
    # Removed first_name assertion - ParentUser doesn't have this field

    assert isinstance(response_data_obj.subscriptions, list)
    assert response_data_obj.subscriptions == []


    decoded_token = jwt.decode(
        response_data_obj.access_token,
        settings.SECRET_KEY,
        algorithms=[settings.JWT_ALGORITHM]
    )
    assert decoded_token["user_type"] == "parent"
    assert decoded_token["email"] == constants.AUTH_PARENT_EMAIL.lower()
    assert decoded_token["account_public_id"] is not None
    assert decoded_token["language"] == signup_data.language


    parent_account = db_session.execute(
        select(Account).where(
            Account.email == constants.AUTH_PARENT_EMAIL.lower()
        )
    ).scalar_one_or_none()
    assert parent_account is not None
    assert str(parent_account.public_id) == decoded_token["account_public_id"]
    assert parent_account.language.value == constants.AUTH_PARENT_LANGUAGE
    assert parent_account.is_verified is False
    assert parent_account.pin_hash is not None
    assert parent_account.is_auth_migrated is True

    auth_security = db_session.execute(
        select(AccountAuthSecurity).where(AccountAuthSecurity.account_id == parent_account.id)
    ).scalar_one_or_none()
    assert auth_security is not None

    signup_record = db_session.execute(
        select(SignUp).where(SignUp.account_id == parent_account.id)
    ).scalar_one_or_none()
    assert signup_record is not None
    assert signup_record.source == "pytest"

    trial_record = db_session.execute(
        select(Trial).where(Trial.account_id == parent_account.id)
    ).scalar_one_or_none()
    assert trial_record is not None
    assert trial_record.status == "active"

    marketing_consent_record = db_session.execute(
        select(MarketingConsent).where(MarketingConsent.account_id == parent_account.id)
    ).scalar_one_or_none()
    assert marketing_consent_record is not None

    price_eligibility_record = db_session.execute(
        select(PriceEligibility).where(PriceEligibility.account_id == parent_account.id)
    ).scalar_one_or_none()
    assert price_eligibility_record is not None

    bg_spy.assert_called_with(
        mocker.ANY,
        mocker.ANY,
        to=constants.AUTH_PARENT_EMAIL,
        verification_code=parent_account.verification_code,
        lang=constants.AUTH_PARENT_LANGUAGE
    )


def test_parent_signup_email_already_exists_parent(
    client, db_session: Session
):
    """Test parent signup with an email that already exists for another parent."""
    existing_parent = Account(
        email=constants.AUTH_PARENT_EMAIL.lower(),
        language=constants.AUTH_PARENT_LANGUAGE,
        pin_hash=constants.AUTH_PARENT_PIN_HASH,
        stripe_customer_id=constants.AUTH_PARENT_STRIPE_CUSTOMER_ID,
        public_id=constants.AUTH_PARENT_PUBLIC_ID
    )
    db_session.add(existing_parent)
    db_session.commit()

    signup_data = SignupRequest(
        email=constants.AUTH_PARENT_EMAIL,
        language=constants.AUTH_PARENT_LANGUAGE,
        system_options=[],
        year_options=[],
        marketing_consent=False,
        user_agent="TestAgent",
        marketing_consent_version="", # Corrected from marketing_consent_text
        privacy_policy_version="",
        login_pin=constants.AUTH_PARENT_PIN,
        signup_source="pytest",
        signup_searchparams="{}"
    )
    response = client.post(SIGNUP_URL, json=signup_data.model_dump())

    assert response.status_code == status.HTTP_409_CONFLICT
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.PARENT_ACCOUNT_ALREADY_EXISTS

def test_parent_signup_email_already_exists_child(
    client, db_session: Session
):
    """Test parent signup with an email that already exists for a child."""
    existing_child = ChildAccount(
        email=constants.AUTH_CHILD_EMAIL,
        name=constants.AUTH_CHILD_NAME,
        pin=constants.AUTH_CHILD_PIN,
        language=constants.AUTH_CHILD_LANGUAGE,
        public_id=constants.AUTH_CHILD_PUBLIC_ID
    )
    db_session.add(existing_child)
    db_session.commit()

    signup_data = SignupRequest(
        email=constants.AUTH_CHILD_EMAIL,
        language=constants.AUTH_PARENT_LANGUAGE,
        system_options=["classroom"],
        year_options=["7e"],
        marketing_consent=False,
        signup_source="pytest",
        signup_searchparams="{}",
        user_agent="TestAgent",
        marketing_consent_version="", # Corrected from marketing_consent_text
        privacy_policy_version="",
        login_pin=constants.AUTH_PARENT_PIN
    )
    response = client.post(SIGNUP_URL, json=signup_data.model_dump())

    assert response.status_code == status.HTTP_409_CONFLICT
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.CHILD_ACCOUNT_ALREADY_EXISTS


def test_parent_signup_stripe_error(client, mocker: MockerFixture):
    """Test parent signup when Stripe customer creation fails."""
    mocker.patch(
        "api.v1.app.auth.parent.utils.account_creation.stripe.Customer.create",
        side_effect=Exception("Stripe API Error")
    )

    signup_data = SignupRequest(
        email="<EMAIL>",
        language=constants.AUTH_PARENT_LANGUAGE,
        system_options=["classroom"],
        year_options=["7e"],
        marketing_consent=False,
        signup_source="pytest",
        signup_searchparams="{}",
        user_agent="TestAgent",
        marketing_consent_version="", # Corrected from marketing_consent_text
        privacy_policy_version="",
        login_pin=constants.AUTH_PARENT_PIN
    )
    response = client.post(SIGNUP_URL, json=signup_data.model_dump())

    assert response.status_code == status.HTTP_503_SERVICE_UNAVAILABLE
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.STRIPE_ERROR