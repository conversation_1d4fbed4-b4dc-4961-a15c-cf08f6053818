import pytest
from fastapi import status
from sqlalchemy.orm import Session
from sqlalchemy import select
from pydantic import ValidationError

from db.models import (
    LearningNode, ChildAccount, Account, Exercise,
    ChildAccountExerciseAssociation, ExerciseStatusEnum
)
from api.v1.app.student.schemas.response import (
    MathNodeContentResponse, AnyExerciseResponse, GetLearningNodeResponse
)
from tests.utils.auth_utils import create_test_child_auth_token, create_test_parent_auth_token
from tests.data import constants
from api.v1.common.schemas import AppErrorCode

GET_LN_CONTENT_URL_PREFIX = "/api/v1/student/learning-nodes"


# Fixtures from test_get_exercises can be reused or adapted if needed
@pytest.fixture
def student_for_ln_content_tests(db_session: Session) -> ChildAccount:
    stmt = select(ChildAccount).where(ChildAccount.public_id == constants.MOCK_CHILD_PUBLIC_ID)
    student = db_session.scalars(stmt).one()
    return student


@pytest.fixture
def root_learning_node(db_session: Session) -> LearningNode:
    """Fixture for a root learning node (e.g., one directly under a chapter)."""
    # ln-math-1 is used as it has exercises and is defined in content_data
    stmt = select(LearningNode).where(LearningNode.public_id == "ln-math-1")
    ln = db_session.scalars(stmt).one()
    # Ensure it has some child relationships for recursive test if applicable, or test as is
    return ln


def test_get_learning_node_content_success(
    client, 
    student_for_ln_content_tests: ChildAccount,
    root_learning_node: LearningNode, 
    db_session: Session
):
    """Test successful retrieval of learning node content, including exercises and status."""
    student_token = create_test_child_auth_token(student_for_ln_content_tests)
    headers = {"x-auth-token": student_token}

    # Setup an association for an exercise within this learning node
    exercise_mc1_stmt = select(Exercise).where(
        Exercise.public_id == "ex-mc-1"  # ex-mc-1 is linked to ln-math-1
    )
    exercise_mc1 = db_session.scalars(exercise_mc1_stmt).one_or_none()
    if exercise_mc1:
        association = ChildAccountExerciseAssociation(
            child_account_id=student_for_ln_content_tests.id,
            exercise_id=exercise_mc1.id,
            status=ExerciseStatusEnum.INCORRECT
        )
        db_session.add(association)
        db_session.commit()

    url = f"{GET_LN_CONTENT_URL_PREFIX}/{root_learning_node.public_id}"
    response = client.get(url, headers=headers)

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    try:
        # Step 1: Validate against the specific expected type
        ln_response = GetLearningNodeResponse.model_validate(response_data)
        # Step 2: If Step 1 passes, explicitly validate the nested content as well
        # content_response = MathContentResponse.model_validate(response_data['content'])
        # Step 3: And the exercises within content
        # for ex_data in response_data['content']['exercises']:
        #     AnyExerciseResponse.model_validate(ex_data)

    except ValidationError as e:
        # Pydantic ValidationError gives more details
        pytest.fail(
            f"Response validation failed against "
            f"GetLearningNodeResponse: {e}\nResponse Data: {response_data}"
        ) 
    except Exception as e:
        # Catch other potential errors during parsing or validation
        pytest.fail(
            f"An unexpected error occurred during validation: {e}\n"
            f"Response Data: {response_data}"
        )

    # Assertions based on the expected structure (assuming ln-math-1 is a MathNode)
    assert isinstance(ln_response, GetLearningNodeResponse)
    assert ln_response.public_id == root_learning_node.public_id
    assert ln_response.title == root_learning_node.title
    assert ln_response.type == root_learning_node.node_type.value  # Compare enum value
    assert isinstance(ln_response.content, MathNodeContentResponse)
    # Video URL may be None if the video file is not found in the mock data
    # assert ln_response.content.video_url is not None  # video_url in content
    assert ln_response.content.notes is not None
    assert len(ln_response.content.exercises) > 0
    assert isinstance(ln_response.content.exercises[0], AnyExerciseResponse)
    if exercise_mc1:  # If ex-mc-1 was found and association created
        found_exercise_in_content = any(
            ex.public_id == "ex-mc-1" for ex in ln_response.content.exercises
        )
        assert found_exercise_in_content, "ex-mc-1 not found in exercises"
        for ex_item in ln_response.content.exercises:
            if ex_item.public_id == "ex-mc-1":
                assert ex_item.status == ExerciseStatusEnum.INCORRECT

    # The GetLearningNodeResponse doesn't have a childNodes field
    # Child nodes would be fetched separately if needed


def test_get_learning_node_content_not_found(client, student_for_ln_content_tests: ChildAccount):
    """Test retrieval for a non-existent learning node."""
    student_token = create_test_child_auth_token(student_for_ln_content_tests)
    headers = {"x-auth-token": student_token}

    url = f"{GET_LN_CONTENT_URL_PREFIX}/non-existent-ln-id"
    response = client.get(url, headers=headers)

    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.LEARNING_NODE_NOT_FOUND


def test_get_learning_node_content_unauthorized_no_token(client, root_learning_node: LearningNode):
    """Test retrieval without an authentication token."""
    url = f"{GET_LN_CONTENT_URL_PREFIX}/{root_learning_node.public_id}"
    response = client.get(url)

    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.AUTHENTICATION_REQUIRED


def test_get_ln_content_with_parent_token(client, db_session: Session, root_learning_node: LearningNode):
    """Test retrieval with a parent token (should be unauthorized)."""
    parent_stmt = select(Account).where(Account.public_id == constants.MOCK_PARENT_PUBLIC_ID)
    parent = db_session.scalars(parent_stmt).one()
    parent_token = create_test_parent_auth_token(parent)
    headers = {"x-auth-token": parent_token}

    url = f"{GET_LN_CONTENT_URL_PREFIX}/{root_learning_node.public_id}"
    response = client.get(url, headers=headers)

    # Service checks auth_data.user_type == "student"
    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    response_data = response.json()
    # Error from service
    assert response_data["error_code"] == AppErrorCode.AUTHENTICATION_REQUIRED