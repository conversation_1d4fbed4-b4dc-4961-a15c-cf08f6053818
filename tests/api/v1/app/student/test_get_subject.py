import pytest
from sqlalchemy.orm import Session
from sqlalchemy import select

from db.models.account import ChildAccount
from db.models.content import Subject
from tests.utils.auth_utils import create_test_child_auth_token
from tests.data import constants

GET_SUBJECT_URL_PREFIX = "/api/v1/student/subjects"

@pytest.fixture
def student_account(db_session: Session) -> ChildAccount:
    """Fixture to get the mock student account seeded by mock_data_loader."""
    stmt = select(ChildAccount).where(ChildAccount.public_id == constants.MOCK_CHILD_PUBLIC_ID)
    student = db_session.execute(stmt).scalar_one_or_none()
    if not student:
        pytest.fail(
            f"Mock student with public_id {constants.MOCK_CHILD_PUBLIC_ID} not found in test DB."
        )
    return student

@pytest.fixture
def subject_with_content(db_session: Session) -> Subject:
    """Fixture to get a subject with content for testing."""
    # Using subj-1 from content_data.py which has chapters and learning nodes
    stmt = select(Subject).where(Subject.public_id == "subj-1")
    subject = db_session.execute(stmt).scalar_one_or_none()
    if not subject:
        pytest.fail("Subject with public_id 'subj-1' not found in test DB.")
    return subject

def test_get_subject_success(client, student_account: ChildAccount, subject_with_content: Subject):
    """Test successful retrieval of a specific subject's details."""
    student_token = create_test_child_auth_token(student_account)
    headers = {"x-auth-token": student_token}
    
    url = f"{GET_SUBJECT_URL_PREFIX}/{subject_with_content.public_id}"
    response = client.get(url, headers=headers)
    
    assert response.status_code == 200
    response_data = response.json()
    
    assert response_data["subject"]["public_id"] == subject_with_content.public_id
    assert "chapters" in response_data
    # Note: learning_nodes would be nested within chapters, not at the top level
    

def test_get_subject_not_found(client, student_account: ChildAccount):
    """Test retrieval for a non-existent subject."""
    student_token = create_test_child_auth_token(student_account)
    headers = {"x-auth-token": student_token}
    
    url = f"{GET_SUBJECT_URL_PREFIX}/non-existent-subject-id"
    response = client.get(url, headers=headers)
    
    assert response.status_code == 404
    response_data = response.json()
    assert response_data["error_code"] == "SUBJECT_NOT_FOUND"

def test_get_subject_unauthorized_no_token(client, subject_with_content: Subject):
    """Test retrieval without an authentication token."""
    url = f"{GET_SUBJECT_URL_PREFIX}/{subject_with_content.public_id}"
    response = client.get(url)
    
    assert response.status_code == 401
