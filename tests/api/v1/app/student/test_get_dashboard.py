import pytest
from fastapi import status
from sqlalchemy.orm import Session
from sqlalchemy import select

from db.models import ChildAccount, Year
from api.v1.app.student.schemas.response import (
    GetStudentDashboardResponse,
)
from tests.utils.auth_utils import create_test_child_auth_token, create_test_parent_auth_token
from tests.data import constants

GET_DASHBOARD_URL = "/api/v1/student/dashboard/get-dashboard"

@pytest.fixture
def student_for_dashboard(db_session: Session) -> ChildAccount:
    """Fixture to get the mock student account seeded by mock_data_loader."""
    stmt = select(ChildAccount).where(ChildAccount.public_id == constants.MOCK_CHILD_PUBLIC_ID)
    student = db_session.execute(stmt).scalar_one_or_none()
    if not student:
        pytest.fail(
            f"Mock student with public_id {constants.MOCK_CHILD_PUBLIC_ID} not found in test DB."
        )
    return student

def test_get_dashboard_success(client, student_for_dashboard: ChildAccount, db_session: Session):
    """Test successful retrieval of dashboard data for a student."""
    student_token = create_test_child_auth_token(student_for_dashboard)
    headers = {"x-auth-token": student_token}

    response = client.get(GET_DASHBOARD_URL, headers=headers)

    assert response.status_code == status.HTTP_200_OK
    dashboard_response = GetStudentDashboardResponse.model_validate(response.json())
    
    # The response has a single profile, not a list of profiles
    profile = dashboard_response.profile
    assert profile.public_id == student_for_dashboard.public_id
    assert profile.name == student_for_dashboard.name
    assert profile.email == student_for_dashboard.email
    assert profile.pin == student_for_dashboard.pin
    
    if student_for_dashboard.year_id:
        year_stmt = select(Year).where(Year.id == student_for_dashboard.year_id)
        year = db_session.execute(year_stmt).scalar_one_or_none()
        assert profile.school_year.public_id == year.public_id
        assert profile.school_year.name == year.name
    
    assert len(dashboard_response.subjects) > 0
    for subject in dashboard_response.subjects:
        assert subject.public_id
        assert subject.name
        assert isinstance(subject.chapter_count, int)
        # The Subject model in the response doesn't have a 'type' field

def test_get_dashboard_unauthorized_no_token(client):
    """Test get dashboard without an auth token."""
    response = client.get(GET_DASHBOARD_URL)
    assert response.status_code == status.HTTP_401_UNAUTHORIZED

def test_get_dashboard_unauthorized_parent_token(client, db_session: Session):
    """Test get dashboard with a parent token (should be unauthorized)."""
    parent_stmt = select(ChildAccount).where(
        ChildAccount.public_id == constants.MOCK_PARENT_PUBLIC_ID
    )
    parent_user = db_session.execute(parent_stmt).scalar_one_or_none()
    
    if parent_user:
        parent_token = create_test_parent_auth_token(parent_user)
        headers = {"x-auth-token": parent_token}
        
        response = client.get(GET_DASHBOARD_URL, headers=headers)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert response.json()["error_code"] == "AUTHENTICATION_REQUIRED"
