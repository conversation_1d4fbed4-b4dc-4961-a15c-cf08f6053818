from datetime import datetime, timedelta, UTC

import pytest
from fastapi import status
from sqlalchemy import select
from sqlalchemy.orm import Session

from api.v1.app.auth.utils import create_access_token

from api.v1.app.student.schemas.response import (
    DailyProgressResponse,
    DetailedSubjectProgressResponse,
    ProgressOverviewResponse,
)
from db.models import (
    Account,
    ChildAccount,
    ChildAccountExerciseAssociation,
    Exercise,
    ExerciseStatusEnum,
    Year,
)
from tests.data import constants
from tests.utils.auth_utils import (
    create_test_child_auth_token,
    create_test_parent_auth_token,
)

GET_PROGRESS_URL_PREFIX = "/api/v1/student"


@pytest.fixture
def student_for_progress_tests(db_session: Session) -> ChildAccount:
    """Fixture to get the mock child account, ensuring year is set."""
    stmt = select(ChildAccount).where(
        ChildAccount.public_id == constants.MOCK_CHILD_PUBLIC_ID
    )
    student = db_session.scalars(stmt).one()
    if not student.year_id:  # Ensure year_id is set from mock_data_loader
        year_stmt = select(Year).where(Year.public_id == "year-1")  # from content_data.py
        year = db_session.scalars(year_stmt).one()
        student.year_id = year.id
        db_session.commit()
        db_session.refresh(student)
    return student


def setup_progress_data(db_session: Session, student: ChildAccount):
    """Helper to create some exercise associations for progress calculation."""
    # Get some exercises from mock data (assuming they are linked to subjects in
    # student's year) ex-mc-1 (subj-1, year-1), ex-input-1 (subj-1, year-1 via
    # ln-vocab-1) Add ex-cloze-1, ex-dd-1 (also linked via ln-vocab-1)
    ex_mc1 = db_session.scalars(select(Exercise).where(
        Exercise.public_id == "ex-mc-1"
    )).one()

    ex_input1 = db_session.scalars(select(Exercise).where(
        Exercise.public_id == "ex-input-1"
    )).one()

    ex_cloze1 = db_session.scalars(select(Exercise).where(
        Exercise.public_id == "ex-cloze-1"
    )).one()

    ex_dd1 = db_session.scalars(select(Exercise).where(
        Exercise.public_id == "ex-dropdown-1"
    )).one()

    today = datetime.now(UTC)

    # Attempt 1: Correct, today
    assoc1 = ChildAccountExerciseAssociation(
        child_account_id=student.id,
        exercise_id=ex_mc1.id,
        status=ExerciseStatusEnum.CORRECT,
        last_attempted_at=today - timedelta(hours=1)
    )
    # Attempt 2: Incorrect, yesterday
    assoc2 = ChildAccountExerciseAssociation(
        child_account_id=student.id,
        exercise_id=ex_input1.id,
        status=ExerciseStatusEnum.INCORRECT,
        last_attempted_at=today - timedelta(days=1, hours=2)
    )
    # Attempt 3: Correct, 3 days ago
    assoc3 = ChildAccountExerciseAssociation(
        child_account_id=student.id,
        exercise_id=ex_cloze1.id,
        status=ExerciseStatusEnum.CORRECT,
        last_attempted_at=today - timedelta(days=3, hours=1)
    )
    # Attempt 4: Correct, 8 days ago (outside 7-day window for daily,
    # but affects weekly trend)
    assoc4 = ChildAccountExerciseAssociation(
        child_account_id=student.id,
        exercise_id=ex_dd1.id,
        status=ExerciseStatusEnum.CORRECT,
        last_attempted_at=today - timedelta(days=8, hours=1)
    )

    db_session.add_all([assoc1, assoc2, assoc3, assoc4])
    db_session.commit()


def test_get_progress_overview_success(
    client, 
    student_for_progress_tests: ChildAccount, 
    db_session: Session
):
    """Test successful retrieval of progress overview."""
    setup_progress_data(db_session, student_for_progress_tests)
    student_token = create_test_child_auth_token(student_for_progress_tests)
    headers = {"x-auth-token": student_token}

    url = f"{GET_PROGRESS_URL_PREFIX}/{student_for_progress_tests.public_id}/progress/detailed"
    response = client.get(url, headers=headers)

    assert response.status_code == status.HTTP_200_OK
    progress_response = ProgressOverviewResponse.model_validate(response.json())

    assert isinstance(progress_response.subjects, list)
    # Based on setup_progress_data and content_data, subj-1 should appear
    found_subj1 = False
    for subj_prog in progress_response.subjects:
        assert isinstance(subj_prog, DetailedSubjectProgressResponse)
        assert subj_prog.subject is not None
        assert subj_prog.public_id is not None
        if subj_prog.public_id == "subj-1":  # From content_data.py
            found_subj1 = True
            # At least ex-mc-1 and ex-input-1 attempted
            assert subj_prog.total_exercises >= 2
            assert 0 <= subj_prog.correct_percentage <= 100
            assert isinstance(subj_prog.weekly_trend, str)
            assert isinstance(subj_prog.daily_progress, list)
            assert len(subj_prog.daily_progress) == 7  # For last 7 days
            for daily in subj_prog.daily_progress:
                assert isinstance(daily, DailyProgressResponse)
                assert daily.day in [
                    "Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"
                ]
    
    assert found_subj1, (
        "Expected subject 'subj-1' not found in progress overview."
    )


def test_get_progress_overview_unauthorized_no_token(client, student_for_progress_tests: ChildAccount):
    """Test retrieval without an authentication token."""
    url = f"{GET_PROGRESS_URL_PREFIX}/{student_for_progress_tests.public_id}/progress/detailed"
    response = client.get(url)

    assert response.status_code == status.HTTP_401_UNAUTHORIZED


def test_get_progress_overview_other_student_id_in_path(client, student_for_progress_tests: ChildAccount):
    """Test retrieval when token is for one student but path requests another (should be forbidden)."""
    student_token = create_test_child_auth_token(student_for_progress_tests)
    headers = {"x-auth-token": student_token}

    other_student_public_id = "some-other-student-public-id"
    url = f"{GET_PROGRESS_URL_PREFIX}/{other_student_public_id}/progress/detailed"
    response = client.get(url, headers=headers)

    # Route logic checks this
    assert response.status_code == status.HTTP_401_UNAUTHORIZED


def test_get_progress_overview_with_parent_token(
    client, 
    db_session: Session, 
    student_for_progress_tests: ChildAccount
):
    """Test retrieval with a parent token (should be forbidden by current route logic)."""
    parent_stmt = select(Account).where(Account.public_id == constants.MOCK_PARENT_PUBLIC_ID)
    parent = db_session.scalars(parent_stmt).one()
    parent_token = create_test_parent_auth_token(parent)
    headers = {"x-auth-token": parent_token}

    url = f"{GET_PROGRESS_URL_PREFIX}/{student_for_progress_tests.public_id}/progress/detailed"
    response = client.get(url, headers=headers)

    # Route logic checks auth_data.user_type == UserType.child
    assert response.status_code == status.HTTP_401_UNAUTHORIZED


def test_get_progress_overview_student_not_found_in_token_data(
    client, 
    db_session: Session
):
    """Test when AuthDependency returns valid structure but account is not ChildAccount."""
    # This simulates a scenario where AuthDependency might return a Parent Account
    # object in the `account` field of AuthDependencyResponse, even if
    # user_type was child (e.g. data error).
    parent_stmt = select(Account).where(
        Account.public_id == constants.MOCK_PARENT_PUBLIC_ID
    )
    parent = db_session.scalars(parent_stmt).one()
    
    # Create a token that looks like a child token but carries parent account data
    # This is to test the `isinstance(auth_data.account, ChildAccount)` check in
    # the route
    faulty_child_token_data = {
        "account_public_id": parent.public_id,  # Using parent's public_id
        "user_type": "child",  # But claiming to be a child
        "name": "Faulty Child"
    }
    faulty_token = create_access_token(data=faulty_child_token_data)
    headers = {"x-auth-token": faulty_token}

    # The path student_public_id should match the one in the token for the
    # initial auth check to pass
    url = f"{GET_PROGRESS_URL_PREFIX}/{parent.public_id}/progress/detailed"
    response = client.get(url, headers=headers)

    # The route has a check: `if not isinstance(auth_data.account, ChildAccount):`
    # This should raise NotFoundError with CHILD_ACCOUNT_NOT_FOUND
    assert response.status_code == status.HTTP_404_NOT_FOUND