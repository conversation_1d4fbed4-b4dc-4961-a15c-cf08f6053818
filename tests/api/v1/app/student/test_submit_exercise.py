# from datetime import timed<PERSON>ta
# from typing import Dict, List, cast

# import pytest
# from fastapi import status
# from sqlalchemy import select
# from sqlalchemy.orm import Session

# from api.v1.common.schemas import AppErrorCode
# from db.models import (
#     Account,
#     ChildAccount,
#     Exercise,
#     ExerciseStatusEnum,
#     ChildAccountExerciseAssociation,
# )
# from api.v1.app.student.schemas import (
#     ExerciseSubmissionRequest,
#     ExerciseSubmissionResponse,
# )
# from tests.data import constants
# from tests.utils.auth_utils import (
#     create_test_child_auth_token,
#     create_test_parent_auth_token,
# )

# SUBMIT_EXERCISE_URL = "/api/v1/student/exercises/submit-exercise"  # noqa: E501


# @pytest.fixture
# def student_for_submission_tests(db_session: Session) -> ChildAccount:
#     stmt = select(ChildAccount).where(
#         ChildAccount.public_id == constants.MOCK_CHILD_PUBLIC_ID
#     )
#     return db_session.scalars(stmt).one()


# @pytest.fixture
# def mc_simple_exercise(db_session: Session) -> Exercise:
#     stmt = select(Exercise).where(
#         Exercise.public_id == "ex-mc-1"
#     )
#     ex = db_session.scalars(stmt).one()
#     return ex


# @pytest.fixture
# def input_exercise(db_session: Session) -> Exercise:
#     stmt = select(Exercise).where(Exercise.public_id == "ex-input-1")
#     ex = db_session.scalars(stmt).one()
#     return ex


# @pytest.fixture
# def mc_multi_exercise(db_session: Session) -> Exercise:
#     stmt = select(Exercise).where(Exercise.public_id == "ex-mc-multi-1")
#     ex = db_session.scalars(stmt).one()
#     return ex


# @pytest.fixture
# def cloze_exercise(db_session: Session) -> Exercise:
#     stmt = select(Exercise).where(Exercise.public_id == "ex-cloze-1")
#     ex = db_session.scalars(stmt).one()
#     return ex


# @pytest.fixture
# def dropdown_exercise(db_session: Session) -> Exercise:
#     stmt = select(Exercise).where(Exercise.public_id == "ex-dropdown-1")
#     ex = db_session.scalars(stmt).one()
#     return ex


# @pytest.fixture
# def highlight_exercise(db_session: Session) -> Exercise:
#     stmt = select(Exercise).where(Exercise.public_id == "ex-highlight-1")
#     ex = db_session.scalars(stmt).one()
#     return ex


# @pytest.fixture
# def matching_exercise(db_session: Session) -> Exercise:
#     stmt = select(Exercise).where(
#         Exercise.public_id == "ex-matching-1"
#     )
#     ex = db_session.scalars(stmt).one()
#     return ex


# @pytest.fixture
# def categorize_exercise(db_session: Session) -> Exercise:
#     stmt = select(Exercise).where(
#         Exercise.public_id == "ex-categorize-1"
#     )
#     ex = db_session.scalars(stmt).one()
#     return ex


# @pytest.fixture
# def error_correction_exercise(db_session: Session) -> Exercise:
#     stmt = select(Exercise).where(
#         Exercise.public_id == "ex-error-correction-1"
#     )
#     ex = db_session.scalars(stmt).one()
#     return ex


# # --- Helper to verify association ---

# def _verify_association(
#     db_session: Session,
#     student_id: int,
#     exercise_id: int,
#     expected_status: ExerciseStatusEnum,
#     expected_attempts: int,
# ) -> None:
#     assoc_stmt = select(ChildAccountExerciseAssociation).where(
#         ChildAccountExerciseAssociation.child_account_id == student_id,
#         ChildAccountExerciseAssociation.exercise_id == exercise_id,
#     )
#     association = db_session.scalars(assoc_stmt).one_or_none()
#     assert association is not None
#     assert (
#         association.status == expected_status
#         and association.attempts_count == expected_attempts
#     )


# # --- Test Cases ---


# def test_submit_mc_simple_correct(
#     client,
#     student_for_submission_tests: ChildAccount,
#     mc_simple_exercise: Exercise,
#     db_session: Session,
# ) -> None:
#     student_token = create_test_child_auth_token(student_for_submission_tests)
#     headers = {"x-auth-token": student_token}

#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=mc_simple_exercise.public_id,
#         answer="opt2",
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )

#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert (
#         submission_response.isCorrect is True
#         and submission_response.correctAnswer == "opt2"
#     )
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         mc_simple_exercise.id,
#         ExerciseStatusEnum.CORRECT,
#         1,
#     )


# def test_submit_mc_simple_incorrect(
#     client,
#     student_for_submission_tests: ChildAccount,
#     mc_simple_exercise: Exercise,
#     db_session: Session,
# ) -> None:
#     student_token = create_test_child_auth_token(student_for_submission_tests)
#     headers = {"x-auth-token": student_token}

#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=mc_simple_exercise.public_id,
#         answer="opt1",
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )

#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert (
#         submission_response.isCorrect is False
#         and submission_response.correctAnswer == "opt2"
#     )
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         mc_simple_exercise.id,
#         ExerciseStatusEnum.INCORRECT,
#         1,
#     )


# def test_submit_input_exercise_correct(
#     client,
#     student_for_submission_tests: ChildAccount,
#     input_exercise: Exercise,
#     db_session: Session,
# ) -> None:
#     student_token = create_test_child_auth_token(student_for_submission_tests)
#     headers = {"x-auth-token": student_token}

#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=input_exercise.public_id,
#         answer="Bonjour",
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )

#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert (
#         submission_response.isCorrect is True
#         and submission_response.correctAnswer == "Bonjour"
#     )
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         input_exercise.id,
#         ExerciseStatusEnum.CORRECT,
#         1,
#     )


# def test_submit_input_exercise_incorrect(
#     client,
#     student_for_submission_tests: ChildAccount,
#     input_exercise: Exercise,
#     db_session: Session,
# ) -> None:
#     student_token = create_test_child_auth_token(student_for_submission_tests)
#     headers = {"x-auth-token": student_token}

#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=input_exercise.public_id,
#         answer="Au revoir",
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )

#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert (
#         submission_response.isCorrect is False
#         and submission_response.correctAnswer == "Bonjour"
#     )
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         input_exercise.id,
#         ExerciseStatusEnum.INCORRECT,
#         1,
#     )


# def test_submit_mc_multi_correct(
#     client,
#     student_for_submission_tests: ChildAccount,
#     mc_multi_exercise: Exercise,
#     db_session: Session,
# ) -> None:
#     student_token = create_test_child_auth_token(student_for_submission_tests)
#     headers = {"x-auth-token": student_token}

#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=mc_multi_exercise.public_id,
#         answer=["opt_mc_multi_1", "opt_mc_multi_3"],
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )

#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert (
#         submission_response.isCorrect is True
#         and sorted(cast(List[str], submission_response.correctAnswer))
#         == sorted(["opt_mc_multi_1", "opt_mc_multi_3"])
#     )
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         mc_multi_exercise.id,
#         ExerciseStatusEnum.CORRECT,
#         1,
#     )


# def test_submit_mc_multi_incorrect(
#     client,
#     student_for_submission_tests: ChildAccount,
#     mc_multi_exercise: Exercise,
#     db_session: Session,
# ) -> None:
#     student_token = create_test_child_auth_token(student_for_submission_tests)
#     headers = {"x-auth-token": student_token}

#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=mc_multi_exercise.public_id,
#         answer=["opt_mc_multi_1", "opt_mc_multi_2"],  # Incorrect
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )

#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert (
#         submission_response.isCorrect is False
#         and sorted(cast(List[str], submission_response.correctAnswer))
#         == sorted(["opt_mc_multi_1", "opt_mc_multi_3"])
#     )
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         mc_multi_exercise.id,
#         ExerciseStatusEnum.INCORRECT,
#         1,
#     )


# def test_submit_cloze_correct(
#     client,
#     student_for_submission_tests: ChildAccount,
#     cloze_exercise: Exercise,
#     db_session: Session,
# ) -> None:
#     student_token = create_test_child_auth_token(student_for_submission_tests)
#     headers = {"x-auth-token": student_token}

#     # Correct answer for ex-cloze-1 is ["Paris", "Berlin"]
#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=cloze_exercise.public_id,
#         answer=["Paris", "Berlin"],
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )

#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert (
#         submission_response.isCorrect is True
#         and cast(List[str], submission_response.correctAnswer)
#         == ["Paris", "Berlin"]
#     )
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         cloze_exercise.id,
#         ExerciseStatusEnum.CORRECT,
#         1,
#     )


# def test_submit_cloze_incorrect(
#     client,
#     student_for_submission_tests: ChildAccount,
#     cloze_exercise: Exercise,
#     db_session: Session,
# ) -> None:
#     student_token = create_test_child_auth_token(student_for_submission_tests)
#     headers = {"x-auth-token": student_token}

#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=cloze_exercise.public_id,
#         answer=["London", "Rome"],  # Incorrect
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )

#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert (
#         submission_response.isCorrect is False
#         and cast(List[str], submission_response.correctAnswer)
#         == ["Paris", "Berlin"]
#     )
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         cloze_exercise.id,
#         ExerciseStatusEnum.INCORRECT,
#         1,
#     )


# def test_submit_dropdown_correct(
#     client,
#     student_for_submission_tests: ChildAccount,
#     dropdown_exercise: Exercise,
#     db_session: Session,
# ) -> None:
#     student_token = create_test_child_auth_token(student_for_submission_tests)
#     headers = {"x-auth-token": student_token}

#     # Correct answer for ex-dropdown-1 is ["+"]
#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=dropdown_exercise.public_id,
#         answer=["+"],
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )

#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert (
#         submission_response.isCorrect is True
#         and cast(List[str], submission_response.correctAnswer)
#         == ["+"]
#     )
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         dropdown_exercise.id,
#         ExerciseStatusEnum.CORRECT,
#         1,
#     )


# def test_submit_dropdown_incorrect(
#     client,
#     student_for_submission_tests: ChildAccount,
#     dropdown_exercise: Exercise,
#     db_session: Session,
# ) -> None:
#     student_token = create_test_child_auth_token(student_for_submission_tests)
#     headers = {"x-auth-token": student_token}

#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=dropdown_exercise.public_id,
#         answer=["-"],  # Incorrect
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )

#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert (
#         submission_response.isCorrect is False
#         and cast(List[str], submission_response.correctAnswer)
#         == ["+"]
#     )
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         dropdown_exercise.id,
#         ExerciseStatusEnum.INCORRECT,
#         1,
#     )


# def test_submit_highlight_correct(
#     client,
#     student_for_submission_tests: ChildAccount,
#     highlight_exercise: Exercise,
#     db_session: Session,
# ) -> None:
#     student_token = create_test_child_auth_token(student_for_submission_tests)
#     headers = {"x-auth-token": student_token}

#     # Correct answer for ex-highlight-1 is ["highlight_1", "highlight_3"] (text parts)
#     # Corresponding to indices [1, 3] in the updated mock data
#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=highlight_exercise.public_id,
#         answer=[1, 3], # Send indices
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )

#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert (
#         submission_response.isCorrect is True
#         and sorted(cast(List[str], submission_response.correctAnswer))
#         == sorted(["highlight_1", "highlight_3"])
#     )
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         highlight_exercise.id,
#         ExerciseStatusEnum.CORRECT,
#         1,
#     )


# def test_submit_highlight_incorrect(
#     client,
#     student_for_submission_tests: ChildAccount,
#     highlight_exercise: Exercise,
#     db_session: Session,
# ) -> None:
#     student_token = create_test_child_auth_token(student_for_submission_tests)
#     headers = {"x-auth-token": student_token}

#     # Incorrect answer, e.g., indices [1, 2]
#     # Correct answer for ex-highlight-1 is ["highlight_1", "highlight_3"] (text parts)
#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=highlight_exercise.public_id,
#         answer=[1, 2],  # Send indices, one correct, one incorrect
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )

#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert (
#         submission_response.isCorrect is False
#         and sorted(cast(List[str], submission_response.correctAnswer))
#         == sorted(["highlight_1", "highlight_3"])
#     )
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         highlight_exercise.id,
#         ExerciseStatusEnum.INCORRECT,
#         1,
#     )


# def test_submit_categorize_correct(
#     client,
#     student_for_submission_tests: ChildAccount,
#     categorize_exercise: Exercise,
#     db_session: Session,
# ) -> None:
#     student_token = create_test_child_auth_token(student_for_submission_tests)
#     headers = {"x-auth-token": student_token}

#     # Correct answer for ex-categorize-1 is {"item_apple": "cat_fruit", "item_carrot": "cat_veg"}  # noqa: E501
#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=categorize_exercise.public_id,
#         answer={"item_apple": "cat_fruit", "item_carrot": "cat_veg"},
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )

#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert (
#         submission_response.isCorrect is True
#         and cast(Dict[str, str], submission_response.correctAnswer)
#         == {"item_apple": "cat_fruit", "item_carrot": "cat_veg"}
#     )
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         categorize_exercise.id,
#         ExerciseStatusEnum.CORRECT,
#         1,
#     )


# def test_submit_categorize_incorrect(
#     client,
#     student_for_submission_tests: ChildAccount,
#     categorize_exercise: Exercise,
#     db_session: Session,
# ) -> None:
#     student_token = create_test_child_auth_token(student_for_submission_tests)
#     headers = {"x-auth-token": student_token}

#     # Incorrect answer for ex-categorize-1
#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=categorize_exercise.public_id,
#         answer={"item_apple": "cat_veg", "item_carrot": "cat_fruit"},
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )

#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert (
#         submission_response.isCorrect is False
#         and cast(Dict[str, str], submission_response.correctAnswer)
#         == {"item_apple": "cat_fruit", "item_carrot": "cat_veg"}
#     )
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         categorize_exercise.id,
#         ExerciseStatusEnum.INCORRECT,
#         1,
#     )


# def test_submit_error_correction_correct(
#     client,
#     student_for_submission_tests: ChildAccount,
#     error_correction_exercise: Exercise,
#     db_session: Session,
# ) -> None:
#     student_token = create_test_child_auth_token(student_for_submission_tests)
#     headers = {"x-auth-token": student_token}

#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=error_correction_exercise.public_id,
#         answer="bananas", # Corrected word, matches mock data solution
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )

#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert (
#         submission_response.isCorrect is True
#         and submission_response.correctAnswer == ["bananas"] # Expect list of strings
#     )
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         error_correction_exercise.id,
#         ExerciseStatusEnum.CORRECT,
#         1,
#     )


# def test_submit_error_correction_incorrect(
#     client,
#     student_for_submission_tests: ChildAccount,
#     error_correction_exercise: Exercise,
#     db_session: Session,
# ) -> None:
#     student_token = create_test_child_auth_token(student_for_submission_tests)
#     headers = {"x-auth-token": student_token}

#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=error_correction_exercise.public_id,
#         answer="banannas",  # Incorrect spelling
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )

#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert (
#         submission_response.isCorrect is False
#         and submission_response.correctAnswer == ["bananas"] # Expect list of strings
#     )
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         error_correction_exercise.id,
#         ExerciseStatusEnum.INCORRECT,
#         1,
#     )


# # --- General Error/Auth Tests ---


# def test_submit_exercise_invalid_data(
#     client, student_for_submission_tests: ChildAccount
# ) -> None:
#     student_token = create_test_child_auth_token(student_for_submission_tests)
#     headers = {"x-auth-token": student_token}

#     # Missing answer field
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json={"exercise_public_id": "123"},
#     )
#     assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

#     # Missing exercise_public_id field
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json={"answer": "any"},
#     )
#     assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

#     # Invalid exercise_public_id
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json={"exercise_public_id": "invalid-id", "answer": "any"},
#     )
#     assert (
#         response.status_code == status.HTTP_404_NOT_FOUND
#         and response.json() == {
#             "status": "error",
#             "error_code": "EXERCISE_NOT_FOUND",
#             "message": "Exercise not found"
#         } # Updated expected response
#     )

#     # Invalid answer type
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json={"exercise_public_id": "123", "answer": 123},
#     )
#     assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


# def test_submit_exercise_with_expired_token(
#     client,
#     student_for_submission_tests: ChildAccount,
#     mc_simple_exercise: Exercise,
# ) -> None:
#     # Create an expired token (expired 1 hour ago)
#     from core.auth.jwt import create_jwt_token as create_jwt_token_func

#     expired_token = create_jwt_token_func(
#         data={
#             "sub": str(student_for_submission_tests.public_id),
#             "account_type": "child",
#         },
#         expires_delta=timedelta(hours=-1),
#     )

#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=mc_simple_exercise.public_id, answer="opt1"
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers={"x-auth-token": expired_token},
#         json=submission.model_dump(),
#     )

#     assert (
#         response.status_code == status.HTTP_401_UNAUTHORIZED
#         and response.json()["error_code"] == AppErrorCode.AUTHENTICATION_ERROR
#     )


# def test_submit_exercise_with_parent_token(
#     client, db_session: Session, mc_simple_exercise: Exercise
# ) -> None:
#     parent_stmt = select(Account).where(
#         Account.public_id == constants.MOCK_PARENT_PUBLIC_ID
#     )
#     parent = db_session.scalars(parent_stmt).one()
#     parent_token = create_test_parent_auth_token(parent)

#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=mc_simple_exercise.public_id, answer="opt1"
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers={"x-auth-token": parent_token},
#         json=submission.model_dump(),
#     )

#     assert (
#         response.status_code == status.HTTP_403_FORBIDDEN
#         and response.json() == {
#             "status": "error",
#             "error_code": AppErrorCode.AUTHORIZATION_ERROR,
#             "message": "Only students can submit exercises"
#         }
#     )


# def test_submit_exercise_updates_attempts_count(
#     client,
#     student_for_submission_tests: ChildAccount,
#     mc_simple_exercise: Exercise,
#     db_session: Session,
# ) -> None:
#     student_token = create_test_child_auth_token(student_for_submission_tests)
#     headers = {"x-auth-token": student_token}

#     # First attempt (incorrect) - mc_simple_exercise (ex-mc-1) correct answer is "opt2"
#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=mc_simple_exercise.public_id,
#         answer="opt1",  # Incorrect
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )
#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert (
#         submission_response.isCorrect is False
#     )
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         mc_simple_exercise.id,
#         ExerciseStatusEnum.INCORRECT,
#         1,
#     )

#     # Second attempt (correct)
#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=mc_simple_exercise.public_id,
#         answer="opt2",  # Correct
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )
#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert submission_response.isCorrect is True
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         mc_simple_exercise.id,
#         ExerciseStatusEnum.CORRECT,
#         2,
#     )

#     # Third attempt (should not update status to incorrect, only increment attempts)
#     submission = ExerciseSubmissionRequest(
#         exercise_public_id=mc_simple_exercise.public_id,
#         answer="opt1",  # Incorrect again
#     )
#     response = client.post(
#         SUBMIT_EXERCISE_URL,
#         headers=headers,
#         json=submission.model_dump(),
#     )
#     assert response.status_code == status.HTTP_200_OK
#     submission_response = ExerciseSubmissionResponse.model_validate(
#         response.json()
#     )
#     assert submission_response.isCorrect is False # Submission is incorrect
#     _verify_association(
#         db_session,
#         student_for_submission_tests.id,
#         mc_simple_exercise.id,
#         ExerciseStatusEnum.CORRECT, # Status should remain CORRECT from previous attempt
#         3
#     )