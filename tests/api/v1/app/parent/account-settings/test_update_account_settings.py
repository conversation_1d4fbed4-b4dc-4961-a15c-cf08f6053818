import pytest
from fastapi import status
from sqlalchemy.orm import Session
from sqlalchemy import select

from db.models import Account, ChildAccount, Year, LanguageEnum
from api.v1.app.parent.account_settings.schemas.request import UpdateAccountSettingsRequest
from api.v1.app.parent.account_settings.schemas.response import (
    AccountSettingsResponse  # Keep for response validation
)
from tests.utils.auth_utils import create_test_parent_auth_token
from tests.data import constants  # For MOCK_PARENT_PUBLIC_ID
# Import subscription data for assertions
from tests.data.subscription_data import ACTIVE_SUBSCRIPTIONS_DATA

UPDATE_ACCOUNT_SETTINGS_URL = "/api/v1/parent/account-settings/update-account-settings"

@pytest.fixture
def parent_for_update(db_session: Session) -> Account:
    """
    Fixture to get the mock parent account, similar to the one used in get_account_settings.
    This parent might have children and subscriptions, as the update response returns full settings.
    """
    # Modern syntax + query by public_id
    parent_stmt = select(Account).where(
        Account.public_id == constants.MOCK_PARENT_PUBLIC_ID
    )
    parent = db_session.scalars(parent_stmt).one()
    return parent

def test_update_account_settings_language_success(client, parent_for_update: Account, db_session: Session):
    """Test successfully updating the parent's default language."""
    parent_token = create_test_parent_auth_token(parent_for_update)
    headers = {"x-auth-token": parent_token}
    
    original_language = parent_for_update.language.value
    # Select a new language different from the original
    new_language = (
        LanguageEnum.FR.value 
        if original_language != LanguageEnum.FR.value 
        else LanguageEnum.DE.value
    )

    update_payload = UpdateAccountSettingsRequest(default_language=new_language)

    response = client.put(UPDATE_ACCOUNT_SETTINGS_URL, headers=headers, json=update_payload.model_dump())

    assert response.status_code == status.HTTP_200_OK
    settings_response = AccountSettingsResponse(**response.json())

    # Verify the language was updated in the response
    assert settings_response.default_language == new_language
    # Email should remain the same
    assert settings_response.email == parent_for_update.email

    # Verify the language was updated in the database
    db_session.refresh(parent_for_update)
    assert parent_for_update.language.value == new_language

    # Basic check for the new response structure (active_subscription)
    # The exact content of activeSubscription will depend on the mock data setup
    # For this test, we primarily care that the language update worked
    mock_active_sub_data = next(
        sub for sub in ACTIVE_SUBSCRIPTIONS_DATA 
        if sub["public_id"] == "active-sub-mock-parent-1"
    )
    # Assuming mock parent has a subscription
    assert settings_response.active_subscription is not None
    assert (
        settings_response.active_subscription.subscription_public_id 
        == mock_active_sub_data["public_id"]
    )
    assert (
        len(settings_response.active_subscription.components) 
        == len(mock_active_sub_data["plan_links"])
    )


def test_update_account_settings_unauthorized_no_token(client):
    """Test update account settings without an auth token."""
    # Use enum value
    update_payload = UpdateAccountSettingsRequest(
        default_language=LanguageEnum.EN.value
    )
    response = client.put(
        UPDATE_ACCOUNT_SETTINGS_URL, 
        json=update_payload.model_dump()
    )
    assert response.status_code == status.HTTP_401_UNAUTHORIZED

def test_update_account_settings_with_child_token(client, db_session: Session):
    """Test update account settings with a child token (should be unauthorized)."""
    # Create a child account for token generation
    year_stmt = select(Year)
    year = db_session.scalars(year_stmt).first()
    
    child_user = ChildAccount(
        name=constants.MOCK_CHILD_NAME,
        email=constants.MOCK_CHILD_EMAIL,
        pin=constants.MOCK_CHILD_PIN,
        language=constants.MOCK_CHILD_LANGUAGE,
        year_id=year.id,
        public_id=constants.MOCK_CHILD_PUBLIC_ID
    )
    
    # Ensure this child doesn't already exist from the main seeding
    existing_child_stmt = select(ChildAccount).where(
        ChildAccount.public_id == constants.MOCK_CHILD_PUBLIC_ID
    )
    existing_child = db_session.scalars(existing_child_stmt).first()
    if not existing_child:
        db_session.add(child_user)
        db_session.commit()
    else:
        child_user = existing_child  # Use the existing one if it's there

    from tests.utils.auth_utils import create_test_child_auth_token
    child_token = create_test_child_auth_token(child_user)
    headers = {"x-auth-token": child_token}

    update_payload = UpdateAccountSettingsRequest(
        default_language=LanguageEnum.FR.value
    )
    response = client.put(
        UPDATE_ACCOUNT_SETTINGS_URL,
        headers=headers,
        json=update_payload.model_dump()
    )

    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    assert response.json()["error_code"] == "PERMISSION_DENIED"

def test_update_account_settings_invalid_language_code(
    client, parent_for_update: Account, db_session: Session
):
    """Test updating with an invalid language code."""
    parent_token = create_test_parent_auth_token(parent_for_update)
    headers = {"x-auth-token": parent_token}
    
    # Test with an invalid language code
    update_payload = {"default_language": "xx"}
    
    response = client.put(
        UPDATE_ACCOUNT_SETTINGS_URL,
        headers=headers,
        json=update_payload
    )

    assert response.status_code == status.HTTP_400_BAD_REQUEST

    # Verify that the account language was not actually changed
    db_session.refresh(parent_for_update)
    assert parent_for_update.language.value != "xx"

    # Check the error detail structure
    error_data = response.json()
    assert "message" in error_data
    assert "Invalid language code: xx" in error_data["message"]