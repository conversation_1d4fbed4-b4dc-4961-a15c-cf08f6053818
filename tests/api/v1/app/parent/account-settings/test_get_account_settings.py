from datetime import datetime, timedelta, UTC

import pytest
from fastapi import status
from sqlalchemy import select
from sqlalchemy.orm import Session

from api.v1.app.parent.account_settings.schemas.response import (
    AccountSettingsResponse,
    ParentActiveSubscriptionResponse,
    SubscriptionPlanComponentResponse,
    SubscribedSubjectDetailResponse,
    ChildResponse,
    SchoolYearResponse,
)
from db.models import Account, ChildAccount, Year
from tests.data import constants
from tests.data.content_data import YEARS_DATA, SUBJECTS_DATA
from tests.data.subscription_data import ACTIVE_SUBSCRIPTIONS_DATA
from tests.utils.auth_utils import create_test_parent_auth_token

GET_ACCOUNT_SETTINGS_URL = "/api/v1/parent/account-settings/get-account-settings"

@pytest.fixture
def parent_with_children_and_subscription(db_session: Session) -> Account:
    """
    Fixture to get the mock parent account seeded by mock_data_loader.
    This parent should have children and an active subscription with various components.
    """
    stmt = select(Account).where(Account.public_id == constants.MOCK_PARENT_PUBLIC_ID)
    parent = db_session.scalars(stmt).one()
    # Ensure children and year are loaded if needed for assertions, though service
    # should handle this.
    # db_session.refresh(parent, attribute_names=['children_accounts'])
    # for child in parent.children_accounts:
    #     if child.year_id:
    #         db_session.refresh(child, attribute_names=['year'])
    return parent

@pytest.fixture
def parent_without_subscription(db_session: Session) -> Account:
    """Fixture for a parent account that has no active subscriptions."""
    # Create unique credentials for this test case
    unique_email = f"nosub+{constants.MOCK_PARENT_EMAIL}"
    unique_public_id = f"{constants.MOCK_PARENT_PUBLIC_ID}-no-sub"
    unique_stripe_id = f"{constants.MOCK_STRIPE_CUSTOMER_ID}-no-sub"
    
    parent = Account(
        email=unique_email,  # Use unique email
        language=constants.MOCK_PARENT_LANGUAGE,
        pin_hash=constants.MOCK_PARENT_PIN_HASH,
        is_verified=True,
        public_id=unique_public_id,  # Use unique public_id
        stripe_customer_id=unique_stripe_id  # Use unique stripe_id
    )
    db_session.add(parent)
    # Flush to get the ID and ensure it's added to the session's transaction
    db_session.flush()
    # db_session.commit() - db_session fixture handles transaction
    db_session.refresh(parent)
    return parent

def test_get_account_settings_success_with_subscription(
    client,
    parent_with_children_and_subscription: Account,
    db_session: Session
):
    """Test successful retrieval of account settings for a parent with children and an active subscription."""
    parent_token = create_test_parent_auth_token(parent_with_children_and_subscription)
    headers = {"x-auth-token": parent_token}

    response = client.get(GET_ACCOUNT_SETTINGS_URL, headers=headers)

    assert response.status_code == status.HTTP_200_OK
    settings_response = AccountSettingsResponse.model_validate(response.json())

    # --- Prepare Expected Data ---
    # Find relevant mock data
    mock_active_sub_data = next(
        sub for sub in ACTIVE_SUBSCRIPTIONS_DATA 
        if sub["public_id"] == "active-sub-mock-parent-1"
    )
    mock_sc_link_data = next(
        link for link in mock_active_sub_data["plan_links"] 
        if link["public_id"] == "planlink-sc-child1-year1"
    )
    mock_yf_link_data = next(
        link for link in mock_active_sub_data["plan_links"] 
        if link["public_id"] == "planlink-yf-parent-year1"
    )
    mock_year_1_data = next(
        y for y in YEARS_DATA 
        if y["public_id"] == parent_with_children_and_subscription.children_accounts[0].year.public_id
    )
    mock_subj_1_data = next(
        s for s in SUBJECTS_DATA 
        if s["public_id"] == mock_sc_link_data["selected_subjects_public_ids"][0]
    )
    all_year_1_subjects_data = [
        s for s in SUBJECTS_DATA 
        if s["year_public_id"] == mock_year_1_data["public_id"]
    ]

    # Construct expected SubscribedSubjectDetailResponse for SC plan
    expected_sc_subjects = [
        SubscribedSubjectDetailResponse(
            public_id=mock_subj_1_data["public_id"],
            name=mock_subj_1_data["name"],
            year_public_id=mock_subj_1_data["year_public_id"],
            year_name=mock_year_1_data["name"]
        )
    ]

    # Construct expected SubscribedSubjectDetailResponse for YF plan (all subjects in year)
    expected_yf_subjects = [
        SubscribedSubjectDetailResponse(
            public_id=subj["public_id"],
            name=subj["name"],
            year_public_id=subj["year_public_id"],
            year_name=mock_year_1_data["name"]
        )
        for subj in all_year_1_subjects_data
    ]

    # Construct expected SubscriptionPlanComponentResponse for SC plan
    expected_sc_component = SubscriptionPlanComponentResponse(
        plan_link_public_id=mock_sc_link_data["public_id"],
        plan_public_id=mock_sc_link_data["subscription_option_public_id"],
        plan_name=f"{mock_year_1_data['name']} - {mock_sc_link_data['chosen_plan_type'].name}",
        plan_type=mock_sc_link_data["chosen_plan_type"].value,
        billing_period=mock_sc_link_data["chosen_billing_period"].value,
        granted_subjects=expected_sc_subjects
    )

    # Construct expected SubscriptionPlanComponentResponse for YF plan
    expected_yf_component = SubscriptionPlanComponentResponse(
        plan_link_public_id=mock_yf_link_data["public_id"],
        plan_public_id=mock_yf_link_data["subscription_option_public_id"],
        plan_name=f"{mock_year_1_data['name']} - {mock_yf_link_data['chosen_plan_type'].name}",
        plan_type=mock_yf_link_data["chosen_plan_type"].value,
        billing_period=mock_yf_link_data["chosen_billing_period"].value,
        granted_subjects=expected_yf_subjects  # YF grants all subjects for the year
    )

    # Calculate expected current_period_end
    now = datetime.now(UTC)
    expected_period_end_dt = now + timedelta(
        days=mock_active_sub_data["current_period_end_offset_days"]
    )

    # Construct expected ParentActiveSubscriptionResponse
    expected_active_subscription = ParentActiveSubscriptionResponse(
        subscription_public_id=mock_active_sub_data["public_id"],
        status=mock_active_sub_data["status"].value,
        current_period_end=expected_period_end_dt,  # Use calculated datetime
        cancel_at_period_end=mock_active_sub_data["cancel_at_period_end"],
        components=sorted(
            [expected_sc_component, expected_yf_component],
            key=lambda c: c.plan_link_public_id
        )  # Sort for consistent comparison
    )

    # Construct expected ChildResponse using fixture data
    # Assuming the fixture 'parent_with_children_and_subscription' has at least one child
    child_from_fixture = parent_with_children_and_subscription.children_accounts[0]
    expected_child_response = ChildResponse(
        public_id=child_from_fixture.public_id,
        name=child_from_fixture.name,
        email=child_from_fixture.email,  # Include email if present
        school_year=SchoolYearResponse(
            public_id=child_from_fixture.year.public_id,
            name=child_from_fixture.year.name
        ),
        pin=constants.MOCK_CHILD_PIN  # Added missing pin field
    )

    # Construct expected SchoolYearResponse list
    expected_school_years = [SchoolYearResponse(public_id=y["public_id"], name=y["name"]) for y in YEARS_DATA]

    # --- Assertions --- 
    # Parent Account Assertions (top-level fields from fixture)
    assert settings_response.email == parent_with_children_and_subscription.email
    assert settings_response.default_language == parent_with_children_and_subscription.language.value

    # Children Assertions
    assert settings_response.children is not None
    # Ensure the list is not empty and contains the expected child (or children if multiple)
    assert len(settings_response.children) == 1  # Assuming fixture creates one child
    # Compare the response child with the expected constructed child
    # Convert to dict for comparison if order might differ or for easier debugging
    assert settings_response.children[0].model_dump() == expected_child_response.model_dump()

    # Active Subscription Assertions
    assert settings_response.active_subscription is not None
    # Sort components in actual response for consistent comparison
    if settings_response.active_subscription.components:
        settings_response.active_subscription.components.sort(key=lambda c: c.plan_link_public_id)
    # Compare granted subjects as sets if order isn't guaranteed within components
    if settings_response.active_subscription.components and expected_active_subscription.components:
        for actual_comp, expected_comp in zip(
            settings_response.active_subscription.components,
            expected_active_subscription.components
        ):
            assert actual_comp.plan_link_public_id == expected_comp.plan_link_public_id
            assert actual_comp.plan_public_id == expected_comp.plan_public_id
            assert actual_comp.plan_name == expected_comp.plan_name
            assert actual_comp.plan_type == expected_comp.plan_type
            assert actual_comp.billing_period == expected_comp.billing_period
            # Compare subject sets
            actual_subjects_set = {subj.public_id for subj in actual_comp.granted_subjects}
            expected_subjects_set = {subj.public_id for subj in expected_comp.granted_subjects}
            assert actual_subjects_set == expected_subjects_set
            # Optionally, compare full subject details if needed after checking sets
            # assert sorted(actual_comp.granted_subjects, key=lambda s: s.public_id) == \
            #     sorted(expected_comp.granted_subjects, key=lambda s: s.public_id)

        # Now compare the main subscription object parts, excluding components
        assert settings_response.active_subscription.subscription_public_id == \
            expected_active_subscription.subscription_public_id
        assert settings_response.active_subscription.status == \
            expected_active_subscription.status
        assert settings_response.active_subscription.cancel_at_period_end == \
            expected_active_subscription.cancel_at_period_end
        assert len(settings_response.active_subscription.components) == \
            len(expected_active_subscription.components)

    # School Years Assertions
    assert settings_response.school_years is not None
    # Sort both actual and expected lists by public_id for consistent comparison
    actual_school_years_sorted = sorted(
        settings_response.school_years, 
        key=lambda sy: sy.public_id
    )
    expected_school_years_sorted = sorted(
        expected_school_years, 
        key=lambda sy: sy.public_id
    )
    assert actual_school_years_sorted == expected_school_years_sorted


def test_get_account_settings_no_active_subscription(
    client,
    parent_without_subscription: Account,
    db_session: Session,
):
    """Test successful retrieval when parent has no active subscription."""
    parent_token = create_test_parent_auth_token(parent_without_subscription)
    headers = {"x-auth-token": parent_token}

    response = client.get(GET_ACCOUNT_SETTINGS_URL, headers=headers)

    assert response.status_code == status.HTTP_200_OK
    settings_response = AccountSettingsResponse(**response.json())

    # AccountSettingsResponse assertions
    assert settings_response.email == parent_without_subscription.email
    assert settings_response.default_language == \
        parent_without_subscription.language.value
    assert settings_response.children == []
    assert settings_response.active_subscription is None
    assert settings_response.school_years is not None
    assert len(settings_response.school_years) >= 1


def test_get_account_settings_unauthorized_no_token(client):
    """Test get account settings without an auth token."""
    response = client.get(GET_ACCOUNT_SETTINGS_URL)
    # AuthDependency handles this
    assert response.status_code == status.HTTP_401_UNAUTHORIZED

def test_get_account_settings_with_child_token(client, db_session: Session):
    """Test get account settings with a child token (should be unauthorized)."""
    # Create a child account for token generation
    year = db_session.scalars(select(Year)).first()
    child_user = ChildAccount(
        name=constants.MOCK_CHILD_NAME,
        email=constants.MOCK_CHILD_EMAIL,
        pin=constants.MOCK_CHILD_PIN,
        language=constants.MOCK_CHILD_LANGUAGE,
        year_id=year.id,
        public_id=constants.MOCK_CHILD_PUBLIC_ID
    )
    # Ensure this child doesn't already exist from the main seeding
    existing_child = db_session.scalars(
        select(ChildAccount).where(
            ChildAccount.public_id == constants.MOCK_CHILD_PUBLIC_ID
        )
    ).first()
    
    if existing_child:
        # If the child from seeding already exists, use it for token generation
        child_user = existing_child
    
    db_session.add(child_user)
    db_session.commit()

    from tests.utils.auth_utils import create_test_child_auth_token
    child_token = create_test_child_auth_token(child_user)
    response = client.get(
        GET_ACCOUNT_SETTINGS_URL,
        headers={"x-auth-token": child_token}
    )
    
    # Expecting 401 because the route checks for parent user type
    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    assert response.json()["error_code"] == "PERMISSION_DENIED"