import uuid

import pytest
from fastapi import status
from sqlalchemy.orm import Session
from sqlalchemy import select

from db.models import Account, ChildAccount, Year
from api.v1.app.parent.dashboard.schemas.request import CreateChildRequestSchema
from api.v1.app.parent.dashboard.schemas.response import AddChildResponseSchema
from tests.utils.auth_utils import (
    create_test_parent_auth_token,
    create_test_child_auth_token,
)
from tests.data import constants
from api.v1.common.schemas import AppErrorCode

ADD_CHILD_URL = "/api/v1/parent/dashboard/add-child-account"


@pytest.fixture
def parent_for_add_child(db_session: Session) -> Account:
    """Fixture to get the mock parent account."""
    stmt = select(Account).where(Account.public_id == constants.MOCK_PARENT_PUBLIC_ID)
    parent = db_session.scalars(stmt).one()
    return parent


@pytest.fixture
def valid_year(db_session: Session) -> Year:
    """Fixture to get a valid year."""
    stmt = select(Year).limit(1)
    year = db_session.scalars(stmt).one()
    return year


def test_add_child_success(client, parent_for_add_child: Account, valid_year: Year, db_session: Session):
    """Test successfully adding a new child."""
    parent_token = create_test_parent_auth_token(parent_for_add_child)
    headers = {"x-auth-token": parent_token}

    new_child_email = f"new_child_{uuid.uuid4().hex[:6]}@example.com"
    add_payload = CreateChildRequestSchema(
        name=constants.MOCK_CHILD_NAME,
        email=new_child_email,
        year_public_id=valid_year.public_id,
        language=constants.MOCK_CHILD_LANGUAGE,
        pin=constants.MOCK_CHILD_PIN
    )

    response = client.post(ADD_CHILD_URL, headers=headers, json=add_payload.model_dump())

    assert response.status_code == status.HTTP_201_CREATED
    response_data = AddChildResponseSchema(**response.json())
    assert response_data.name == add_payload.name
    assert response_data.email == add_payload.email
    assert response_data.school_year.public_id == add_payload.year_public_id
    assert response_data.school_year.name == valid_year.name
    assert response_data.language == add_payload.language
    assert response_data.pin == add_payload.pin
    assert response_data.public_id is not None
    assert response_data.message == "Child account created successfully."

    # Verify in DB
    child_stmt = select(ChildAccount).where(ChildAccount.email == new_child_email)
    child_db = db_session.scalars(child_stmt).one_or_none()
    assert child_db is not None
    assert child_db.parent_account_id == parent_for_add_child.id
    assert child_db.year_id == valid_year.id
    assert child_db.pin == constants.MOCK_CHILD_PIN
    assert child_db.is_verified is True


def test_add_child_email_conflict_child(
    client, parent_for_add_child: Account, valid_year: Year, db_session: Session
):
    """Test adding a child with an email already used by another child."""
    parent_token = create_test_parent_auth_token(parent_for_add_child)
    headers = {"x-auth-token": parent_token}

    existing_child_email = constants.MOCK_CHILD_EMAIL

    add_payload = CreateChildRequestSchema(
        name="Conflicting Name",
        email=existing_child_email,
        year_public_id=valid_year.public_id,
        language=constants.MOCK_CHILD_LANGUAGE,
        pin=constants.MOCK_CHILD_PIN
    )

    response = client.post(ADD_CHILD_URL, headers=headers, json=add_payload.model_dump())

    assert response.status_code == status.HTTP_409_CONFLICT
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.CHILD_ACCOUNT_ALREADY_EXISTS
    assert "another child account" in response_data["message"]


def test_add_child_email_conflict_parent(
    client, parent_for_add_child: Account, valid_year: Year, db_session: Session
):
    """Test adding a child with an email already used by another parent."""
    parent_token = create_test_parent_auth_token(parent_for_add_child)
    headers = {"x-auth-token": parent_token}

    # Create another parent account with a different email
    other_parent = Account(
        email="<EMAIL>",
        public_id=str(uuid.uuid4()),
        is_verified=True,
        stripe_customer_id="cus_test_other_parent"
    )
    db_session.add(other_parent)
    db_session.commit()

    # Try to add a child with the other parent's email
    add_payload = CreateChildRequestSchema(
        name="Conflicting Name 2",
        email=other_parent.email,
        year_public_id=valid_year.public_id,
        language=constants.MOCK_CHILD_LANGUAGE,
        pin=constants.MOCK_CHILD_PIN
    )

    response = client.post(ADD_CHILD_URL, headers=headers, json=add_payload.model_dump())

    assert response.status_code == status.HTTP_409_CONFLICT
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.CHILD_ACCOUNT_ALREADY_EXISTS
    assert "parent account" in response_data["message"].lower()


def test_add_child_invalid_year(client, parent_for_add_child: Account):
    """Test adding a child with an invalid year_public_id."""
    parent_token = create_test_parent_auth_token(parent_for_add_child)
    headers = {"x-auth-token": parent_token}

    add_payload = CreateChildRequestSchema(
        name=constants.MOCK_CHILD_NAME,
        email=f"invalid_year_child_{uuid.uuid4().hex[:6]}@example.com",
        year_public_id="invalid-year-id",
        language=constants.MOCK_CHILD_LANGUAGE,
        pin=constants.MOCK_CHILD_PIN
    )

    response = client.post(ADD_CHILD_URL, headers=headers, json=add_payload.model_dump())

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.INVALID_REQUEST


def test_add_child_unauthorized_no_token(client, valid_year: Year):
    """Test adding a child without an auth token."""
    add_payload = CreateChildRequestSchema(
        name=constants.MOCK_CHILD_NAME,
        email=f"unauth_child_{uuid.uuid4().hex[:6]}@example.com",
        year_public_id=valid_year.public_id,
        language=constants.MOCK_CHILD_LANGUAGE,
        pin=constants.MOCK_CHILD_PIN
    )
    response = client.post(ADD_CHILD_URL, json=add_payload.model_dump())
    assert response.status_code == status.HTTP_401_UNAUTHORIZED


def test_add_child_with_child_token(client, valid_year: Year, db_session: Session):
    """Test adding a child with a child token (should be unauthorized)."""
    child_stmt = select(ChildAccount).limit(1)
    child_user = db_session.scalars(child_stmt).first()
    if not child_user:
        pytest.skip("Skipping test, no child account found in seeded data.")

    child_token = create_test_child_auth_token(child_user)
    headers = {"x-auth-token": child_token}

    add_payload = CreateChildRequestSchema(
        name=constants.MOCK_CHILD_NAME,
        email=f"child_token_add_{uuid.uuid4().hex[:6]}@example.com",
        year_public_id=valid_year.public_id,
        language=constants.MOCK_CHILD_LANGUAGE,
        pin=constants.MOCK_CHILD_PIN
    )
    response = client.post(ADD_CHILD_URL, headers=headers, json=add_payload.model_dump())

    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    assert response.json()["error_code"] == AppErrorCode.PERMISSION_DENIED