import pytest
from fastapi import status
from sqlalchemy import select
from sqlalchemy.orm import Session, selectinload

from db.models import Account, ChildAccount, Year  # Import necessary models
from api.v1.app.parent.dashboard.schemas.response import (
    ParentDashboardResponseSchema,
    ParentConnectedProfileSchema,
    SchoolYearSchema,
)  # Import response schemas
from tests.utils.auth_utils import (
    create_test_parent_auth_token,
    create_test_child_auth_token,
)
from tests.data import constants  # For mock parent/child IDs
from tests.data.content_data import YEARS_DATA  # For expected school years

GET_DASHBOARD_URL = "/api/v1/parent/dashboard/get-dashboard"

@pytest.fixture
def parent_for_dashboard(db_session: Session) -> Account:
    """Fixture to get the mock parent account seeded by mock_data_loader."""
    # Fetch the parent using the public ID from constants
    stmt = select(Account).where(Account.public_id == constants.MOCK_PARENT_PUBLIC_ID)
    parent = db_session.execute(stmt).scalar_one_or_none()
    if not parent:
        pytest.fail(
            f"Mock parent with public_id {constants.MOCK_PARENT_PUBLIC_ID} not found in test DB."
        )
    
    # Eagerly load children and their years if needed for assertions,
    # though the service should handle loading
    # db_session.refresh(parent, attribute_names=['children_accounts'])
    # for child in parent.children_accounts:
    #     if child.year_id:
    #         db_session.refresh(child, attribute_names=['year'])

    return parent

def test_get_dashboard_success(client, parent_for_dashboard: Account, db_session: Session):
    """Test successful retrieval of dashboard data for a parent with children."""
    parent_token = create_test_parent_auth_token(parent_for_dashboard)
    headers = {"x-auth-token": parent_token}

    response = client.get(GET_DASHBOARD_URL, headers=headers)

    assert response.status_code == status.HTTP_200_OK
    dashboard_response = ParentDashboardResponseSchema.model_validate(response.json())

    # --- Prepare Expected Data ---
    # Fetch the child associated with the mock parent from the DB
    # (seeded by mock_data_loader)
    child_stmt = (
        select(ChildAccount)
        .where(ChildAccount.parent_account_id == parent_for_dashboard.id)
        .options(selectinload(ChildAccount.year))
    )
    child_from_db = db_session.execute(child_stmt).scalar_one_or_none()

    assert child_from_db is not None, "Mock child for the parent was not found in the DB."
    assert child_from_db.year is not None, "Mock child's year was not loaded or set."

    # Construct expected profile for the child
    expected_profile = ParentConnectedProfileSchema(
        public_id=child_from_db.public_id,
        name=child_from_db.name,
        email=child_from_db.email,
        school_year=SchoolYearSchema(
            public_id=child_from_db.year.public_id,
            name=child_from_db.year.name
        ),
        pin=child_from_db.pin,
        profile_pic_url="/logo.svg"  # Default value
    )

    # Construct expected school years list from YEARS_DATA
    expected_school_years = [
        SchoolYearSchema(public_id=y["public_id"], name=y["name"])
        for y in YEARS_DATA
    ]

    # --- Assertions ---
    # Profiles (Children) Assertions
    assert dashboard_response.profiles is not None
    assert len(dashboard_response.profiles) == 1  # One child expected
    # Compare the actual profile with the expected one
    assert dashboard_response.profiles[0].model_dump() == expected_profile.model_dump()

    # School Years Assertions
    assert dashboard_response.school_years is not None
    # Sort both actual and expected lists by public_id for consistent comparison
    actual_school_years_sorted = sorted(
        dashboard_response.school_years, key=lambda sy: sy.public_id
    )
    expected_school_years_sorted = sorted(
        expected_school_years, key=lambda sy: sy.public_id
    )
    assert actual_school_years_sorted == expected_school_years_sorted



def test_get_dashboard_unauthorized_no_token(client):
    """Test get dashboard without an auth token."""
    response = client.get(GET_DASHBOARD_URL)
    assert response.status_code == status.HTTP_401_UNAUTHORIZED


def test_get_dashboard_unauthorized_child_token(client, db_session: Session):
    """Test get dashboard with a child token (should be unauthorized)."""
    # Fetch or create a mock child for token generation
    child_stmt = select(ChildAccount).where(
        ChildAccount.public_id == constants.MOCK_CHILD_PUBLIC_ID
    )
    child_user = db_session.execute(child_stmt).scalar_one_or_none()

    if not child_user:
        # If the mock child doesn't exist, create a basic one
        year_stmt = select(Year).limit(1)
        year = db_session.execute(year_stmt).scalar_one_or_none()
        child_user = ChildAccount(
            name=constants.MOCK_CHILD_NAME,
            email=constants.MOCK_CHILD_EMAIL,
            pin=constants.MOCK_CHILD_PIN,
            language=constants.MOCK_CHILD_LANGUAGE,
            year_id=year.id,
            public_id=constants.MOCK_CHILD_PUBLIC_ID
        )
        db_session.add(child_user)
        db_session.commit()  # Commit if created within the test

    child_token = create_test_child_auth_token(child_user)
    headers = {"x-auth-token": child_token}

    response = client.get(GET_DASHBOARD_URL, headers=headers)

    # Expecting 401 because the route checks for parent user type via AuthDependency
    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    # The specific error code comes from the service/route check
    assert response.json()["error_code"] == "PERMISSION_DENIED"