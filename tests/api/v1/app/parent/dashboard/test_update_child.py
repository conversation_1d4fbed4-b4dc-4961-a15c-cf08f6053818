import uuid

import pytest
from fastapi import status
from sqlalchemy import select
from sqlalchemy.orm import Session

from api.v1.app.parent.dashboard.schemas.request import UpdateChildRequestSchema
from api.v1.app.parent.dashboard.schemas.response import UpdateChildResponseSchema
from api.v1.common.schemas import AppErrorCode
from db.models import Account, ChildAccount, LanguageEnum, Year
from tests.data import constants
from tests.utils.auth_utils import (
    create_test_child_auth_token,
    create_test_parent_auth_token,
)

UPDATE_CHILD_URL = "/api/v1/parent/dashboard/update-child-account"


@pytest.fixture
def parent_for_update_child(db_session: Session) -> Account:
    """Fixture to get the mock parent account."""
    stmt = select(Account).where(Account.public_id == constants.MOCK_PARENT_PUBLIC_ID)
    parent = db_session.scalars(stmt).one()
    db_session.refresh(parent)
    return parent


@pytest.fixture
def child_to_update(
    db_session: Session, parent_for_update_child: Account
) -> ChildAccount:
    """Fixture to get the mock child account linked to the parent."""
    stmt = select(ChildAccount).where(
        ChildAccount.public_id == constants.MOCK_CHILD_PUBLIC_ID,
        ChildAccount.parent_account_id == parent_for_update_child.id,
    )
    child = db_session.scalars(stmt).one()
    db_session.refresh(child)
    return child


@pytest.fixture
def another_year(db_session: Session, child_to_update: ChildAccount) -> Year:
    """Fixture to get a different valid year."""
    stmt = select(Year).where(Year.id != child_to_update.year_id).limit(1)
    year = db_session.scalars(stmt).first()
    if not year:  # Fallback if only one year exists
        year = Year(name="Another Test Year", public_id="year-another")
        db_session.add(year)
        db_session.commit()
        db_session.refresh(year)
    return year


def test_update_child_name_success(
    client, parent_for_update_child: Account, child_to_update: ChildAccount, db_session: Session
):
    """Test successfully updating the child's name."""
    parent_token = create_test_parent_auth_token(parent_for_update_child)
    headers = {"x-auth-token": parent_token}
    update_url = UPDATE_CHILD_URL

    new_name = "Updated Child Name"
    update_payload = UpdateChildRequestSchema(child_public_id=child_to_update.public_id, name=new_name)

    response = client.put(
        update_url, headers=headers, json=update_payload.model_dump()
    )

    assert response.status_code == status.HTTP_200_OK
    response_data = UpdateChildResponseSchema(**response.json())
    assert response_data.name == new_name
    assert response_data.public_id == child_to_update.public_id
    assert response_data.message == "Child account updated successfully."

    # Verify in DB
    db_session.refresh(child_to_update)
    assert child_to_update.name == new_name


def test_update_child_email_success(
    client, parent_for_update_child: Account, child_to_update: ChildAccount, db_session: Session
):
    """Test successfully updating the child's email."""
    parent_token = create_test_parent_auth_token(parent_for_update_child)
    headers = {"x-auth-token": parent_token}
    update_url = UPDATE_CHILD_URL

    new_email = f"updated_child_{uuid.uuid4().hex[:6]}@example.com"
    update_payload = UpdateChildRequestSchema(child_public_id=child_to_update.public_id, email=new_email)

    response = client.put(update_url, headers=headers, json=update_payload.model_dump())

    assert response.status_code == status.HTTP_200_OK
    response_data = UpdateChildResponseSchema(**response.json())
    assert response_data.email == new_email

    db_session.refresh(child_to_update)
    assert child_to_update.email == new_email


def test_update_child_year_success(
    client,
    parent_for_update_child: Account,
    child_to_update: ChildAccount,
    another_year: Year,
    db_session: Session,
):
    """Test successfully updating the child's school year."""
    parent_token = create_test_parent_auth_token(parent_for_update_child)
    headers = {"x-auth-token": parent_token}
    update_url = UPDATE_CHILD_URL

    update_payload = UpdateChildRequestSchema(
        child_public_id=child_to_update.public_id,
        year_public_id=another_year.public_id
    )

    response = client.put(update_url, headers=headers, json=update_payload.model_dump())

    assert response.status_code == status.HTTP_200_OK
    response_data = UpdateChildResponseSchema(**response.json())
    rel_year = db_session.get(Year, child_to_update.year_id)
    assert response_data.school_year.public_id == rel_year.public_id
    assert response_data.school_year.name == rel_year.name

    db_session.refresh(child_to_update)
    assert child_to_update.year_id == another_year.id


def test_update_child_pin_success(
    client,
    parent_for_update_child: Account,
    child_to_update: ChildAccount,
    db_session: Session,
):
    """Test successfully updating the child's PIN."""
    parent_token = create_test_parent_auth_token(parent_for_update_child)
    headers = {"x-auth-token": parent_token}
    update_url = UPDATE_CHILD_URL

    new_pin = "0000"
    update_payload = UpdateChildRequestSchema(
        child_public_id=child_to_update.public_id,
        pin=new_pin
    )

    response = client.put(
        update_url, headers=headers, json=update_payload.model_dump()
    )

    assert response.status_code == status.HTTP_200_OK
    response_data = UpdateChildResponseSchema(**response.json())
    assert response_data.pin == new_pin

    db_session.refresh(child_to_update)
    assert child_to_update.pin == new_pin


def test_update_child_no_changes(
    client, parent_for_update_child: Account, child_to_update: ChildAccount, db_session: Session
):
    """Test updating a child with no actual changes."""
    parent_token = create_test_parent_auth_token(parent_for_update_child)
    headers = {"x-auth-token": parent_token}
    update_url = UPDATE_CHILD_URL

    #  Send existing data or empty payload
    update_payload = UpdateChildRequestSchema(
        child_public_id=child_to_update.public_id,
        name=child_to_update.name
    )  # Sending same name

    response = client.put(
        update_url, headers=headers, json=update_payload.model_dump()
    )

    assert response.status_code == status.HTTP_200_OK
    response_data = UpdateChildResponseSchema(**response.json())
    assert response_data.public_id == child_to_update.public_id
    assert response_data.name == child_to_update.name
    assert response_data.email == child_to_update.email

    rel_year = db_session.get(Year, child_to_update.year_id)
    assert response_data.school_year.public_id == rel_year.public_id
    assert response_data.school_year.name == rel_year.name
    assert response_data.language == child_to_update.language
    assert response_data.pin == child_to_update.pin

def test_update_child_email_conflict_child(
    client,
    parent_for_update_child: Account,
    child_to_update: ChildAccount,
    db_session: Session,
):
    """Test updating child email to one used by another child."""
    #  Create another child for the same parent
    another_child = ChildAccount(
        name="Another Child",
        email="<EMAIL>",
        pin="1111",
        language=LanguageEnum.DE,
        year_id=child_to_update.year_id,
        parent_account_id=parent_for_update_child.id,
        public_id=str(uuid.uuid4()),
    )
    db_session.add(another_child)
    db_session.commit()

    parent_token = create_test_parent_auth_token(parent_for_update_child)
    headers = {"x-auth-token": parent_token}
    update_url = UPDATE_CHILD_URL

    update_payload = UpdateChildRequestSchema(
        child_public_id=child_to_update.public_id,
        email=another_child.email
    )  

    response = client.put(update_url, headers=headers, json=update_payload.model_dump())

    assert response.status_code == status.HTTP_409_CONFLICT
    assert response.json()["error_code"] == AppErrorCode.CHILD_ACCOUNT_ALREADY_EXISTS
    assert "another child account" in response.json()["message"]


def test_update_child_email_conflict_parent(
    client, parent_for_update_child: Account, child_to_update: ChildAccount, db_session: Session
):
    """Test updating child email to one used by a parent."""
    parent_token = create_test_parent_auth_token(parent_for_update_child)
    headers = {"x-auth-token": parent_token}
    update_url = UPDATE_CHILD_URL


    different_parent = Account(
        email="<EMAIL>",
        pin_hash="password",
        is_auth_migrated=True,
        stripe_customer_id=str(uuid.uuid4()),
        verification_code="123456",
        is_verified=True,
        language=LanguageEnum.LU,
    )
    db_session.add(different_parent)
    db_session.commit()

    update_payload = UpdateChildRequestSchema(
        child_public_id=child_to_update.public_id,
        email=different_parent.email
    )  

    response = client.put(update_url, headers=headers, json=update_payload.model_dump())

    assert response.status_code == status.HTTP_409_CONFLICT
    assert response.json()["error_code"] == AppErrorCode.PARENT_ACCOUNT_ALREADY_EXISTS
    assert "a parent account" in response.json()["message"]


def test_update_child_invalid_year(
    client, parent_for_update_child: Account, child_to_update: ChildAccount
):
    """Test updating child with an invalid year_public_id."""
    parent_token = create_test_parent_auth_token(parent_for_update_child)
    headers = {"x-auth-token": parent_token}
    update_url = UPDATE_CHILD_URL

    update_payload = UpdateChildRequestSchema(
        child_public_id=child_to_update.public_id,
        year_public_id="invalid-year-id"
    )

    response = client.put(update_url, headers=headers, json=update_payload.model_dump())

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json()["error_code"] == AppErrorCode.INVALID_REQUEST

def test_update_child_not_found(client, parent_for_update_child: Account):
    """Test updating a non-existent child."""
    parent_token = create_test_parent_auth_token(parent_for_update_child)
    headers = {"x-auth-token": parent_token}
    update_url = UPDATE_CHILD_URL

    update_payload = UpdateChildRequestSchema(
        child_public_id="non-existent-child-id",
        name="Ghost Child"
    )

    response = client.put(update_url, headers=headers, json=update_payload.model_dump())

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json()["error_code"] == AppErrorCode.CHILD_ACCOUNT_NOT_FOUND


def test_update_child_unauthorized_no_token(client, child_to_update: ChildAccount):
    """Test updating a child without an auth token."""
    update_url = UPDATE_CHILD_URL
    update_payload = UpdateChildRequestSchema(
        child_public_id=child_to_update.public_id,
        name="Unauthorized Update"
    )
    response = client.put(
        update_url, json=update_payload.model_dump()
    )
    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    assert response.json()["error_code"] == AppErrorCode.AUTHENTICATION_REQUIRED


def test_update_child_with_child_token(client, child_to_update: ChildAccount):
    """Test updating a child with a child token (should be unauthorized)."""
    child_token = create_test_child_auth_token(child_to_update)
    headers = {"x-auth-token": child_token}

    update_data = UpdateChildRequestSchema(
        child_public_id=child_to_update.public_id,
        name="New Name"
    )
    update_url = UPDATE_CHILD_URL
    response = client.put(
        update_url,
        headers=headers,
        json=update_data.model_dump(exclude_unset=True),
    )

    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json()["error_code"] == AppErrorCode.PERMISSION_DENIED