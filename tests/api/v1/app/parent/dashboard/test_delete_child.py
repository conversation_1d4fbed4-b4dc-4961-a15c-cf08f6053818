import uuid

import pytest
from fastapi import status
from sqlalchemy.orm import Session
from sqlalchemy import select

from db.models import Account, ChildAccount, Year, LanguageEnum
from api.v1.app.parent.dashboard.schemas.response import DeleteChildResponseSchema
from tests.utils.auth_utils import (
    create_test_parent_auth_token,
    create_test_child_auth_token,
)
from tests.data import constants
from api.v1.common.schemas import AppErrorCode

DELETE_CHILD_URL_TEMPLATE = "/api/v1/parent/dashboard/delete-child-account/{child_public_id}"


@pytest.fixture
def parent_for_delete_child(db_session: Session) -> Account:
    """Fixture to get the mock parent account."""
    stmt = select(Account).where(Account.public_id == constants.MOCK_PARENT_PUBLIC_ID)
    parent = db_session.scalars(stmt).one()
    return parent


@pytest.fixture
def child_to_delete(db_session: Session, parent_for_delete_child: Account) -> ChildAccount:
    """Fixture to get the mock child account linked to the parent."""
    stmt = select(ChildAccount).where(
        ChildAccount.public_id == constants.MOCK_CHILD_PUBLIC_ID,
        ChildAccount.parent_account_id == parent_for_delete_child.id
    )
    child = db_session.scalars(stmt).one()
    return child


def test_delete_child_success(
    client, parent_for_delete_child: Account, child_to_delete: ChildAccount, db_session: Session
):
    """Test successfully deleting a child."""
    parent_token = create_test_parent_auth_token(parent_for_delete_child)
    headers = {"x-auth-token": parent_token}
    delete_url = DELETE_CHILD_URL_TEMPLATE.format(child_public_id=child_to_delete.public_id)

    response = client.delete(delete_url, headers=headers)

    assert response.status_code == status.HTTP_200_OK
    response_data = DeleteChildResponseSchema(**response.json())
    assert response_data.message == "Child account deleted successfully."

    # Verify in DB
    # Use get for primary key lookup
    child_db = db_session.get(ChildAccount, child_to_delete.id)
    assert child_db is None


def test_delete_child_not_found(client, parent_for_delete_child: Account):
    """Test deleting a non-existent child."""
    parent_token = create_test_parent_auth_token(parent_for_delete_child)
    headers = {"x-auth-token": parent_token}
    delete_url = DELETE_CHILD_URL_TEMPLATE.format(child_public_id="non-existent-child-id")

    response = client.delete(delete_url, headers=headers)

    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_data = response.json()
    print("response_data ", response_data)
    assert response_data["error_code"] == AppErrorCode.CHILD_ACCOUNT_NOT_FOUND


def test_delete_child_belongs_to_another_parent(
    client, parent_for_delete_child: Account, db_session: Session
):
    """Test deleting a child that belongs to another parent."""
    # Create another parent and child
    another_parent = Account(
        email="<EMAIL>", language=LanguageEnum.EN, public_id=str(uuid.uuid4()),
        stripe_customer_id="cus_anotherparent"
    )
    db_session.add(another_parent)
    # Commit parent first
    db_session.commit()

    year_stmt = select(Year).limit(1)
    year = db_session.scalars(year_stmt).one()
    other_child = ChildAccount(
        name="Other Parent's Child", email="<EMAIL>", pin="1111",
        language=LanguageEnum.DE, year_id=year.id, parent_account_id=another_parent.id,
        public_id=str(uuid.uuid4())
    )
    db_session.add(other_child)
    # Commit child
    db_session.commit()

    # Use token of the first parent
    parent_token = create_test_parent_auth_token(parent_for_delete_child)
    headers = {"x-auth-token": parent_token}
    # Try to delete the other parent's child
    delete_url = DELETE_CHILD_URL_TEMPLATE.format(child_public_id=other_child.public_id)

    response = client.delete(delete_url, headers=headers)

    assert response.status_code == status.HTTP_404_NOT_FOUND  # Service checks parent_id match
    response_data = response.json()
    assert response_data["error_code"] == AppErrorCode.CHILD_ACCOUNT_NOT_FOUND
    assert "does not belong to this parent" in response_data["message"]


def test_delete_child_unauthorized_no_token(client, child_to_delete: ChildAccount):
    """Test deleting a child without an auth token."""
    delete_url = DELETE_CHILD_URL_TEMPLATE.format(child_public_id=child_to_delete.public_id)
    response = client.delete(delete_url)
    assert response.status_code == status.HTTP_401_UNAUTHORIZED


def test_delete_child_with_child_token(client, child_to_delete: ChildAccount):
    """Test deleting a child with a child token (should be unauthorized)."""
    child_token = create_test_child_auth_token(child_to_delete)
    headers = {"x-auth-token": child_token}
    delete_url = DELETE_CHILD_URL_TEMPLATE.format(child_public_id=child_to_delete.public_id)

    response = client.delete(delete_url, headers=headers)

    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    assert response.json()["error_code"] == AppErrorCode.PERMISSION_DENIED