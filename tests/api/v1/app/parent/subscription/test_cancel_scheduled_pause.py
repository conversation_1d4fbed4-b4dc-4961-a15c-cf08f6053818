"""
Integration tests for cancel scheduled pause route.
"""
import pytest
from datetime import datetime, UTC
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from db.models.subscription import SubscriptionStatusType, SubscriptionPauseStatus


class TestCancelScheduledPauseRoute:
    """Integration tests for POST /api/v1/app/parent/subscription/{subscription_public_id}/cancel-scheduled-pause"""
    
    def test_cancel_scheduled_pause_success(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account,
        monthly_subscription,
        auth_headers,
        mock_qstash_client,
        mocker
    ):
        """Test successfully canceling a scheduled pause."""
        # Create a scheduled pause
        from db.models import SubscriptionPause
        scheduled_pause = SubscriptionPause(
            public_id="scheduled_123",
            active_subscription_id=monthly_subscription.id,
            start_date=datetime(2025, 7, 15, tzinfo=UTC),
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=monthly_subscription.current_period_end,
            new_period_end=monthly_subscription.current_period_end,
            status=SubscriptionPauseStatus.SCHEDULED,
            qstash_message_id="msg_test_123"
        )
        db_session.add(scheduled_pause)
        db_session.commit()
        
        # Make request
        response = client.post(
            "/api/v1/app/parent/subscription/cancel-scheduled-pause",
            json={
                "subscription_public_id": monthly_subscription.public_id,
                "pause_public_id": scheduled_pause.public_id
            },
            headers=auth_headers
        )
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert "cancelled" in data["message"].lower()
        assert data["canceled_pause_id"] == scheduled_pause.public_id
        assert data["canceled"] is True
        
        # Verify QStash was called to delete the message
        mock_qstash_client.delete.assert_called_once()
        delete_call = mock_qstash_client.delete.call_args
        assert "/messages/msg_test_123" in str(delete_call)
        
        # Verify pause record was marked as canceled
        from db.models import SubscriptionPause
        db_session.refresh(scheduled_pause)
        assert scheduled_pause.status == SubscriptionPauseStatus.CANCELED
    
    def test_cancel_scheduled_pause_no_scheduled_pause(
        self,
        client: TestClient,
        test_parent_account,
        monthly_subscription,
        auth_headers
    ):
        """Test canceling when no scheduled pause exists."""
        # No pause record exists
        
        response = client.post(
            "/api/v1/app/parent/subscription/cancel-scheduled-pause",
            json={
                "subscription_public_id": monthly_subscription.public_id,
                "pause_public_id": "non_existent_pause_id"
            },
            headers=auth_headers
        )
        
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["message"].lower()
    
    def test_cancel_already_active_pause(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account,
        paused_monthly_subscription,
        auth_headers
    ):
        """Test canceling a pause that's already active (not scheduled)."""
        subscription, active_pause = paused_monthly_subscription
        
        response = client.post(
            "/api/v1/app/parent/subscription/cancel-scheduled-pause",
            json={
                "subscription_public_id": subscription.public_id,
                "pause_public_id": active_pause.public_id
            },
            headers=auth_headers
        )
        
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["message"].lower()
        
        # Verify the active pause wasn't affected
        db_session.refresh(active_pause)
        assert active_pause.status == SubscriptionPauseStatus.PAUSED
    
    def test_cancel_scheduled_pause_qstash_failure(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account,
        monthly_subscription,
        auth_headers,
        mock_qstash_client
    ):
        """Test handling QStash deletion failure."""
        # Create a scheduled pause
        from db.models import SubscriptionPause
        scheduled_pause = SubscriptionPause(
            public_id="scheduled_fail",
            active_subscription_id=monthly_subscription.id,
            start_date=datetime(2025, 7, 15, tzinfo=UTC),
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=monthly_subscription.current_period_end,
            new_period_end=monthly_subscription.current_period_end,
            status=SubscriptionPauseStatus.SCHEDULED,
            qstash_message_id="msg_fail_123"
        )
        db_session.add(scheduled_pause)
        db_session.commit()
        
        # Mock QStash to fail
        mock_qstash_client.delete.side_effect = Exception("QStash error")
        
        # Request should still succeed (graceful degradation)
        response = client.post(
            "/api/v1/app/parent/subscription/cancel-scheduled-pause",
            json={
                "subscription_public_id": monthly_subscription.public_id,
                "pause_public_id": scheduled_pause.public_id
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "cancelled" in data["message"].lower()
        assert data["canceled_pause_id"] == scheduled_pause.public_id
        
        # Pause record should still be marked as canceled
        from db.models import SubscriptionPause
        db_session.refresh(scheduled_pause)
        assert scheduled_pause.status == SubscriptionPauseStatus.CANCELED
    
    def test_cancel_scheduled_pause_unauthorized(
        self,
        client: TestClient,
        monthly_subscription
    ):
        """Test canceling scheduled pause without authentication."""
        response = client.post(
            "/api/v1/app/parent/subscription/cancel-scheduled-pause",
            json={
                "subscription_public_id": monthly_subscription.public_id,
                "pause_public_id": "some_pause_id"
            }
        )
        
        assert response.status_code == 401
    
    def test_cancel_scheduled_pause_wrong_parent(
        self,
        client: TestClient,
        db_session: Session,
        monthly_subscription,
        auth_headers
    ):
        """Test canceling scheduled pause for subscription belonging to different parent."""
        # Create scheduled pause
        from db.models import SubscriptionPause
        scheduled_pause = SubscriptionPause(
            public_id="scheduled_wrong_parent",
            active_subscription_id=monthly_subscription.id,
            start_date=datetime(2025, 7, 15, tzinfo=UTC),
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=monthly_subscription.current_period_end,
            new_period_end=monthly_subscription.current_period_end,
            status=SubscriptionPauseStatus.SCHEDULED,
            qstash_message_id="msg_123"
        )
        db_session.add(scheduled_pause)
        
        # Create another parent and assign subscription
        from db.models import Account
        import uuid
        other_parent = Account(
            email="<EMAIL>",
            public_id=str(uuid.uuid4()),
            is_verified=True,
            stripe_customer_id="cus_test_other_parent"
        )
        db_session.add(other_parent)
        db_session.commit()  # Commit to get the ID
        monthly_subscription.parent_account_id = other_parent.id
        db_session.commit()
        
        # Try to cancel with original parent's auth
        response = client.post(
            "/api/v1/app/parent/subscription/cancel-scheduled-pause",
            json={
                "subscription_public_id": monthly_subscription.public_id,
                "pause_public_id": scheduled_pause.public_id
            },
            headers=auth_headers
        )
        
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["message"].lower()
    
    def test_cancel_multiple_scheduled_pauses(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account,
        monthly_subscription,
        yearly_subscription,
        auth_headers,
        mock_qstash_client
    ):
        """Test that each subscription's scheduled pause is independent."""
        # Create scheduled pauses for both subscriptions
        from db.models import SubscriptionPause
        
        monthly_pause = SubscriptionPause(
            public_id="scheduled_monthly",
            active_subscription_id=monthly_subscription.id,
            start_date=datetime(2025, 7, 15, tzinfo=UTC),
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=monthly_subscription.current_period_end,
            new_period_end=monthly_subscription.current_period_end,
            status=SubscriptionPauseStatus.SCHEDULED,
            qstash_message_id="msg_monthly_123"
        )
        
        yearly_pause = SubscriptionPause(
            public_id="scheduled_yearly",
            active_subscription_id=yearly_subscription.id,
            start_date=datetime(2025, 7, 15, tzinfo=UTC),
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=yearly_subscription.current_period_end,
            new_period_end=yearly_subscription.current_period_end,
            status=SubscriptionPauseStatus.SCHEDULED,
            qstash_message_id="msg_yearly_123"
        )
        
        db_session.add(monthly_pause)
        db_session.add(yearly_pause)
        db_session.commit()
        
        # Cancel only the monthly subscription's pause
        response = client.post(
            "/api/v1/app/parent/subscription/cancel-scheduled-pause",
            json={
                "subscription_public_id": monthly_subscription.public_id,
                "pause_public_id": monthly_pause.public_id
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        
        # Verify only monthly pause was deleted
        from db.models import SubscriptionPause
        remaining_pauses = db_session.query(SubscriptionPause).filter_by(
            status=SubscriptionPauseStatus.SCHEDULED
        ).all()
        
        assert len(remaining_pauses) == 1
        assert remaining_pauses[0].active_subscription_id == yearly_subscription.id