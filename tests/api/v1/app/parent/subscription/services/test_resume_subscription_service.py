"""
Tests for resume_subscription_service.
"""
import pytest
from datetime import datetime, UTC, timedelta
from unittest.mock import patch, MagicMock
from sqlalchemy.orm import Session

from api.v1.app.parent.subscription.services.resume_subscription_service import resume_subscription_service
from core.exception_handling.exceptions.custom_exceptions import Validation<PERSON>rror, NotFoundError
from db.models.subscription import SubscriptionStatusType, SubscriptionPauseStatus, BillingPeriodEnum
from tests.mocks.stripe_subscription_mocks import create_mock_stripe_subscription


class TestResumeSubscriptionService:
    """Test cases for resume_subscription_service."""
    
    def test_resume_monthly_subscription(
        self,
        db_session: Session,
        test_parent_account,
        paused_monthly_subscription,
        mock_stripe_subscription_modify,
        mocker
    ):
        """Test resuming a paused monthly subscription."""
        subscription, pause_record = paused_monthly_subscription
        
        # Mock current time
        mock_now = datetime(2025, 7, 25, 12, 0, 0, tzinfo=UTC)
        mocker.patch("api.v1.app.parent.subscription.services.resume_subscription_service.datetime")
        mocker.patch("api.v1.app.parent.subscription.services.resume_subscription_service.datetime.now", return_value=mock_now)
        
        # Execute resume
        response = resume_subscription_service(
            db=db_session,
            parent_public_id=test_parent_account.public_id,
            subscription_public_id=subscription.public_id
        )
        
        # Verify response
        assert response.subscription_status == SubscriptionStatusType.ACTIVE
        assert response.message == "Your subscription has been resumed successfully."
        assert response.resumed_at == mock_now
        
        # Verify Stripe was called to clear pause
        mock_stripe_subscription_modify.assert_called_once()
        call_args = mock_stripe_subscription_modify.call_args
        assert call_args[0][0] == subscription.stripe_subscription_id
        assert call_args[1]['pause_collection'] == ""
        
        # Verify database updates
        db_session.refresh(subscription)
        db_session.refresh(pause_record)
        assert subscription.status == SubscriptionStatusType.ACTIVE
        assert pause_record.status == SubscriptionPauseStatus.RESUMED
        assert pause_record.resumed_at == mock_now
    
    def test_resume_yearly_subscription_with_credit(
        self,
        db_session: Session,
        test_parent_account,
        yearly_subscription,
        mock_stripe_subscription_modify,
        mocker
    ):
        """Test resuming a yearly subscription with prorated credit."""
        # Create a pause record for yearly subscription
        pause_start = datetime(2025, 7, 15, tzinfo=UTC)
        original_period_end = datetime(2025, 12, 31, tzinfo=UTC)
        
        from db.models import SubscriptionPause
        pause_record = SubscriptionPause(
            public_id="pause_123",
            active_subscription_id=yearly_subscription.id,
            start_date=pause_start,
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=original_period_end,
            new_period_end=original_period_end + timedelta(days=47),  # Extended by full pause period
            status=SubscriptionPauseStatus.PAUSED
        )
        db_session.add(pause_record)
        db_session.commit()
        
        # Mock current time (resuming after 10 days)
        mock_now = datetime(2025, 7, 25, 12, 0, 0, tzinfo=UTC)
        mock_datetime = mocker.patch("api.v1.app.parent.subscription.services.resume_subscription_service.datetime")
        mock_datetime.now.return_value = mock_now
        mock_datetime.fromtimestamp.side_effect = lambda ts, tz=None: datetime.fromtimestamp(ts, tz=tz)
        
        # Execute resume
        response = resume_subscription_service(
            db=db_session,
            parent_public_id=test_parent_account.public_id,
            subscription_public_id=yearly_subscription.public_id
        )
        
        # Verify response
        assert response.subscription_status == SubscriptionStatusType.ACTIVE
        assert "credited 10 days" in response.message
        assert "Your next billing date is" in response.message
        
        # Verify Stripe was called to adjust trial_end
        mock_stripe_subscription_modify.assert_called_once()
        call_args = mock_stripe_subscription_modify.call_args
        assert call_args[0][0] == yearly_subscription.stripe_subscription_id
        assert 'trial_end' in call_args[1]
        assert call_args[1]['proration_behavior'] == 'none'
        
        # Calculate expected trial_end (original + 10 days)
        actual_pause_duration = (mock_now - pause_start).total_seconds()
        expected_trial_end = original_period_end + timedelta(seconds=actual_pause_duration)
        actual_trial_end = datetime.fromtimestamp(call_args[1]['trial_end'], tz=UTC)
        
        # Allow small time difference due to calculation
        assert abs((actual_trial_end - expected_trial_end).total_seconds()) < 60
        
        # Verify database updates
        db_session.refresh(pause_record)
        assert pause_record.status == SubscriptionPauseStatus.RESUMED
        assert pause_record.resumed_at == mock_now
        assert pause_record.new_period_end == expected_trial_end
    
    def test_resume_non_paused_subscription(
        self,
        db_session: Session,
        test_parent_account,
        monthly_subscription
    ):
        """Test attempting to resume a subscription that isn't paused."""
        # No pause record exists for this subscription
        
        with pytest.raises(ValidationError) as exc_info:
            resume_subscription_service(
                db=db_session,
                parent_public_id=test_parent_account.public_id,
                subscription_public_id=monthly_subscription.public_id
            )
        
        assert "not currently paused" in str(exc_info.value.message)
    
    def test_resume_scheduled_pause(
        self,
        db_session: Session,
        test_parent_account,
        monthly_subscription
    ):
        """Test attempting to resume a scheduled (not yet active) pause."""
        # Create a scheduled pause
        from db.models import SubscriptionPause
        scheduled_pause = SubscriptionPause(
            public_id="scheduled_123",
            active_subscription_id=monthly_subscription.id,
            start_date=datetime(2025, 7, 15, tzinfo=UTC),  # Future date
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=monthly_subscription.current_period_end,
            new_period_end=monthly_subscription.current_period_end,
            status=SubscriptionPauseStatus.SCHEDULED,
            qstash_message_id="msg_123"
        )
        db_session.add(scheduled_pause)
        db_session.commit()
        
        with pytest.raises(ValidationError) as exc_info:
            resume_subscription_service(
                db=db_session,
                parent_public_id=test_parent_account.public_id,
                subscription_public_id=monthly_subscription.public_id
            )
        
        assert "not currently paused" in str(exc_info.value.message)
    
    def test_resume_nonexistent_subscription(
        self,
        db_session: Session,
        test_parent_account
    ):
        """Test attempting to resume a non-existent subscription."""
        with pytest.raises(ValidationError) as exc_info:
            resume_subscription_service(
                db=db_session,
                parent_public_id=test_parent_account.public_id,
                subscription_public_id="non_existent_id"
            )
        
        assert "Subscription not found" in str(exc_info.value.message)