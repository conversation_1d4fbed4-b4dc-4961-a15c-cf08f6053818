"""
Tests for pause_subscription_service.
"""
import pytest
from datetime import datetime, UTC, timedelta
from dateutil import parser
from unittest.mock import patch, MagicMock
from sqlalchemy.orm import Session

from api.v1.app.parent.subscription.services.pause_subscription_service import pause_subscription_service
from core.exception_handling.exceptions.custom_exceptions import ValidationError, NotFoundError
from db.models.subscription import SubscriptionStatusType, SubscriptionPauseStatus, BillingPeriodEnum
from tests.mocks.stripe_subscription_mocks import create_mock_stripe_subscription
from tests.mocks.qstash_mocks import create_mock_qstash_response


class TestPauseSubscriptionService:
    """Test cases for pause_subscription_service."""
    
    @pytest.fixture(autouse=True)
    def setup_pause_window(self, mocker):
        """Mock pause window settings to be always valid."""
        mocker.patch("core.config.settings.settings.SUMMER_PAUSE_START_UTC", "2025-07-15T00:00:00Z")
        mocker.patch("core.config.settings.settings.SUMMER_PAUSE_END_UTC", "2025-08-31T23:59:59Z")
        mocker.patch("core.config.settings.settings.PAUSE_UI_VISIBLE_FROM", "2025-05-01T00:00:00Z")
        mocker.patch("core.config.settings.settings.PAUSE_UI_VISIBLE_TO", "2025-09-01T00:00:00Z")
    
    def test_pause_monthly_subscription_immediate(
        self,
        db_session: Session,
        test_parent_account,
        monthly_subscription,
        mock_stripe_subscription_retrieve,
        mock_stripe_subscription_modify,
        mocker
    ):
        """Test pausing a monthly subscription immediately (after July 15)."""
        # Mock current time to be after pause start
        mock_now = datetime(2025, 7, 20, 12, 0, 0, tzinfo=UTC)
        mock_datetime = mocker.patch("api.v1.app.parent.subscription.services.pause_subscription_service.datetime")
        mock_datetime.now.return_value = mock_now
        mock_datetime.fromtimestamp.side_effect = lambda ts, tz=None: datetime.fromtimestamp(ts, tz=tz)
        
        # Mock Stripe subscription
        mock_stripe_sub = create_mock_stripe_subscription(
            subscription_id=monthly_subscription.stripe_subscription_id,
            current_period_end=monthly_subscription.current_period_end,
            billing_period=BillingPeriodEnum.MONTHLY
        )
        mock_stripe_subscription_retrieve.return_value = mock_stripe_sub
        
        # Execute pause
        response = pause_subscription_service(
            db=db_session,
            parent_public_id=test_parent_account.public_id,
            subscription_public_id=monthly_subscription.public_id
        )
        
        # Verify response
        assert response.status == "paused"
        assert "automatically resume on August 31" in response.message
        assert response.pause_start_date.date() == mock_now.date()
        
        # Verify Stripe was called correctly
        mock_stripe_subscription_modify.assert_called_once()
        call_args = mock_stripe_subscription_modify.call_args
        assert call_args[0][0] == monthly_subscription.stripe_subscription_id
        assert call_args[1]['pause_collection']['behavior'] == 'mark_uncollectible'
        assert 'resumes_at' in call_args[1]['pause_collection']
        
        # Verify database updates
        db_session.refresh(monthly_subscription)
        assert monthly_subscription.status == SubscriptionStatusType.PAUSED
        
        # Verify pause record created
        from db.models import SubscriptionPause
        pause_record = db_session.query(SubscriptionPause).filter_by(
            active_subscription_id=monthly_subscription.id
        ).first()
        assert pause_record is not None
        assert pause_record.status == SubscriptionPauseStatus.PAUSED
    
    def test_pause_yearly_subscription_immediate(
        self,
        db_session: Session,
        test_parent_account,
        yearly_subscription,
        mock_stripe_subscription_retrieve,
        mock_stripe_subscription_modify,
        mocker
    ):
        """Test pausing a yearly subscription immediately with trial_end extension."""
        # Mock current time to be after pause start
        mock_now = datetime(2025, 7, 20, 12, 0, 0, tzinfo=UTC)
        mock_datetime = mocker.patch("api.v1.app.parent.subscription.services.pause_subscription_service.datetime")
        mock_datetime.now.return_value = mock_now
        mock_datetime.fromtimestamp.side_effect = lambda ts, tz=None: datetime.fromtimestamp(ts, tz=tz)
        
        # Mock Stripe subscription
        current_period_end = datetime(2025, 12, 31, tzinfo=UTC)
        mock_stripe_sub = create_mock_stripe_subscription(
            subscription_id=yearly_subscription.stripe_subscription_id,
            current_period_end=current_period_end,
            billing_period=BillingPeriodEnum.YEARLY
        )
        mock_stripe_subscription_retrieve.return_value = mock_stripe_sub
        
        # Execute pause
        response = pause_subscription_service(
            db=db_session,
            parent_public_id=test_parent_account.public_id,
            subscription_public_id=yearly_subscription.public_id
        )
        
        # Verify response
        assert response.status == "paused"
        assert "billing has been extended" in response.message
        assert "Your next billing date is now" in response.message
        
        # Verify Stripe was called correctly
        mock_stripe_subscription_modify.assert_called_once()
        call_args = mock_stripe_subscription_modify.call_args
        assert call_args[0][0] == yearly_subscription.stripe_subscription_id
        assert 'trial_end' in call_args[1]
        assert 'proration_behavior' in call_args[1]
        assert call_args[1]['proration_behavior'] == 'none'
        
        # Verify subscription status remains ACTIVE (not PAUSED)
        db_session.refresh(yearly_subscription)
        assert yearly_subscription.status == SubscriptionStatusType.ACTIVE
        
        # Verify pause record created with extended period
        from db.models import SubscriptionPause
        pause_record = db_session.query(SubscriptionPause).filter_by(
            active_subscription_id=yearly_subscription.id
        ).first()
        assert pause_record is not None
        assert pause_record.status == SubscriptionPauseStatus.PAUSED
        assert pause_record.new_period_end > pause_record.original_period_end
    
    def test_pause_subscription_scheduled(
        self,
        db_session: Session,
        test_parent_account,
        monthly_subscription,
        mock_stripe_subscription_retrieve,
        mock_qstash_client,
        mocker
    ):
        """Test scheduling a pause for future activation."""
        # Mock current time to be before pause start
        mock_now = datetime(2025, 5, 15, 12, 0, 0, tzinfo=UTC)
        mock_datetime = mocker.patch("api.v1.app.parent.subscription.services.pause_subscription_service.datetime")
        mock_datetime.now.return_value = mock_now
        mock_datetime.fromtimestamp.side_effect = lambda ts, tz=None: datetime.fromtimestamp(ts, tz=tz)
        
        # Mock QStash scheduling
        mock_qstash_client.post.return_value = create_mock_qstash_response()
        
        # Mock Stripe subscription
        mock_stripe_sub = create_mock_stripe_subscription(
            subscription_id=monthly_subscription.stripe_subscription_id
        )
        mock_stripe_subscription_retrieve.return_value = mock_stripe_sub
        
        # Execute pause
        response = pause_subscription_service(
            db=db_session,
            parent_public_id=test_parent_account.public_id,
            subscription_public_id=monthly_subscription.public_id
        )
        
        # Verify response
        assert response.status == "scheduled"
        assert "scheduled to pause" in response.message
        assert response.pause_start_date.date() == datetime(2025, 7, 15).date()
        
        # Verify QStash was called
        assert mock_qstash_client.post.called
        
        # Verify subscription status remains ACTIVE
        db_session.refresh(monthly_subscription)
        assert monthly_subscription.status == SubscriptionStatusType.ACTIVE
        
        # Verify pause record created with SCHEDULED status
        from db.models import SubscriptionPause
        pause_record = db_session.query(SubscriptionPause).filter_by(
            active_subscription_id=monthly_subscription.id
        ).first()
        assert pause_record is not None
        assert pause_record.status == SubscriptionPauseStatus.SCHEDULED
        assert pause_record.qstash_message_id is not None
    
    def test_pause_already_paused_subscription(
        self,
        db_session: Session,
        test_parent_account,
        paused_monthly_subscription,
        mock_stripe_subscription_retrieve
    ):
        """Test attempting to pause an already paused subscription."""
        subscription, existing_pause = paused_monthly_subscription
        
        # Mock Stripe subscription
        mock_stripe_sub = create_mock_stripe_subscription(
            subscription_id=subscription.stripe_subscription_id,
            pause_collection={'behavior': 'mark_uncollectible'}
        )
        mock_stripe_subscription_retrieve.return_value = mock_stripe_sub
        
        # Attempt to pause again
        with pytest.raises(ValidationError) as exc_info:
            pause_subscription_service(
                db=db_session,
                parent_public_id=test_parent_account.public_id,
                subscription_public_id=subscription.public_id
            )
        
        assert "Subscription cannot be paused" in str(exc_info.value.message) or "already has a pause scheduled" in str(exc_info.value.message)
    
    def test_pause_outside_visibility_window(
        self,
        db_session: Session,
        test_parent_account,
        monthly_subscription,
        mocker
    ):
        """Test attempting to pause outside the visibility window."""
        # Mock settings to make window closed
        mocker.patch("core.config.settings.settings.PAUSE_UI_VISIBLE_FROM", "2025-05-01T00:00:00Z")
        mocker.patch("core.config.settings.settings.PAUSE_UI_VISIBLE_TO", "2025-07-01T00:00:00Z")
        
        # Mock current time to be outside window
        mock_now = datetime(2025, 9, 15, 12, 0, 0, tzinfo=UTC)
        mock_datetime = mocker.patch("api.v1.app.parent.subscription.services.pause_subscription_service.datetime")
        mock_datetime.now.return_value = mock_now
        mock_datetime.fromtimestamp.side_effect = lambda ts, tz=None: datetime.fromtimestamp(ts, tz=tz)
        
        # Attempt to pause
        with pytest.raises(ValidationError) as exc_info:
            pause_subscription_service(
                db=db_session,
                parent_public_id=test_parent_account.public_id,
                subscription_public_id=monthly_subscription.public_id
            )
        
        assert "not available at this time" in str(exc_info.value.message)
    
    def test_pause_nonexistent_subscription(
        self,
        db_session: Session,
        test_parent_account
    ):
        """Test attempting to pause a non-existent subscription."""
        with pytest.raises(ValidationError) as exc_info:
            pause_subscription_service(
                db=db_session,
                parent_public_id=test_parent_account.public_id,
                subscription_public_id="non_existent_id"
            )
        
        assert "Subscription not found" in str(exc_info.value.message)