"""
Tests for activate_pause service (called by QStash).
"""
import pytest
from datetime import datetime, UTC, timedelta
from unittest.mock import patch, MagicMock
from sqlalchemy.orm import Session

from api.v1.app.parent.subscription.services.activate_pause_service import activate_pause
from core.exception_handling.exceptions.custom_exceptions import NotFoundError, ServiceError
from db.models.subscription import SubscriptionStatusType, SubscriptionPauseStatus, BillingPeriodEnum
from tests.mocks.stripe_subscription_mocks import create_mock_stripe_subscription


class TestActivatePauseService:
    """Test cases for activate_pause service."""
    
    @pytest.fixture(autouse=True)
    def setup_pause_window(self, mocker):
        """Mock pause window settings."""
        mocker.patch("core.config.settings.settings.SUMMER_PAUSE_END_UTC", "2025-08-31T23:59:59Z")
    
    def test_activate_pause_monthly_subscription(
        self,
        db_session: Session,
        monthly_subscription,
        mock_stripe_subscription_retrieve,
        mock_stripe_subscription_modify,
        mocker
    ):
        """Test activating a scheduled pause for a monthly subscription."""
        # Create a scheduled pause
        from db.models import SubscriptionPause
        scheduled_pause = SubscriptionPause(
            public_id="pause_scheduled_123",
            active_subscription_id=monthly_subscription.id,
            start_date=datetime(2025, 7, 15, tzinfo=UTC),
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=monthly_subscription.current_period_end,
            new_period_end=monthly_subscription.current_period_end,
            status=SubscriptionPauseStatus.SCHEDULED,
            qstash_message_id="msg_123"
        )
        db_session.add(scheduled_pause)
        db_session.commit()
        
        # Mock current time
        mock_now = datetime(2025, 7, 15, 0, 0, 0, tzinfo=UTC)
        mock_datetime = mocker.patch("api.v1.app.parent.subscription.services.activate_pause_service.datetime")
        mock_datetime.now.return_value = mock_now
        mock_datetime.fromtimestamp.side_effect = lambda ts, tz=None: datetime.fromtimestamp(ts, tz=tz)
        
        # Execute activation
        result = activate_pause(
            db=db_session,
            active_subscription_id=monthly_subscription.id,
            subscription_pause_public_id=scheduled_pause.public_id
        )
        
        # Verify success
        assert result is True
        
        # Verify Stripe was called correctly for monthly
        mock_stripe_subscription_modify.assert_called_once()
        call_args = mock_stripe_subscription_modify.call_args
        assert call_args[0][0] == monthly_subscription.stripe_subscription_id
        assert 'pause_collection' in call_args[1]
        assert call_args[1]['pause_collection']['behavior'] == 'mark_uncollectible'
        assert 'resumes_at' in call_args[1]['pause_collection']
        
        # Verify database updates
        db_session.refresh(monthly_subscription)
        db_session.refresh(scheduled_pause)
        assert monthly_subscription.status == SubscriptionStatusType.PAUSED
        assert scheduled_pause.status == SubscriptionPauseStatus.PAUSED
        assert scheduled_pause.start_date == mock_now
    
    def test_activate_pause_yearly_subscription(
        self,
        db_session: Session,
        yearly_subscription,
        mock_stripe_subscription_retrieve,
        mock_stripe_subscription_modify,
        mocker
    ):
        """Test activating a scheduled pause for a yearly subscription."""
        # Create a scheduled pause - set original_period_end to match what Stripe will return
        current_period_end = datetime(2025, 12, 31, tzinfo=UTC)
        from db.models import SubscriptionPause
        scheduled_pause = SubscriptionPause(
            public_id="pause_scheduled_yearly",
            active_subscription_id=yearly_subscription.id,
            start_date=datetime(2025, 7, 15, tzinfo=UTC),
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=current_period_end,
            new_period_end=current_period_end,
            status=SubscriptionPauseStatus.SCHEDULED
        )
        db_session.add(scheduled_pause)
        db_session.commit()
        
        # Mock current time
        mock_now = datetime(2025, 7, 15, 0, 0, 0, tzinfo=UTC)
        mock_datetime = mocker.patch("api.v1.app.parent.subscription.services.activate_pause_service.datetime")
        mock_datetime.now.return_value = mock_now
        mock_datetime.fromtimestamp.side_effect = lambda ts, tz=None: datetime.fromtimestamp(ts, tz=tz)
        
        # Mock Stripe subscription - reuse the same current_period_end
        mock_stripe_sub = create_mock_stripe_subscription(
            subscription_id=yearly_subscription.stripe_subscription_id,
            current_period_end=current_period_end
        )
        mock_stripe_subscription_retrieve.return_value = mock_stripe_sub
        
        # Execute activation
        result = activate_pause(
            db=db_session,
            active_subscription_id=yearly_subscription.id,
            subscription_pause_public_id=scheduled_pause.public_id
        )
        
        # Verify success
        assert result is True
        
        # Verify Stripe was called correctly for yearly
        mock_stripe_subscription_modify.assert_called_once()
        call_args = mock_stripe_subscription_modify.call_args
        assert call_args[0][0] == yearly_subscription.stripe_subscription_id
        assert 'trial_end' in call_args[1]
        assert call_args[1]['proration_behavior'] == 'none'
        
        # Verify database updates
        db_session.refresh(yearly_subscription)
        db_session.refresh(scheduled_pause)
        # Yearly subscriptions stay ACTIVE
        assert yearly_subscription.status == SubscriptionStatusType.ACTIVE
        assert scheduled_pause.status == SubscriptionPauseStatus.PAUSED
        assert scheduled_pause.new_period_end > scheduled_pause.original_period_end
    
    def test_activate_pause_idempotency(
        self,
        db_session: Session,
        monthly_subscription,
        mock_stripe_subscription_modify
    ):
        """Test that activating an already paused subscription is idempotent."""
        # Create an already paused subscription
        from db.models import SubscriptionPause
        paused_pause = SubscriptionPause(
            public_id="pause_already_paused",
            active_subscription_id=monthly_subscription.id,
            start_date=datetime(2025, 7, 15, tzinfo=UTC),
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=monthly_subscription.current_period_end,
            new_period_end=monthly_subscription.current_period_end,
            status=SubscriptionPauseStatus.PAUSED  # Already paused
        )
        db_session.add(paused_pause)
        db_session.commit()
        
        # Attempt to activate again - should not raise error
        result = activate_pause(
            db=db_session,
            active_subscription_id=monthly_subscription.id,
            subscription_pause_public_id=paused_pause.public_id
        )
        
        # Should return True but not call Stripe
        assert result is True
        mock_stripe_subscription_modify.assert_not_called()
    
    def test_activate_pause_missing_subscription(
        self,
        db_session: Session
    ):
        """Test activating pause for non-existent subscription."""
        # Don't create a pause record in DB, just test with non-existent IDs
        with pytest.raises(NotFoundError) as exc_info:
            activate_pause(
                db=db_session,
                active_subscription_id=99999,
                subscription_pause_public_id="non_existent_pause"
            )
        
        assert "Active subscription not found" in str(exc_info.value.message)
    
    def test_activate_pause_missing_pause_record(
        self,
        db_session: Session,
        monthly_subscription
    ):
        """Test activating non-existent pause record."""
        with pytest.raises(NotFoundError) as exc_info:
            activate_pause(
                db=db_session,
                active_subscription_id=monthly_subscription.id,
                subscription_pause_public_id="non_existent_pause"
            )
        
        assert "Scheduled pause not found" in str(exc_info.value.message)
    
    def test_activate_pause_mismatch(
        self,
        db_session: Session,
        monthly_subscription,
        yearly_subscription
    ):
        """Test activating pause with mismatched subscription ID."""
        # Create pause for monthly but try to activate with yearly ID
        from db.models import SubscriptionPause
        pause = SubscriptionPause(
            public_id="pause_mismatch",
            active_subscription_id=monthly_subscription.id,  # For monthly
            start_date=datetime(2025, 7, 15, tzinfo=UTC),
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=monthly_subscription.current_period_end,
            new_period_end=monthly_subscription.current_period_end,
            status=SubscriptionPauseStatus.SCHEDULED
        )
        db_session.add(pause)
        db_session.commit()
        
        with pytest.raises(ServiceError) as exc_info:
            activate_pause(
                db=db_session,
                active_subscription_id=yearly_subscription.id,  # Wrong subscription
                subscription_pause_public_id=pause.public_id
            )
        
        assert "Pause record mismatch" in str(exc_info.value.message)