"""
Edge case tests for subscription pause/resume functionality.
"""
import pytest
from datetime import datetime, UTC, timedelta
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from unittest.mock import patch, MagicMock
import stripe

from db.models.subscription import SubscriptionStatusType, SubscriptionPauseStatus, Billing<PERSON>eriodEnum
from tests.mocks.stripe_subscription_mocks import create_mock_stripe_subscription
from main import app
from api.v1.tasks.internal.routes.activate_pause import verify_qstash_signature
from tests.mocks.webhook_auth_mocks import mock_verify_qstash_signature


class TestPauseResumeEdgeCases:
    """Test edge cases and error scenarios for pause/resume functionality."""
    
    @pytest.fixture(autouse=True)
    def setup_pause_window(self, mocker):
        """Mock pause window settings."""
        mocker.patch("core.config.settings.settings.SUMMER_PAUSE_START_UTC", "2025-07-15T00:00:00Z")
        mocker.patch("core.config.settings.settings.SUMMER_PAUSE_END_UTC", "2025-08-31T23:59:59Z")
        mocker.patch("core.config.settings.settings.PAUSE_UI_VISIBLE_FROM", "2025-05-01T00:00:00Z")
        mocker.patch("core.config.settings.settings.PAUSE_UI_VISIBLE_TO", "2025-09-01T00:00:00Z")
    
    @pytest.fixture
    def qstash_client(self, client: TestClient, db_session):
        """Test client with QStash signature verification mocked."""
        # Override QStash signature verification dependency
        app.dependency_overrides[verify_qstash_signature] = mock_verify_qstash_signature
        
        yield client
        
        # Clean up override
        app.dependency_overrides.pop(verify_qstash_signature, None)
    
    @pytest.fixture
    def qstash_headers(self):
        """Create valid QStash headers."""
        return {
            "Upstash-Signature": "test_signature",
            "Content-Type": "application/json"
        }
    
    def test_pause_with_stripe_api_error(
        self,
        client: TestClient,
        test_parent_account,
        monthly_subscription,
        auth_headers,
        mock_stripe_subscription_retrieve,
        mocker
    ):
        """Test pause handling when Stripe API fails."""
        mock_now = datetime(2025, 7, 20, 12, 0, 0, tzinfo=UTC)
        mocker.patch("api.v1.app.parent.subscription.services.pause_subscription_service.datetime")
        mocker.patch("api.v1.app.parent.subscription.services.pause_subscription_service.datetime.now", return_value=mock_now)
        
        # Mock Stripe to fail on modify
        mock_stripe_sub = create_mock_stripe_subscription(
            subscription_id=monthly_subscription.stripe_subscription_id
        )
        mock_stripe_subscription_retrieve.return_value = mock_stripe_sub
        
        mocker.patch(
            "stripe.Subscription.modify",
            side_effect=stripe.error.StripeError("Stripe API error")
        )
        
        response = client.post(
            "/api/v1/app/parent/subscription/pause",
            json={"subscription_public_id": monthly_subscription.public_id},
            headers=auth_headers
        )
        
        assert response.status_code == 503  # ExternalServiceError returns 503
        data = response.json()
        # Check for error message in either detail or message field
        error_msg = data.get("detail", data.get("message", ""))
        assert "payment gateway error" in error_msg.lower() or "stripe" in error_msg.lower()
    
    def test_pause_subscription_at_exact_window_boundaries(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account,
        monthly_subscription,
        auth_headers,
        mock_stripe_subscription_retrieve,
        mock_stripe_subscription_modify,
        mock_qstash_client,
        mocker
    ):
        """Test pausing at exact pause window start and end times."""
        mock_stripe_sub = create_mock_stripe_subscription(
            subscription_id=monthly_subscription.stripe_subscription_id
        )
        mock_stripe_subscription_retrieve.return_value = mock_stripe_sub
        
        # Test 1: Exactly at pause start time - should schedule
        mock_time = datetime(2025, 7, 15, 0, 0, 0, tzinfo=UTC)
        mock_datetime = mocker.patch("api.v1.app.parent.subscription.services.pause_subscription_service.datetime")
        mock_datetime.now.return_value = mock_time
        mock_datetime.fromtimestamp.side_effect = lambda ts, tz=None: datetime.fromtimestamp(ts, tz=tz)
        mock_datetime.fromisoformat.side_effect = lambda s: datetime.fromisoformat(s)
        
        response1 = client.post(
            "/api/v1/app/parent/subscription/pause",
            json={"subscription_public_id": monthly_subscription.public_id},
            headers=auth_headers
        )
        
        if response1.status_code != 200:
            print(f"Response status: {response1.status_code}")
            print(f"Response body: {response1.json()}")
        assert response1.status_code == 200
        data1 = response1.json()
        # At exact start time, it might schedule or activate immediately depending on implementation
        assert data1["status"] in ["scheduled", "paused"]
        
        # Clean up for next test
        from db.models import SubscriptionPause
        db_session.query(SubscriptionPause).filter_by(
            active_subscription_id=monthly_subscription.id
        ).delete()
        db_session.commit()
        
        # Reset subscription status
        monthly_subscription.status = SubscriptionStatusType.ACTIVE
        db_session.commit()
        
        # Test 2: One second before end time - should still work
        mock_time2 = datetime(2025, 8, 31, 23, 59, 58, tzinfo=UTC)
        mock_datetime2 = mocker.patch("api.v1.app.parent.subscription.services.pause_subscription_service.datetime")
        mock_datetime2.now.return_value = mock_time2
        mock_datetime2.fromtimestamp.side_effect = lambda ts, tz=None: datetime.fromtimestamp(ts, tz=tz)
        mock_datetime2.fromisoformat.side_effect = lambda s: datetime.fromisoformat(s)
        
        response2 = client.post(
            "/api/v1/app/parent/subscription/pause",
            json={"subscription_public_id": monthly_subscription.public_id},
            headers=auth_headers
        )
        
        assert response2.status_code == 200
        data2 = response2.json()
        assert data2["status"] == "paused"
    
    def test_resume_subscription_at_pause_end_date(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account,
        paused_monthly_subscription,
        auth_headers,
        mock_stripe_subscription_modify,
        mocker
    ):
        """Test resuming exactly at the scheduled pause end date."""
        subscription, pause_record = paused_monthly_subscription
        
        # Set pause end date
        pause_record.end_date = datetime(2025, 8, 31, 23, 59, 59, tzinfo=UTC)
        db_session.commit()
        
        # Mock current time to be exactly at pause end
        mock_now = datetime(2025, 8, 31, 23, 59, 59, tzinfo=UTC)
        mocker.patch("api.v1.app.parent.subscription.services.resume_subscription_service.datetime")
        mocker.patch("api.v1.app.parent.subscription.services.resume_subscription_service.datetime.now", return_value=mock_now)
        
        response = client.post(
            "/api/v1/app/parent/subscription/resume",
            json={"subscription_public_id": subscription.public_id},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["subscription_status"] == "active"
    
    def test_yearly_subscription_pause_with_zero_duration(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account,
        yearly_subscription,
        auth_headers,
        mock_stripe_subscription_retrieve,
        mock_stripe_subscription_modify,
        mocker
    ):
        """Test pausing and immediately resuming a yearly subscription."""
        # Pause the subscription
        mock_pause_time = datetime(2025, 7, 20, 12, 0, 0, tzinfo=UTC)
        mock_datetime = mocker.patch("api.v1.app.parent.subscription.services.pause_subscription_service.datetime")
        mock_datetime.now.return_value = mock_pause_time
        mock_datetime.fromtimestamp.side_effect = lambda ts, tz=None: datetime.fromtimestamp(ts, tz=tz)
        mock_datetime.fromisoformat.side_effect = lambda s: datetime.fromisoformat(s)
        
        mock_stripe_sub = create_mock_stripe_subscription(
            subscription_id=yearly_subscription.stripe_subscription_id,
            current_period_end=datetime(2025, 12, 31, tzinfo=UTC),
            billing_period=BillingPeriodEnum.YEARLY
        )
        mock_stripe_subscription_retrieve.return_value = mock_stripe_sub
        
        pause_response = client.post(
            "/api/v1/app/parent/subscription/pause",
            json={"subscription_public_id": yearly_subscription.public_id},
            headers=auth_headers
        )
        assert pause_response.status_code == 200
        
        # Resume immediately (same time)
        mock_datetime_resume = mocker.patch("api.v1.app.parent.subscription.services.resume_subscription_service.datetime")
        mock_datetime_resume.now.return_value = mock_pause_time
        mock_datetime_resume.fromtimestamp.side_effect = lambda ts, tz=None: datetime.fromtimestamp(ts, tz=tz)
        mock_datetime_resume.fromisoformat.side_effect = lambda s: datetime.fromisoformat(s)
        
        resume_response = client.post(
            "/api/v1/app/parent/subscription/resume",
            json={"subscription_public_id": yearly_subscription.public_id},
            headers=auth_headers
        )
        
        assert resume_response.status_code == 200
        data = resume_response.json()
        # Should indicate no credit since pause duration was 0
        assert "credited 0 days" in data["message"] or "no credit" in data["message"]
    
    def test_concurrent_pause_requests(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account,
        monthly_subscription,
        auth_headers,
        mock_stripe_subscription_retrieve,
        mock_stripe_subscription_modify,
        mocker
    ):
        """Test handling of concurrent pause requests."""
        mock_time = datetime(2025, 7, 20, 12, 0, 0, tzinfo=UTC)
        mock_datetime = mocker.patch("api.v1.app.parent.subscription.services.pause_subscription_service.datetime")
        mock_datetime.now.return_value = mock_time
        mock_datetime.fromtimestamp.side_effect = lambda ts, tz=None: datetime.fromtimestamp(ts, tz=tz)
        mock_datetime.fromisoformat.side_effect = lambda s: datetime.fromisoformat(s)
        
        mock_stripe_sub = create_mock_stripe_subscription(
            subscription_id=monthly_subscription.stripe_subscription_id
        )
        mock_stripe_subscription_retrieve.return_value = mock_stripe_sub
        
        # Make first pause request - should succeed
        response1 = client.post(
            "/api/v1/app/parent/subscription/pause",
            json={"subscription_public_id": monthly_subscription.public_id},
            headers=auth_headers
        )
        assert response1.status_code == 200
        
        # Make second pause request - should fail because pause already exists
        response2 = client.post(
            "/api/v1/app/parent/subscription/pause",
            json={"subscription_public_id": monthly_subscription.public_id},
            headers=auth_headers
        )
        assert response2.status_code == 400
        # The subscription is already paused from the first request
        assert "cannot be paused" in response2.json()["message"].lower()
        
        # Verify only one pause record created
        from db.models import SubscriptionPause
        pause_count = db_session.query(SubscriptionPause).filter_by(
            active_subscription_id=monthly_subscription.id
        ).count()
        assert pause_count == 1
    
    def test_pause_with_invalid_subscription_status(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account,
        monthly_subscription,
        auth_headers,
        mock_stripe_subscription_retrieve,
        mocker
    ):
        """Test pausing a subscription in various invalid states."""
        mock_time = datetime(2025, 7, 20, 12, 0, 0, tzinfo=UTC)
        mocker.patch("api.v1.app.parent.subscription.services.pause_subscription_service.datetime")
        mocker.patch("api.v1.app.parent.subscription.services.pause_subscription_service.datetime.now", return_value=mock_time)
        
        # Test with CANCELED subscription
        monthly_subscription.status = SubscriptionStatusType.CANCELED
        db_session.commit()
        
        response = client.post(
            "/api/v1/app/parent/subscription/pause",
            json={"subscription_public_id": monthly_subscription.public_id},
            headers=auth_headers
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "cannot be paused" in data["message"].lower() or "invalid" in data["message"].lower()
    
    def test_qstash_activation_with_deleted_subscription(
        self,
        qstash_client: TestClient,
        db_session: Session,
        monthly_subscription,
        qstash_headers,
        mocker
    ):
        """Test QStash activation when subscription has been deleted."""
        # Create a scheduled pause
        from db.models import SubscriptionPause
        scheduled_pause = SubscriptionPause(
            public_id="pause_deleted_sub",
            active_subscription_id=monthly_subscription.id,
            start_date=datetime(2025, 7, 15, tzinfo=UTC),
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=monthly_subscription.current_period_end,
            new_period_end=monthly_subscription.current_period_end,
            status=SubscriptionPauseStatus.SCHEDULED,
            qstash_message_id="msg_deleted"
        )
        db_session.add(scheduled_pause)
        db_session.commit()
        
        # Delete the subscription
        db_session.delete(monthly_subscription)
        db_session.commit()
        
        # QStash tries to activate
        response = qstash_client.post(
            "/api/v1/tasks/internal/activate-pause",
            json={
                "active_subscription_id": scheduled_pause.active_subscription_id,
                "subscription_pause_public_id": scheduled_pause.public_id,
                "action": "activate_pause"
            },
            headers=qstash_headers
        )
        
        assert response.status_code == 200  # Service returns 200 to prevent QStash retries for missing resources
        data = response.json()
        assert data["status"] == "success"  # Even though subscription is missing
    
    def test_pause_with_stripe_subscription_missing_items(
        self,
        client: TestClient,
        test_parent_account,
        monthly_subscription,
        auth_headers,
        mock_stripe_subscription_retrieve,
        mocker
    ):
        """Test pausing when Stripe subscription has missing data."""
        mock_time = datetime(2025, 7, 20, 12, 0, 0, tzinfo=UTC)
        mocker.patch("api.v1.app.parent.subscription.services.pause_subscription_service.datetime")
        mocker.patch("api.v1.app.parent.subscription.services.pause_subscription_service.datetime.now", return_value=mock_time)
        
        # Mock Stripe subscription with missing items data
        mock_stripe_sub = MagicMock()
        mock_stripe_sub.id = monthly_subscription.stripe_subscription_id
        mock_stripe_sub.status = "active"
        mock_stripe_sub.current_period_end = int(datetime(2025, 8, 1, tzinfo=UTC).timestamp())
        mock_stripe_sub.items = MagicMock()
        mock_stripe_sub.items.data = []  # No items
        mock_stripe_sub.pause_collection = None
        
        mock_stripe_subscription_retrieve.return_value = mock_stripe_sub
        
        response = client.post(
            "/api/v1/app/parent/subscription/pause",
            json={"subscription_public_id": monthly_subscription.public_id},
            headers=auth_headers
        )
        
        # Service returns 503 when Stripe operations fail
        assert response.status_code == 503
    
    def test_database_rollback_on_pause_failure(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account,
        monthly_subscription,
        auth_headers,
        mock_stripe_subscription_retrieve,
        mocker
    ):
        """Test that database changes are rolled back if pause fails."""
        mock_time = datetime(2025, 7, 20, 12, 0, 0, tzinfo=UTC)
        mocker.patch("api.v1.app.parent.subscription.services.pause_subscription_service.datetime")
        mocker.patch("api.v1.app.parent.subscription.services.pause_subscription_service.datetime.now", return_value=mock_time)
        
        mock_stripe_sub = create_mock_stripe_subscription(
            subscription_id=monthly_subscription.stripe_subscription_id
        )
        mock_stripe_subscription_retrieve.return_value = mock_stripe_sub
        
        # Mock Stripe modify to succeed, but then mock DB commit to fail
        original_status = monthly_subscription.status
        
        # Create a mock that fails on the second commit (after creating pause record)
        commit_count = 0
        def mock_commit():
            nonlocal commit_count
            commit_count += 1
            if commit_count > 1:
                raise Exception("Database error")
        
        mocker.patch.object(db_session, 'commit', side_effect=mock_commit)
        
        response = client.post(
            "/api/v1/app/parent/subscription/pause",
            json={"subscription_public_id": monthly_subscription.public_id},
            headers=auth_headers
        )
        
        # Should return error (503 for external service error)
        assert response.status_code == 503
        
        # Verify subscription status wasn't changed
        db_session.rollback()  # Ensure we can read
        db_session.refresh(monthly_subscription)
        assert monthly_subscription.status == original_status
        
        # Verify no pause record was created
        from db.models import SubscriptionPause
        pause_count = db_session.query(SubscriptionPause).filter_by(
            active_subscription_id=monthly_subscription.id
        ).count()
        assert pause_count == 0