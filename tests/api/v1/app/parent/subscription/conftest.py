"""
Shared fixtures for subscription tests.
"""
import pytest
from datetime import datetime, UTC, timedelta
from sqlalchemy.orm import Session
import uuid
from fastapi.testclient import TestClient

from db.models import (
    Account, 
    ActiveSubscription, 
    ActiveSubscriptionPlanLink,
    SubscriptionOption,
    PriceVersion,
    PriceEligibility,
    Year
)
from db.models.subscription import (
    SubscriptionStatusType, 
    BillingPeriodEnum,
    PlanTypeEnum,
    SubscriptionPause,
    SubscriptionPauseStatus
)
from tests.data import constants
from tests.utils.auth_utils import create_test_parent_auth_token

# Note: client and db_session fixtures are automatically discovered by pytest
# from parent conftest.py files


@pytest.fixture
def test_parent_account(db_session: Session) -> Account:
    """Create a test parent account."""
    account = Account(
        email="<EMAIL>",
        public_id=str(uuid.uuid4()),
        stripe_customer_id=f"cus_test_{uuid.uuid4().hex[:8]}",
        is_verified=True,
        language="en"
    )
    db_session.add(account)
    db_session.commit()
    db_session.refresh(account)
    return account


@pytest.fixture
def test_price_version(db_session: Session) -> PriceVersion:
    """Create a test price version."""
    price_version = PriceVersion(
        public_id=str(uuid.uuid4()),
        name="Test Pricing",
        slug="test-pricing",
        is_current=True,
        is_active=True
    )
    db_session.add(price_version)
    db_session.commit()
    db_session.refresh(price_version)
    return price_version


@pytest.fixture
def test_year(db_session: Session) -> Year:
    """Create a test year."""
    year = Year(
        public_id=str(uuid.uuid4()),
        name="Test Year",
        is_classique=False,
        is_general=True
    )
    db_session.add(year)
    db_session.commit()
    db_session.refresh(year)
    return year


@pytest.fixture
def test_subscription_option(db_session: Session, test_year: Year, test_price_version: PriceVersion) -> SubscriptionOption:
    """Create a test subscription option."""
    option = SubscriptionOption(
        public_id=str(uuid.uuid4()),
        year_id=test_year.id,
        price_version_id=test_price_version.id,
        sc_monthly_stripe_price_id="price_test_sc_monthly",
        sc_yearly_stripe_price_id="price_test_sc_yearly",
        display_price_config_json={},
        is_active=True
    )
    db_session.add(option)
    db_session.commit()
    db_session.refresh(option)
    return option


@pytest.fixture
def monthly_subscription(
    db_session: Session, 
    test_parent_account: Account,
    test_subscription_option: SubscriptionOption
) -> ActiveSubscription:
    """Create a test monthly subscription."""
    subscription = ActiveSubscription(
        public_id=str(uuid.uuid4()),
        parent_account_id=test_parent_account.id,
        stripe_subscription_id=f"sub_test_monthly_{uuid.uuid4().hex[:8]}",
        status=SubscriptionStatusType.ACTIVE,
        current_period_start=datetime.now(UTC),
        current_period_end=datetime.now(UTC) + timedelta(days=30),
        cancel_at_period_end=False
    )
    db_session.add(subscription)
    db_session.flush()
    
    # Add plan link
    plan_link = ActiveSubscriptionPlanLink(
        public_id=str(uuid.uuid4()),
        active_subscription_id=subscription.id,
        subscription_option_id=test_subscription_option.id,
        chosen_stripe_price_id="price_test_sc_monthly",
        chosen_plan_type=PlanTypeEnum.SC,
        chosen_billing_period=BillingPeriodEnum.MONTHLY,
        stripe_subscription_item_id=f"si_test_{uuid.uuid4().hex[:8]}",
        quantity=1
    )
    db_session.add(plan_link)
    db_session.commit()
    db_session.refresh(subscription)
    
    return subscription


@pytest.fixture
def yearly_subscription(
    db_session: Session, 
    test_parent_account: Account,
    test_subscription_option: SubscriptionOption
) -> ActiveSubscription:
    """Create a test yearly subscription."""
    subscription = ActiveSubscription(
        public_id=str(uuid.uuid4()),
        parent_account_id=test_parent_account.id,
        stripe_subscription_id=f"sub_test_yearly_{uuid.uuid4().hex[:8]}",
        status=SubscriptionStatusType.ACTIVE,
        current_period_start=datetime.now(UTC),
        current_period_end=datetime.now(UTC) + timedelta(days=365),
        cancel_at_period_end=False
    )
    db_session.add(subscription)
    db_session.flush()
    
    # Add plan link
    plan_link = ActiveSubscriptionPlanLink(
        public_id=str(uuid.uuid4()),
        active_subscription_id=subscription.id,
        subscription_option_id=test_subscription_option.id,
        chosen_stripe_price_id="price_test_sc_yearly",
        chosen_plan_type=PlanTypeEnum.SC,
        chosen_billing_period=BillingPeriodEnum.YEARLY,
        stripe_subscription_item_id=f"si_test_{uuid.uuid4().hex[:8]}",
        quantity=1
    )
    db_session.add(plan_link)
    db_session.commit()
    db_session.refresh(subscription)
    
    return subscription


@pytest.fixture
def paused_monthly_subscription(
    db_session: Session,
    monthly_subscription: ActiveSubscription
) -> tuple[ActiveSubscription, SubscriptionPause]:
    """Create a monthly subscription that is already paused."""
    # Update subscription status
    monthly_subscription.status = SubscriptionStatusType.PAUSED
    
    # Create pause record
    pause = SubscriptionPause(
        public_id=str(uuid.uuid4()),
        active_subscription_id=monthly_subscription.id,
        start_date=datetime.now(UTC),
        end_date=datetime.now(UTC) + timedelta(days=45),  # Aug 31
        paused_seconds=0,
        original_period_end=monthly_subscription.current_period_end,
        new_period_end=monthly_subscription.current_period_end,
        status=SubscriptionPauseStatus.PAUSED
    )
    db_session.add(pause)
    db_session.commit()
    db_session.refresh(monthly_subscription)
    
    return monthly_subscription, pause


@pytest.fixture
def price_eligibility(
    db_session: Session,
    test_parent_account: Account,
    test_price_version: PriceVersion
) -> PriceEligibility:
    """Create a price eligibility for test account."""
    eligibility = PriceEligibility(
        public_id=str(uuid.uuid4()),
        account_id=test_parent_account.id,
        price_version_id=test_price_version.id
    )
    db_session.add(eligibility)
    db_session.commit()
    db_session.refresh(eligibility)
    return eligibility


@pytest.fixture
def multiple_subscriptions(
    db_session: Session,
    test_parent_account: Account,
    test_subscription_option: SubscriptionOption
) -> list[ActiveSubscription]:
    """Create multiple active subscriptions for testing."""
    subscriptions = []
    
    for i in range(2):
        subscription = ActiveSubscription(
            public_id=str(uuid.uuid4()),
            parent_account_id=test_parent_account.id,
            stripe_subscription_id=f"sub_test_multi_{i}_{uuid.uuid4().hex[:8]}",
            status=SubscriptionStatusType.ACTIVE,
            current_period_start=datetime.now(UTC),
            current_period_end=datetime.now(UTC) + timedelta(days=30),
            cancel_at_period_end=False
        )
        db_session.add(subscription)
        db_session.flush()
        
        # Add plan link
        plan_link = ActiveSubscriptionPlanLink(
            public_id=str(uuid.uuid4()),
            active_subscription_id=subscription.id,
            subscription_option_id=test_subscription_option.id,
            chosen_stripe_price_id="price_test_sc_monthly",
            chosen_plan_type=PlanTypeEnum.SC,
            chosen_billing_period=BillingPeriodEnum.MONTHLY,
            stripe_subscription_item_id=f"si_test_multi_{i}_{uuid.uuid4().hex[:8]}",
            quantity=1
        )
        db_session.add(plan_link)
        subscriptions.append(subscription)
    
    db_session.commit()
    for sub in subscriptions:
        db_session.refresh(sub)
    
    return subscriptions


@pytest.fixture
def auth_headers(test_parent_account: Account, monthly_subscription: ActiveSubscription) -> dict:
    """Create auth headers for test requests."""
    # Generate a proper JWT token with subscription info
    token = create_test_parent_auth_token(
        test_parent_account,
        active_subscriptions=[monthly_subscription.public_id]
    )
    return {
        "x-auth-token": token
    }


@pytest.fixture
def paused_subscription(paused_monthly_subscription):
    """Alias for paused_monthly_subscription for backward compatibility."""
    subscription, pause = paused_monthly_subscription
    return subscription