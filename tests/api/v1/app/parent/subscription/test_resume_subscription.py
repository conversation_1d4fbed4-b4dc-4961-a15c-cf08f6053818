"""
Integration tests for resume subscription route.
"""
import pytest
from datetime import datetime, UTC, timedelta
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from db.models.subscription import SubscriptionStatusType, SubscriptionPauseStatus


class TestResumeSubscriptionRoute:
    """Integration tests for POST /api/v1/app/parent/subscription/{subscription_public_id}/resume"""
    
    def test_resume_monthly_subscription_success(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account,
        paused_monthly_subscription,
        auth_headers,
        mock_stripe_subscription_modify,
        mocker
    ):
        """Test successful monthly subscription resume via API."""
        subscription, pause_record = paused_monthly_subscription
        
        # Mock current time
        mock_now = datetime(2025, 7, 25, 12, 0, 0, tzinfo=UTC)
        mocker.patch("api.v1.app.parent.subscription.services.resume_subscription_service.datetime")
        mocker.patch("api.v1.app.parent.subscription.services.resume_subscription_service.datetime.now", return_value=mock_now)
        
        # Make request
        response = client.post(
            "/api/v1/app/parent/subscription/resume",
            json={"subscription_public_id": subscription.public_id},
            headers=auth_headers
        )
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["subscription_status"] == "active"
        assert data["message"] == "Your subscription has been resumed successfully."
        assert data["resumed_at"] is not None
        
        # Verify Stripe was called
        mock_stripe_subscription_modify.assert_called_once()
        call_args = mock_stripe_subscription_modify.call_args
        assert call_args[0][0] == subscription.stripe_subscription_id
        assert call_args[1]['pause_collection'] == ""
        
        # Verify database changes
        db_session.refresh(subscription)
        db_session.refresh(pause_record)
        assert subscription.status == SubscriptionStatusType.ACTIVE
        assert pause_record.status == SubscriptionPauseStatus.RESUMED
        assert pause_record.resumed_at == mock_now
    
    def test_resume_yearly_subscription_with_credit(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account,
        yearly_subscription,
        auth_headers,
        mock_stripe_subscription_modify,
        mocker
    ):
        """Test resuming yearly subscription calculates prorated credit."""
        # Create a pause record
        pause_start = datetime(2025, 7, 15, tzinfo=UTC)
        original_period_end = datetime(2025, 12, 31, tzinfo=UTC)
        
        from db.models import SubscriptionPause
        pause_record = SubscriptionPause(
            public_id="pause_yearly_123",
            active_subscription_id=yearly_subscription.id,
            start_date=pause_start,
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=original_period_end,
            new_period_end=original_period_end + timedelta(days=47),
            status=SubscriptionPauseStatus.PAUSED
        )
        db_session.add(pause_record)
        db_session.commit()
        
        # Mock current time (10 days after pause start)
        mock_now = datetime(2025, 7, 25, 12, 0, 0, tzinfo=UTC)
        mocker.patch("api.v1.app.parent.subscription.services.resume_subscription_service.datetime")
        mocker.patch("api.v1.app.parent.subscription.services.resume_subscription_service.datetime.now", return_value=mock_now)
        
        # Make request
        response = client.post(
            "/api/v1/app/parent/subscription/resume",
            json={"subscription_public_id": yearly_subscription.public_id},
            headers=auth_headers
        )
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["subscription_status"] == "active"
        assert "credited 10 days" in data["message"]
        assert "Your next billing date is" in data["message"]
        
        # Verify Stripe was called with trial_end adjustment
        mock_stripe_subscription_modify.assert_called_once()
        call_args = mock_stripe_subscription_modify.call_args
        assert 'trial_end' in call_args[1]
        assert call_args[1]['proration_behavior'] == 'none'
        
        # Verify pause record updated
        db_session.refresh(pause_record)
        assert pause_record.status == SubscriptionPauseStatus.RESUMED
        assert pause_record.resumed_at == mock_now
        # New period end should be original + actual pause duration in seconds
        actual_pause_duration = (mock_now - pause_start).total_seconds()
        expected_new_end = original_period_end + timedelta(seconds=actual_pause_duration)
        assert abs((pause_record.new_period_end - expected_new_end).total_seconds()) < 60
    
    def test_resume_non_paused_subscription(
        self,
        client: TestClient,
        test_parent_account,
        monthly_subscription,
        auth_headers
    ):
        """Test attempting to resume a subscription that isn't paused."""
        # No pause record exists
        
        response = client.post(
            "/api/v1/app/parent/subscription/resume",
            json={"subscription_public_id": monthly_subscription.public_id},
            headers=auth_headers
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "not currently paused" in data["message"]
    
    def test_resume_scheduled_pause_not_allowed(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account,
        monthly_subscription,
        auth_headers
    ):
        """Test that scheduled (future) pauses cannot be resumed."""
        # Create a scheduled pause
        from db.models import SubscriptionPause
        scheduled_pause = SubscriptionPause(
            public_id="scheduled_pause_123",
            active_subscription_id=monthly_subscription.id,
            start_date=datetime(2025, 7, 15, tzinfo=UTC),  # Future
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=monthly_subscription.current_period_end,
            new_period_end=monthly_subscription.current_period_end,
            status=SubscriptionPauseStatus.SCHEDULED,
            qstash_message_id="msg_123"
        )
        db_session.add(scheduled_pause)
        db_session.commit()
        
        response = client.post(
            "/api/v1/app/parent/subscription/resume",
            json={"subscription_public_id": monthly_subscription.public_id},
            headers=auth_headers
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "not currently paused" in data["message"]
    
    def test_resume_subscription_unauthorized(
        self,
        client: TestClient,
        paused_monthly_subscription
    ):
        """Test resuming subscription without authentication."""
        subscription, _ = paused_monthly_subscription
        
        response = client.post(
            "/api/v1/app/parent/subscription/resume",
            json={"subscription_public_id": subscription.public_id}
        )
        
        assert response.status_code == 401
    
    def test_resume_subscription_wrong_parent(
        self,
        client: TestClient,
        db_session: Session,
        paused_monthly_subscription,
        auth_headers
    ):
        """Test resuming subscription belonging to different parent."""
        subscription, _ = paused_monthly_subscription
        
        # Create another parent account
        from db.models import Account
        import uuid
        other_parent = Account(
            email="<EMAIL>",
            public_id=str(uuid.uuid4()),
            is_verified=True,
            stripe_customer_id="cus_test_other_parent"
        )
        db_session.add(other_parent)
        db_session.commit()  # Commit to get the ID
        
        # Assign subscription to other parent
        subscription.parent_account_id = other_parent.id
        db_session.commit()
        
        # Try to resume with original parent's auth
        response = client.post(
            "/api/v1/app/parent/subscription/resume",
            json={"subscription_public_id": subscription.public_id},
            headers=auth_headers
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "subscription not found" in data["message"].lower()
    
    def test_resume_subscription_stripe_error_handling(
        self,
        client: TestClient,
        test_parent_account,
        paused_monthly_subscription,
        auth_headers,
        mocker
    ):
        """Test error handling when Stripe call fails."""
        subscription, _ = paused_monthly_subscription
        
        # Mock Stripe to raise an error
        import stripe
        mocker.patch(
            "stripe.Subscription.modify",
            side_effect=stripe.error.StripeError("Stripe API error")
        )
        
        response = client.post(
            "/api/v1/app/parent/subscription/resume",
            json={"subscription_public_id": subscription.public_id},
            headers=auth_headers
        )
        
        assert response.status_code == 503  # External service errors return 503
        data = response.json()
        assert "failed" in data["message"].lower() or "error" in data["message"].lower()