"""
Tests for customer.subscription.deleted webhook handling with PriceEligibility deletion.
"""
import pytest
import json
import stripe
from datetime import datetime, UTC, timedelta
from unittest.mock import patch, MagicMock
from sqlalchemy.orm import Session
from sqlalchemy import select
from fastapi.testclient import TestClient

from db.models import Account, ActiveSubscription, PriceEligibility
from db.models.subscription import SubscriptionStatusType
from api.v1.app.parent.subscription.services.webhook_service import (
    handle_customer_subscription_deleted,
    SERVICE_EVENT_HANDLER_MAP
)


class TestWebhookSubscriptionDeleted:
    """Test cases for customer.subscription.deleted webhook handling."""
    
    @pytest.fixture
    def stripe_webhook_event_deleted(self, monthly_subscription: ActiveSubscription):
        """Create a mock Stripe webhook event for subscription deletion."""
        return {
            "id": "evt_test_deleted",
            "object": "event",
            "api_version": "2023-10-16",
            "created": int(datetime.now(UTC).timestamp()),
            "data": {
                "object": {
                    "id": monthly_subscription.stripe_subscription_id,
                    "object": "subscription",
                    "customer": monthly_subscription.parent_account.stripe_customer_id,
                    "status": "canceled",
                    "current_period_start": int(datetime.now(UTC).timestamp()),
                    "current_period_end": int((datetime.now(UTC) + timedelta(days=30)).timestamp()),
                    "cancel_at_period_end": False,
                }
            },
            "type": "customer.subscription.deleted",
            "livemode": False,
            "pending_webhooks": 1,
            "request": {
                "id": None,
                "idempotency_key": None
            }
        }
    
    def test_subscription_deleted_removes_price_eligibility(
        self,
        db_session: Session,
        test_parent_account: Account,
        test_price_version,
        price_eligibility,
        monthly_subscription: ActiveSubscription,
        stripe_webhook_event_deleted: dict
    ):
        """Test that deleting the last subscription removes price eligibility."""
        # Verify initial state
        assert monthly_subscription.status == SubscriptionStatusType.ACTIVE
        eligibilities = db_session.execute(
            select(PriceEligibility).where(PriceEligibility.account_id == test_parent_account.id)
        ).scalars().all()
        assert len(eligibilities) == 1
        
        # Call webhook handler
        handle_customer_subscription_deleted(stripe_webhook_event_deleted, db_session)
        
        # Verify subscription is canceled
        db_session.refresh(monthly_subscription)
        assert monthly_subscription.status == SubscriptionStatusType.CANCELED
        
        # Verify price eligibility is deleted
        eligibilities_after = db_session.execute(
            select(PriceEligibility).where(PriceEligibility.account_id == test_parent_account.id)
        ).scalars().all()
        assert len(eligibilities_after) == 0
    
    def test_subscription_deleted_keeps_eligibility_with_other_active_subs(
        self,
        db_session: Session,
        test_parent_account: Account,
        test_price_version,
        price_eligibility,
        multiple_subscriptions: list[ActiveSubscription],
        mocker
    ):
        """Test that price eligibility is kept when other active subscriptions exist."""
        # Setup: two active subscriptions
        sub_to_delete, remaining_sub = multiple_subscriptions
        
        # Create webhook event for first subscription
        webhook_event = {
            "id": "evt_test_deleted_multi",
            "object": "event",
            "data": {
                "object": {
                    "id": sub_to_delete.stripe_subscription_id,
                    "object": "subscription",
                    "customer": test_parent_account.stripe_customer_id,
                    "status": "canceled",
                    "current_period_start": int(datetime.now(UTC).timestamp()),
                    "current_period_end": int((datetime.now(UTC) + timedelta(days=30)).timestamp()),
                    "cancel_at_period_end": False,
                }
            },
            "type": "customer.subscription.deleted"
        }
        
        # Verify initial state
        eligibilities_before = db_session.execute(
            select(PriceEligibility).where(PriceEligibility.account_id == test_parent_account.id)
        ).scalars().all()
        assert len(eligibilities_before) == 1
        
        # Call webhook handler
        handle_customer_subscription_deleted(webhook_event, db_session)
        
        # Verify first subscription is canceled
        db_session.refresh(sub_to_delete)
        assert sub_to_delete.status == SubscriptionStatusType.CANCELED
        
        # Verify second subscription is still active
        db_session.refresh(remaining_sub)
        assert remaining_sub.status == SubscriptionStatusType.ACTIVE
        
        # Verify price eligibility is NOT deleted
        eligibilities_after = db_session.execute(
            select(PriceEligibility).where(PriceEligibility.account_id == test_parent_account.id)
        ).scalars().all()
        assert len(eligibilities_after) == 1
    
    def test_subscription_deleted_no_price_eligibility(
        self,
        db_session: Session,
        test_parent_account: Account,
        monthly_subscription: ActiveSubscription,
        stripe_webhook_event_deleted: dict
    ):
        """Test that webhook handles cases where no price eligibility exists."""
        # Verify no price eligibility exists
        eligibilities = db_session.execute(
            select(PriceEligibility).where(PriceEligibility.account_id == test_parent_account.id)
        ).scalars().all()
        assert len(eligibilities) == 0
        
        # Call webhook handler - should not raise any errors
        handle_customer_subscription_deleted(stripe_webhook_event_deleted, db_session)
        
        # Verify subscription is canceled
        db_session.refresh(monthly_subscription)
        assert monthly_subscription.status == SubscriptionStatusType.CANCELED
    
    def test_subscription_deleted_no_active_subscription_in_db(
        self,
        db_session: Session,
        test_parent_account: Account,
        mocker
    ):
        """Test webhook handles case where subscription doesn't exist in database."""
        # Create webhook event for non-existent subscription
        webhook_event = {
            "id": "evt_test_deleted_missing",
            "object": "event",
            "data": {
                "object": {
                    "id": "sub_nonexistent",
                    "object": "subscription",
                    "customer": test_parent_account.stripe_customer_id,
                    "status": "canceled",
                }
            },
            "type": "customer.subscription.deleted"
        }
        
        # Mock logger to verify warning is logged
        mock_logger = mocker.patch("api.v1.app.parent.subscription.services.webhook_service.logger")
        
        # Call webhook handler - should handle gracefully
        handle_customer_subscription_deleted(webhook_event, db_session)
        
        # Verify warning was logged
        mock_logger.warning.assert_called()
    
    def test_webhook_endpoint_routes_deleted_event(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account: Account,
        price_eligibility,
        monthly_subscription: ActiveSubscription,
        stripe_webhook_event_deleted: dict,
        mocker
    ):
        """Test the webhook endpoint properly routes deletion events."""
        # Mock Stripe webhook signature verification
        mocker.patch(
            "stripe.Webhook.construct_event",
            return_value=stripe_webhook_event_deleted
        )
        
        # Mock admin notification
        mocker.patch(
            "api.v1.app.parent.subscription.services.webhook_service.send_admin_notification_email"
        )
        
        # Send webhook request
        response = client.post(
            "/api/v1/app/parent/subscription/webhook",
            content=json.dumps(stripe_webhook_event_deleted),
            headers={
                "stripe-signature": "test_signature",
                "content-type": "application/json"
            }
        )
        
        # Verify response
        assert response.status_code == 200
        assert response.json() == {"status": "success"}
        
        # Verify subscription is canceled
        db_session.refresh(monthly_subscription)
        assert monthly_subscription.status == SubscriptionStatusType.CANCELED
        
        # Verify price eligibility is deleted
        eligibilities = db_session.execute(
            select(PriceEligibility).where(PriceEligibility.account_id == test_parent_account.id)
        ).scalars().all()
        assert len(eligibilities) == 0
    
    def test_event_handler_map_includes_deleted(self):
        """Test that the event handler map includes customer.subscription.deleted."""
        assert "customer.subscription.deleted" in SERVICE_EVENT_HANDLER_MAP
        assert SERVICE_EVENT_HANDLER_MAP["customer.subscription.deleted"] == handle_customer_subscription_deleted