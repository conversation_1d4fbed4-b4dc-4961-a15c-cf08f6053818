"""
Tests for internal activate pause route (called by QStash).
"""
import pytest
from datetime import datetime, UTC, timedelta
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
import json
import hmac
import hashlib
import base64

from db.models.subscription import SubscriptionStatusType, SubscriptionPauseStatus, BillingPeriodEnum
from tests.mocks.stripe_subscription_mocks import create_mock_stripe_subscription
from main import app
from api.v1.tasks.internal.routes.activate_pause import verify_qstash_signature
from tests.mocks.webhook_auth_mocks import mock_verify_qstash_signature

# Import subscription fixtures from conftest
from tests.api.v1.app.parent.subscription.conftest import (
    monthly_subscription,
    yearly_subscription,
    test_parent_account,
    test_subscription_option,
    test_price_version,
    test_year
)


class TestActivatePauseInternal:
    """Tests for POST /api/v1/tasks/internal/subscription/{subscription_id}/pause/{pause_id}/activate"""
    
    @pytest.fixture(autouse=True)
    def setup_pause_window(self, mocker):
        """Mock pause window settings."""
        mocker.patch("core.config.settings.settings.SUMMER_PAUSE_END_UTC", "2025-08-31T23:59:59Z")
    
    @pytest.fixture
    def qstash_client(self, client: TestClient, db_session):
        """Test client with QStash signature verification mocked."""
        # Override QStash signature verification dependency
        app.dependency_overrides[verify_qstash_signature] = mock_verify_qstash_signature
        
        yield client
        
        # Clean up override
        app.dependency_overrides.pop(verify_qstash_signature, None)
    
    @pytest.fixture
    def qstash_headers(self):
        """Create valid QStash headers."""
        return {
            "Upstash-Signature": "test_signature",
            "Content-Type": "application/json"
        }
    
    def test_activate_pause_monthly_success(
        self,
        qstash_client: TestClient,
        db_session: Session,
        monthly_subscription,
        qstash_headers,
        mock_stripe_subscription_retrieve,
        mock_stripe_subscription_modify,
        mocker
    ):
        """Test successful pause activation for monthly subscription."""
        # Create a scheduled pause
        from db.models import SubscriptionPause
        scheduled_pause = SubscriptionPause(
            public_id="pause_scheduled_123",
            active_subscription_id=monthly_subscription.id,
            start_date=datetime(2025, 7, 15, tzinfo=UTC),
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=monthly_subscription.current_period_end,
            new_period_end=monthly_subscription.current_period_end,
            status=SubscriptionPauseStatus.SCHEDULED,
            qstash_message_id="msg_123"
        )
        db_session.add(scheduled_pause)
        db_session.commit()
        
        # Mock current time
        mock_now = datetime(2025, 7, 15, 0, 0, 0, tzinfo=UTC)
        mocker.patch("api.v1.app.parent.subscription.services.activate_pause_service.datetime")
        mocker.patch("api.v1.app.parent.subscription.services.activate_pause_service.datetime.now", return_value=mock_now)
        
        # Make request with JSON body
        response = qstash_client.post(
            "/api/v1/tasks/internal/activate-pause",
            headers=qstash_headers,
            json={
                "active_subscription_id": monthly_subscription.id,
                "subscription_pause_public_id": scheduled_pause.public_id,
                "action": "activate_pause"
            }
        )
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "Pause activated successfully" in data["message"]
        
        # Verify Stripe was called correctly
        mock_stripe_subscription_modify.assert_called_once()
        call_args = mock_stripe_subscription_modify.call_args
        assert 'pause_collection' in call_args[1]
        assert call_args[1]['pause_collection']['behavior'] == 'mark_uncollectible'
        
        # Verify database updates
        db_session.refresh(monthly_subscription)
        db_session.refresh(scheduled_pause)
        assert monthly_subscription.status == SubscriptionStatusType.PAUSED
        assert scheduled_pause.status == SubscriptionPauseStatus.PAUSED
    
    def test_activate_pause_yearly_success(
        self,
        qstash_client: TestClient,
        db_session: Session,
        yearly_subscription,
        qstash_headers,
        mock_stripe_subscription_retrieve,
        mock_stripe_subscription_modify,
        mocker
    ):
        """Test successful pause activation for yearly subscription."""
        # Create a scheduled pause
        from db.models import SubscriptionPause
        scheduled_pause = SubscriptionPause(
            public_id="pause_scheduled_yearly",
            active_subscription_id=yearly_subscription.id,
            start_date=datetime(2025, 7, 15, tzinfo=UTC),
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=yearly_subscription.current_period_end,
            new_period_end=yearly_subscription.current_period_end,
            status=SubscriptionPauseStatus.SCHEDULED
        )
        db_session.add(scheduled_pause)
        db_session.commit()
        
        # Mock current time
        mock_now = datetime(2025, 7, 15, 0, 0, 0, tzinfo=UTC)
        # Patch datetime more carefully to preserve functionality
        datetime_mock = mocker.patch("api.v1.app.parent.subscription.services.activate_pause_service.datetime")
        datetime_mock.now.return_value = mock_now
        datetime_mock.fromtimestamp.side_effect = lambda ts, tz=None: datetime.fromtimestamp(ts, tz=tz)
        datetime_mock.side_effect = lambda *args, **kwargs: datetime(*args, **kwargs)
        
        mock_stripe_sub = create_mock_stripe_subscription(
            subscription_id=yearly_subscription.stripe_subscription_id,
            current_period_end=datetime(2025, 12, 31, tzinfo=UTC),
            billing_period=BillingPeriodEnum.YEARLY
        )
        mock_stripe_subscription_retrieve.return_value = mock_stripe_sub
        
        # Ensure modify returns a properly structured subscription with new_period_end
        def mock_modify(sub_id, **kwargs):
            modified_sub = create_mock_stripe_subscription(
                subscription_id=sub_id,
                current_period_end=datetime(2025, 12, 31, tzinfo=UTC),
                billing_period=BillingPeriodEnum.YEARLY
            )
            if 'trial_end' in kwargs:
                modified_sub.trial_end = kwargs['trial_end']
                # Add the new_period_end attribute that the service expects
                modified_sub.new_period_end = datetime.fromtimestamp(kwargs['trial_end'], tz=UTC)
            # Ensure dictionary access works
            modified_sub.__getitem__ = lambda self, key: getattr(self, key)
            modified_sub.get = lambda self, key, default=None: getattr(self, key, default)
            return modified_sub
            
        mock_stripe_subscription_modify.side_effect = mock_modify
        
        # Make request with JSON body
        response = qstash_client.post(
            "/api/v1/tasks/internal/activate-pause",
            headers=qstash_headers,
            json={
                "active_subscription_id": yearly_subscription.id,
                "subscription_pause_public_id": scheduled_pause.public_id,
                "action": "activate_pause"
            }
        )
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "Pause activated successfully" in data["message"]
        
        # Verify Stripe was called with trial_end
        mock_stripe_subscription_modify.assert_called_once()
        call_args = mock_stripe_subscription_modify.call_args
        assert 'trial_end' in call_args[1]
        assert 'pause_collection' not in call_args[1]
        
        # Verify subscription remains ACTIVE
        db_session.refresh(yearly_subscription)
        assert yearly_subscription.status == SubscriptionStatusType.ACTIVE
    
    def test_activate_pause_idempotency(
        self,
        qstash_client: TestClient,
        db_session: Session,
        monthly_subscription,
        qstash_headers,
        mock_stripe_subscription_modify
    ):
        """Test that activating an already paused subscription is idempotent."""
        # Create an already paused subscription
        from db.models import SubscriptionPause
        paused_pause = SubscriptionPause(
            public_id="pause_already_paused",
            active_subscription_id=monthly_subscription.id,
            start_date=datetime(2025, 7, 15, tzinfo=UTC),
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=monthly_subscription.current_period_end,
            new_period_end=monthly_subscription.current_period_end,
            status=SubscriptionPauseStatus.PAUSED  # Already paused
        )
        db_session.add(paused_pause)
        db_session.commit()
        
        # Make request with JSON body
        response = qstash_client.post(
            "/api/v1/tasks/internal/activate-pause",
            headers=qstash_headers,
            json={
                "active_subscription_id": monthly_subscription.id,
                "subscription_pause_public_id": paused_pause.public_id,
                "action": "activate_pause"
            }
        )
        
        # Should succeed without calling Stripe
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "Pause already activated" in data["message"]
        mock_stripe_subscription_modify.assert_not_called()
    
    def test_activate_pause_invalid_signature(
        self,
        client: TestClient,
        monthly_subscription,
        mocker
    ):
        """Test that invalid QStash signature is rejected."""
        # No dependency override, so signature verification will fail
        response = client.post(
            "/api/v1/tasks/internal/activate-pause",
            headers={
                "Upstash-Signature": "invalid_signature",
                "Content-Type": "application/json"
            },
            json={
                "active_subscription_id": monthly_subscription.id,
                "subscription_pause_public_id": "some_pause_id",
                "action": "activate_pause"
            }
        )
        
        assert response.status_code == 401
        data = response.json()
        assert "Invalid signature" in data["detail"]
    
    def test_activate_pause_missing_subscription(
        self,
        qstash_client: TestClient,
        db_session: Session,
        qstash_headers
    ):
        """Test activating pause for non-existent subscription."""
        # Make request with non-existent subscription ID
        response = qstash_client.post(
            "/api/v1/tasks/internal/activate-pause",
            headers=qstash_headers,
            json={
                "active_subscription_id": 99999,
                "subscription_pause_public_id": "pause_orphan",
                "action": "activate_pause"
            }
        )
        
        # The route returns success to prevent QStash retries for non-existent pause
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "Pause record not found" in data["message"]
    
    def test_activate_pause_missing_pause_record(
        self,
        qstash_client: TestClient,
        monthly_subscription,
        qstash_headers
    ):
        """Test activating non-existent pause record."""
        response = qstash_client.post(
            "/api/v1/tasks/internal/activate-pause",
            headers=qstash_headers,
            json={
                "active_subscription_id": monthly_subscription.id,
                "subscription_pause_public_id": "non_existent_pause",
                "action": "activate_pause"
            }
        )
        
        # The route returns success to prevent QStash retries for non-existent pause
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "Pause record not found" in data["message"]
    
    def test_activate_pause_mismatch(
        self,
        qstash_client: TestClient,
        db_session: Session,
        monthly_subscription,
        yearly_subscription,
        qstash_headers
    ):
        """Test activating pause with mismatched subscription ID."""
        # Create pause for monthly but try to activate with yearly ID
        from db.models import SubscriptionPause
        pause = SubscriptionPause(
            public_id="pause_mismatch",
            active_subscription_id=monthly_subscription.id,  # For monthly
            start_date=datetime(2025, 7, 15, tzinfo=UTC),
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=monthly_subscription.current_period_end,
            new_period_end=monthly_subscription.current_period_end,
            status=SubscriptionPauseStatus.SCHEDULED
        )
        db_session.add(pause)
        db_session.commit()
        
        # Try to activate with wrong subscription ID
        response = qstash_client.post(
            "/api/v1/tasks/internal/activate-pause",
            headers=qstash_headers,
            json={
                "active_subscription_id": yearly_subscription.id,
                "subscription_pause_public_id": pause.public_id,
                "action": "activate_pause"
            }
        )
        
        # The route will return success because it only checks if pause exists for the given IDs
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "Pause record not found" in data["message"]
    
    def test_activate_pause_retry_behavior(
        self,
        qstash_client: TestClient,
        db_session: Session,
        monthly_subscription,
        qstash_headers,
        mock_stripe_subscription_modify,
        mocker
    ):
        """Test that QStash retries are handled gracefully."""
        # Create a scheduled pause
        from db.models import SubscriptionPause
        scheduled_pause = SubscriptionPause(
            public_id="pause_retry_test",
            active_subscription_id=monthly_subscription.id,
            start_date=datetime(2025, 7, 15, tzinfo=UTC),
            end_date=datetime(2025, 8, 31, tzinfo=UTC),
            paused_seconds=0,
            original_period_end=monthly_subscription.current_period_end,
            new_period_end=monthly_subscription.current_period_end,
            status=SubscriptionPauseStatus.SCHEDULED,
            qstash_message_id="msg_retry"
        )
        db_session.add(scheduled_pause)
        db_session.commit()
        
        # First request - simulate Stripe failure
        mock_stripe_subscription_modify.side_effect = Exception("Stripe error")
        
        response1 = qstash_client.post(
            "/api/v1/tasks/internal/activate-pause",
            headers=qstash_headers,
            json={
                "active_subscription_id": monthly_subscription.id,
                "subscription_pause_public_id": scheduled_pause.public_id,
                "action": "activate_pause"
            }
        )
        
        # Should return error for retry
        assert response1.status_code == 500
        
        # Verify pause status unchanged
        db_session.refresh(scheduled_pause)
        assert scheduled_pause.status == SubscriptionPauseStatus.SCHEDULED
        
        # Second request - simulate success
        mock_stripe_subscription_modify.side_effect = None
        mock_stripe_subscription_modify.reset_mock()
        
        response2 = qstash_client.post(
            "/api/v1/tasks/internal/activate-pause",
            headers=qstash_headers,
            json={
                "active_subscription_id": monthly_subscription.id,
                "subscription_pause_public_id": scheduled_pause.public_id,
                "action": "activate_pause"
            }
        )
        
        # Should succeed
        assert response2.status_code == 200
        
        # Verify pause is now active
        db_session.refresh(scheduled_pause)
        assert scheduled_pause.status == SubscriptionPauseStatus.PAUSED