import pytest
from datetime import datetime, timedelta, UTC
import jwt

from api.v1.editorial.auth.utils.jwt_utils import (
    create_editor_access_token,
    create_editor_refresh_token,
    decode_editor_token,
    verify_token_type
)
from core.config import settings

class TestJWTTokens:
    """Test JWT token generation and validation"""
    
    def test_jwt_token_generation(self):
        """Test token contains required claims"""
        editor_id = 123
        email = "<EMAIL>"
        role = "editor"
        
        token = create_editor_access_token(
            editor_id=editor_id,
            email=email,
            role=role
        )
        
        # Decode and verify
        decoded = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        
        assert decoded["editor_id"] == editor_id
        assert decoded["email"] == email
        assert decoded["role"] == role
        assert decoded["type"] == "editor_access"
        assert "exp" in decoded
        assert "iat" in decoded
    
    def test_jwt_token_custom_expiration(self):
        """Test token with custom expiration"""
        custom_delta = timedelta(hours=1)
        
        token = create_editor_access_token(
            editor_id=1,
            email="<EMAIL>",
            role="editor",
            expires_delta=custom_delta
        )
        
        decoded = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        
        exp_time = datetime.fromtimestamp(decoded["exp"], UTC)
        iat_time = datetime.fromtimestamp(decoded["iat"], UTC)
        duration = exp_time - iat_time
        
        # Should be approximately 1 hour
        assert 3590 <= duration.total_seconds() <= 3610
    
    def test_jwt_refresh_token_generation(self):
        """Test refresh token generation"""
        editor_id = 456
        
        token = create_editor_refresh_token(editor_id=editor_id)
        
        decoded = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        
        assert decoded["editor_id"] == editor_id
        assert decoded["type"] == "editor_refresh"
        assert "exp" in decoded
        assert "iat" in decoded
        
        # Verify default 30-day expiration
        exp_time = datetime.fromtimestamp(decoded["exp"], UTC)
        iat_time = datetime.fromtimestamp(decoded["iat"], UTC)
        duration = exp_time - iat_time
        
        # Should be approximately 30 days
        assert 29.9 <= duration.days <= 30.1
    
    def test_jwt_token_validation(self):
        """Test valid token accepted"""
        token = create_editor_access_token(
            editor_id=1,
            email="<EMAIL>",
            role="editor"
        )
        
        decoded = decode_editor_token(token)
        
        assert decoded is not None
        assert decoded["editor_id"] == 1
        assert decoded["email"] == "<EMAIL>"
    
    def test_expired_token_rejected(self):
        """Test expired token rejected"""
        # Create already expired token
        token = create_editor_access_token(
            editor_id=1,
            email="<EMAIL>",
            role="editor",
            expires_delta=timedelta(seconds=-1)
        )
        
        decoded = decode_editor_token(token)
        assert decoded is None
    
    def test_tampered_token_rejected(self):
        """Test tampered token rejected"""
        token = create_editor_access_token(
            editor_id=1,
            email="<EMAIL>",
            role="editor"
        )
        
        # Tamper with token
        tampered_token = token[:-5] + "xxxxx"
        
        decoded = decode_editor_token(tampered_token)
        assert decoded is None
    
    def test_invalid_token_rejected(self):
        """Test invalid token format rejected"""
        invalid_tokens = [
            "not.a.token",
            "invalid_jwt_format",
            "",
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9"  # Incomplete JWT
        ]
        
        for invalid_token in invalid_tokens:
            decoded = decode_editor_token(invalid_token)
            assert decoded is None
    
    def test_wrong_token_type_rejected(self):
        """Test token with wrong type rejected"""
        # Create a token with wrong type
        payload = {
            "editor_id": 1,
            "type": "wrong_type",
            "exp": datetime.now(UTC) + timedelta(hours=1),
            "iat": datetime.now(UTC)
        }
        
        token = jwt.encode(
            payload,
            settings.SECRET_KEY,
            algorithm=settings.JWT_ALGORITHM
        )
        
        # Should be rejected as it's not editor_access or editor_refresh
        decoded = decode_editor_token(token)
        assert decoded is None
    
    def test_verify_token_type(self):
        """Test token type verification"""
        access_token = create_editor_access_token(
            editor_id=1,
            email="<EMAIL>",
            role="editor"
        )
        
        refresh_token = create_editor_refresh_token(editor_id=1)
        
        # Verify correct types
        assert verify_token_type(access_token, "editor_access") is True
        assert verify_token_type(refresh_token, "editor_refresh") is True
        
        # Verify incorrect types
        assert verify_token_type(access_token, "editor_refresh") is False
        assert verify_token_type(refresh_token, "editor_access") is False
        assert verify_token_type("invalid_token", "editor_access") is False
    
    def test_token_with_different_secret_rejected(self):
        """Test token signed with different secret is rejected"""
        # Create token with different secret
        payload = {
            "editor_id": 1,
            "email": "<EMAIL>",
            "role": "editor",
            "type": "editor_access",
            "exp": datetime.now(UTC) + timedelta(hours=1),
            "iat": datetime.now(UTC)
        }
        
        wrong_token = jwt.encode(
            payload,
            "wrong_secret_key",
            algorithm=settings.JWT_ALGORITHM
        )
        
        decoded = decode_editor_token(wrong_token)
        assert decoded is None