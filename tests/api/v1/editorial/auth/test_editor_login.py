import pytest
from fastapi.testclient import <PERSON><PERSON>lient
from sqlalchemy.orm import Session
from datetime import datetime, UTC
import jwt

from db.models import EditorAccount, EditorRole, EditorScope, Subject
from tests.fixtures.editorial_fixtures import EditorFixtures
from api.v1.editorial.auth.utils.password_utils import hash_password
from core.config import settings

class TestEditorLogin:
    """Test editor login functionality"""
    
    def test_editor_login_success(self, client: TestClient, db_session: Session):
        """Test valid credentials return JWT token"""
        # Create test editor
        password = "TestPassword123!"
        editor = EditorFixtures.create_editor_account(
            db_session,
            email="<EMAIL>",
            password=password,
            role=EditorRole.EDITOR
        )
        
        # Create some scopes for the editor
        subject = Subject(name="Test Subject", year_id=1)
        db_session.add(subject)
        db_session.commit()
        
        scope = EditorScope(editor_id=editor.id, subject_id=subject.id)
        db_session.add(scope)
        db_session.commit()
        
        # Attempt login
        response = client.post(
            "/api/v1/editorial/auth/login",
            json={
                "email": "<EMAIL>",
                "password": password
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert "access_token" in data
        assert "refresh_token" in data
        assert "token_type" in data
        assert data["token_type"] == "Bearer"
        assert "expires_in" in data
        assert "editor" in data
        assert "scopes" in data
        
        # Verify editor info
        assert data["editor"]["email"] == "<EMAIL>"
        assert data["editor"]["role"] == "EDITOR"
        assert data["editor"]["is_active"] is True
        
        # Verify scopes
        assert len(data["scopes"]) == 1
        assert data["scopes"][0]["subject_name"] == "Test Subject"
        
        # Verify JWT token
        decoded = jwt.decode(
            data["access_token"],
            settings.SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        assert decoded["editor_id"] == editor.id
        assert decoded["email"] == editor.email
        assert decoded["role"] == "EDITOR"
    
    def test_admin_login_success(self, client: TestClient, db_session: Session):
        """Test admin login returns proper role"""
        password = "AdminPassword123!"
        admin = EditorFixtures.create_editor_account(
            db_session,
            email="<EMAIL>",
            password=password,
            role=EditorRole.ADMIN
        )
        
        response = client.post(
            "/api/v1/editorial/auth/login",
            json={
                "email": "<EMAIL>",
                "password": password
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["editor"]["role"] == "ADMIN"
        
        # Verify JWT contains admin role
        decoded = jwt.decode(
            data["access_token"],
            settings.SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        assert decoded["role"] == "ADMIN"
    
    def test_editor_login_invalid_credentials(self, client: TestClient, db_session: Session):
        """Test wrong password returns 401"""
        editor = EditorFixtures.create_editor_account(
            db_session,
            email="<EMAIL>",
            password="CorrectPassword123!"
        )
        
        # Wrong password
        response = client.post(
            "/api/v1/editorial/auth/login",
            json={
                "email": "<EMAIL>",
                "password": "WrongPassword123!"
            }
        )
        
        assert response.status_code == 401
        assert response.json()["detail"] == "Invalid email or password"
    
    def test_editor_login_nonexistent_email(self, client: TestClient, db_session: Session):
        """Test non-existent email returns 401"""
        response = client.post(
            "/api/v1/editorial/auth/login",
            json={
                "email": "<EMAIL>",
                "password": "Password123!"
            }
        )
        
        assert response.status_code == 401
        assert response.json()["detail"] == "Invalid email or password"
    
    def test_editor_login_inactive_account(self, client: TestClient, db_session: Session):
        """Test inactive editor cannot login"""
        password = "TestPassword123!"
        editor = EditorFixtures.create_editor_account(
            db_session,
            email="<EMAIL>",
            password=password,
            is_active=False
        )
        
        response = client.post(
            "/api/v1/editorial/auth/login",
            json={
                "email": "<EMAIL>",
                "password": password
            }
        )
        
        assert response.status_code == 401
        assert response.json()["detail"] == "Account is inactive"
    
    def test_editor_login_case_insensitive_email(self, client: TestClient, db_session: Session):
        """Test email is case-insensitive"""
        password = "TestPassword123!"
        editor = EditorFixtures.create_editor_account(
            db_session,
            email="<EMAIL>",
            password=password
        )
        
        # Login with uppercase email
        response = client.post(
            "/api/v1/editorial/auth/login",
            json={
                "email": "<EMAIL>",
                "password": password
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["editor"]["email"] == "<EMAIL>"
    
    def test_editor_login_token_expiration(self, client: TestClient, db_session: Session):
        """Test token contains correct expiration"""
        password = "TestPassword123!"
        editor = EditorFixtures.create_editor_account(
            db_session,
            email="<EMAIL>",
            password=password
        )
        
        response = client.post(
            "/api/v1/editorial/auth/login",
            json={
                "email": "<EMAIL>",
                "password": password
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Decode and check expiration
        decoded = jwt.decode(
            data["access_token"],
            settings.SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        
        assert "exp" in decoded
        assert "iat" in decoded
        
        # Verify expiration is approximately 8 hours from now
        exp_time = datetime.fromtimestamp(decoded["exp"], UTC)
        iat_time = datetime.fromtimestamp(decoded["iat"], UTC)
        duration = exp_time - iat_time
        
        # Should be approximately 8 hours (28800 seconds)
        assert 28700 <= duration.total_seconds() <= 28900
    
    def test_editor_login_multiple_scopes(self, client: TestClient, db_session: Session):
        """Test editor with multiple scopes"""
        password = "TestPassword123!"
        editor = EditorFixtures.create_editor_account(
            db_session,
            email="<EMAIL>",
            password=password
        )
        
        # Create multiple subjects and scopes
        subject1 = Subject(name="Math", year_id=1)
        subject2 = Subject(name="Science", year_id=1)
        db_session.add_all([subject1, subject2])
        db_session.commit()
        
        scope1 = EditorScope(editor_id=editor.id, subject_id=subject1.id)
        scope2 = EditorScope(editor_id=editor.id, subject_id=subject2.id)
        db_session.add_all([scope1, scope2])
        db_session.commit()
        
        response = client.post(
            "/api/v1/editorial/auth/login",
            json={
                "email": "<EMAIL>",
                "password": password
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert len(data["scopes"]) == 2
        scope_names = [s["subject_name"] for s in data["scopes"]]
        assert "Math" in scope_names
        assert "Science" in scope_names