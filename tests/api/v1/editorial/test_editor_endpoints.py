import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import json

from db.models import (
    DraftExercise, DraftExerciseStatus, EditorAccount,
    DraftLearningNodeExercise, LearningNode
)
from tests.fixtures.editorial_fixtures import (
    admin_account, editor_account, another_editor,
    draft_exercise_new, draft_exercise_in_review,
    draft_exercise_accepted, equation_node
)
from tests.utils.editorial_test_utils import create_editor_token


class TestEditorEndpoints:
    """Test suite for editor API endpoints"""
    
    @pytest.fixture
    def editor_headers(self, editor_account: EditorAccount):
        """Create auth headers for regular editor"""
        token = create_editor_token(
            editor_id=editor_account.id,
            email=editor_account.email,
            role=editor_account.role
        )
        return {"x-editor-token": token}
    
    @pytest.fixture
    def admin_headers(self, admin_account: EditorAccount):
        """Create auth headers for admin"""
        token = create_editor_token(
            editor_id=admin_account.id,
            email=admin_account.email,
            role=admin_account.role
        )
        return {"x-editor-token": token}
    
    def test_list_drafts(
        self, client, db_session: Session,
        editor_headers: dict, editor_account: EditorAccount,
        draft_exercise_new: DraftExercise,
        draft_exercise_in_review: DraftExercise,
        equation_node: LearningNode
    ):
        """Test listing drafts endpoint"""
        # Create editor scope so they can see drafts
        from tests.fixtures.editorial_fixtures import EditorFixtures
        EditorFixtures.create_editor_scope(db_session, editor_account, learning_node=equation_node)
        
        # Note: Associations are already created by the fixtures
        
        response = client.get(
            "/api/v1/editorial/editor/drafts",
            headers=editor_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "drafts" in data
        assert "total" in data
        assert "page" in data
        assert "page_size" in data  # Changed from limit to page_size
        assert data["total"] >= 2  # Should see at least the two drafts we created
    
    def test_list_drafts_with_filters(
        self, client: TestClient, db_session: Session,
        editor_headers: dict, editor_account: EditorAccount,
        draft_exercise_new: DraftExercise, equation_node: LearningNode
    ):
        """Test listing drafts with filters"""
        # Create editor scope
        from tests.fixtures.editorial_fixtures import EditorFixtures
        EditorFixtures.create_editor_scope(db_session, editor_account, learning_node=equation_node)
        
        # Note: Association is already created by the fixture
        
        response = client.get(
            "/api/v1/editorial/editor/drafts",
            params={
                "status": "NEW",
                "page": 1,
                "limit": 10
            },
            headers=editor_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert all(d["status"] == "NEW" for d in data["drafts"])
    
    def test_get_draft_detail(
        self, client: TestClient, db_session: Session,
        editor_headers: dict, editor_account: EditorAccount,
        draft_exercise_in_review: DraftExercise,
        equation_node: LearningNode
    ):
        """Test getting draft details"""
        # Create editor scope
        from tests.fixtures.editorial_fixtures import EditorFixtures
        EditorFixtures.create_editor_scope(db_session, editor_account, learning_node=equation_node)
        
        # Note: Association is already created by the fixture
        
        response = client.get(
            f"/api/v1/editorial/editor/drafts/{draft_exercise_in_review.id}",
            headers=editor_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == draft_exercise_in_review.id
        assert "learning_nodes" in data
        assert "assigned_editor" in data
        assert "media" in data
    
    def test_get_draft_detail_no_access(
        self, client, another_editor: EditorAccount,
        draft_exercise_in_review: DraftExercise
    ):
        """Test getting draft details without access"""
        token = create_editor_token(
            editor_id=another_editor.id,
            email=another_editor.email,
            role=another_editor.role
        )
        headers = {"x-editor-token": token}
        
        response = client.get(
            f"/api/v1/editorial/editor/drafts/{draft_exercise_in_review.id}",
            headers=headers
        )
        
        assert response.status_code == 403
    
    def test_claim_draft(
        self, client: TestClient, db_session: Session,
        editor_headers: dict, editor_account: EditorAccount,
        draft_exercise_new: DraftExercise, equation_node: LearningNode
    ):
        """Test claiming a draft"""
        # Create editor scope
        from tests.fixtures.editorial_fixtures import EditorFixtures
        EditorFixtures.create_editor_scope(db_session, editor_account, learning_node=equation_node)
        
        # Note: Association is already created by the fixture
        
        response = client.post(
            f"/api/v1/editorial/editor/drafts/{draft_exercise_new.id}/claim",
            headers=editor_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "draft" in data
        assert data["draft"]["status"] == "IN_REVIEW"
        assert data["draft"]["assigned_editor"] is not None
    
    def test_update_draft(
        self, client: TestClient, db_session: Session,
        editor_headers: dict, editor_account: EditorAccount,
        draft_exercise_in_review: DraftExercise, equation_node: LearningNode
    ):
        """Test updating a draft"""
        # Create editor scope
        from tests.fixtures.editorial_fixtures import EditorFixtures
        EditorFixtures.create_editor_scope(db_session, editor_account, learning_node=equation_node)
        
        # Note: Association is already created by the fixture
        
        update_data = {
            "data_json": {
                "prompt": "Updated question",
                "options": [
                    {"public_id": "a", "text": "Option A"},
                    {"public_id": "b", "text": "Option B"}
                ]
            },
            "solution_json": {
                "correct_answer": {"correct_option_id": ["a"]}
            }
        }
        
        response = client.patch(
            f"/api/v1/editorial/editor/drafts/{draft_exercise_in_review.id}",
            json=update_data,
            headers=editor_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["draft"]["data_json"]["prompt"] == "Updated question"
    
    def test_update_draft_auto_claim(
        self, client: TestClient, db_session: Session,
        editor_headers: dict, editor_account: EditorAccount,
        draft_exercise_new: DraftExercise, equation_node: LearningNode
    ):
        """Test updating a NEW draft auto-claims it"""
        # Create editor scope
        from tests.fixtures.editorial_fixtures import EditorFixtures
        EditorFixtures.create_editor_scope(db_session, editor_account, learning_node=equation_node)
        
        # Ensure draft is NEW and unassigned
        assert draft_exercise_new.status == DraftExerciseStatus.NEW
        assert draft_exercise_new.assigned_editor_id is None
        
        update_data = {
            "data_json": {
                "prompt": "Auto-claimed question",
                "options": [
                    {"public_id": "x", "text": "Option X"},
                    {"public_id": "y", "text": "Option Y"}
                ]
            },
            "solution_json": {
                "correct_answer": {"correct_option_id": ["x"]}
            }
        }
        
        response = client.patch(
            f"/api/v1/editorial/editor/drafts/{draft_exercise_new.id}",
            json=update_data,
            headers=editor_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["draft"]["status"] == "IN_REVIEW"  # Changed from NEW
        assert data["draft"]["assigned_editor"]["id"] == editor_account.id  # Auto-assigned
        assert data["draft"]["data_json"]["prompt"] == "Auto-claimed question"
    
    def test_accept_draft(
        self, client: TestClient, db_session: Session,
        editor_headers: dict, editor_account: EditorAccount,
        draft_exercise_in_review: DraftExercise, equation_node: LearningNode
    ):
        """Test accepting a draft"""
        # Create editor scope
        from tests.fixtures.editorial_fixtures import EditorFixtures
        EditorFixtures.create_editor_scope(db_session, editor_account, learning_node=equation_node)
        
        # Note: Association is already created by the fixture
        
        # Ensure draft has required data
        draft_exercise_in_review.data_json = {"prompt": "Test"}
        draft_exercise_in_review.solution_json = {"correct_answer": {}}
        db_session.commit()
        
        response = client.post(
            f"/api/v1/editorial/editor/drafts/{draft_exercise_in_review.id}/accept",
            headers=editor_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["draft"]["status"] == "ACCEPTED_BY_EDITOR"
    
    def test_release_draft(
        self, client: TestClient, db_session: Session,
        editor_headers: dict, editor_account: EditorAccount,
        draft_exercise_in_review: DraftExercise, equation_node: LearningNode
    ):
        """Test releasing a draft"""
        # Create editor scope
        from tests.fixtures.editorial_fixtures import EditorFixtures
        EditorFixtures.create_editor_scope(db_session, editor_account, learning_node=equation_node)
        
        # Note: Association is already created by the fixture
        
        response = client.post(
            f"/api/v1/editorial/editor/drafts/{draft_exercise_in_review.id}/release",
            json={},  # Empty body, reason is optional
            headers=editor_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["draft"]["status"] == "NEW"
        assert data["draft"]["assigned_editor"] is None
    
    def test_upload_media(
        self, client: TestClient, db_session: Session,
        editor_headers: dict, editor_account: EditorAccount,
        draft_exercise_in_review: DraftExercise, equation_node: LearningNode
    ):
        """Test uploading media to a draft"""
        # Create editor scope
        from tests.fixtures.editorial_fixtures import EditorFixtures
        EditorFixtures.create_editor_scope(db_session, editor_account, learning_node=equation_node)
        
        # Note: Association is already created by the fixture
        
        # Create mock file
        files = {
            "file": ("test_image.png", b"fake image content", "image/png")
        }
        data = {
            "media_type": "IMAGE",
            "metadata": json.dumps({"alt_text": "Test image"})
        }
        
        response = client.post(
            f"/api/v1/editorial/editor/drafts/{draft_exercise_in_review.id}/media",
            files=files,
            data=data,
            headers=editor_headers
        )
        
        # Note: This test will fail without proper S3 mocking
        # In a real test environment, you'd mock the S3 client
        assert response.status_code in [201, 400, 500]  # 400 if validation fails, 500 if S3 not configured
    
    def test_list_draft_media(
        self, client: TestClient, db_session: Session,
        editor_headers: dict, editor_account: EditorAccount,
        draft_exercise_in_review: DraftExercise, equation_node: LearningNode
    ):
        """Test listing media for a draft"""
        # Create editor scope
        from tests.fixtures.editorial_fixtures import EditorFixtures
        EditorFixtures.create_editor_scope(db_session, editor_account, learning_node=equation_node)
        
        # Note: Association is already created by the fixture
        
        response = client.get(
            f"/api/v1/editorial/editor/drafts/{draft_exercise_in_review.id}/media",
            headers=editor_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_unauthorized_access(self, client: TestClient):
        """Test accessing endpoints without authentication"""
        response = client.get("/api/v1/editorial/editor/drafts")
        assert response.status_code == 401
        
        response = client.post("/api/v1/editorial/editor/drafts/1/claim")
        assert response.status_code == 401