import pytest
from unittest.mock import MagicMock
from sqlalchemy.orm import Session

from api.v1.other.contact.schemas.request import StudentContactFormRequest
from dependencies.auth_dependencies.auth_dependency import AuthDependency
from services.student_contact_service import StudentContactService
from core.exception_handling.exceptions.custom_exceptions import ServiceError
from api.v1.common.schemas import AppErrorCode
from tests.utils.auth_utils import create_test_parent_auth_token, create_test_child_auth_token
from tests.data import constants
from tests.mocks.auth_dependency_mocks import MockAuthDependency, UserType
from db.models import Account, ChildAccount
from sqlalchemy import select
from main import app

# The correct endpoint path based on the router structure:
# /api/v1/other (from main.py) + /student-contact (from other/router.py) + /submit-contact-form (from the route)
STUDENT_CONTACT_URL = "/api/v1/other/student-contact/submit-contact-form"

@pytest.fixture
def mock_student_service():
    service = MagicMock(spec=StudentContactService)
    # Default behavior: successful processing
    service.process_student_contact_form.return_value = None
    return service

@pytest.fixture
def authenticated_parent(db_session: Session) -> Account:
    """Get the mock parent account from the test database."""
    stmt = select(Account).where(Account.public_id == constants.MOCK_PARENT_PUBLIC_ID)
    return db_session.scalars(stmt).one()

@pytest.fixture
def authenticated_child(db_session: Session) -> ChildAccount:
    """Get the mock child account from the test database."""
    stmt = select(ChildAccount).where(ChildAccount.public_id == constants.MOCK_CHILD_PUBLIC_ID)
    return db_session.scalars(stmt).one()


def test_successful_student_contact_form_submission_with_parent_auth(
    client, 
    mock_student_service, 
    authenticated_parent: Account
):
    """Test successful form submission with parent authentication."""
    # Override the service dependency
    app.dependency_overrides[StudentContactService] = lambda: mock_student_service
    
    # Create auth token for the parent
    auth_token = create_test_parent_auth_token(authenticated_parent)
    headers = {"x-auth-token": auth_token}
    
    form_data = {
        "email": "<EMAIL>", 
        "name": "Test Student", 
        "message": "This is a test message."
    }
    
    response = client.post(STUDENT_CONTACT_URL, json=form_data, headers=headers)

    assert response.status_code == 200
    assert response.json() == {"message": "Success"}

    # Check if service's process_student_contact_form was called correctly
    mock_student_service.process_student_contact_form.assert_called_once()
    # Verify the argument passed to the service method
    call_args = mock_student_service.process_student_contact_form.call_args[0][0]
    assert isinstance(call_args, StudentContactFormRequest)
    assert call_args.email == form_data["email"]
    assert call_args.name == form_data["name"]
    assert call_args.message == form_data["message"]
    
    # Clear overrides
    app.dependency_overrides.clear()


def test_successful_student_contact_form_submission_with_child_auth(
    client, 
    mock_student_service, 
    authenticated_child: ChildAccount
):
    """Test successful form submission with child authentication."""
    # Override the service dependency
    app.dependency_overrides[StudentContactService] = lambda: mock_student_service
    
    # Create auth token for the child
    auth_token = create_test_child_auth_token(authenticated_child)
    headers = {"x-auth-token": auth_token}
    
    form_data = {
        "email": "<EMAIL>", 
        "name": "Test Student", 
        "message": "This is a test message."
    }
    
    response = client.post(STUDENT_CONTACT_URL, json=form_data, headers=headers)

    assert response.status_code == 200
    assert response.json() == {"message": "Success"}

    # Check if service's process_student_contact_form was called correctly
    mock_student_service.process_student_contact_form.assert_called_once()
    # Verify the argument passed to the service method
    call_args = mock_student_service.process_student_contact_form.call_args[0][0]
    assert isinstance(call_args, StudentContactFormRequest)
    assert call_args.email == form_data["email"]
    assert call_args.name == form_data["name"]
    assert call_args.message == form_data["message"]
    
    # Clear overrides
    app.dependency_overrides.clear()


def test_student_contact_form_unauthenticated(client, mock_student_service):
    """Test form submission without authentication token."""
    # Override the service dependency
    app.dependency_overrides[StudentContactService] = lambda: mock_student_service
    
    form_data = {
        "email": "<EMAIL>", 
        "name": "Test Student", 
        "message": "This is a test message."
    }
    
    # No auth headers
    response = client.post(STUDENT_CONTACT_URL, json=form_data)

    assert response.status_code == 401  # AuthenticationError in the route should lead to 401
    assert "message" in response.json()
    # The exact message depends on the exception handler, but should indicate auth required
    mock_student_service.process_student_contact_form.assert_not_called()
    
    # Clear overrides
    app.dependency_overrides.clear()


def test_student_contact_form_service_error(
    client, 
    mock_student_service, 
    authenticated_parent: Account
):
    """Test form submission when service raises an error."""
    # Override the service dependency
    app.dependency_overrides[StudentContactService] = lambda: mock_student_service
    
    # Configure the mock service to raise ServiceError
    expected_error_message = "Service processing failed!"
    mock_student_service.process_student_contact_form.side_effect = ServiceError(
        message=expected_error_message,
        error_code=AppErrorCode.SERVICE_ERROR
    )
    
    # Create auth token
    auth_token = create_test_parent_auth_token(authenticated_parent)
    headers = {"x-auth-token": auth_token}
    
    form_data = {
        "email": "<EMAIL>", 
        "name": "Test Student", 
        "message": "This is a test message."
    }
    
    response = client.post(STUDENT_CONTACT_URL, json=form_data, headers=headers)

    # ServiceError is handled by the global exception handler
    # Check main.py - ServiceError is registered with service_exception_handler
    # The exact status code depends on the error_code mapping in the handler
    # ServiceError typically maps to 500 for generic service errors
    assert response.status_code >= 400  # Some error status
    assert "message" in response.json()
    # The message should be the one from ServiceError
    assert response.json()["message"] == expected_error_message
    
    # Clear overrides
    app.dependency_overrides.clear()


def test_student_contact_form_invalid_data(client, authenticated_parent: Account):
    """Test form submission with invalid data (missing required fields)."""
    # Create auth token
    auth_token = create_test_parent_auth_token(authenticated_parent)
    headers = {"x-auth-token": auth_token}
    
    # Missing required fields
    form_data = {
        "email": "<EMAIL>", 
        # Missing name and message
    }
    
    response = client.post(STUDENT_CONTACT_URL, json=form_data, headers=headers)

    # FastAPI validation error
    assert response.status_code == 422  # Unprocessable Entity
    assert "detail" in response.json()
