import pytest
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from datetime import datetime, UTC
import uuid

from db.models import (
    DraftExercise, DraftExerciseStatus, DraftLearningNodeExercise,
    EditorAccount, EditorRole, Exercise, LearningNode,
    LearningNodeFactory, LearningNodeTypeEnum
)
from tests.fixtures.editorial_fixtures import EditorFixtures, DraftFixtures

class TestDraftExercise:
    """Test DraftExercise model"""
    
    def test_create_draft_exercise(self, db_session: Session):
        """Test valid draft creation with required fields"""
        draft = DraftFixtures.create_draft_exercise(
            db_session,
            public_id="draft_123",
            exercise_type="mc-simple",
            difficulty="medium",
            data={
                "prompt": "What is 2+2?",
                "options": [
                    {"public_id": "opt1", "text": "3"},
                    {"public_id": "opt2", "text": "4"}
                ]
            }
        )
        
        assert draft.id is not None
        assert draft.public_id == "draft_123"
        assert draft.exercise_type == "mc-simple"
        assert draft.difficulty == "medium"
        assert draft.data_json["prompt"] == "What is 2+2?"
        assert draft.status == DraftExerciseStatus.NEW
        assert draft.created_at is not None
        assert draft.updated_at is not None
    
    def test_status_defaults_to_new(self, db_session: Session):
        """Test status defaults to NEW"""
        draft = DraftFixtures.create_draft_exercise(db_session)
        assert draft.status == DraftExerciseStatus.NEW
    
    def test_json_fields_properly_stored(self, db_session: Session):
        """Test JSON fields properly stored"""
        complex_data = {
            "prompt": "Complex question",
            "options": [
                {"public_id": "opt1", "text": "Option 1", "image_public_id": "img1"},
                {"public_id": "opt2", "text": "Option 2"}
            ],
            "metadata": {"difficulty_score": 0.7}
        }
        
        draft = DraftFixtures.create_draft_exercise(
            db_session,
            data=complex_data
        )
        
        # Verify data is stored and retrieved correctly
        assert draft.data_json == complex_data
        assert draft.data_json["metadata"]["difficulty_score"] == 0.7
    
    def test_draft_status_transitions(self, db_session: Session):
        """Test valid status transitions"""
        editor = EditorFixtures.create_editor_account(db_session)
        draft = DraftFixtures.create_draft_exercise(db_session)
        
        # NEW → IN_REVIEW (valid)
        draft.status = DraftExerciseStatus.IN_REVIEW
        draft.assigned_editor_id = editor.id
        db_session.commit()
        assert draft.status == DraftExerciseStatus.IN_REVIEW
        
        # IN_REVIEW → ACCEPTED_BY_EDITOR (valid)
        draft.status = DraftExerciseStatus.ACCEPTED_BY_EDITOR
        db_session.commit()
        assert draft.status == DraftExerciseStatus.ACCEPTED_BY_EDITOR
        
        # ACCEPTED_BY_EDITOR → PUBLISHED (valid)
        draft.status = DraftExerciseStatus.PUBLISHED
        draft.published_at = datetime.now(UTC)
        db_session.commit()
        assert draft.status == DraftExerciseStatus.PUBLISHED
    
    def test_draft_status_rejection_flow(self, db_session: Session):
        """Test rejection and re-review flow"""
        editor = EditorFixtures.create_editor_account(db_session)
        draft = DraftFixtures.create_draft_exercise(
            db_session,
            status=DraftExerciseStatus.ACCEPTED_BY_EDITOR,
            assigned_editor=editor
        )
        
        # ACCEPTED_BY_EDITOR → REJECTED_BY_ADMIN (valid)
        draft.status = DraftExerciseStatus.REJECTED_BY_ADMIN
        draft.reject_reason = "Needs improvement"
        db_session.commit()
        assert draft.status == DraftExerciseStatus.REJECTED_BY_ADMIN
        assert draft.reject_reason == "Needs improvement"
        
        # REJECTED_BY_ADMIN → IN_REVIEW (valid)
        draft.status = DraftExerciseStatus.IN_REVIEW
        draft.reject_reason = None
        db_session.commit()
        assert draft.status == DraftExerciseStatus.IN_REVIEW
        assert draft.reject_reason is None
    
    def test_draft_editor_assignment(self, db_session: Session):
        """Test editor assignment to drafts"""
        editor1 = EditorFixtures.create_editor_account(db_session, email="<EMAIL>")
        editor2 = EditorFixtures.create_editor_account(db_session, email="<EMAIL>")
        
        # Can assign editor to NEW draft
        draft = DraftFixtures.create_draft_exercise(db_session)
        draft.assigned_editor_id = editor1.id
        draft.status = DraftExerciseStatus.IN_REVIEW
        db_session.commit()
        
        assert draft.assigned_editor_id == editor1.id
        assert draft.assigned_editor.email == "<EMAIL>"
        
        # Can reassign to different editor (for testing - in practice this would be controlled)
        draft.assigned_editor_id = editor2.id
        db_session.commit()
        assert draft.assigned_editor_id == editor2.id
    
    def test_draft_relationships(self, db_session: Session):
        """Test draft can have multiple associations"""
        draft = DraftFixtures.create_draft_exercise(db_session)
        
        # Create learning nodes
        node1 = LearningNodeFactory.create_node(
            node_type=LearningNodeTypeEnum.GRAMMAR.value,
            public_id=f"ln_1_{uuid.uuid4().hex[:8]}",
            title="Node 1",
            content={"video_public_id": None, "notes": "Test"}
        )
        node2 = LearningNodeFactory.create_node(
            node_type=LearningNodeTypeEnum.GRAMMAR.value,
            public_id=f"ln_2_{uuid.uuid4().hex[:8]}",
            title="Node 2",
            content={"video_public_id": None, "notes": "Test"}
        )
        db_session.add_all([node1, node2])
        db_session.commit()
        
        # Associate with learning nodes
        assoc1 = DraftLearningNodeExercise(
            learning_node_id=node1.id,
            draft_exercise_id=draft.id
        )
        assoc2 = DraftLearningNodeExercise(
            learning_node_id=node2.id,
            draft_exercise_id=draft.id
        )
        db_session.add_all([assoc1, assoc2])
        db_session.commit()
        
        # Test relationships
        assert len(draft.learning_node_associations) == 2
        assert len(draft.learning_nodes) == 2
        assert node1 in draft.learning_nodes
        assert node2 in draft.learning_nodes
    
    def test_published_exercise_relationship(self, db_session: Session):
        """Test relationship with published exercise"""
        # Create a published exercise
        from db.models import ExerciseTypeEnum, DifficultyEnum
        exercise = Exercise(
            public_id="ex_123",
            exercise_type=ExerciseTypeEnum.MC_SIMPLE,
            difficulty=DifficultyEnum.MEDIUM,
            _data={"prompt": "Published question"}
        )
        db_session.add(exercise)
        db_session.commit()
        
        # Create draft linked to published exercise
        draft = DraftFixtures.create_draft_exercise(
            db_session,
            status=DraftExerciseStatus.PUBLISHED
        )
        draft.published_exercise_id = exercise.id
        draft.published_at = datetime.now(UTC)
        db_session.commit()
        
        assert draft.published_exercise_id == exercise.id
        assert draft.published_exercise.public_id == "ex_123"

class TestDraftLearningNodeExercise:
    """Test DraftLearningNodeExercise association model"""
    
    def test_create_association(self, db_session: Session):
        """Test creating draft-learning node association"""
        draft = DraftFixtures.create_draft_exercise(db_session)
        
        node = LearningNodeFactory.create_node(
            node_type=LearningNodeTypeEnum.GRAMMAR.value,
            public_id=f"ln_{uuid.uuid4().hex[:8]}",
            title="Test Node",
            content={"video_public_id": None, "notes": "Test"}
        )
        db_session.add(node)
        db_session.commit()
        
        assoc = DraftLearningNodeExercise(
            learning_node_id=node.id,
            draft_exercise_id=draft.id
        )
        db_session.add(assoc)
        db_session.commit()
        
        assert assoc.id is not None
        assert assoc.learning_node_id == node.id
        assert assoc.draft_exercise_id == draft.id
        assert assoc.created_at is not None
    
    def test_unique_constraint(self, db_session: Session):
        """Test unique constraint on learning_node_id and draft_exercise_id"""
        draft = DraftFixtures.create_draft_exercise(db_session)
        node = LearningNodeFactory.create_node(
            node_type=LearningNodeTypeEnum.GRAMMAR.value,
            public_id=f"ln_{uuid.uuid4().hex[:8]}",
            title="Test Node",
            content={"video_public_id": None, "notes": "Test"}
        )
        db_session.add(node)
        db_session.commit()
        
        # First association
        assoc1 = DraftLearningNodeExercise(
            learning_node_id=node.id,
            draft_exercise_id=draft.id
        )
        db_session.add(assoc1)
        db_session.commit()
        
        # Try to create duplicate
        assoc2 = DraftLearningNodeExercise(
            learning_node_id=node.id,
            draft_exercise_id=draft.id
        )
        db_session.add(assoc2)
        
        with pytest.raises(IntegrityError):
            db_session.commit()
    
    def test_cascade_delete_from_draft(self, db_session: Session):
        """Test cascade delete when draft is deleted"""
        draft = DraftFixtures.create_draft_exercise(db_session)
        node = LearningNodeFactory.create_node(
            node_type=LearningNodeTypeEnum.GRAMMAR.value,
            public_id=f"ln_{uuid.uuid4().hex[:8]}",
            title="Test Node",
            content={"video_public_id": None, "notes": "Test"}
        )
        db_session.add(node)
        db_session.commit()
        
        assoc = DraftLearningNodeExercise(
            learning_node_id=node.id,
            draft_exercise_id=draft.id
        )
        db_session.add(assoc)
        db_session.commit()
        
        assoc_id = assoc.id
        
        # Delete draft
        db_session.delete(draft)
        db_session.commit()
        
        # Verify association is deleted
        assert db_session.get(DraftLearningNodeExercise, assoc_id) is None
        # But node should still exist
        assert db_session.get(LearningNode, node.id) is not None