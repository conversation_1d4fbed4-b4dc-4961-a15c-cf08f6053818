import pytest
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
import uuid

from db.models import (
    DraftMediaFile, DraftMediaType, DraftExercise,
    ImageFile, AudioFile
)
from tests.fixtures.editorial_fixtures import DraftFixtures

class TestDraftMediaFile:
    """Test DraftMediaFile model"""
    
    def test_create_draft_media_image(self, db_session: Session):
        """Test valid image media with metadata"""
        draft = DraftFixtures.create_draft_exercise(db_session)
        
        media = DraftFixtures.create_draft_media(
            db_session,
            draft_exercise=draft,
            media_media_type=DraftMediaType.IMAGE,
            storage_path="drafts/exercise_images/test_image.png",
            media_metadata={
                "width": 1920,
                "height": 1080,
                "file_size": 512000
            }
        )
        
        assert media.id is not None
        assert media.public_id is not None
        assert media.media_type == DraftMediaType.IMAGE
        assert media.storage_path == "drafts/exercise_images/test_image.png"
        assert media.content_type == "image/png"
        assert media.media_metadata["width"] == 1920
        assert media.media_metadata["height"] == 1080
        assert media.media_metadata["file_size"] == 512000
        assert media.draft_exercise_id == draft.id
        assert media.created_at is not None
    
    def test_create_draft_media_audio(self, db_session: Session):
        """Test valid audio media with metadata"""
        draft = DraftFixtures.create_draft_exercise(db_session)
        
        media = DraftFixtures.create_draft_media(
            db_session,
            draft_exercise=draft,
            media_media_type=DraftMediaType.AUDIO,
            storage_path="drafts/audio_files/test_audio.mp3",
            media_metadata={
                "duration": 5.5,
                "bitrate": 192000,
                "sample_rate": 44100
            }
        )
        
        assert media.media_type == DraftMediaType.AUDIO
        assert media.storage_path == "drafts/audio_files/test_audio.mp3"
        assert media.content_type == "audio/mp3"
        assert media.media_metadata["duration"] == 5.5
        assert media.media_metadata["bitrate"] == 192000
    
    def test_required_storage_path(self, db_session: Session):
        """Test storage_path is required"""
        draft = DraftFixtures.create_draft_exercise(db_session)
        
        media = DraftMediaFile(
            public_id=f"media_{uuid.uuid4().hex[:8]}",
            media_type=DraftMediaType.IMAGE,
            # storage_path missing
            draft_exercise_id=draft.id
        )
        db_session.add(media)
        
        with pytest.raises(IntegrityError):
            db_session.commit()
    
    def test_type_enum_validation(self, db_session: Session):
        """Test type enum validation"""
        draft = DraftFixtures.create_draft_exercise(db_session)
        
        # Valid types
        image_media = DraftMediaFile(
            public_id=f"media_img_{uuid.uuid4().hex[:8]}",
            media_type=DraftMediaType.IMAGE,
            storage_path="test.png",
            draft_exercise_id=draft.id
        )
        
        audio_media = DraftMediaFile(
            public_id=f"media_aud_{uuid.uuid4().hex[:8]}",
            media_type=DraftMediaType.AUDIO,
            storage_path="test.mp3",
            draft_exercise_id=draft.id
        )
        
        db_session.add_all([image_media, audio_media])
        db_session.commit()
        
        assert image_media.media_type == DraftMediaType.IMAGE
        assert audio_media.media_type == DraftMediaType.AUDIO
    
    def test_media_draft_association(self, db_session: Session):
        """Test media linked to draft exercise"""
        draft = DraftFixtures.create_draft_exercise(db_session)
        
        media1 = DraftFixtures.create_draft_media(
            db_session,
            draft_exercise=draft,
            media_media_type=DraftMediaType.IMAGE
        )
        media2 = DraftFixtures.create_draft_media(
            db_session,
            draft_exercise=draft,
            media_media_type=DraftMediaType.AUDIO
        )
        
        # Check relationship from draft side
        assert len(draft.media_files) == 2
        assert media1 in draft.media_files
        assert media2 in draft.media_files
        
        # Check relationship from media side
        assert media1.draft_exercise.id == draft.id
        assert media2.draft_exercise.id == draft.id
    
    def test_cascade_delete_when_draft_deleted(self, db_session: Session):
        """Test cascade delete when draft deleted"""
        draft = DraftFixtures.create_draft_exercise(db_session)
        
        media = DraftFixtures.create_draft_media(
            db_session,
            draft_exercise=draft,
            media_media_type=DraftMediaType.IMAGE
        )
        
        media_id = media.id
        
        # Delete draft
        db_session.delete(draft)
        db_session.commit()
        
        # Verify media is deleted
        assert db_session.get(DraftMediaFile, media_id) is None
    
    def test_media_metadata_storage(self, db_session: Session):
        """Test various metadata storage scenarios"""
        draft = DraftFixtures.create_draft_exercise(db_session)
        
        # Test complex image metadata
        image_metadata = {
            "width": 1920,
            "height": 1080,
            "file_size": 512000,
            "format": "PNG",
            "color_space": "sRGB",
            "has_transparency": True,
            "dpi": 72
        }
        
        image_media = DraftMediaFile(
            public_id=f"img_{uuid.uuid4().hex[:8]}",
            media_type=DraftMediaType.IMAGE,
            storage_path="test.png",
            media_metadata=image_metadata,
            draft_exercise_id=draft.id
        )
        db_session.add(image_media)
        db_session.commit()
        
        # Verify all metadata is stored
        assert image_media.media_metadata == image_metadata
        assert image_media.media_metadata["has_transparency"] is True
        assert image_media.media_metadata["dpi"] == 72
        
        # Test complex audio metadata
        audio_metadata = {
            "duration": 10.5,
            "bitrate": 320000,
            "sample_rate": 48000,
            "channels": 2,
            "codec": "mp3",
            "has_metadata_tags": True,
            "artist": "Test Artist",
            "title": "Test Audio"
        }
        
        audio_media = DraftMediaFile(
            public_id=f"aud_{uuid.uuid4().hex[:8]}",
            media_type=DraftMediaType.AUDIO,
            storage_path="test.mp3",
            media_metadata=audio_metadata,
            draft_exercise_id=draft.id
        )
        db_session.add(audio_media)
        db_session.commit()
        
        # Verify all metadata is stored
        assert audio_media.media_metadata == audio_metadata
        assert audio_media.media_metadata["channels"] == 2
        assert audio_media.media_metadata["artist"] == "Test Artist"
    
    def test_published_media_reference(self, db_session: Session):
        """Test reference to published media after publishing"""
        draft = DraftFixtures.create_draft_exercise(db_session)
        
        # Create draft media
        draft_media = DraftFixtures.create_draft_media(
            db_session,
            draft_exercise=draft,
            media_media_type=DraftMediaType.IMAGE
        )
        
        # Simulate publishing - create production image
        prod_image = ImageFile(
            public_id=draft_media.public_id,
            storage_path="exercise_images/published.png",
            mime_type="image/png",
            width=800,
            height=600
        )
        db_session.add(prod_image)
        db_session.commit()
        
        # Update draft media with published reference
        draft_media.production_media_id = prod_image.id
        db_session.commit()
        
        assert draft_media.production_media_id == prod_image.id
    
    def test_default_metadata_empty_dict(self, db_session: Session):
        """Test metadata defaults to empty dict"""
        draft = DraftFixtures.create_draft_exercise(db_session)
        
        media = DraftMediaFile(
            public_id=f"media_{uuid.uuid4().hex[:8]}",
            media_type=DraftMediaType.IMAGE,
            storage_path="test.png",
            draft_exercise_id=draft.id
            # metadata not provided
        )
        db_session.add(media)
        db_session.commit()
        
        assert media.media_metadata == {}
    
    def test_multiple_media_files_per_draft(self, db_session: Session):
        """Test draft can have multiple media files of different types"""
        draft = DraftFixtures.create_draft_exercise(db_session)
        
        # Add multiple images
        image1 = DraftFixtures.create_draft_media(
            db_session, draft, DraftMediaType.IMAGE,
            storage_path="drafts/exercise_images/img1.png"
        )
        image2 = DraftFixtures.create_draft_media(
            db_session, draft, DraftMediaType.IMAGE,
            storage_path="drafts/exercise_images/img2.png"
        )
        
        # Add multiple audio files
        audio1 = DraftFixtures.create_draft_media(
            db_session, draft, DraftMediaType.AUDIO,
            storage_path="drafts/audio_files/audio1.mp3"
        )
        audio2 = DraftFixtures.create_draft_media(
            db_session, draft, DraftMediaType.AUDIO,
            storage_path="drafts/audio_files/audio2.mp3"
        )
        
        # Verify all are associated
        assert len(draft.media_files) == 4
        
        # Verify we can filter by type
        image_files = [m for m in draft.media_files if m.media_type == DraftMediaType.IMAGE]
        audio_files = [m for m in draft.media_files if m.media_type == DraftMediaType.AUDIO]
        
        assert len(image_files) == 2
        assert len(audio_files) == 2