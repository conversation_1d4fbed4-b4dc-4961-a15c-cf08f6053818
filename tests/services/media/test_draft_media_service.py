import pytest
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session
from io import Bytes<PERSON>
from datetime import datetime, timedelta
from botocore.exceptions import ClientError

from db.models import DraftExercise, DraftMediaFile, DraftMediaType, ImageFile, AudioFile, DraftExerciseStatus
from services.media.draft_media_service import DraftMediaService
from tests.fixtures.editorial_fixtures import (
    draft_exercise_in_review, editor_account, admin_account,
    math_subject, equation_node
)


class TestDraftMediaService:
    """Test suite for DraftMediaService"""
    
    @pytest.fixture
    def mock_s3_client(self):
        """Mock S3 client for testing"""
        with patch('services.media.draft_media_service.boto3.client') as mock_client:
            s3_mock = Mock()
            # Mock all S3 operations
            s3_mock.head_bucket.return_value = {}
            s3_mock.create_bucket.return_value = {}
            s3_mock.upload_fileobj.return_value = None
            s3_mock.copy_object.return_value = {}
            s3_mock.delete_object.return_value = {}
            s3_mock.list_objects_v2.return_value = {'Contents': []}
            s3_mock.generate_presigned_url.return_value = "https://example.com/signed-url"
            
            mock_client.return_value = s3_mock
            yield s3_mock
    
    @pytest.fixture
    def media_service(self, mock_s3_client):
        """Create media service with mocked S3"""
        with patch('services.media.draft_media_service.boto3.client') as mock_boto:
            mock_boto.return_value = mock_s3_client
            # Mock the _ensure_bucket_exists method to avoid actual S3 calls
            with patch.object(DraftMediaService, '_ensure_bucket_exists'):
                service = DraftMediaService()
                return service
    
    def test_generate_storage_path(self, media_service):
        """Test storage path generation"""
        path = media_service._generate_storage_path(
            draft_exercise_id=123,
            media_type=DraftMediaType.IMAGE,
            filename="test_image.png"
        )
        
        assert path.startswith("draft/123/image/")
        assert path.endswith(".png")
        assert len(path.split('/')) == 4  # draft/id/type/filename
    
    def test_generate_storage_path_without_extension(self, media_service):
        """Test storage path generation for files without extension"""
        path = media_service._generate_storage_path(
            draft_exercise_id=456,
            media_type=DraftMediaType.AUDIO,
            filename="audio_file"
        )
        
        assert path.startswith("draft/456/audio/")
        # Should still generate a valid path even without extension
        assert len(path.split('/')) == 4
    
    def test_get_draft_media_url_success(self, media_service, mock_s3_client):
        """Test successful URL generation"""
        mock_s3_client.generate_presigned_url.return_value = "https://example.com/signed-url"
        
        url = media_service.get_draft_media_url("draft/123/image/test.png")
        
        assert url == "https://example.com/signed-url"
        mock_s3_client.generate_presigned_url.assert_called_once_with(
            'get_object',
            Params={
                'Bucket': 'luxedu-media-draft',
                'Key': 'draft/123/image/test.png'
            },
            ExpiresIn=3600
        )
    
    def test_get_draft_media_url_fallback(self, media_service, mock_s3_client):
        """Test URL generation fallback on error"""
        mock_s3_client.generate_presigned_url.side_effect = ClientError(
            {'Error': {'Code': 'NoSuchKey'}}, 'generate_presigned_url'
        )
        
        url = media_service.get_draft_media_url("draft/123/image/test.png")
        
        # Should return fallback URL
        assert url == "https://luxedu-media-draft.cloudflare.r2cdn.net/draft/123/image/test.png"
    
    def test_upload_draft_media_success(
        self, db_session: Session, media_service, mock_s3_client,
        draft_exercise_in_review: DraftExercise
    ):
        """Test successful media upload"""
        # Mock file
        file_content = b"fake image content"
        file_obj = BytesIO(file_content)
        
        # Mock S3 upload
        mock_s3_client.upload_fileobj.return_value = None
        
        # Upload media
        draft_media = media_service.upload_draft_media(
            db=db_session,
            draft_exercise_id=draft_exercise_in_review.id,
            file=file_obj,
            filename="test_image.png",
            media_type=DraftMediaType.IMAGE,
            media_metadata={"alt_text": "Test image"}
        )
        
        # Verify media record created
        assert draft_media.draft_exercise_id == draft_exercise_in_review.id
        assert draft_media.media_type == DraftMediaType.IMAGE
        assert draft_media.original_filename == "test_image.png"
        assert draft_media.content_type == "image/png"
        assert draft_media.media_metadata["alt_text"] == "Test image"
        assert draft_media.storage_path.startswith(f"draft/{draft_exercise_in_review.id}/image/")
        
        # Verify S3 upload called
        mock_s3_client.upload_fileobj.assert_called_once()
    
    def test_upload_draft_media_s3_error(
        self, db_session: Session, media_service, mock_s3_client,
        draft_exercise_in_review: DraftExercise
    ):
        """Test media upload with S3 error"""
        file_obj = BytesIO(b"content")
        
        # Mock S3 error
        mock_s3_client.upload_fileobj.side_effect = ClientError(
            {'Error': {'Code': 'AccessDenied'}}, 'upload_fileobj'
        )
        
        # Should raise exception
        with pytest.raises(Exception) as exc_info:
            media_service.upload_draft_media(
                db=db_session,
                draft_exercise_id=draft_exercise_in_review.id,
                file=file_obj,
                filename="test.png",
                media_type=DraftMediaType.IMAGE
            )
        
        assert "Failed to upload media" in str(exc_info.value)
    
    def test_copy_to_production_image(
        self, db_session: Session, media_service, mock_s3_client,
        draft_exercise_in_review: DraftExercise
    ):
        """Test copying image to production"""
        # Create draft media with valid exercise reference
        draft_media = DraftMediaFile(
            draft_exercise_id=draft_exercise_in_review.id,
            media_type=DraftMediaType.IMAGE,
            storage_path=f"draft/{draft_exercise_in_review.id}/image/test.png",
            original_filename="test.png",
            content_type="image/png"
        )
        db_session.add(draft_media)
        db_session.commit()
        
        # Mock S3 copy
        mock_s3_client.copy_object.return_value = {}
        
        # Copy to production
        # Create a real ImageFile instance instead of mocking
        with patch.object(db_session, 'add'), \
             patch.object(db_session, 'flush'), \
             patch.object(db_session, 'refresh'):
            
            # Mock the ImageFile creation
            with patch('services.media.draft_media_service.ImageFile') as MockImageFile:
                # Create a mock that behaves like a SQLAlchemy model
                mock_image = Mock()
                mock_image.id = 999
                mock_image.storage_path = 'image/test.png'
                mock_image.mime_type = 'image/png'
                
                # Configure the mock to return our instance
                MockImageFile.return_value = mock_image
                
                production_id = media_service.copy_to_production(
                    db=db_session,
                    draft_media=draft_media
                )
        
        assert production_id == 999
        
        # Verify S3 copy called
        mock_s3_client.copy_object.assert_called_once_with(
            CopySource={'Bucket': 'luxedu-media-draft', 'Key': f'draft/{draft_exercise_in_review.id}/image/test.png'},
            Bucket=media_service.production_image_bucket,
            Key='image/test.png',
            MetadataDirective='COPY'
        )
        
        # Verify draft media updated
        assert draft_media.production_media_id == 999
        assert draft_media.copied_to_production_at is not None
    
    def test_delete_draft_media(
        self, db_session: Session, media_service, mock_s3_client,
        draft_exercise_in_review: DraftExercise
    ):
        """Test deleting draft media"""
        # Create draft media with valid exercise reference
        draft_media = DraftMediaFile(
            draft_exercise_id=draft_exercise_in_review.id,
            media_type=DraftMediaType.IMAGE,
            storage_path=f"draft/{draft_exercise_in_review.id}/image/test.png",
            original_filename="test.png",
            content_type="image/png"
        )
        db_session.add(draft_media)
        db_session.commit()
        
        media_id = draft_media.id
        
        # Mock S3 delete
        mock_s3_client.delete_object.return_value = {}
        
        # Delete media
        media_service.delete_draft_media(db_session, draft_media)
        
        # Verify S3 delete called
        mock_s3_client.delete_object.assert_called_once_with(
            Bucket='luxedu-media-draft',
            Key=f'draft/{draft_exercise_in_review.id}/image/test.png'
        )
        
        # Verify database record deleted
        assert db_session.query(DraftMediaFile).filter_by(id=media_id).first() is None
    
    def test_list_draft_media(self, media_service, mock_s3_client):
        """Test listing draft media files"""
        # Mock S3 response
        mock_s3_client.list_objects_v2.return_value = {
            'Contents': [
                {'Key': 'draft/123/image/file1.png'},
                {'Key': 'draft/123/image/file2.jpg'},
                {'Key': 'draft/123/audio/file3.mp3'}
            ]
        }
        
        paths = media_service.list_draft_media(123)
        
        assert len(paths) == 3
        assert 'draft/123/image/file1.png' in paths
        assert 'draft/123/audio/file3.mp3' in paths
        
        mock_s3_client.list_objects_v2.assert_called_once_with(
            Bucket='luxedu-media-draft',
            Prefix='draft/123/'
        )
    
    def test_cleanup_old_draft_media(
        self, db_session: Session, media_service, mock_s3_client,
        draft_exercise_in_review: DraftExercise
    ):
        """Test cleaning up old draft media"""
        # First, we need to create additional draft exercises for our test media
        from tests.fixtures.editorial_fixtures import DraftFixtures
        
        # Create additional draft exercises
        draft_exercise_2 = DraftFixtures.create_draft_exercise(
            db=db_session,
            public_id="test_draft_2",
            status=DraftExerciseStatus.NEW
        )
        draft_exercise_3 = DraftFixtures.create_draft_exercise(
            db=db_session,
            public_id="test_draft_3",
            status=DraftExerciseStatus.NEW
        )
        
        # Create old media (35 days old)
        old_date = datetime.utcnow() - timedelta(days=35)
        old_media = DraftMediaFile(
            draft_exercise_id=draft_exercise_in_review.id,
            media_type=DraftMediaType.IMAGE,
            storage_path=f"draft/{draft_exercise_in_review.id}/image/old.png",
            original_filename="old.png",
            content_type="image/png"
        )
        old_media.created_at = old_date
        
        # Create recent media (5 days old)
        recent_media = DraftMediaFile(
            draft_exercise_id=draft_exercise_2.id,
            media_type=DraftMediaType.IMAGE,
            storage_path=f"draft/{draft_exercise_2.id}/image/recent.png",
            original_filename="recent.png",
            content_type="image/png"
        )
        recent_media.created_at = datetime.utcnow() - timedelta(days=5)
        
        # Create published media (old but published)
        published_media = DraftMediaFile(
            draft_exercise_id=draft_exercise_3.id,
            media_type=DraftMediaType.IMAGE,
            storage_path=f"draft/{draft_exercise_3.id}/image/published.png",
            original_filename="published.png",
            content_type="image/png",
            production_media_id=999
        )
        published_media.created_at = old_date
        
        db_session.add_all([old_media, recent_media, published_media])
        db_session.commit()
        
        # Mock S3 delete
        mock_s3_client.delete_object.return_value = {}
        
        # Run cleanup
        deleted_count = media_service.cleanup_old_draft_media(
            db=db_session,
            days=30
        )
        
        assert deleted_count == 1  # Only old_media should be deleted
        
        # Verify correct media deleted
        assert db_session.query(DraftMediaFile).filter_by(id=old_media.id).first() is None
        assert db_session.query(DraftMediaFile).filter_by(id=recent_media.id).first() is not None
        assert db_session.query(DraftMediaFile).filter_by(id=published_media.id).first() is not None