"""
Fixtures for draft management service tests.
"""
import pytest
from unittest.mock import MagicMock, AsyncMock
from services.draft_management.publishing_service import PublishingService

@pytest.fixture
def mock_publishing_service(mocker):
    """Mock the publishing service to avoid async issues in tests."""
    # Mock the bulk_publish method
    mock_bulk_publish = MagicMock()
    mock_bulk_publish.return_value = {
        "batch_id": 1,
        "total": 2,
        "successful": 2,
        "failed": 0,
        "results": {
            1: {"success": True, "exercise_id": 100},
            2: {"success": True, "exercise_id": 101}
        }
    }
    
    # Mock the publish_draft method
    mock_publish_draft = MagicMock()
    mock_publish_draft.return_value = {
        "exercise_id": 100,
        "draft_id": 1
    }
    
    mocker.patch.object(PublishingService, 'bulk_publish', mock_bulk_publish)
    mocker.patch.object(PublishingService, 'publish_draft', mock_publish_draft)
    
    return PublishingService