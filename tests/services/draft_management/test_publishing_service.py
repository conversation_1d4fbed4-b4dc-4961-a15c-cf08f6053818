import pytest
import asyncio
from unittest.mock import Mock, patch, MagicMock, AsyncMock
from sqlalchemy.orm import Session
from datetime import datetime

from db.models import (
    DraftExercise, DraftExerciseStatus, EditorAccount, EditorRole,
    DraftLearningNodeExercise, DraftMediaFile, DraftMediaType,
    Exercise, ExerciseTypeEnum, DifficultyEnum, PublishBatch,
    LearningNode
)
from services.draft_management.publishing_service import PublishingService
from services.draft_management.scope_service import ScopeService
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ValidationError, PermissionDeniedError
)
from tests.fixtures.editorial_fixtures import (
    admin_account, editor_account, draft_exercise_accepted,
    draft_exercise_in_review, equation_node
)


class TestPublishingService:
    """Test suite for PublishingService"""
    
    @pytest.fixture
    def mock_media_service(self):
        """Mock DraftMediaService"""
        with patch('services.draft_management.publishing_service.DraftMediaService') as MockService:
            mock_instance = Mock()
            mock_instance.copy_to_production = MagicMock(return_value=999)
            MockService.return_value = mock_instance
            yield mock_instance
    
    @pytest.fixture(autouse=True)
    def mock_scope_service(self):
        """Mock ScopeService async methods"""
        with patch.object(ScopeService, 'editor_has_scope_for_draft', new_callable=AsyncMock) as mock_scope:
            mock_scope.return_value = True
            yield mock_scope
    
    @pytest.fixture(autouse=True)
    def mock_boto3_client(self):
        """Mock boto3 S3/R2 client to prevent actual AWS calls"""
        with patch('boto3.client') as mock_client:
            mock_s3 = Mock()
            mock_s3.head_bucket.return_value = {}
            mock_s3.create_bucket.return_value = {}
            mock_client.return_value = mock_s3
            yield mock_s3
    
    @pytest.fixture(autouse=True)
    def mock_learning_node_association(self):
        """Mock LearningNodeExerciseAssociation creation to avoid database conflicts"""
        with patch('services.draft_management.publishing_service.LearningNodeExerciseAssociation.create_association') as mock_create:
            mock_create.return_value = Mock(id=1)
            yield mock_create
    
    @pytest.fixture(autouse=True)
    def mock_exercise_factory(self, db_session):
        """Mock ExerciseFactory to create base Exercise instances instead of subclasses"""
        with patch('services.draft_management.publishing_service.ExerciseFactory.create_exercise') as mock_factory:
            exercise_counter = {'count': 0}
            
            def create_base_exercise(exercise_type, learning_node, data, solution=None, difficulty=DifficultyEnum.MEDIUM):
                from db.models import Exercise, ExerciseTypeEnum, ExerciseSolutionModel
                import uuid
                # Create a base Exercise instance instead of subclass
                exercise = Exercise(
                    exercise_type=ExerciseTypeEnum(exercise_type),
                    difficulty=difficulty,
                    public_id=str(uuid.uuid4())
                )
                exercise.data = data
                if solution:
                    # The publishing service passes a dict, we need to validate it
                    solution_model = ExerciseSolutionModel.model_validate(solution)
                    exercise.solution = solution_model
                
                # Simulate database save with unique IDs
                exercise_counter['count'] += 1
                exercise.id = 9000 + exercise_counter['count']
                
                # Mock the database operations
                def mock_add(obj):
                    if isinstance(obj, Exercise):
                        # The exercise is being added to the session
                        pass
                
                def mock_flush():
                    # Simulate flush by ensuring the exercise has an ID
                    pass
                
                # Patch the session methods temporarily
                original_add = db_session.add
                original_flush = db_session.flush
                db_session.add = mock_add
                db_session.flush = mock_flush
                
                # Don't create the association here - let the publishing service do it
                return exercise
            
            mock_factory.side_effect = create_base_exercise
            yield mock_factory
    
    def test_publish_draft_success(
        self, db_session: Session, admin_account: EditorAccount,
        draft_exercise_accepted: DraftExercise, equation_node: LearningNode,
        mock_media_service, mock_exercise_factory
    ):
        """Test successful draft publication"""
        # Set up draft data
        draft_exercise_accepted.data_json = {
            "prompt": "Solve for x: 2x + 5 = 15",
            "options": []
        }
        draft_exercise_accepted.solution_json = {
            "correct_answer": {"correct_option_id": ["opt1"]}
        }
        
        # The draft_exercise_accepted fixture already has a learning node association
        # No need to create another one
        
        # Mock the database operations to avoid actual inserts
        with patch.object(db_session, 'add', autospec=True):
            with patch.object(db_session, 'flush', autospec=True):
                # Publish draft
                result = asyncio.run(PublishingService.publish_draft(
                    db=db_session,
                    draft_id=draft_exercise_accepted.id,
                    admin=admin_account,
                    publish_notes="Looks good!"
                ))
        
        # Verify result
        assert "exercise_id" in result
        assert "exercise_public_id" in result
        assert "published_at" in result
        
        # Verify draft status was set (even if transaction rolled back)
        assert draft_exercise_accepted.status == DraftExerciseStatus.PUBLISHED
        assert draft_exercise_accepted.published_at is not None
        assert draft_exercise_accepted.published_exercise_id == result["exercise_id"]
        
        # Verify the factory was called with correct parameters
        mock_exercise_factory.assert_called_once_with(
            exercise_type=draft_exercise_accepted.exercise_type,
            learning_node=equation_node,
            data=draft_exercise_accepted.data_json,
            solution=draft_exercise_accepted.solution_json,
            difficulty=DifficultyEnum(draft_exercise_accepted.difficulty)
        )
        

    
    def test_publish_draft_wrong_status(
        self, db_session: Session, admin_account: EditorAccount,
        draft_exercise_in_review: DraftExercise
    ):
        """Test publishing draft in wrong status"""
        with pytest.raises(ValidationError) as exc_info:
            asyncio.run(PublishingService.publish_draft(
                db=db_session,
                draft_id=draft_exercise_in_review.id,
                admin=admin_account
            ))
        
        assert "must be in ACCEPTED_BY_EDITOR status" in str(exc_info.value)
    
    def test_publish_draft_incomplete_data(
        self, db_session: Session, admin_account: EditorAccount,
        draft_exercise_accepted: DraftExercise
    ):
        """Test publishing draft with incomplete data"""
        # Remove solution data
        draft_exercise_accepted.solution_json = None
        db_session.commit()
        
        with pytest.raises(ValidationError) as exc_info:
            asyncio.run(PublishingService.publish_draft(
                db=db_session,
                draft_id=draft_exercise_accepted.id,
                admin=admin_account
            ))
        
        assert "must have complete exercise and solution data" in str(exc_info.value)
    
    def test_publish_draft_no_scope(
        self, db_session: Session, admin_account: EditorAccount,
        draft_exercise_accepted: DraftExercise, mock_scope_service
    ):
        """Test publishing draft without proper scope"""
        # Mock scope service to return False for this test
        mock_scope_service.return_value = False
        
        # Remove admin's scope
        from db.models import EditorScope
        db_session.query(EditorScope).filter_by(editor_id=admin_account.id).delete()
        db_session.commit()
        
        with pytest.raises(PermissionDeniedError) as exc_info:
            asyncio.run(PublishingService.publish_draft(
                db=db_session,
                draft_id=draft_exercise_accepted.id,
                admin=admin_account
            ))
        
        assert "does not have scope" in str(exc_info.value)
    
    def test_publish_draft_with_media(
        self, db_session: Session, admin_account: EditorAccount,
        draft_exercise_accepted: DraftExercise, equation_node: LearningNode,
        mock_media_service
    ):
        """Test publishing draft with media files"""
        # Set up draft data
        draft_exercise_accepted.data_json = {"prompt": "Test", "options": []}
        draft_exercise_accepted.solution_json = {"correct_answer": {"correct_option_id": ["opt1"]}}
        
        # The draft_exercise_accepted fixture already has a learning node association
        # No need to create another one
        
        # Create draft media
        media1 = DraftMediaFile(
            draft_exercise_id=draft_exercise_accepted.id,
            media_type=DraftMediaType.IMAGE,
            storage_path="draft/1/image/test1.png",
            original_filename="test1.png",
            content_type="image/png"
        )
        media2 = DraftMediaFile(
            draft_exercise_id=draft_exercise_accepted.id,
            media_type=DraftMediaType.AUDIO,
            storage_path="draft/1/audio/test.mp3",
            original_filename="test.mp3",
            content_type="audio/mpeg"
        )
        db_session.add_all([media1, media2])
        db_session.commit()
        
        # Mock the database operations to avoid actual inserts
        with patch.object(db_session, 'add', autospec=True):
            with patch.object(db_session, 'flush', autospec=True):
                # Publish draft
                result = asyncio.run(PublishingService.publish_draft(
                    db=db_session,
                    draft_id=draft_exercise_accepted.id,
                    admin=admin_account
                ))
        
        # Verify media copied
        assert mock_media_service.copy_to_production.call_count == 2
        
        # Media records would be updated in the service but we're mocking the DB operations
        # So we'll just verify the service was called correctly
        calls = mock_media_service.copy_to_production.call_args_list
        assert len(calls) == 2
    
    def test_bulk_publish_success(
        self, db_session: Session, admin_account: EditorAccount,
        equation_node: LearningNode, mock_media_service
    ):
        """Test bulk publishing multiple drafts"""
        # Create multiple accepted drafts
        drafts = []
        for i in range(3):
            draft = DraftExercise(
                public_id=f"draft-{i}",
                exercise_type="mc-simple",
                difficulty="medium",
                data_json={
                    "prompt": f"Question {i}",
                    "options": [
                        {"public_id": f"opt1-{i}", "text": "Option A"},
                        {"public_id": f"opt2-{i}", "text": "Option B"}
                    ]
                },
                solution_json={"correct_answer": {"correct_option_id": [f"opt1-{i}"]}},
                status=DraftExerciseStatus.ACCEPTED_BY_EDITOR
            )
            db_session.add(draft)
            db_session.flush()
            
            # Add association
            assoc = DraftLearningNodeExercise(
                draft_exercise_id=draft.id,
                learning_node_id=equation_node.id
            )
            db_session.add(assoc)
            drafts.append(draft)
        
        db_session.commit()
        draft_ids = [d.id for d in drafts]
        
        # Mock the database operations to avoid actual inserts
        with patch.object(db_session, 'add', autospec=True):
            with patch.object(db_session, 'flush', autospec=True):
                # Bulk publish
                result = asyncio.run(PublishingService.bulk_publish(
                    db=db_session,
                    draft_ids=draft_ids,
                    admin=admin_account,
                    batch_name="Math exercises batch",
                    batch_notes="Q1 2024 content"
                ))
        
        # Verify results
        assert result["successful_count"] == 3
        assert result["failed_count"] == 0
        assert "batch_id" in result
        
        # Since we're mocking DB operations, we'll verify the drafts were marked as published
        for draft in drafts:
            assert draft.status == DraftExerciseStatus.PUBLISHED
            assert draft.published_exercise_id is not None
    
    def test_bulk_publish_partial_failure(
        self, db_session: Session, admin_account: EditorAccount,
        draft_exercise_accepted: DraftExercise, equation_node: LearningNode
    ):
        """Test bulk publish with some failures"""
        # The draft_exercise_accepted fixture already has a learning node association
        # No need to create another one
        
        # Create draft in wrong status
        bad_draft = DraftExercise(
            public_id="bad-draft",
            exercise_type="mc-simple",
            difficulty="medium",
            data_json={"prompt": "Bad"},
            status=DraftExerciseStatus.NEW  # Wrong status
        )
        db_session.add(bad_draft)
        db_session.commit()
        
        # Bulk publish
        result = asyncio.run(PublishingService.bulk_publish(
            db=db_session,
            draft_ids=[draft_exercise_accepted.id, bad_draft.id],
            admin=admin_account,
            batch_name="Mixed batch"
        ))
        
        # Verify partial success
        assert result["successful_count"] == 1
        assert result["failed_count"] == 1
        
        # Check individual results
        assert result["results"][draft_exercise_accepted.id]["success"] is True
        assert result["results"][bad_draft.id]["success"] is False
        assert "must be in ACCEPTED_BY_EDITOR status" in result["results"][bad_draft.id]["error"]
    
    def test_process_media_references(
        self, db_session: Session, draft_exercise_accepted: DraftExercise
    ):
        """Test media reference processing (placeholder test)"""
        # This is a simplified test since the actual implementation is marked as TODO
        media_service = Mock()
        
        data = {"prompt": "Test", "image_public_id": "draft-123"}
        
        processed = asyncio.run(PublishingService._process_media_references(
            db_session,
            draft_exercise_accepted,
            data,
            media_service
        ))
        
        # For now, should return data unchanged
        assert processed == data