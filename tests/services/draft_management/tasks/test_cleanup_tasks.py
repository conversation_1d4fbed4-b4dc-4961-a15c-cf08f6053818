import pytest
import asyncio
from unittest.mock import Mock, patch, MagicMock, AsyncMock
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from db.models import (
    DraftExercise, DraftExerciseStatus, DraftMediaFile, DraftMediaType,
    EditorAccount
)
from services.draft_management.tasks.cleanup_tasks import DraftCleanupTasks
from tests.fixtures.editorial_fixtures import (
    draft_exercise_new, draft_exercise_in_review,
    editor_account
)


class TestDraftCleanupTasks:
    """Test suite for DraftCleanupTasks"""
    
    def test_cleanup_old_draft_media(self, db_session: Session):
        """Test cleaning up old draft media"""
        # Mock DraftMediaService
        with patch('services.draft_management.tasks.cleanup_tasks.DraftMediaService') as MockService:
            mock_service = Mock()
            mock_service.cleanup_old_draft_media = MagicMock(return_value=5)
            MockService.return_value = mock_service
            
            # Run async method using asyncio.run()
            result = asyncio.run(DraftCleanupTasks.cleanup_old_draft_media(
                db=db_session,
                retention_days=30
            ))
            
            assert result["deleted_media_count"] == 5
            assert result["retention_days"] == 30
            assert "completed_at" in result
            
            mock_service.cleanup_old_draft_media.assert_called_once_with(
                db=db_session,
                days=30
            )
    
    def test_cleanup_stale_drafts(self, db_session: Session):
        """Test cleaning up stale drafts"""
        # Create stale drafts
        stale_new = DraftExercise(
            public_id="stale-new",
            exercise_type="mc-simple",
            difficulty="medium",
            data_json={},
            status=DraftExerciseStatus.NEW
        )
        stale_new.updated_at = datetime.utcnow() - timedelta(days=100)
        
        stale_rejected = DraftExercise(
            public_id="stale-rejected",
            exercise_type="input",
            difficulty="high",
            data_json={},
            status=DraftExerciseStatus.REJECTED_BY_ADMIN
        )
        stale_rejected.updated_at = datetime.utcnow() - timedelta(days=95)
        
        # Create recent draft (should not be deleted)
        recent_new = DraftExercise(
            public_id="recent-new",
            exercise_type="cloze",
            difficulty="low",
            data_json={},
            status=DraftExerciseStatus.NEW
        )
        recent_new.updated_at = datetime.utcnow() - timedelta(days=10)
        
        # Create accepted draft (should not be deleted even if old)
        old_accepted = DraftExercise(
            public_id="old-accepted",
            exercise_type="highlight",
            difficulty="medium",
            data_json={},
            status=DraftExerciseStatus.ACCEPTED_BY_EDITOR
        )
        old_accepted.updated_at = datetime.utcnow() - timedelta(days=100)
        
        db_session.add_all([stale_new, stale_rejected, recent_new, old_accepted])
        db_session.commit()
        
        # Run cleanup async method
        result = asyncio.run(DraftCleanupTasks.cleanup_stale_drafts(
            db=db_session,
            stale_days=90
        ))
        
        assert result["deleted_draft_count"] == 2
        assert result["deleted_by_status"]["NEW"] == 1
        assert result["deleted_by_status"]["REJECTED_BY_ADMIN"] == 1
        assert result["stale_threshold_days"] == 90
        
        # Verify correct drafts deleted
        assert db_session.query(DraftExercise).filter_by(public_id="stale-new").first() is None
        assert db_session.query(DraftExercise).filter_by(public_id="stale-rejected").first() is None
        assert db_session.query(DraftExercise).filter_by(public_id="recent-new").first() is not None
        assert db_session.query(DraftExercise).filter_by(public_id="old-accepted").first() is not None
    
    def test_release_abandoned_assignments(
        self, db_session: Session, editor_account: EditorAccount
    ):
        """Test releasing abandoned assignments"""
        # Create abandoned assignment
        abandoned = DraftExercise(
            public_id="abandoned",
            exercise_type="mc-simple",
            difficulty="medium",
            data_json={},
            status=DraftExerciseStatus.IN_REVIEW,
            assigned_editor_id=editor_account.id
        )
        abandoned.updated_at = datetime.utcnow() - timedelta(days=8)
        
        # Create recent assignment (should not be released)
        recent = DraftExercise(
            public_id="recent",
            exercise_type="input",
            difficulty="low",
            data_json={},
            status=DraftExerciseStatus.IN_REVIEW,
            assigned_editor_id=editor_account.id
        )
        recent.updated_at = datetime.utcnow() - timedelta(days=2)
        
        db_session.add_all([abandoned, recent])
        db_session.commit()
        
        # Run cleanup async method
        result = asyncio.run(DraftCleanupTasks.release_abandoned_assignments(
            db=db_session,
            abandoned_days=7
        ))
        
        assert result["released_count"] == 1
        assert result["abandoned_threshold_days"] == 7
        
        # Verify correct draft released
        db_session.refresh(abandoned)
        db_session.refresh(recent)
        
        assert abandoned.status == DraftExerciseStatus.NEW
        assert abandoned.assigned_editor_id is None
        assert recent.status == DraftExerciseStatus.IN_REVIEW
        assert recent.assigned_editor_id == editor_account.id
    
    def test_generate_cleanup_report(self, db_session: Session, editor_account: EditorAccount):
        """Test generating cleanup report"""
        # Create test data
        # First create a draft exercise for the media
        draft_for_media = DraftExercise(
            public_id="draft-for-media",
            exercise_type="mc-simple",
            difficulty="medium",
            data_json={},
            status=DraftExerciseStatus.NEW
        )
        db_session.add(draft_for_media)
        db_session.flush()  # Get the ID
        
        # Old media
        old_media = DraftMediaFile(
            draft_exercise_id=draft_for_media.id,
            media_type=DraftMediaType.IMAGE,
            storage_path="old.png",
            original_filename="old.png",
            content_type="image/png"
        )
        old_media.created_at = datetime.utcnow() - timedelta(days=35)
        
        # Stale draft
        stale_draft = DraftExercise(
            public_id="stale",
            exercise_type="mc-simple",
            difficulty="medium",
            data_json={},
            status=DraftExerciseStatus.NEW
        )
        stale_draft.updated_at = datetime.utcnow() - timedelta(days=95)
        
        # Abandoned assignment
        abandoned = DraftExercise(
            public_id="abandoned",
            exercise_type="input",
            difficulty="low",
            data_json={},
            status=DraftExerciseStatus.IN_REVIEW,
            assigned_editor_id=editor_account.id
        )
        abandoned.updated_at = datetime.utcnow() - timedelta(days=8)
        
        db_session.add_all([old_media, stale_draft, abandoned])
        db_session.commit()
        
        # Generate report async method
        report = asyncio.run(DraftCleanupTasks.generate_cleanup_report(db_session))
        
        assert report["cleanup_candidates"]["old_media_files"] >= 1
        assert report["cleanup_candidates"]["stale_new_drafts"] >= 1
        assert report["cleanup_candidates"]["abandoned_assignments"] >= 1
        assert "current_statistics" in report
        assert "generated_at" in report
    
    def test_run_all_cleanup_tasks(self, db_session: Session):
        """Test running all cleanup tasks"""
        # Mock individual cleanup methods as async
        with patch.object(DraftCleanupTasks, 'release_abandoned_assignments', new_callable=AsyncMock) as mock_release:
            with patch.object(DraftCleanupTasks, 'cleanup_stale_drafts', new_callable=AsyncMock) as mock_stale:
                with patch.object(DraftCleanupTasks, 'cleanup_old_draft_media', new_callable=AsyncMock) as mock_media:
                    
                    # Set return values for async mocks
                    mock_release.return_value = {"released_count": 2}
                    mock_stale.return_value = {"deleted_draft_count": 5}
                    mock_media.return_value = {"deleted_media_count": 10}
                    
                    # Run async method
                    result = asyncio.run(DraftCleanupTasks.run_all_cleanup_tasks(
                        db=db_session,
                        media_retention_days=30,
                        stale_draft_days=90,
                        abandoned_assignment_days=7
                    ))
                    
                    assert "started_at" in result
                    assert "completed_at" in result
                    assert "tasks" in result
                    
                    assert result["tasks"]["abandoned_assignments"]["released_count"] == 2
                    assert result["tasks"]["stale_drafts"]["deleted_draft_count"] == 5
                    assert result["tasks"]["old_media"]["deleted_media_count"] == 10
                    
                    # Verify all tasks were called
                    mock_release.assert_called_once()
                    mock_stale.assert_called_once()
                    mock_media.assert_called_once()
    
    def test_run_all_cleanup_tasks_with_errors(self, db_session: Session):
        """Test running all cleanup tasks with some failures"""
        # Mock individual cleanup methods with one failure
        with patch.object(DraftCleanupTasks, 'release_abandoned_assignments', new_callable=AsyncMock) as mock_release:
            with patch.object(DraftCleanupTasks, 'cleanup_stale_drafts', new_callable=AsyncMock) as mock_stale:
                with patch.object(DraftCleanupTasks, 'cleanup_old_draft_media', new_callable=AsyncMock) as mock_media:
                    
                    # Set return values and side effects for async mocks
                    mock_release.return_value = {"released_count": 2}
                    mock_stale.side_effect = Exception("Database error")
                    mock_media.return_value = {"deleted_media_count": 10}
                    
                    # Run async method
                    result = asyncio.run(DraftCleanupTasks.run_all_cleanup_tasks(
                        db=db_session,
                        media_retention_days=30,
                        stale_draft_days=90,
                        abandoned_assignment_days=7
                    ))
                    
                    # Should continue despite error
                    assert result["tasks"]["abandoned_assignments"]["released_count"] == 2
                    assert "error" in result["tasks"]["stale_drafts"]
                    assert result["tasks"]["old_media"]["deleted_media_count"] == 10