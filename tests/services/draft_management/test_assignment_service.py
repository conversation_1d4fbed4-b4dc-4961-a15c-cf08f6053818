import pytest
import asyncio
from sqlalchemy.orm import Session
from datetime import datetime, UTC
from db.models import DraftExercise, DraftExerciseStatus, EditorAccount, Subject
from services.draft_management.assignment_service import AssignmentService
from services.draft_management.tasks.cleanup_tasks import DraftCleanupTasks
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ValidationError, PermissionDeniedError
)
from tests.fixtures.editorial_fixtures import (
    admin_account, editor_account, another_editor,
    draft_exercise_new, draft_exercise_in_review,
    draft_exercise_accepted, math_subject, equation_node
)


class TestAssignmentService:
    """Test suite for AssignmentService"""
    
    def test_release_draft_by_assigned_editor(
        self, db_session: Session, editor_account: EditorAccount,
        draft_exercise_in_review: DraftExercise
    ):
        """Test releasing a draft by the assigned editor"""
        draft = asyncio.run(AssignmentService.release_draft(
            db=db_session,
            draft_id=draft_exercise_in_review.id,
            editor=editor_account
        ))
        
        assert draft.status == DraftExerciseStatus.NEW
        assert draft.assigned_editor_id is None
        

    
    def test_release_draft_by_admin(
        self, db_session: Session, admin_account: EditorAccount,
        draft_exercise_in_review: DraftExercise
    ):
        """Test admin releasing any draft"""
        draft = asyncio.run(AssignmentService.release_draft(
            db=db_session,
            draft_id=draft_exercise_in_review.id,
            editor=admin_account
        ))
        
        assert draft.status == DraftExerciseStatus.NEW
        assert draft.assigned_editor_id is None
    
    def test_release_draft_by_unassigned_editor(
        self, db_session: Session, another_editor: EditorAccount,
        draft_exercise_in_review: DraftExercise
    ):
        """Test non-assigned editor trying to release a draft"""
        with pytest.raises(PermissionDeniedError) as exc_info:
            asyncio.run(AssignmentService.release_draft(
                db=db_session,
                draft_id=draft_exercise_in_review.id,
                editor=another_editor
            ))
        
        assert "cannot release this draft" in str(exc_info.value)
    
    def test_release_draft_wrong_status(
        self, db_session: Session, editor_account: EditorAccount,
        draft_exercise_accepted: DraftExercise
    ):
        """Test releasing a draft in wrong status"""
        # Assign the draft to editor_account and set status to PUBLISHED
        draft_exercise_accepted.assigned_editor_id = editor_account.id
        draft_exercise_accepted.status = DraftExerciseStatus.PUBLISHED
        db_session.commit()
        
        with pytest.raises(ValidationError) as exc_info:
            asyncio.run(AssignmentService.release_draft(
                db=db_session,
                draft_id=draft_exercise_accepted.id,
                editor=editor_account
            ))
        
        assert "Cannot release a published draft" in str(exc_info.value)
    
    def test_check_editor_assignments(
        self, db_session: Session, editor_account: EditorAccount,
        draft_exercise_in_review: DraftExercise
    ):
        """Test checking editor assignments"""
        assignments = asyncio.run(AssignmentService.get_editor_assignments(
            db=db_session,
            editor_id=editor_account.id
        ))
        
        assert assignments["editor_id"] == editor_account.id
        assert assignments["active_count"] >= 1  # At least the in_review draft
        assert "status_breakdown" in assignments
        assert "recent_drafts" in assignments
        assert isinstance(assignments["recent_drafts"], list)
    
    def test_find_eligible_editors(
        self, db_session: Session, math_subject: Subject,
        editor_account: EditorAccount, another_editor: EditorAccount,
        draft_exercise_new: DraftExercise
    ):
        """Test finding eligible editors for a draft"""
        # Use the private method to find eligible editors
        editors = asyncio.run(AssignmentService._find_eligible_editors(
            db=db_session,
            draft=draft_exercise_new
        ))
        
        # Should include editor_account who has math scope
        editor_ids = [e.id for e in editors]
        assert editor_account.id in editor_ids
        
        # Should not include another_editor who has no scope
        assert another_editor.id not in editor_ids
    
    def test_reassign_draft(
        self, db_session: Session, admin_account: EditorAccount,
        editor_account: EditorAccount, another_editor: EditorAccount,
        draft_exercise_in_review: DraftExercise, math_subject: Subject
    ):
        """Test reassigning a draft to another editor"""
        # Create scope for another_editor
        from db.models import EditorScope
        from tests.fixtures.editorial_fixtures import EditorFixtures
        EditorFixtures.create_editor_scope(
            db_session, another_editor, subject=math_subject
        )
        
        draft = asyncio.run(AssignmentService.reassign_draft(
            db=db_session,
            draft_id=draft_exercise_in_review.id,
            new_editor_id=another_editor.id,
            admin=admin_account
        ))
        
        assert draft.assigned_editor_id == another_editor.id
        assert draft.status == DraftExerciseStatus.IN_REVIEW  # Status unchanged
    
    def test_reassign_draft_non_admin(
        self, db_session: Session, editor_account: EditorAccount,
        another_editor: EditorAccount, draft_exercise_in_review: DraftExercise
    ):
        """Test non-admin trying to reassign a draft"""
        with pytest.raises(PermissionDeniedError) as exc_info:
            asyncio.run(AssignmentService.reassign_draft(
                db=db_session,
                draft_id=draft_exercise_in_review.id,
                new_editor_id=another_editor.id,
                admin=editor_account
            ))
        
        assert "Only admins can reassign" in str(exc_info.value)
    
    def test_auto_release_abandoned_drafts(
        self, db_session: Session, draft_exercise_in_review: DraftExercise
    ):
        """Test auto-releasing abandoned drafts"""
        # Set updated_at to 8 days ago
        from datetime import timedelta
        draft_exercise_in_review.updated_at = datetime.now(UTC) - timedelta(days=8)
        db_session.commit()
        
        result = asyncio.run(DraftCleanupTasks.release_abandoned_assignments(
            db=db_session,
            abandoned_days=7
        ))
        
        assert result["released_count"] == 1
        
        # Verify draft was released
        db_session.refresh(draft_exercise_in_review)
        assert draft_exercise_in_review.status == DraftExerciseStatus.NEW
        assert draft_exercise_in_review.assigned_editor_id is None