#!/usr/bin/env python3
"""
<PERSON>ript to run all subscription pause/resume tests with coverage reporting.
"""
import subprocess
import sys
import os

def run_tests():
    """Run subscription tests with coverage."""
    # Test paths
    test_paths = [
        # Unit tests for services
        "tests/api/v1/app/parent/subscription/services/test_pause_subscription_service.py",
        "tests/api/v1/app/parent/subscription/services/test_resume_subscription_service.py",
        "tests/api/v1/app/parent/subscription/services/test_activate_pause_service.py",
        
        # Integration tests for routes
        "tests/api/v1/app/parent/subscription/test_pause_subscription.py",
        "tests/api/v1/app/parent/subscription/test_resume_subscription.py",
        "tests/api/v1/app/parent/subscription/test_cancel_scheduled_pause.py",
        
        # QStash internal route tests
        "tests/api/v1/tasks/internal/test_activate_pause.py",
        
        # End-to-end tests
        "tests/integration/test_pause_resume_flow.py",
        
        # Edge case tests
        "tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py"
    ]
    
    # Coverage focus on pause/resume services
    coverage_sources = [
        "api/v1/app/parent/subscription/services/pause_subscription_service.py",
        "api/v1/app/parent/subscription/services/resume_subscription_service.py",
        "api/v1/app/parent/subscription/services/activate_pause_service.py",
        "api/v1/app/parent/subscription/services/cancel_scheduled_pause_service.py",
        "api/v1/app/parent/subscription/routes/pause_subscription.py",
        "api/v1/app/parent/subscription/routes/resume_subscription.py",
        "api/v1/app/parent/subscription/routes/cancel_scheduled_pause.py",
        "api/v1/tasks/internal/routes/activate_pause.py"
    ]
    
    # Build pytest command
    cmd = [
        "pytest",
        "-v",  # Verbose
        "-s",  # Show print statements
        "--tb=short",  # Short traceback format
        "--cov=" + ",".join(coverage_sources),  # Coverage for specific modules
        "--cov-report=term-missing",  # Show missing lines
        "--cov-report=html",  # Generate HTML report
        "--cov-fail-under=80",  # Fail if coverage below 80%
    ] + test_paths
    
    # Run tests
    print("Running subscription pause/resume tests with coverage...")
    print("Command:", " ".join(cmd))
    print("-" * 80)
    
    result = subprocess.run(cmd, cwd=os.path.dirname(os.path.abspath(__file__)) + "/..")
    
    if result.returncode == 0:
        print("\n✅ All tests passed!")
        print("📊 Coverage report generated in htmlcov/index.html")
    else:
        print("\n❌ Tests failed!")
        sys.exit(result.returncode)

if __name__ == "__main__":
    run_tests()