"""
Pytest will import this first.  We *only* re‑export the fixtures from
tests._infra.fixtures – no logic lives here.
"""
from tests._infra.fixtures import *  # noqa: F401,F403
# Import subscription test mocks to make them available globally
from tests.mocks.stripe_subscription_mocks import *  # noqa: F401,F403
from tests.mocks.qstash_mocks import *  # noqa: F401,F403

import os
import sys
import logging
from loguru import logger
from dotenv import load_dotenv


def pytest_configure(config):
    """Configure pytest and logging for tests."""
    load_dotenv()

    # Set pytest-asyncio default fixture loop scope to function to avoid
    # warnings
    config.option.asyncio_default_fixture_loop_scope = "function"

    # Configure Loguru for testing
    logger.remove()  # Remove any existing handlers

    # Configure a unified format for all test logs
    logger.add(
        sys.stdout,
        format=(
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | <cyan>{extra[request_id]}</cyan> | "
            "<cyan>{name}:{function}:{line}</cyan> - <level>{message}</level>"
        ),
        level=os.environ.get("LOG_LEVEL", "INFO").upper(),
        colorize=True,
        backtrace=True,
        diagnose=True
    )

    # Intercept all standard logging to route through Loguru
    class InterceptHandler(logging.Handler):
        def emit(self, record):
            # Get corresponding Loguru level if it exists
            try:
                level = logger.level(record.levelname).name
            except ValueError:
                level = record.levelno

            # Find caller from where the logged message originated
            frame, depth = logging.currentframe(), 2
            while frame and frame.f_code.co_filename == logging.__file__:
                frame = frame.f_back
                depth += 1

            # Extract extras from record
            log_record = logging.LogRecord('', 0, '', 0, '', (), None).__dict__
            extras = {
                key: val for key, val in record.__dict__.items()
                if key not in log_record and key != "args"
            }

            # Ensure request_id is set
            if "request_id" not in extras:
                extras["request_id"] = "NO_REQ_ID"

            logger.opt(depth=depth, exception=record.exc_info).bind(
                **extras
            ).log(level, record.getMessage())

    # Configure root logger to use our handler
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)

    # Set specific levels for noisy libraries
    for noisy_logger in [
        "urllib3",
        "docker",
        "testcontainers",
        "asyncio",
        "sqlalchemy.engine"
    ]:
        logging.getLogger(noisy_logger).setLevel(logging.WARNING)
    
    # Patch logger to ensure request_id is always available
    logger.configure(patcher=lambda record: record["extra"].setdefault(
        "request_id", "NO_REQ_ID"
    ))

    logger.info("Test logging configured with Loguru")