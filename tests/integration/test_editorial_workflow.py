import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import json

from db.models import (
    DraftExercise, DraftExerciseStatus, EditorAccount, EditorRole,
    DraftLearningNodeExercise, LearningNode, Chapter, Subject, Year,
    Exercise, DraftMediaFile, DraftMediaType
)
from tests.utils.editorial_test_utils import create_editor_token


class TestEditorialWorkflowIntegration:
    """Integration tests for complete editorial workflow"""
    
    @pytest.fixture
    def setup_workflow(self, db_session: Session):
        """Set up complete workflow test data"""
        # Create year first (required for Subject)
        from db.models import Year
        year = Year(
            public_id="test-year",
            name="Test Year"
        )
        db_session.add(year)
        db_session.flush()
        
        # Create subject and chapter
        subject = Subject(
            public_id="test-math",
            name="Mathematics",
            year_id=year.id,
            description="Test Mathematics Subject",
            is_active=True
        )
        db_session.add(subject)
        db_session.flush()
        
        chapter = Chapter(
            public_id="test-algebra",
            title="Algebra",
            description="Test Algebra Chapter",
            subject_id=subject.id,
            ordering=1
        )
        
        db_session.add(chapter)
        db_session.flush()
        
        # Create learning node
        from db.models.learning_node import LearningNodeTypeEnum
        node = LearningNode(
            public_id="test-linear-eq",
            title="Linear Equations",
            description="Test Linear Equations Node",
            node_type=LearningNodeTypeEnum.MATH,
            chapter_id=chapter.id,
            ordering=1,
            _content={},
            _data={}
        )
        db_session.add(node)
        db_session.flush()
        
        # Create editors
        editor = EditorAccount(
            public_id="test-editor-001",
            email="<EMAIL>",
            role=EditorRole.EDITOR,
            pwd_hash="hashed"
        )
        admin = EditorAccount(
            public_id="test-admin-001",
            email="<EMAIL>",
            role=EditorRole.ADMIN,
            pwd_hash="hashed"
        )
        db_session.add_all([editor, admin])
        db_session.flush()
        
        # Add scopes
        from db.models import EditorScope
        editor_scope = EditorScope(
            editor_id=editor.id,
            subject_id=subject.id  # Direct foreign key to subject
        )
        admin_scope = EditorScope(
            editor_id=admin.id,
            subject_id=subject.id  # Direct foreign key to subject
        )
        db_session.add_all([editor_scope, admin_scope])
        db_session.flush()  # Ensure scopes are persisted
        
        # Create draft
        draft = DraftExercise(
            public_id="test-draft-001",
            exercise_type="mc-simple",
            difficulty="medium",
            data_json={},
            status=DraftExerciseStatus.NEW
        )
        db_session.add(draft)
        db_session.flush()
        
        # Create association
        assoc = DraftLearningNodeExercise(
            draft_exercise_id=draft.id,
            learning_node_id=node.id
        )
        db_session.add(assoc)
        db_session.commit()
        
        return {
            "subject": subject,
            "chapter": chapter,
            "node": node,
            "editor": editor,
            "admin": admin,
            "draft": draft
        }
    
    def test_complete_editorial_workflow(
        self, client: TestClient, db_session: Session, setup_workflow: dict
    ):
        """Test complete workflow from draft creation to publication"""
        editor = setup_workflow["editor"]
        admin = setup_workflow["admin"]
        draft = setup_workflow["draft"]
        
        # Step 1: Editor logs in
        editor_token = create_editor_token(editor.id, editor_public_id=editor.public_id, email=editor.email, role=editor.role.value)
        editor_headers = {"x-editor-token": editor_token}
        
        # Step 2: Editor lists available drafts
        response = client.get(
            "/api/v1/editorial/editor/drafts",
            headers=editor_headers
        )
        assert response.status_code == 200
        drafts = response.json()["drafts"]
        assert len(drafts) > 0
        assert any(d["id"] == draft.id for d in drafts)
        
        # Step 3: Editor claims the draft
        response = client.post(
            f"/api/v1/editorial/editor/drafts/{draft.id}/claim",
            headers=editor_headers
        )
        assert response.status_code == 200
        assert response.json()["draft"]["status"] == "IN_REVIEW"
        
        # Step 4: Editor updates the draft
        update_data = {
            "data_json": {
                "prompt": "Solve for x: 2x + 5 = 15",
                "options": [
                    {"public_id": "a", "text": "x = 5"},
                    {"public_id": "b", "text": "x = 10"},
                    {"public_id": "c", "text": "x = 7.5"},
                    {"public_id": "d", "text": "x = 2.5"}
                ]
            },
            "solution_json": {
                "correct_answer": {"correct_option_id": ["a"]},
                "solution_steps": [
                    {"text": "Subtract 5 from both sides: 2x = 10"},
                    {"text": "Divide by 2: x = 5"}
                ]
            }
        }
        
        response = client.patch(
            f"/api/v1/editorial/editor/drafts/{draft.id}",
            json=update_data,
            headers=editor_headers
        )
        if response.status_code != 200:
            print(f"Update draft failed with status {response.status_code}: {response.json()}")
        assert response.status_code == 200
        
        # Step 5: Editor accepts the draft
        response = client.post(
            f"/api/v1/editorial/editor/drafts/{draft.id}/accept",
            headers=editor_headers
        )
        assert response.status_code == 200
        assert response.json()["draft"]["status"] == "ACCEPTED_BY_EDITOR"
        
        # Step 6: Admin logs in and reviews
        admin_token = create_editor_token(admin.id, editor_public_id=admin.public_id, email=admin.email, role=admin.role.value)
        admin_headers = {"x-editor-token": admin_token}
        
        # Step 7: Admin publishes the draft
        response = client.post(
            f"/api/v1/editorial/admin/drafts/{draft.public_id}/publish",
            json={"publish_notes": "Good quality exercise"},
            headers=admin_headers
        )
        assert response.status_code == 200
        publish_result = response.json()
        assert publish_result["success"] is True
        assert "exercise_id" in publish_result
        
        # Verify exercise was created
        exercise = db_session.query(Exercise).filter_by(
            id=publish_result["exercise_id"]
        ).first()
        assert exercise is not None
        assert exercise._data["prompt"] == "Solve for x: 2x + 5 = 15"
        
        # Verify draft status
        db_session.refresh(draft)
        assert draft.status == DraftExerciseStatus.PUBLISHED
        assert draft.published_exercise_id == exercise.id  # This should be the database ID
        
        # TODO: Audit trail verification removed - DraftExerciseAudit not implemented
        # The workflow can be verified by checking the draft status changes instead
        assert draft.status == DraftExerciseStatus.PUBLISHED
    
    def test_rejection_workflow(
        self, client: TestClient, db_session: Session, setup_workflow: dict
    ):
        """Test workflow with admin rejection"""
        editor = setup_workflow["editor"]
        admin = setup_workflow["admin"]
        draft = setup_workflow["draft"]
        
        editor_token = create_editor_token(editor.id, editor_public_id=editor.public_id, email=editor.email, role=editor.role.value)
        editor_headers = {"x-editor-token": editor_token}
        admin_token = create_editor_token(admin.id, editor_public_id=admin.public_id, email=admin.email, role=admin.role.value)
        admin_headers = {"x-editor-token": admin_token}
        
        # Editor workflow
        client.post(f"/api/v1/editorial/editor/drafts/{draft.id}/claim", headers=editor_headers)
        client.patch(
            f"/api/v1/editorial/editor/drafts/{draft.id}",
            json={
                "data_json": {"prompt": "Too simple"},
                "solution_json": {"correct_answer": {}}
            },
            headers=editor_headers
        )
        client.post(f"/api/v1/editorial/editor/drafts/{draft.id}/accept", headers=editor_headers)
        
        # Admin rejects
        response = client.post(
            f"/api/v1/editorial/admin/drafts/{draft.id}/reject",
            json={
                "rejection_reason": "Exercise is too simple, needs more complexity",
                "suggested_changes": [
                    "Add more challenging options",
                    "Include step-by-step solution"
                ]
            },
            headers=admin_headers
        )
        
        assert response.status_code == 200
        assert response.json()["draft"]["status"] == "REJECTED_BY_ADMIN"
        
        # Verify draft can be reclaimed by editor
        db_session.refresh(draft)
        assert draft.status == DraftExerciseStatus.REJECTED_BY_ADMIN
        assert draft.reject_reason == "Exercise is too simple, needs more complexity"
    
    def test_concurrent_editor_access(
        self, client: TestClient, db_session: Session, setup_workflow: dict
    ):
        """Test that single-owner model prevents concurrent editing"""
        draft = setup_workflow["draft"]
        
        # Create second editor
        editor2 = EditorAccount(
            public_id="test-editor-002",
            email="<EMAIL>",
            role=EditorRole.EDITOR,
            pwd_hash="hashed"
        )
        db_session.add(editor2)
        db_session.flush()  # Flush to get the ID
        
        # Give scope
        from db.models import EditorScope
        scope = EditorScope(
            editor_id=editor2.id,
            subject_id=setup_workflow["subject"].id  # Direct foreign key to subject
        )
        db_session.add(scope)
        db_session.commit()
        
        # First editor claims
        editor1 = setup_workflow["editor"]
        editor1_token = create_editor_token(editor1.id, editor_public_id=editor1.public_id, email=editor1.email, role=editor1.role.value)
        editor1_headers = {"x-editor-token": editor1_token}
        
        response = client.post(
            f"/api/v1/editorial/editor/drafts/{draft.id}/claim",
            headers=editor1_headers
        )
        assert response.status_code == 200
        
        # Second editor tries to claim
        editor2_token = create_editor_token(editor2.id, editor_public_id=editor2.public_id, email=editor2.email, role=editor2.role.value)
        editor2_headers = {"x-editor-token": editor2_token}
        
        response = client.post(
            f"/api/v1/editorial/editor/drafts/{draft.id}/claim",
            headers=editor2_headers
        )
        assert response.status_code == 400
        assert "already assigned" in response.json()["detail"]
    
    def test_media_workflow(
        self, client: TestClient, db_session: Session, setup_workflow: dict
    ):
        """Test workflow with media files (mocked)"""
        editor = setup_workflow["editor"]
        draft = setup_workflow["draft"]
        
        editor_token = create_editor_token(editor.id, editor_public_id=editor.public_id, email=editor.email, role=editor.role.value)
        editor_headers = {"x-editor-token": editor_token}
        
        # Claim draft
        client.post(f"/api/v1/editorial/editor/drafts/{draft.id}/claim", headers=editor_headers)
        
        # Create mock media record (simulating upload)
        media = DraftMediaFile(
            draft_exercise_id=draft.id,
            media_type=DraftMediaType.IMAGE,
            storage_path="draft/1/image/test.png",
            original_filename="test.png",
            content_type="image/png"
        )
        db_session.add(media)
        db_session.commit()
        
        # List media
        response = client.get(
            f"/api/v1/editorial/editor/drafts/{draft.id}/media",
            headers=editor_headers
        )
        assert response.status_code == 200
        media_list = response.json()
        assert len(media_list) == 1
        assert media_list[0]["original_filename"] == "test.png"