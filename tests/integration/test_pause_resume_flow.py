"""
End-to-end integration tests for subscription pause/resume flow.
"""
import pytest
from datetime import datetime, UTC, timedelta
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
import concurrent.futures

from db.models.subscription import SubscriptionStatusType, SubscriptionPauseStatus, BillingPeriodEnum
from tests.mocks.stripe_subscription_mocks import create_mock_stripe_subscription


class TestPauseResumeFlow:
    """End-to-end tests for complete pause/resume scenarios."""
    
    @pytest.fixture(autouse=True)
    def setup_pause_window(self, mocker):
        """Mock pause window settings."""
        mocker.patch("core.config.settings.settings.SUMMER_PAUSE_START_UTC", "2025-07-15T00:00:00Z")
        mocker.patch("core.config.settings.settings.SUMMER_PAUSE_END_UTC", "2025-08-31T23:59:59Z")
        mocker.patch("core.config.settings.settings.PAUSE_UI_VISIBLE_FROM", "2025-05-01T00:00:00Z")
        mocker.patch("core.config.settings.settings.PAUSE_UI_VISIBLE_TO", "2025-09-01T00:00:00Z")
    
    def _mock_datetime(self, mocker, module_path: str, mock_time: datetime):
        """Helper to properly mock datetime to avoid MagicMock database errors."""
        datetime_mock = mocker.patch(f"{module_path}.datetime")
        datetime_mock.now.return_value = mock_time
        datetime_mock.fromisoformat = datetime.fromisoformat
        datetime_mock.fromtimestamp = datetime.fromtimestamp
        datetime_mock.side_effect = lambda *args, **kwargs: datetime(*args, **kwargs)
        return datetime_mock
    
    @pytest.fixture
    def qstash_headers(self, mocker):
        """Create valid QStash headers with signature."""
        # Mock QStash signature verification to always pass
        mocker.patch("qstash.Receiver.verify", return_value=True)
        
        return {
            "Upstash-Signature": "test_signature",
            "Upstash-Topic": "pause-activation",
            "Content-Type": "application/json"
        }
    
    def test_complete_monthly_pause_resume_flow(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account,
        monthly_subscription,
        auth_headers,
        mock_stripe_subscription_retrieve,
        mock_stripe_subscription_modify,
        mocker
    ):
        """Test complete flow: pause monthly subscription, then resume early."""
        # Step 1: Pause the subscription
        mock_pause_time = datetime(2025, 7, 20, 12, 0, 0, tzinfo=UTC)
        self._mock_datetime(mocker, "api.v1.app.parent.subscription.services.pause_subscription_service", mock_pause_time)
        
        mock_stripe_sub = create_mock_stripe_subscription(
            subscription_id=monthly_subscription.stripe_subscription_id,
            billing_period=BillingPeriodEnum.MONTHLY
        )
        mock_stripe_subscription_retrieve.return_value = mock_stripe_sub
        
        pause_response = client.post(
            "/api/v1/app/parent/subscription/pause",
            json={"subscription_public_id": monthly_subscription.public_id},
            headers=auth_headers
        )
        
        assert pause_response.status_code == 200
        pause_data = pause_response.json()
        assert pause_data["status"] == "paused"
        
        # Verify database state after pause
        db_session.refresh(monthly_subscription)
        assert monthly_subscription.status == SubscriptionStatusType.PAUSED
        
        from db.models import SubscriptionPause
        pause_record = db_session.query(SubscriptionPause).filter_by(
            active_subscription_id=monthly_subscription.id
        ).first()
        assert pause_record is not None
        assert pause_record.status == SubscriptionPauseStatus.PAUSED
        
        # Step 2: Resume the subscription early
        mock_resume_time = datetime(2025, 7, 25, 12, 0, 0, tzinfo=UTC)
        self._mock_datetime(mocker, "api.v1.app.parent.subscription.services.resume_subscription_service", mock_resume_time)
        
        # Don't reset mock - we want to verify both calls
        
        resume_response = client.post(
            "/api/v1/app/parent/subscription/resume",
            json={"subscription_public_id": monthly_subscription.public_id},
            headers=auth_headers
        )
        
        assert resume_response.status_code == 200
        resume_data = resume_response.json()
        assert resume_data["subscription_status"] == "active"
        assert "resumed successfully" in resume_data["message"]
        
        # Verify final state
        db_session.refresh(monthly_subscription)
        db_session.refresh(pause_record)
        assert monthly_subscription.status == SubscriptionStatusType.ACTIVE
        assert pause_record.status == SubscriptionPauseStatus.RESUMED
        assert pause_record.resumed_at == mock_resume_time
        
        # Verify Stripe calls
        assert mock_stripe_subscription_modify.call_count == 2
        # First call: pause with pause_collection
        first_call = mock_stripe_subscription_modify.call_args_list[0]
        assert 'pause_collection' in first_call[1]
        # Second call: resume by clearing pause_collection
        second_call = mock_stripe_subscription_modify.call_args_list[1]
        assert second_call[1]['pause_collection'] == ""
    
    def test_complete_yearly_pause_resume_flow(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account,
        yearly_subscription,
        auth_headers,
        mock_stripe_subscription_retrieve,
        mock_stripe_subscription_modify,
        mocker
    ):
        """Test complete flow: pause yearly subscription with trial extension, then resume early with credit."""
        # Step 1: Pause the yearly subscription
        mock_pause_time = datetime(2025, 7, 20, 12, 0, 0, tzinfo=UTC)
        self._mock_datetime(mocker, "api.v1.app.parent.subscription.services.pause_subscription_service", mock_pause_time)
        
        original_period_end = datetime(2025, 12, 31, tzinfo=UTC)
        mock_stripe_sub = create_mock_stripe_subscription(
            subscription_id=yearly_subscription.stripe_subscription_id,
            current_period_end=original_period_end,
            billing_period=BillingPeriodEnum.YEARLY
        )
        mock_stripe_subscription_retrieve.return_value = mock_stripe_sub
        
        pause_response = client.post(
            "/api/v1/app/parent/subscription/pause",
            json={"subscription_public_id": yearly_subscription.public_id},
            headers=auth_headers
        )
        
        assert pause_response.status_code == 200
        pause_data = pause_response.json()
        assert pause_data["status"] == "paused"
        assert "billing has been extended" in pause_data["message"]
        
        # Verify subscription remains ACTIVE (yearly doesn't actually pause)
        db_session.refresh(yearly_subscription)
        assert yearly_subscription.status == SubscriptionStatusType.ACTIVE
        
        # Verify pause record created
        from db.models import SubscriptionPause
        pause_record = db_session.query(SubscriptionPause).filter_by(
            active_subscription_id=yearly_subscription.id
        ).first()
        assert pause_record is not None
        assert pause_record.status == SubscriptionPauseStatus.PAUSED
        assert pause_record.new_period_end > pause_record.original_period_end
        
        # Verify Stripe was called with trial_end extension
        assert mock_stripe_subscription_modify.call_count == 1
        first_call = mock_stripe_subscription_modify.call_args_list[0]
        assert 'trial_end' in first_call[1]
        assert first_call[1]['proration_behavior'] == 'none'
        
        # Step 2: Resume early to get credit
        mock_resume_time = datetime(2025, 7, 30, 12, 0, 0, tzinfo=UTC)  # 10 days after pause
        self._mock_datetime(mocker, "api.v1.app.parent.subscription.services.resume_subscription_service", mock_resume_time)
        
        mock_stripe_subscription_modify.reset_mock()
        
        resume_response = client.post(
            "/api/v1/app/parent/subscription/resume",
            json={"subscription_public_id": yearly_subscription.public_id},
            headers=auth_headers
        )
        
        assert resume_response.status_code == 200
        resume_data = resume_response.json()
        assert resume_data["subscription_status"] == "active"
        assert "credited 10 days" in resume_data["message"]
        
        # Verify pause record updated
        db_session.refresh(pause_record)
        assert pause_record.status == SubscriptionPauseStatus.RESUMED
        assert pause_record.resumed_at == mock_resume_time
        
        # Verify trial_end was adjusted for actual pause duration
        assert mock_stripe_subscription_modify.call_count == 1
        resume_call = mock_stripe_subscription_modify.call_args_list[0]
        assert 'trial_end' in resume_call[1]
        
        # Calculate expected trial_end (original + 10 days actual pause)
        actual_pause_duration = (mock_resume_time - pause_record.start_date).total_seconds()
        expected_trial_end = original_period_end + timedelta(seconds=actual_pause_duration)
        actual_trial_end = datetime.fromtimestamp(resume_call[1]['trial_end'], tz=UTC)
        assert abs((actual_trial_end - expected_trial_end).total_seconds()) < 60
    
    def test_scheduled_pause_activation_flow(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account,
        monthly_subscription,
        auth_headers,
        qstash_headers,
        mock_stripe_subscription_retrieve,
        mock_stripe_subscription_modify,
        mock_qstash_client,
        mocker
    ):
        """Test flow: schedule pause, QStash activates it, then resume."""
        # Step 1: Schedule a pause for future
        mock_schedule_time = datetime(2025, 5, 15, 12, 0, 0, tzinfo=UTC)
        self._mock_datetime(mocker, "api.v1.app.parent.subscription.services.pause_subscription_service", mock_schedule_time)
        
        mock_stripe_sub = create_mock_stripe_subscription(
            subscription_id=monthly_subscription.stripe_subscription_id
        )
        mock_stripe_subscription_retrieve.return_value = mock_stripe_sub
        
        schedule_response = client.post(
            "/api/v1/app/parent/subscription/pause",
            json={"subscription_public_id": monthly_subscription.public_id},
            headers=auth_headers
        )
        
        assert schedule_response.status_code == 200
        schedule_data = schedule_response.json()
        assert schedule_data["status"] == "scheduled"
        
        # Verify QStash was called
        assert mock_qstash_client.post.called
        
        # Get the scheduled pause record
        from db.models import SubscriptionPause
        pause_record = db_session.query(SubscriptionPause).filter_by(
            active_subscription_id=monthly_subscription.id
        ).first()
        assert pause_record.status == SubscriptionPauseStatus.SCHEDULED
        assert pause_record.qstash_message_id is not None
        
        # Step 2: Simulate QStash activating the pause
        mock_activation_time = datetime(2025, 7, 15, 0, 0, 0, tzinfo=UTC)
        self._mock_datetime(mocker, "api.v1.app.parent.subscription.services.activate_pause_service", mock_activation_time)
        
        mock_stripe_subscription_modify.reset_mock()
        
        activation_response = client.post(
            "/api/v1/tasks/internal/activate-pause",
            headers=qstash_headers,
            json={
                "active_subscription_id": monthly_subscription.id,
                "subscription_pause_public_id": pause_record.public_id,
                "action": "activate_pause"
            }
        )
        
        assert activation_response.status_code == 200
        
        # Verify pause is now active
        db_session.refresh(monthly_subscription)
        db_session.refresh(pause_record)
        assert monthly_subscription.status == SubscriptionStatusType.PAUSED
        assert pause_record.status == SubscriptionPauseStatus.PAUSED
        
        # Step 3: Resume the subscription
        mock_resume_time = datetime(2025, 7, 25, 12, 0, 0, tzinfo=UTC)
        self._mock_datetime(mocker, "api.v1.app.parent.subscription.services.resume_subscription_service", mock_resume_time)
        
        resume_response = client.post(
            "/api/v1/app/parent/subscription/resume",
            json={"subscription_public_id": monthly_subscription.public_id},
            headers=auth_headers
        )
        
        assert resume_response.status_code == 200
        
        # Verify final state
        db_session.refresh(monthly_subscription)
        db_session.refresh(pause_record)
        assert monthly_subscription.status == SubscriptionStatusType.ACTIVE
        assert pause_record.status == SubscriptionPauseStatus.RESUMED
    
    def test_cancel_scheduled_pause_flow(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account,
        monthly_subscription,
        auth_headers,
        mock_stripe_subscription_retrieve,
        mock_qstash_client,
        mocker
    ):
        """Test flow: schedule pause, then cancel it before activation."""
        # Step 1: Schedule a pause
        mock_schedule_time = datetime(2025, 5, 15, 12, 0, 0, tzinfo=UTC)
        self._mock_datetime(mocker, "api.v1.app.parent.subscription.services.pause_subscription_service", mock_schedule_time)
        
        mock_stripe_sub = create_mock_stripe_subscription(
            subscription_id=monthly_subscription.stripe_subscription_id
        )
        mock_stripe_subscription_retrieve.return_value = mock_stripe_sub
        
        schedule_response = client.post(
            "/api/v1/app/parent/subscription/pause",
            json={"subscription_public_id": monthly_subscription.public_id},
            headers=auth_headers
        )
        
        assert schedule_response.status_code == 200
        assert schedule_response.json()["status"] == "scheduled"
        
        # Verify pause record created
        from db.models import SubscriptionPause
        pause_count_before = db_session.query(SubscriptionPause).filter_by(
            active_subscription_id=monthly_subscription.id
        ).count()
        assert pause_count_before == 1
        
        # Get the pause record to get its public_id
        pause_record = db_session.query(SubscriptionPause).filter_by(
            active_subscription_id=monthly_subscription.id
        ).first()
        
        # Step 2: Cancel the scheduled pause
        cancel_response = client.post(
            "/api/v1/app/parent/subscription/cancel-scheduled-pause",
            json={
                "subscription_public_id": monthly_subscription.public_id,
                "pause_public_id": pause_record.public_id
            },
            headers=auth_headers
        )
        
        assert cancel_response.status_code == 200
        assert "cancelled" in cancel_response.json()["message"]
        
        # Verify QStash delete was called
        assert mock_qstash_client.delete.called
        
        # Verify pause record marked as canceled
        pause_after = db_session.query(SubscriptionPause).filter_by(
            active_subscription_id=monthly_subscription.id
        ).first()
        assert pause_after is not None
        assert pause_after.status == SubscriptionPauseStatus.CANCELED
        
        # Verify subscription remains active
        db_session.refresh(monthly_subscription)
        assert monthly_subscription.status == SubscriptionStatusType.ACTIVE
    
    def test_multiple_pause_attempts_blocked(
        self,
        client: TestClient,
        db_session: Session,
        test_parent_account,
        monthly_subscription,
        auth_headers,
        mock_stripe_subscription_retrieve,
        mock_stripe_subscription_modify,
        mocker
    ):
        """Test that multiple pause attempts are properly blocked."""
        # First pause
        mock_time = datetime(2025, 7, 20, 12, 0, 0, tzinfo=UTC)
        self._mock_datetime(mocker, "api.v1.app.parent.subscription.services.pause_subscription_service", mock_time)
        
        mock_stripe_sub = create_mock_stripe_subscription(
            subscription_id=monthly_subscription.stripe_subscription_id
        )
        mock_stripe_subscription_retrieve.return_value = mock_stripe_sub
        
        first_response = client.post(
            "/api/v1/app/parent/subscription/pause",
            json={"subscription_public_id": monthly_subscription.public_id},
            headers=auth_headers
        )
        assert first_response.status_code == 200
        
        # Mock Stripe to show subscription is paused
        mock_stripe_sub_paused = create_mock_stripe_subscription(
            subscription_id=monthly_subscription.stripe_subscription_id,
            pause_collection={'behavior': 'mark_uncollectible'}
        )
        mock_stripe_subscription_retrieve.return_value = mock_stripe_sub_paused
        
        # Second pause attempt should fail
        second_response = client.post(
            "/api/v1/app/parent/subscription/pause",
            json={"subscription_public_id": monthly_subscription.public_id},
            headers=auth_headers
        )
        assert second_response.status_code == 400
        data = second_response.json()
        assert "cannot be paused" in data.get("message", data.get("detail", "")).lower()
        
        # Verify only one pause record exists
        from db.models import SubscriptionPause
        pause_count = db_session.query(SubscriptionPause).filter_by(
            active_subscription_id=monthly_subscription.id
        ).count()
        assert pause_count == 1
    
    def test_pause_window_enforcement(
        self,
        client: TestClient,
        test_parent_account,
        monthly_subscription,
        auth_headers,
        mocker
    ):
        """Test that pause requests outside the visibility window are rejected."""
        # Mock time to be outside the pause window
        mock_time = datetime(2025, 10, 15, 12, 0, 0, tzinfo=UTC)
        self._mock_datetime(mocker, "api.v1.app.parent.subscription.services.pause_subscription_service", mock_time)
        
        # Override pause window to be closed
        mocker.patch("core.config.settings.settings.PAUSE_UI_VISIBLE_TO", "2025-09-01T00:00:00Z")
        
        response = client.post(
            "/api/v1/app/parent/subscription/pause",
            json={"subscription_public_id": monthly_subscription.public_id},
            headers=auth_headers
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "not available at this time" in data.get("message", data.get("detail", ""))