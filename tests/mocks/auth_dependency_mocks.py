"""
Mock auth dependencies for testing.
"""
from typing import Optional, List, Dict, Union
from sqlalchemy.orm import Session
from sqlalchemy import select

from dependencies.auth_dependencies.base_auth_dependency import (
    AuthDependencyResponse, UserType
)
from dependencies.auth_dependencies.subscription_dependency import SubscriptionInfo
from db.models import Account, ChildAccount
from tests.data import constants


class MockAuthDependency:
    """Mock auth dependency that returns predefined accounts for testing."""
    
    def __init__(self, user_type: UserType = UserType.parent, account_public_id: Optional[str] = None):
        self.user_type = user_type
        self.account_public_id = account_public_id or (
            constants.MOCK_PARENT_PUBLIC_ID if user_type == UserType.parent 
            else constants.MOCK_CHILD_PUBLIC_ID
        )
        self.required = True
    
    def __call__(self, db: Session) -> AuthDependencyResponse:
        """Return a mock auth response with the configured account."""
        if self.user_type == UserType.parent:
            stmt = select(Account).where(Account.public_id == self.account_public_id)
            account = db.execute(stmt).scalar_one_or_none()
            if not account:
                raise ValueError(f"Mock parent account {self.account_public_id} not found in test database")
        else:
            stmt = select(ChildAccount).where(ChildAccount.public_id == self.account_public_id)
            account = db.execute(stmt).scalar_one_or_none()
            if not account:
                raise ValueError(f"Mock child account {self.account_public_id} not found in test database")
        
        return AuthDependencyResponse(
            access_token="mock_token",
            account_id=account.id,
            account_public_id=account.public_id,
            user_type=self.user_type,
            account=account
        )


class MockSubscriptionDependency:
    """Mock subscription dependency that returns a subscribed state for testing."""
    
    def __init__(self, user_type: UserType = UserType.parent, account_public_id: Optional[str] = None, 
                 is_subscribed: bool = True, trial_active: bool = False):
        self.user_type = user_type
        self.account_public_id = account_public_id or (
            constants.MOCK_PARENT_PUBLIC_ID if user_type == UserType.parent 
            else constants.MOCK_CHILD_PUBLIC_ID
        )
        self.is_subscribed = is_subscribed
        self.trial_active = trial_active
        self.required = True
    
    def __call__(self, db: Session) -> SubscriptionInfo:
        """Return a mock subscription info with the configured account."""
        # First get the auth info
        mock_auth = MockAuthDependency(self.user_type, self.account_public_id)
        auth_response = mock_auth(db)
        
        # Return subscription info with test subjects
        return SubscriptionInfo(
            access_token=auth_response.access_token,
            account_id=auth_response.account_id,
            account_public_id=auth_response.account_public_id,
            user_type=auth_response.user_type,
            account=auth_response.account,
            is_subscribed=self.is_subscribed,
            trial_active=self.trial_active,
            subscribed_subjects=[
                {
                    "public_id": "subj-1",
                    "name": "Test Subject 1",
                    "year": {"public_id": "year-1", "name": "Test Year 1"}
                }
            ]
        )


def get_mock_parent_auth() -> MockAuthDependency:
    """Get a mock auth dependency that returns the default parent account."""
    return MockAuthDependency(user_type=UserType.parent)


def get_mock_child_auth() -> MockAuthDependency:
    """Get a mock auth dependency that returns the default child account."""
    return MockAuthDependency(user_type=UserType.child)


def get_mock_subscription(is_subscribed: bool = True, trial_active: bool = False) -> MockSubscriptionDependency:
    """Get a mock subscription dependency with configurable subscription state."""
    return MockSubscriptionDependency(is_subscribed=is_subscribed, trial_active=trial_active)