"""
Mock fixtures and utilities for Stripe subscription testing.
"""
import pytest
from unittest.mock import MagicMock
from datetime import datetime, UTC, timedelta
from db.models.subscription import BillingPeriodEnum, SubscriptionStatusType


def create_mock_stripe_subscription(
    subscription_id: str = "sub_test123",
    customer_id: str = "cus_test123",
    status: str = "active",
    current_period_end: datetime = None,
    trial_end: int = None,
    pause_collection: dict = None,
    billing_period: BillingPeriodEnum = BillingPeriodEnum.MONTHLY
):
    """Create a mock Stripe subscription object."""
    if current_period_end is None:
        current_period_end = datetime.now(UTC) + timedelta(days=30)
    
    mock_sub = MagicMock()
    mock_sub.id = subscription_id
    mock_sub.customer = customer_id
    mock_sub.status = status
    mock_sub.current_period_end = int(current_period_end.timestamp())
    mock_sub.trial_end = trial_end
    mock_sub.pause_collection = pause_collection
    
    # Mock subscription items
    price_id = f"price_test_{billing_period.value}"
    mock_sub.items = MagicMock()
    mock_sub.items.data = [
        MagicMock(
            id=f"si_test_{subscription_id}",
            price=MagicMock(id=price_id)
        )
    ]
    
    # Add dict-like behavior
    mock_sub.__getitem__ = lambda self, key: getattr(self, key)
    mock_sub.get = lambda self, key, default=None: getattr(self, key, default)
    
    return mock_sub


@pytest.fixture
def mock_stripe_subscription_retrieve(mocker):
    """Mock stripe.Subscription.retrieve."""
    mock = mocker.patch("stripe.Subscription.retrieve")
    mock.return_value = create_mock_stripe_subscription()
    return mock


@pytest.fixture
def mock_stripe_subscription_modify(mocker):
    """Mock stripe.Subscription.modify."""
    def modify_subscription(sub_id, **kwargs):
        # Create a subscription that reflects the modifications
        sub = create_mock_stripe_subscription(subscription_id=sub_id)
        
        # Apply modifications
        if 'pause_collection' in kwargs:
            if kwargs['pause_collection'] == "":
                sub.pause_collection = None
            else:
                sub.pause_collection = kwargs['pause_collection']
        
        if 'trial_end' in kwargs:
            sub.trial_end = kwargs['trial_end']
            
        return sub
    
    mock = mocker.patch("stripe.Subscription.modify", side_effect=modify_subscription)
    return mock


@pytest.fixture
def mock_stripe_price_retrieve(mocker):
    """Mock stripe.Price.retrieve."""
    def retrieve_price(price_id):
        mock_price = MagicMock()
        mock_price.id = price_id
        
        # Determine billing period from price ID
        if 'monthly' in price_id:
            mock_price.recurring = {'interval': 'month'}
        else:
            mock_price.recurring = {'interval': 'year'}
            
        return mock_price
    
    mock = mocker.patch("stripe.Price.retrieve", side_effect=retrieve_price)
    return mock