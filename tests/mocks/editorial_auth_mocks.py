"""
Mock editorial auth dependencies for testing.
"""
from typing import Op<PERSON>
from sqlalchemy.orm import Session
from sqlalchemy import select

from dependencies.auth_dependencies.editorial_auth_dependency import EditorAuthResponse
from db.models import EditorAccount, EditorRole
from tests.fixtures.editorial_fixtures import EditorFixtures


class MockEditorAuthDependency:
    """Mock editor auth dependency that returns predefined editor accounts for testing."""
    
    def __init__(self, role: EditorRole = EditorRole.EDITOR, editor_email: str = "<EMAIL>"):
        self.role = role
        self.editor_email = editor_email
        self.required_role = None
    
    def __call__(self, db: Session) -> EditorAuthResponse:
        """Return a mock editor auth response."""
        # Try to get existing editor from DB first
        stmt = select(EditorAccount).where(
            EditorAccount.email == self.editor_email,
            EditorAccount.is_active == True
        )
        editor = db.execute(stmt).scalar_one_or_none()
        
        # If not found, create one
        if not editor:
            editor = EditorFixtures.create_editor_account(
                db,
                email=self.editor_email,
                role=self.role
            )
        
        return EditorAuthResponse(
            editor_id=editor.id,
            editor_public_id=editor.public_id,
            email=editor.email,
            role=editor.role,
            editor=editor
        )


class MockAdminAuthDependency(MockEditorAuthDependency):
    """Mock admin auth dependency."""
    
    def __init__(self, editor_email: str = "<EMAIL>"):
        super().__init__(role=EditorRole.ADMIN, editor_email=editor_email)
        self.required_role = EditorRole.ADMIN


def get_mock_editor_auth(email: str = "<EMAIL>") -> MockEditorAuthDependency:
    """Get a mock editor auth dependency."""
    return MockEditorAuthDependency(editor_email=email)


def get_mock_admin_auth(email: str = "<EMAIL>") -> MockAdminAuthDependency:
    """Get a mock admin auth dependency."""
    return MockAdminAuthDependency(editor_email=email)