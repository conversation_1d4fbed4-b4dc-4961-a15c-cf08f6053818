"""
Mock webhook authentication for testing.
"""
from fastapi import Request
from unittest.mock import AsyncMock


class MockWebhookAuth:
    """Mock webhook signature verification that always passes."""
    
    async def __call__(self, request: Request):
        """Mock verification that always succeeds."""
        return True


async def mock_verify_qstash_signature(request: Request):
    """Mock QStash signature verification for tests."""
    # Always pass verification in tests
    return None


def get_mock_webhook_auth():
    """Get a mock webhook auth dependency."""
    return MockWebhookAuth()