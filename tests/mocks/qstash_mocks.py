"""
Mock fixtures and utilities for QStash testing.
"""
import pytest
from unittest.mock import MagicMock, Mock
import json
import uuid


def create_mock_qstash_response(
    status_code: int = 202,
    message_id: str = None
):
    """Create a mock QStash API response."""
    if message_id is None:
        message_id = f"msg_{uuid.uuid4().hex[:16]}"
    
    mock_response = Mock()
    mock_response.status_code = status_code
    mock_response.json.return_value = {"messageId": message_id}
    mock_response.text = json.dumps({"messageId": message_id})
    
    return mock_response


@pytest.fixture
def mock_qstash_client(mocker):
    """Mock httpx.Client for QStash requests."""
    mock_client = MagicMock()
    mock_response = create_mock_qstash_response()
    
    # Mock POST for scheduling
    mock_client.post.return_value = mock_response
    
    # Mock DELETE for cancellation
    mock_delete_response = Mock()
    mock_delete_response.status_code = 200
    mock_client.delete.return_value = mock_delete_response
    
    # Mock the context manager
    mock_client_cm = MagicMock()
    mock_client_cm.__enter__.return_value = mock_client
    mock_client_cm.__exit__.return_value = None
    
    mock = mocker.patch("httpx.Client", return_value=mock_client_cm)
    return mock_client


@pytest.fixture
def mock_qstash_scheduling_success(mocker):
    """Mock successful QStash scheduling."""
    def mock_schedule(*args, **kwargs):
        return f"msg_{uuid.uuid4().hex[:16]}"
    
    mocker.patch(
        "api.v1.app.parent.subscription.services.pause_subscription_service.schedule_pause_activation",
        side_effect=mock_schedule
    )


@pytest.fixture
def mock_qstash_scheduling_failure(mocker):
    """Mock failed QStash scheduling."""
    mocker.patch(
        "api.v1.app.parent.subscription.services.pause_subscription_service.schedule_pause_activation",
        return_value=""
    )


@pytest.fixture
def mock_qstash_signature_valid(mocker):
    """Mock valid QStash signature verification."""
    mock_receiver = MagicMock()
    mock_receiver.verify.return_value = True
    
    mocker.patch("qstash.Receiver", return_value=mock_receiver)
    return mock_receiver


@pytest.fixture
def mock_qstash_signature_invalid(mocker):
    """Mock invalid QStash signature verification."""
    mock_receiver = MagicMock()
    mock_receiver.verify.side_effect = Exception("Invalid signature")
    
    mocker.patch("qstash.Receiver", return_value=mock_receiver)
    return mock_receiver