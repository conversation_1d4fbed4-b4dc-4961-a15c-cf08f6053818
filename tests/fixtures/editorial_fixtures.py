import pytest
from datetime import datetime, UTC, timedelta
from sqlalchemy.orm import Session
import uuid
import bcrypt
from typing import List, Dict, Any

from db.models import (
    EditorAccount, EditorScope, EditorRole,
    DraftExercise, DraftExerciseStatus, DraftLearningNodeExercise,
    DraftMediaFile, DraftMediaType,
    PublishBatch,
    Subject, Chapter, LearningNode, Exercise,
    ImageFile, AudioFile
)

class EditorFixtures:
    """Helper class to create test editor accounts"""
    
    @staticmethod
    def create_editor_account(
        db: Session,
        email: str = None,
        password: str = "Test123!",
        role: EditorRole = EditorRole.EDITOR,
        is_active: bool = True
    ) -> EditorAccount:
        """Create a test editor account"""
        if email is None:
            email = f"editor_{uuid.uuid4().hex[:8]}@test.com"
        
        pwd_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        editor = EditorAccount(
            email=email,
            pwd_hash=pwd_hash,
            role=role,
            is_active=is_active
        )
        db.add(editor)
        db.commit()
        return editor
    
    @staticmethod
    def create_editor_scope(
        db: Session,
        editor: EditorAccount,
        subject: Subject = None,
        chapter: Chapter = None,
        learning_node: LearningNode = None
    ) -> EditorScope:
        """Create an editor scope"""
        scope = EditorScope(
            editor_id=editor.id,
            subject_id=subject.id if subject else None,
            chapter_id=chapter.id if chapter else None,
            learning_node_id=learning_node.id if learning_node else None
        )
        db.add(scope)
        db.commit()
        return scope

class DraftFixtures:
    """Helper class to create test draft exercises"""
    
    @staticmethod
    def create_draft_exercise(
        db: Session,
        public_id: str = None,
        exercise_type: str = "mc-simple",
        difficulty: str = "medium",
        data: Dict[str, Any] = None,
        solution: Dict[str, Any] = None,
        status: DraftExerciseStatus = DraftExerciseStatus.NEW,
        assigned_editor: EditorAccount = None,
        learning_node: LearningNode = None
    ) -> DraftExercise:
        """Create a test draft exercise"""
        if public_id is None:
            public_id = f"draft_{uuid.uuid4().hex[:8]}"
        
        if data is None:
            data = {
                "prompt": "Test question?",
                "options": [
                    {"public_id": "opt1", "text": "Option 1"},
                    {"public_id": "opt2", "text": "Option 2"}
                ]
            }
        
        if solution is None:
            solution = {
                "correct_answer": {"correct_option_id": ["opt1"]}
            }
        
        draft = DraftExercise(
            public_id=public_id,
            exercise_type=exercise_type,
            difficulty=difficulty,
            data_json=data,
            solution_json=solution,
            status=status,
            assigned_editor_id=assigned_editor.id if assigned_editor else None
        )
        db.add(draft)
        db.flush()
        
        # Create learning node association if provided
        if learning_node:
            association = DraftLearningNodeExercise(
                learning_node_id=learning_node.id,
                draft_exercise_id=draft.id
            )
            db.add(association)
        
        db.commit()
        return draft
    
    @staticmethod
    def create_draft_media(
        db: Session,
        draft_exercise: DraftExercise,
        media_media_type: DraftMediaType = DraftMediaType.IMAGE,
        storage_path: str = None,
        media_metadata: Dict[str, Any] = None
    ) -> DraftMediaFile:
        """Create a test draft media file"""
        if storage_path is None:
            storage_path = f"drafts/{'exercise_images' if media_media_type == DraftMediaType.IMAGE else 'audio_files'}/{uuid.uuid4().hex}.png"
        
        if media_metadata is None:
            if media_media_type == DraftMediaType.IMAGE:
                media_metadata = {"width": 800, "height": 600, "file_size": 102400}
            else:
                media_metadata = {"duration": 3.5, "bitrate": 128000}
        
        media = DraftMediaFile(
            public_id=f"media_{uuid.uuid4().hex[:8]}",
            media_type=media_media_type,
            storage_path=storage_path,
            content_type="image/png" if media_media_type == DraftMediaType.IMAGE else "audio/mp3",
            original_filename=f"test_file.{'png' if media_media_type == DraftMediaType.IMAGE else 'mp3'}",
            media_metadata=media_metadata,
            draft_exercise_id=draft_exercise.id
        )
        db.add(media)
        db.commit()
        return media



# Pytest fixtures
@pytest.fixture
def editor_account(db_session: Session) -> EditorAccount:
    """Create a test editor account with math subject scope"""
    editor = EditorFixtures.create_editor_account(db_session)
    # Add math scope to the editor after getting math subject
    subject = db_session.query(Subject).filter_by(public_id="subj-1").first()
    if subject:
        EditorFixtures.create_editor_scope(db_session, editor, subject=subject)
    return editor

@pytest.fixture
def admin_account(db_session: Session) -> EditorAccount:
    """Create a test admin account with math subject scope"""
    admin = EditorFixtures.create_editor_account(
        db_session,
        email="<EMAIL>",
        role=EditorRole.ADMIN
    )
    # Add math scope to the admin (even though admins have access to everything)
    subject = db_session.query(Subject).filter_by(public_id="subj-1").first()
    if subject:
        EditorFixtures.create_editor_scope(db_session, admin, subject=subject)
    return admin

@pytest.fixture
def another_editor(db_session: Session) -> EditorAccount:
    """Create another test editor account"""
    return EditorFixtures.create_editor_account(
        db_session,
        email="<EMAIL>"
    )

@pytest.fixture
def draft_exercise_new(db_session: Session, equation_node: LearningNode) -> DraftExercise:
    """Create a new draft exercise with learning node association"""
    return DraftFixtures.create_draft_exercise(db_session, learning_node=equation_node)

@pytest.fixture
def draft_exercise_in_review(db_session: Session, editor_account: EditorAccount, equation_node: LearningNode) -> DraftExercise:
    """Create a draft exercise in review with learning node association"""
    return DraftFixtures.create_draft_exercise(
        db_session,
        status=DraftExerciseStatus.IN_REVIEW,
        assigned_editor=editor_account,
        learning_node=equation_node
    )

@pytest.fixture
def draft_exercise_accepted(db_session: Session, editor_account: EditorAccount, equation_node: LearningNode) -> DraftExercise:
    """Create an accepted draft exercise with learning node association"""
    return DraftFixtures.create_draft_exercise(
        db_session,
        status=DraftExerciseStatus.ACCEPTED_BY_EDITOR,
        assigned_editor=editor_account,
        learning_node=equation_node
    )

@pytest.fixture
def equation_node(db_session: Session) -> LearningNode:
    """Get the math learning node from the seeded test data"""
    from db.models import LearningNode
    node = db_session.query(LearningNode).filter_by(public_id="ln-math-1").first()
    if not node:
        raise ValueError("Math learning node not found. Make sure test data is seeded properly.")
    return node

@pytest.fixture
def math_subject(db_session: Session) -> Subject:
    """Get the math subject from the seeded test data"""
    subject = db_session.query(Subject).filter_by(public_id="subj-1").first()
    if not subject:
        raise ValueError("Math subject not found. Make sure test data is seeded properly.")
    return subject

@pytest.fixture
def mock_r2_client(mocker):
    """Mock R2 (S3) client for media operations"""
    mock_client = mocker.Mock()
    
    # Mock successful uploads
    mock_client.put_object.return_value = {"ETag": "mock-etag"}
    
    # Mock successful copies
    mock_client.copy_object.return_value = {"CopyObjectResult": {"ETag": "mock-etag"}}
    
    # Mock successful deletes
    mock_client.delete_object.return_value = {"DeleteMarker": True}
    
    # Mock head_object for existence checks
    mock_client.head_object.return_value = {
        "ContentLength": 102400,
        "ContentType": "image/png"
    }
    
    return mock_client


def create_draft_without_scope(db: Session) -> DraftExercise:
    """Create a draft exercise in a learning node that no test editor has scope for"""
    from db.models import Year
    
    # Get or create a test year
    year = db.query(Year).first()
    if not year:
        year = Year(
            public_id=f"year-test-{uuid.uuid4().hex[:8]}",
            name="Test Year"
        )
        db.add(year)
        db.flush()
    
    # Create a new subject that test editors don't have scope for
    subject = Subject(
        public_id=f"subj-test-{uuid.uuid4().hex[:8]}",
        name="Test Subject No Scope",
        year_id=year.id,
        description="Subject for testing no scope access"
    )
    db.add(subject)
    db.flush()
    
    # Create a chapter in that subject
    chapter = Chapter(
        public_id=f"ch-test-{uuid.uuid4().hex[:8]}",
        title="Test Chapter No Scope",
        subject_id=subject.id,
        ordering=1
    )
    db.add(chapter)
    db.flush()
    
    # Create a learning node in that chapter
    from db.models.learning_node import LearningNodeTypeEnum
    node = LearningNode(
        public_id=f"ln-test-{uuid.uuid4().hex[:8]}",
        title="Test Node No Scope",
        chapter_id=chapter.id,
        node_type=LearningNodeTypeEnum.MATH,
        _content={"title": "Test Math Content", "body": "Test content"},
        ordering=1
    )
    db.add(node)
    db.flush()
    
    # Create a draft in that learning node
    draft = DraftFixtures.create_draft_exercise(
        db=db,
        learning_node=node,
        status=DraftExerciseStatus.NEW
    )
    
    return draft