"""Test constants for the application."""
# User for Auth

# Parent

AUTH_PARENT_EMAIL = "<EMAIL>"
AUTH_PARENT_PIN = "123456"
AUTH_PARENT_PUBLIC_ID = "auth_parent_public_id"
AUTH_PARENT_STRIPE_CUSTOMER_ID = "auth_parent_stripe_customer_id"
AUTH_PARENT_LANGUAGE = "lu"
AUTH_PARENT_VERIFICATION_CODE = "123456"
AUTH_PARENT_PIN_HASH = "123456"

# Child

AUTH_CHILD_EMAIL = "<EMAIL>"
AUTH_CHILD_PIN = "1234"
AUTH_CHILD_NAME = "Test Child"
AUTH_CHILD_LANGUAGE = "lu"
AUTH_CHILD_PUBLIC_ID = "auth_child_public_id"
AUTH_CHILD_VERIFICATION_CODE = "123456"


# Used for mock data

MOCK_PARENT_EMAIL = "<EMAIL>"
MOCK_PARENT_PIN = "123456"
MOCK_PARENT_PUBLIC_ID = "mock_parent_public_id"
MOCK_PARENT_STRIPE_CUSTOMER_ID = "mock_parent_stripe_customer_id"
MOCK_PARENT_LANGUAGE = "lu"
MOCK_PARENT_VERIFICATION_CODE = "123456"
MOCK_PARENT_PIN_HASH = "123456"
MOCK_STRIPE_CUSTOMER_ID = "mock_parent_stripe_customer_id"
# Child

MOCK_CHILD_EMAIL = "<EMAIL>"
MOCK_CHILD_PIN = "1234"
MOCK_CHILD_NAME = "Test Child"
MOCK_CHILD_LANGUAGE = "lu"
MOCK_CHILD_PUBLIC_ID = "mock_child_public_id"
MOCK_CHILD_VERIFICATION_CODE = "123456"