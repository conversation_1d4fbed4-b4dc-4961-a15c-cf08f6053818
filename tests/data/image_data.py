# tests/data/image_data.py
"""Mock data for ImageFile entries"""

from db.models.media import ImageFileTypeEnum

# Mock ImageFile entries that would be created in the database
MOCK_IMAGE_FILES_DATA = [
    {
        "public_id": "img-cat-photo-1",
        "storage_path": "exercise_images/animals",
        "mime_type": "image/jpeg",
        "width": 400,
        "height": 300,
        "file_size_bytes": 45000,
        "alt_text": "A cat sitting and looking at camera",
        "description": "Cat image for multiple choice exercise",
        "image_type": ImageFileTypeEnum.EXERCISE_OPTION,
        # Original URL for reference (not stored in DB): https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba
    },
    {
        "public_id": "img-dog-photo-1",
        "storage_path": "exercise_images/animals",
        "mime_type": "image/jpeg", 
        "width": 400,
        "height": 300,
        "file_size_bytes": 52000,
        "alt_text": "A golden retriever sitting outdoors",
        "description": "Dog image for multiple choice exercise",
        "image_type": ImageFileTypeEnum.EXERCISE_OPTION,
        # Original URL for reference: https://images.unsplash.com/photo-1552053831-71594a27632d
    },
    {
        "public_id": "img-bird-photo-1",
        "storage_path": "exercise_images/animals",
        "mime_type": "image/jpeg",
        "width": 400,
        "height": 300,
        "file_size_bytes": 38000,
        "alt_text": "A colorful bird perched on a branch",
        "description": "Bird image for multiple choice exercise",
        "image_type": ImageFileTypeEnum.EXERCISE_OPTION,
        # Original URL for reference: https://images.unsplash.com/photo-1444464666168-49d633b86797
    },
    {
        "public_id": "img-fish-photo-1",
        "storage_path": "exercise_images/animals",
        "mime_type": "image/jpeg",
        "width": 400,
        "height": 300,
        "file_size_bytes": 41000,
        "alt_text": "A tropical fish swimming",
        "description": "Fish image for multiple choice exercise",
        "image_type": ImageFileTypeEnum.EXERCISE_OPTION,
        # Original URL for reference: https://images.unsplash.com/photo-1544551763-46a013bb70d5
    },
    # Add more images as needed for other exercises
    {
        "public_id": "img-math-diagram-1",
        "storage_path": "exercise_images/math",
        "mime_type": "image/png",
        "width": 600,
        "height": 400,
        "file_size_bytes": 25000,
        "alt_text": "Geometric shapes diagram",
        "description": "Math exercise prompt image",
        "image_type": ImageFileTypeEnum.EXERCISE_PROMPT,
    },
]

# Map old image URLs to new image IDs for migration purposes
IMAGE_URL_TO_ID_MAP = {
    "https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=400&h=300&fit=crop&crop=center": "img-cat-photo-1",
    "https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=300&fit=crop&crop=center": "img-dog-photo-1",
    "https://images.unsplash.com/photo-1444464666168-49d633b86797?w=400&h=300&fit=crop&crop=center": "img-bird-photo-1",
    "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400&h=300&fit=crop&crop=center": "img-fish-photo-1",
}