# flake8: noqa


from sqlalchemy import select
from loguru import logger
from sqlalchemy.orm import Session
from datetime import datetime, timedelta, UTC # Ensure UTC is imported

from db.models import (
    Year, Subject, Chapter, LearningNode, Exercise, ImageFile,
    LearningNodeExerciseAssociation,
    PriceVersion, # SubscriptionPlan, OfferingPrice, -- Removed old models
    ActiveSubscription, ActiveSubscriptionPlanLink, PlanSelectedSubject,
    SubscriptionOption, DiscountCode # Added SubscriptionOption, DiscountCode
)
from db.models.account import Account, ChildAccount
from db.models.learning_node import (
    LearningNodeTypeEnum,
    MathContentModel, VocabContentModel, ReadingContentModel, ListeningContentModel,
    GrammarContentModel, VerbContentModel
)
from db.models.exercise import (
    ExerciseTypeEnum,
    MCExerciseData, InputExerciseData, ClozeExerciseData, DropdownExerciseData,
    HighlightExerciseData, MatchingPairsExerciseData, CategorizeExerciseData,
    ErrorCorrectionExerciseData, ExerciseSolutionModel
)
# Import enums for subscription
from db.models.subscription import SubscriptionStatusType, BillingPeriodEnum, PlanTypeEnum, DiscountApplicabilityType


from .content_data import (
    YEARS_DATA, SUBJECTS_DATA,
    CHAPTERS_DATA, LEARNING_NODES_DATA, EXERCISES_DATA
)
from .subscription_data import (
    PRICE_VERSIONS_DATA, # SUBSCRIPTION_PLANS_DATA, OFFERING_PRICES_DATA, -- Removed old data lists
    SUBSCRIPTION_OPTIONS_DATA, # Added new data list
    ACTIVE_SUBSCRIPTIONS_DATA,
    DISCOUNT_CODES_DATA, # Added discount codes
    MOCK_PARENT_FOR_DISCOUNT_TEST_EMAIL, MOCK_PARENT_FOR_DISCOUNT_TEST_PUBLIC_ID,
    MOCK_PARENT_FOR_DISCOUNT_TEST_STRIPE_ID, MOCK_PARENT_ACCOUNT_NEW_CREATED_AT,
    MOCK_PARENT_ACCOUNT_MID_AGE_CREATED_AT, MOCK_PARENT_ACCOUNT_OLD_CREATED_AT

)

from tests.data import constants

# Helper to get Pydantic model class for exercise data
def get_exercise_data_model(exercise_type: ExerciseTypeEnum):
    mapping = {
        ExerciseTypeEnum.MC_SIMPLE: MCExerciseData,
        ExerciseTypeEnum.MC_MULTI: MCExerciseData,
        ExerciseTypeEnum.INPUT: InputExerciseData,
        ExerciseTypeEnum.CLOZE: ClozeExerciseData,
        ExerciseTypeEnum.DROPDOWN: DropdownExerciseData,
        ExerciseTypeEnum.HIGHLIGHT: HighlightExerciseData,
        ExerciseTypeEnum.MATCHING_PAIRS: MatchingPairsExerciseData,
        ExerciseTypeEnum.CATEGORIZE: CategorizeExerciseData,
        ExerciseTypeEnum.ERROR_CORRECTION: ErrorCorrectionExerciseData,
    }
    return mapping.get(exercise_type)

# Helper to get Pydantic model class for learning node content
def get_learning_node_content_model(node_type_str: str):
    mapping = {
        "MATH": MathContentModel,
        "GRAMMAR": GrammarContentModel,
        "VOCABULARY": VocabContentModel,
        "READING": ReadingContentModel,
        "LISTENING": ListeningContentModel,
        "VERB": VerbContentModel,
        "SPEAKING": GrammarContentModel,
        "WRITING": GrammarContentModel,
    }
    return mapping.get(node_type_str)


def seed_mock_data(db: Session):
    logger.info("Seeding mock data into the test database...")
    public_id_to_db_id_cache = {}
    public_id_to_orm_cache = {} # Cache ORM objects by public_id

    # --- Account and ChildAccount Data ---
    parent_account = Account(
        public_id=constants.MOCK_PARENT_PUBLIC_ID,
        email=constants.MOCK_PARENT_EMAIL,
        language=constants.MOCK_PARENT_LANGUAGE,
        pin_hash=constants.MOCK_PARENT_PIN_HASH,
        is_verified=True,
        verification_code=None,
        stripe_customer_id=constants.MOCK_PARENT_STRIPE_CUSTOMER_ID
    )
    db.add(parent_account)
    db.flush()
    public_id_to_db_id_cache[constants.MOCK_PARENT_PUBLIC_ID] = parent_account.id
    public_id_to_orm_cache[constants.MOCK_PARENT_PUBLIC_ID] = parent_account

    child_account = ChildAccount(
        public_id=constants.MOCK_CHILD_PUBLIC_ID,
        name=constants.MOCK_CHILD_NAME,
        email=constants.MOCK_CHILD_EMAIL,
        parent_account_id=parent_account.id,
        pin=constants.MOCK_CHILD_PIN,
        language=constants.MOCK_CHILD_LANGUAGE,
        is_verified=True,
        verification_code=None,
    )
    db.add(child_account)
    db.flush()
    public_id_to_db_id_cache[constants.MOCK_CHILD_PUBLIC_ID] = child_account.id
    public_id_to_orm_cache[constants.MOCK_CHILD_PUBLIC_ID] = child_account
    
    # Accounts for discount code tests
    discount_parent_new = Account(
        public_id="dp_new_pid", email="<EMAIL>", stripe_customer_id="cus_dp_new",
        created_at=MOCK_PARENT_ACCOUNT_NEW_CREATED_AT, is_verified=True
    )
    discount_parent_mid = Account(
        public_id="dp_mid_pid", email="<EMAIL>", stripe_customer_id="cus_dp_mid",
        created_at=MOCK_PARENT_ACCOUNT_MID_AGE_CREATED_AT, is_verified=True
    )
    discount_parent_old = Account(
        public_id="dp_old_pid", email="<EMAIL>", stripe_customer_id="cus_dp_old",
        created_at=MOCK_PARENT_ACCOUNT_OLD_CREATED_AT, is_verified=True
    )
    db.add_all([discount_parent_new, discount_parent_mid, discount_parent_old])
    db.flush()
    public_id_to_orm_cache["dp_new_pid"] = discount_parent_new
    public_id_to_orm_cache["dp_mid_pid"] = discount_parent_mid
    public_id_to_orm_cache["dp_old_pid"] = discount_parent_old


    # --- Image Data ---
    # Import image data
    try:
        from .image_data import MOCK_IMAGE_FILES_DATA
        
        # Seed ImageFile entries
        for img_data in MOCK_IMAGE_FILES_DATA:
            image_file = ImageFile(
                public_id=img_data["public_id"],
                storage_path=img_data["storage_path"],
                mime_type=img_data.get("mime_type"),
                width=img_data.get("width"),
                height=img_data.get("height"),
                file_size_bytes=img_data.get("file_size_bytes"),
                alt_text=img_data.get("alt_text"),
                description=img_data.get("description"),
                image_type=img_data.get("image_type")
            )
            db.add(image_file)
        db.flush()
        
        # Cache image file references
        for img_data in MOCK_IMAGE_FILES_DATA:
            img_db = db.execute(select(ImageFile).where(ImageFile.public_id == img_data["public_id"])).scalar_one()
            public_id_to_db_id_cache[img_data["public_id"]] = img_db.id
            public_id_to_orm_cache[img_data["public_id"]] = img_db
            
        logger.info(f"Seeded {len(MOCK_IMAGE_FILES_DATA)} ImageFile entries")
    except ImportError:
        logger.warning("image_data.py not found, skipping ImageFile seeding")

    # --- Content Data ---
    for year_data in YEARS_DATA:
        year = Year(public_id=year_data["public_id"], name=year_data["name"])
        db.add(year)
    db.flush()
    for year_data in YEARS_DATA:
        year_db = db.execute(select(Year).where(Year.public_id == year_data["public_id"])).scalar_one()
        public_id_to_db_id_cache[year_data["public_id"]] = year_db.id
        public_id_to_orm_cache[year_data["public_id"]] = year_db


    # SubjectCategory is no longer used - removed this section


    for subj_data in SUBJECTS_DATA:
        subject = Subject(
            public_id=subj_data["public_id"],
            name=subj_data["name"],
            year_id=public_id_to_db_id_cache[subj_data["year_public_id"]],
            description=subj_data["description"],
            is_active=subj_data["is_active"],
            image_url=subj_data["image_url"]
        )
        db.add(subject)
    db.flush()
    for subj_data in SUBJECTS_DATA:
        subj_db = db.execute(select(Subject).where(Subject.public_id == subj_data["public_id"])).scalar_one()
        public_id_to_db_id_cache[subj_data["public_id"]] = subj_db.id
        public_id_to_orm_cache[subj_data["public_id"]] = subj_db

    if constants.MOCK_CHILD_PUBLIC_ID in public_id_to_orm_cache and "year-1" in public_id_to_orm_cache:
        mock_child_orm: ChildAccount = public_id_to_orm_cache[constants.MOCK_CHILD_PUBLIC_ID]
        mock_year_orm: Year = public_id_to_orm_cache["year-1"]
        mock_child_orm.year_id = mock_year_orm.id
        db.add(mock_child_orm)
        db.flush()


    for chapter_data in CHAPTERS_DATA:
        chapter = Chapter(
            public_id=chapter_data["public_id"],
            title=chapter_data["title"],
            description=chapter_data["description"],
            subject_id=public_id_to_db_id_cache[chapter_data["subject_public_id"]],
            ordering=chapter_data["ordering"]
        )
        db.add(chapter)
    db.flush()
    for chapter_data in CHAPTERS_DATA:
        chapter_db = db.execute(select(Chapter).where(Chapter.public_id == chapter_data["public_id"])).scalar_one()
        public_id_to_db_id_cache[chapter_data["public_id"]] = chapter_db.id
        public_id_to_orm_cache[chapter_data["public_id"]] = chapter_db

    for ln_data in LEARNING_NODES_DATA:
        ln = LearningNode(
            public_id=ln_data["public_id"],
            node_type=LearningNodeTypeEnum(ln_data["node_type"]),
            title=ln_data["title"],
            description=ln_data["description"],
            chapter_id=public_id_to_db_id_cache[ln_data["chapter_public_id"]],
            ordering=ln_data["ordering"],
            _content=ln_data["content"],  # Use raw content, not parsed
            _data=ln_data.get("data")
        )
        db.add(ln)
    db.flush()
    for ln_data in LEARNING_NODES_DATA:
        ln_db = db.execute(select(LearningNode).where(LearningNode.public_id == ln_data["public_id"])).scalar_one()
        public_id_to_db_id_cache[ln_data["public_id"]] = ln_db.id
        public_id_to_orm_cache[ln_data["public_id"]] = ln_db

    for ex_data in EXERCISES_DATA:
        exercise_type_enum = ex_data["exercise_type"]
        data_model_class = get_exercise_data_model(exercise_type_enum)
        
        parsed_data = data_model_class.model_validate(ex_data["data"])
        parsed_solution = ExerciseSolutionModel.model_validate(ex_data["solution"])

        exercise = Exercise(
            public_id=ex_data["public_id"],
            exercise_type=exercise_type_enum,
            data=parsed_data, # Use validated Pydantic model
            solution=parsed_solution # Use validated Pydantic model
        )
        db.add(exercise)
        db.flush()
        ex_db = db.get(Exercise, exercise.id) # Fetch persisted instance
        public_id_to_db_id_cache[ex_data["public_id"]] = ex_db.id
        public_id_to_orm_cache[ex_data["public_id"]] = ex_db

        for ln_public_id in ex_data["learning_node_public_ids"]:
            assoc = LearningNodeExerciseAssociation(
                learning_node_id=public_id_to_db_id_cache[ln_public_id],
                exercise_id=ex_db.id
            )
            db.add(assoc)
    db.flush()


    # --- Subscription Data ---
    logger.info("Seeding PriceVersions...")
    for pv_data in PRICE_VERSIONS_DATA:
        price_version = PriceVersion( # Explicitly map known fields
            public_id=pv_data["public_id"],
            name=pv_data["name"],
            slug=pv_data.get("slug"), # Use .get() for optional fields
            display_name=pv_data.get("display_name"), # Use .get() for optional fields
            is_current=pv_data["is_current"], # Map to the correct field
            is_active=pv_data["is_active"]   # Map to the correct field
            # Do NOT pass is_default even if it exists in pv_data
        )
        db.add(price_version)
    db.flush() # Flush after adding all PriceVersions
    # Cache PriceVersion objects
    for pv_data in PRICE_VERSIONS_DATA:
        pv_db = db.execute(select(PriceVersion).where(PriceVersion.public_id == pv_data["public_id"])).scalar_one()
        public_id_to_db_id_cache[pv_data["public_id"]] = pv_db.id
        public_id_to_orm_cache[pv_data["public_id"]] = pv_db

    for so_data in SUBSCRIPTION_OPTIONS_DATA:
        sub_option = SubscriptionOption(
            public_id=so_data["public_id"],
            year_id=public_id_to_db_id_cache[so_data["year_public_id"]],
            price_version_id=public_id_to_db_id_cache[so_data["price_version_public_id"]],
            sc_stripe_product_id=so_data["sc_stripe_product_id"],
            yf_stripe_product_id=so_data["yf_stripe_product_id"],
            sc_monthly_stripe_price_id=so_data["sc_monthly_stripe_price_id"],
            sc_yearly_stripe_price_id=so_data["sc_yearly_stripe_price_id"],
            yf_monthly_stripe_price_id=so_data["yf_monthly_stripe_price_id"],
            yf_yearly_stripe_price_id=so_data["yf_yearly_stripe_price_id"],
            display_price_config_json=so_data["display_price_config_json"],
            is_active=so_data["is_active"]
        )
        db.add(sub_option)
    db.flush()
    for so_data in SUBSCRIPTION_OPTIONS_DATA:
        so_db = db.execute(select(SubscriptionOption).where(SubscriptionOption.public_id == so_data["public_id"])).scalar_one()
        public_id_to_db_id_cache[so_data["public_id"]] = so_db.id
        public_id_to_orm_cache[so_data["public_id"]] = so_db
        
    # Discount Codes
    for dc_data in DISCOUNT_CODES_DATA:
        discount = DiscountCode(
            public_id=dc_data["public_id"],
            stripe_monthly_id=dc_data.get("stripe_monthly_id"),
            stripe_yearly_id=dc_data.get("stripe_yearly_id"),
            public_code=dc_data["public_code"],
            applicable_to=DiscountApplicabilityType(dc_data["applicable_to"]),
            one_time=dc_data["one_time"],
            is_active=dc_data["is_active"],
            min_account_age_days=dc_data.get("min_account_age_days"),
            max_account_age_days=dc_data.get("max_account_age_days")
        )
        db.add(discount)
    db.flush()
    for dc_data in DISCOUNT_CODES_DATA:
        dc_db = db.execute(select(DiscountCode).where(DiscountCode.public_id == dc_data["public_id"])).scalar_one()
        public_id_to_db_id_cache[dc_data["public_id"]] = dc_db.id
        public_id_to_orm_cache[dc_data["public_id"]] = dc_db


    # --- Active Subscription Seeding ---
    for active_sub_data in ACTIVE_SUBSCRIPTIONS_DATA:
        parent_account_orm = public_id_to_orm_cache.get(active_sub_data["parent_account_public_id"])
        if not parent_account_orm:
            logger.warning(f"Parent account {active_sub_data['parent_account_public_id']} not found for active subscription seeding. Skipping.")
            continue

        now = datetime.now(UTC)
        current_period_start = now + timedelta(days=active_sub_data.get("current_period_start_offset_days", 0))
        current_period_end = now + timedelta(days=active_sub_data.get("current_period_end_offset_days", 30))


        active_sub = ActiveSubscription(
            public_id=active_sub_data["public_id"],
            parent_account_id=parent_account_orm.id,
            stripe_subscription_id=active_sub_data["stripe_subscription_id"],
            status=SubscriptionStatusType(active_sub_data["status"]),
            current_period_start=current_period_start,
            current_period_end=current_period_end,
            cancel_at_period_end=active_sub_data["cancel_at_period_end"]
        )
        db.add(active_sub)
        db.flush() # Ensure active_sub.id is available
        public_id_to_db_id_cache[active_sub_data["public_id"]] = active_sub.id
        public_id_to_orm_cache[active_sub_data["public_id"]] = active_sub

        for link_data in active_sub_data["plan_links"]:
            subscription_option_orm = public_id_to_orm_cache.get(link_data["subscription_option_public_id"])
            if not subscription_option_orm:
                logger.warning(f"SubscriptionOption {link_data['subscription_option_public_id']} not found for plan link. Skipping.")
                continue
            
            plan_link = ActiveSubscriptionPlanLink(
                public_id=link_data["public_id"],
                active_subscription_id=active_sub.id,
                subscription_option_id=subscription_option_orm.id,
                chosen_stripe_price_id=link_data["chosen_stripe_price_id"],
                chosen_plan_type=PlanTypeEnum(link_data["chosen_plan_type"]),
                chosen_billing_period=BillingPeriodEnum(link_data["chosen_billing_period"]),
                stripe_subscription_item_id=link_data["stripe_subscription_item_id"],
                quantity=link_data["quantity"]
                # target_child_account_id can be added if needed for specific tests later
            )
            db.add(plan_link)
            db.flush() # Ensure plan_link.id is available
            public_id_to_db_id_cache[link_data["public_id"]] = plan_link.id
            public_id_to_orm_cache[link_data["public_id"]] = plan_link

            if plan_link.chosen_plan_type == PlanTypeEnum.SC:
                for subj_public_id in link_data["selected_subjects_public_ids"]:
                    subject_orm = public_id_to_orm_cache.get(subj_public_id)
                    if not subject_orm:
                        logger.warning(f"Subject {subj_public_id} not found for user selected subject. Skipping.")
                        continue
                    
                    user_selected_subject = PlanSelectedSubject(
                        plan_link_id=plan_link.id,
                        subject_id=subject_orm.id
                    )
                    db.add(user_selected_subject)
    db.flush()

    db.commit()
    logger.info("Mock data seeding complete (data committed to test database).")