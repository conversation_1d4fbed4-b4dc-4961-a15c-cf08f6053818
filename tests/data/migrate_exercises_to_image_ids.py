# tests/data/migrate_exercises_to_image_ids.py
"""Helper script to migrate exercise data from image_url to image_id"""

from typing import Dict, Any, List
from .image_data import IMAGE_URL_TO_ID_MAP


def migrate_exercise_data(exercise_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Migrate a single exercise's data from image_url to image_id format.
    
    Args:
        exercise_data: Original exercise data with image_url fields
        
    Returns:
        Updated exercise data with image_id fields
    """
    migrated = exercise_data.copy()
    
    # Migrate prompt_image_url to prompt_image_id
    if 'prompt_image_url' in migrated:
        url = migrated.pop('prompt_image_url')
        migrated['prompt_image_id'] = IMAGE_URL_TO_ID_MAP.get(url) if url else None
    
    # Migrate options for multiple choice exercises
    if 'options' in migrated and isinstance(migrated['options'], list):
        new_options = []
        for option in migrated['options']:
            new_option = option.copy()
            
            # If this option has an image_url, convert it to image_id
            if 'image_url' in new_option:
                url = new_option.pop('image_url')
                image_id = IMAGE_URL_TO_ID_MAP.get(url)
                
                if image_id:
                    new_option['image_id'] = image_id
                    # Ensure text field exists (even if None) for image options
                    if 'text' not in new_option:
                        new_option['text'] = None
                else:
                    # If no mapping found, log warning and keep as text option
                    print(f"Warning: No image ID mapping found for URL: {url}")
                    if 'text' not in new_option:
                        new_option['text'] = f"Option {new_option.get('public_id', 'unknown')}"
            
            new_options.append(new_option)
        migrated['options'] = new_options
    
    return migrated


def migrate_solution_data(solution_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Migrate solution data from image_url to image_id format.
    
    Args:
        solution_data: Original solution data with image_url in steps
        
    Returns:
        Updated solution data with image_id fields
    """
    if not solution_data:
        return solution_data
        
    migrated = solution_data.copy()
    
    # Migrate solution_steps
    if 'solution_steps' in migrated and isinstance(migrated['solution_steps'], list):
        new_steps = []
        for step in migrated['solution_steps']:
            new_step = step.copy()
            
            # Convert image_url to image_id
            if 'image_url' in new_step:
                url = new_step.pop('image_url')
                new_step['image_id'] = IMAGE_URL_TO_ID_MAP.get(url) if url else None
            
            new_steps.append(new_step)
        migrated['solution_steps'] = new_steps
    
    return migrated


def migrate_exercises_list(exercises: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Migrate a list of exercises from image_url to image_id format.
    
    Args:
        exercises: List of exercise data dictionaries
        
    Returns:
        List of migrated exercise data dictionaries
    """
    migrated_exercises = []
    
    for exercise in exercises:
        migrated_exercise = exercise.copy()
        
        # Migrate the data field
        if 'data' in migrated_exercise:
            migrated_exercise['data'] = migrate_exercise_data(migrated_exercise['data'])
        
        # Migrate the solution field
        if 'solution' in migrated_exercise:
            migrated_exercise['solution'] = migrate_solution_data(migrated_exercise['solution'])
        
        migrated_exercises.append(migrated_exercise)
    
    return migrated_exercises


# Example usage:
if __name__ == "__main__":
    # Example of migrating a single exercise
    old_exercise_data = {
        "prompt": "Which animal is a cat?",
        "prompt_image_url": None,
        "options": [
            {
                "public_id": "img-opt-cat-1",
                "image_url": "https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=400&h=300&fit=crop&crop=center"
            },
            {
                "public_id": "img-opt-dog-1", 
                "image_url": "https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=300&fit=crop&crop=center"
            },
        ]
    }
    
    new_exercise_data = migrate_exercise_data(old_exercise_data)
    print("Migrated exercise data:")
    print(new_exercise_data)