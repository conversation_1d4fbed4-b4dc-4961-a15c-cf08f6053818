# /tests/data/subscription_data.py
from datetime import UTC, datetime, timedelta

from db.models.subscription import (
    BillingPeriodEnum,
    DiscountApplicabilityType,
    PlanTypeEnum,
    SubscriptionStatusType,
)

PRICE_VERSIONS_DATA = [
    {
        "public_id": "pv-1",
        "name": "Standard Prices 2024",
        "slug": "standard-2024",
        "display_name": "Standard Pricing",
        # Kept for _get_or_create_eligible_price_version logic
        "is_active": True,
        # Kept for _get_or_create_eligible_price_version logic
        "is_default": True,
        "is_current": True
    }
]

# Replaces SUBSCRIPTION_PLANS_DATA and part of OFFERING_PRICES_DATA
SUBSCRIPTION_OPTIONS_DATA = [
    {
        "public_id": "subopt-year-1-pv-1",
        # Link to Year defined in content_data.py
        "year_public_id": "year-1",
        "price_version_public_id": "pv-1",
        "sc_stripe_product_id": "prod_sc_year1_mock",
        "yf_stripe_product_id": "prod_yf_year1_mock",
        "sc_monthly_stripe_price_id": "price_sc_monthly_y1_mock",
        "sc_yearly_stripe_price_id": "price_sc_yearly_y1_mock",
        "yf_monthly_stripe_price_id": "price_yf_monthly_y1_mock",
        "yf_yearly_stripe_price_id": "price_yf_yearly_y1_mock",
        # Example structure, adjust as needed by frontend/services
        "display_price_config_json": {
            "SC": {
                "monthly": {
                    "base_price_per_subject": 1000,
                    "tiers": [
                        {"quantity": 3, "price_per_subject": 800}
                    ]
                },
                "yearly": {
                    "base_price_per_subject": 9000,
                    "tiers": [
                        {"quantity": 3, "price_per_subject": 7000}
                    ]
                }
            },
            "YF": {
                "monthly": {"flat_price": 2500},
                "yearly": {"flat_price": 22000}
            },
            "currency": "EUR"
        },
        "is_active": True,
    }
]

ACTIVE_SUBSCRIPTIONS_DATA = [
    {
        "public_id": "active-sub-mock-parent-1",
        # from constants.py
        "parent_account_public_id": "mock_parent_public_id",
        "stripe_subscription_id": "sub_mockstripe_active123",
        "status": SubscriptionStatusType.ACTIVE,
        # Relative to test execution time
        "current_period_start_offset_days": -15,
        # Relative to test execution time
        "current_period_end_offset_days": 15,
        "cancel_at_period_end": False,
        "plan_links": [
            {
                # SC plan: 2 subjects from year-1, monthly billing
                "public_id": "planlink-sc-child1-year1",
                # Links to the SubscriptionOption for Year 1
                "subscription_option_public_id": "subopt-year-1-pv-1",
                # This specific Stripe Price ID was chosen
                "chosen_stripe_price_id": "price_sc_monthly_y1_mock",
                "chosen_plan_type": PlanTypeEnum.SC,
                "chosen_billing_period": BillingPeriodEnum.MONTHLY,
                "stripe_subscription_item_id": "si_mock_sc_child1_year1",
                # Parent paid for 2 subject credits
                "quantity": 2,
                # Example: Only 1 subject actually selected out of 2 paid.
                # Or ["subj-1", "subj-non-existent-for-test"] for other
                # scenarios
                "selected_subjects_public_ids": ["subj-1"],
                # Note: For testing with quantity=2, you might want to add
                # another subject to content_data.py for year-1 and include it
                # here.
            },
            {
                # YF plan: full access to year-1, yearly billing
                "public_id": "planlink-yf-parent-year1",
                "subscription_option_public_id": "subopt-year-1-pv-1",
                "chosen_stripe_price_id": "price_yf_yearly_y1_mock",
                "chosen_plan_type": PlanTypeEnum.YF,
                "chosen_billing_period": BillingPeriodEnum.YEARLY,
                "stripe_subscription_item_id": "si_mock_yf_parent_year1",
                # YF plans always have quantity 1
                "quantity": 1,
                # No specific selections needed for YF
                "selected_subjects_public_ids": []
            }
        ]
    },
    {  # Example of a trialing subscription
        "public_id": "active-sub-trial-parent-1",
        # from constants.py for specific auth tests
        "parent_account_public_id": "auth_parent_public_id",
        "stripe_subscription_id": "sub_mockstripe_trial123",
        "status": SubscriptionStatusType.TRIALING,
        "current_period_start_offset_days": -2,
        # Trial ends in 5 days
        "current_period_end_offset_days": 5,
        "cancel_at_period_end": False,
        # Trialing subscriptions might still have plan links if they convert
        "plan_links": [
            {
                "public_id": "planlink-trial-yf-year1",
                "subscription_option_public_id": "subopt-year-1-pv-1",
                # Example: trials a YF monthly
                "chosen_stripe_price_id": "price_yf_monthly_y1_mock",
                "chosen_plan_type": PlanTypeEnum.YF,
                "chosen_billing_period": BillingPeriodEnum.MONTHLY,
                "stripe_subscription_item_id": "si_mock_trial_yf_year1",
                "quantity": 1,
                "selected_subjects_public_ids": []
            }
        ]
    }
]

DISCOUNT_CODES_DATA = [
    {
        "public_id": "discount-valid-1",
        "stripe_monthly_id": "coupon_valid_mock_monthly_1",
        "stripe_yearly_id": "coupon_valid_mock_yearly_1",
        "public_code": "VALID10",
        "applicable_to": DiscountApplicabilityType.BOTH,
        "one_time": False,
        "is_active": True,
    },
    {
        "public_id": "discount-monthly-1",
        "stripe_monthly_id": "coupon_monthly_mock_1",
        "stripe_yearly_id": None,
        "public_code": "MONTHLYONLY",
        "applicable_to": DiscountApplicabilityType.MONTHLY,
        "one_time": False,
        "is_active": True,
    },
    {
        "public_id": "discount-yearly-1",
        "stripe_monthly_id": None,
        "stripe_yearly_id": "coupon_yearly_mock_1",
        "public_code": "YEARLYONLY",
        "applicable_to": DiscountApplicabilityType.YEARLY,
        "one_time": False,
        "is_active": True,
    },
    {
        "public_id": "discount-inactive-1",
        "stripe_monthly_id": "coupon_inactive_mock_monthly_1",
        "stripe_yearly_id": "coupon_inactive_mock_yearly_1",
        "public_code": "INACTIVEPROMO",
        "applicable_to": DiscountApplicabilityType.BOTH,
        "one_time": False,
        "is_active": False,
    },
    {
        "public_id": "discount-min-age-1",
        "stripe_monthly_id": "coupon_min_age_mock_monthly_1",
        "stripe_yearly_id": "coupon_min_age_mock_yearly_1",
        "public_code": "MINAGEPROMO",
        "applicable_to": DiscountApplicabilityType.BOTH,
        "min_account_age_days": 30,
        "max_account_age_days": None,
        "one_time": False,
        "is_active": True,
    },
    {
        "public_id": "discount-max-age-1",
        "stripe_monthly_id": "coupon_max_age_mock_monthly_1",
        "stripe_yearly_id": "coupon_max_age_mock_yearly_1",
        "public_code": "MAXAGEPROMO",
        "applicable_to": DiscountApplicabilityType.BOTH,
        "min_account_age_days": None,
        "max_account_age_days": 60,
        "one_time": False,
        "is_active": True,
    },
]

# For verify_discount_code tests - ensure accounts with specific creation dates
MOCK_PARENT_FOR_DISCOUNT_TEST_EMAIL = "<EMAIL>"
MOCK_PARENT_FOR_DISCOUNT_TEST_PUBLIC_ID = "discount_parent_pid"
MOCK_PARENT_FOR_DISCOUNT_TEST_STRIPE_ID = "cus_discount_parent_stripe"

# 5 days old
MOCK_PARENT_ACCOUNT_NEW_CREATED_AT = datetime.now(UTC) - timedelta(days=5)
# 45 days old
MOCK_PARENT_ACCOUNT_MID_AGE_CREATED_AT = datetime.now(UTC) - timedelta(days=45)
# 90 days old
MOCK_PARENT_ACCOUNT_OLD_CREATED_AT = datetime.now(UTC) - timedelta(days=90)