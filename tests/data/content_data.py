# Ignore flake8 errors in this file
# flake8: noqa

# /tests/data/content_data.py
from db.models.exercise import ExerciseTypeEnum

YEARS_DATA = [
    {"public_id": "year-1", "name": "Test Year 1"},
]

SUBJECT_CATEGORIES_DATA = [
    {"public_id": "subj-cat-1", "name": "Test Subject Category 1"},
]

SUBJECTS_DATA = [
    {
        "public_id": "subj-1",
        "name": "Test Subject 1",
        "year_public_id": "year-1",
        "description": "Description for Test Subject 1",
        "is_active": True,
        "image_url": "http://example.com/subject1.png"
    },
]


CHAPTERS_DATA = [
    {
        "public_id": "chapter-1",
        "title": "Test Chapter 1",
        "description": "Description for Test Chapter 1",
        "subject_public_id": "subj-1",
        "ordering": 1,
    },
]

LEARNING_NODES_DATA = [
    {
        "public_id": "ln-math-1",
        "node_type": "MATH", # Corresponds to LearningNodeTypeEnum.MATH
        "title": "Introduction to Algebra",
        "description": "Basic algebraic concepts.",
        "chapter_public_id": "chapter-1",
        "ordering": 1,
        "content": { # This is MathContentModel structure
            "video_public_id": "video-algebra-intro",
            "notes": "Some notes about algebra."
        },
        "data": None # Optional _data field
    },
    {
        "public_id": "ln-vocab-1",
        "node_type": "VOCABULARY", # Corresponds to LearningNodeTypeEnum.VOCABULARY
        "title": "Basic French Vocabulary",
        "description": "Common French words.",
        "chapter_public_id": "chapter-1", # Assuming it belongs to the same chapter for simplicity
        "ordering": 2,
        "content": { # This is VocabContentModel structure
            "base_language": "en",
            "target_language": "fr",
            "groups": [
                {
                    "id": "vg-1",
                    "title": "Greetings",
                    "words": [
                        {
                            "base_language": "Hello",
                            "target_language": "Bonjour",
                            "gender": None,
                            "example_sentence": "Bonjour, comment ça va?",
                            "explanation": "A common greeting.",
                            "audio_public_id": "audio-bonjour"
                        }
                    ]
                }
            ]
        },
        "data": None
    }
]

EXERCISES_DATA = [
    {
        "public_id": "ex-mc-1",
        "exercise_type": ExerciseTypeEnum.MC_SIMPLE,
        "learning_node_public_ids": ["ln-math-1"], # Link to one or more learning nodes
        "data": { # MCExerciseData structure
            "prompt": "What is 2 + 2?",
            "prompt_image_url": None,
            "options": [
                {"public_id": "opt1", "text": "3"},
                {"public_id": "opt2", "text": "4"},
                {"public_id": "opt3", "text": "5"},
            ],
        },
        "solution": { # ExerciseSolutionModel structure
            "correct_answer": {"correct_option_id": ["opt2"]}, # MCSimpleSolutionAnswer
            "solution_steps": [{"text": "2 + 2 equals 4."}],
            "video_id": None,
        },
    },
    {
        "public_id": "ex-input-1",
        "exercise_type": ExerciseTypeEnum.INPUT,
        "learning_node_public_ids": ["ln-vocab-1"],
        "data": { # InputExerciseData structure
            "prompt": "Translate 'Hello' to French.",
            "prompt_image_url": None,
        },
        "solution": { # ExerciseSolutionModel structure
            "correct_answer": {"correct_answer": "Bonjour"}, # InputSolutionAnswer
            "solution_steps": [{"text": "'Hello' in French is 'Bonjour'."}],
            "video_id": None,
        },
    },
    {
        "public_id": "ex-mc-multi-1",
        "exercise_type": ExerciseTypeEnum.MC_MULTI,
        "learning_node_public_ids": ["ln-math-1"],
        "data": {
            "prompt": "Which of the following are prime numbers?",
            "options": [
                {"public_id": "opt_mc_multi_1", "text": "2"},
                {"public_id": "opt_mc_multi_2", "text": "4"},
                {"public_id": "opt_mc_multi_3", "text": "7"},
                {"public_id": "opt_mc_multi_4", "text": "9"},
            ],
        },
        "solution": {
            "correct_answer": {"correct_option_ids": ["opt_mc_multi_1", "opt_mc_multi_3"]},
            "solution_steps": [{"text": "2 and 7 are prime numbers."}],
        },
    },
    {
        "public_id": "ex-cloze-1",
        "exercise_type": ExerciseTypeEnum.CLOZE,
        "learning_node_public_ids": ["ln-vocab-1"],
        "data": {
            "prompt": "Fill in the blanks: The capital of France is ____ and the capital of Germany is ____.",
            "text_parts": ["The capital of France is ", " and the capital of Germany is ", "."],
            "correct_answers": ["Paris", "Berlin"], # For the response, not used in initial data model for student
            "hints": [None, None]
        },
        "solution": {
            "correct_answer": {"correct_answers": ["Paris", "Berlin"]},
            "solution_steps": [{"text": "Paris is the capital of France. Berlin is the capital of Germany."}],
        },
    },
    {
        "public_id": "ex-dropdown-1",
        "exercise_type": ExerciseTypeEnum.DROPDOWN,
        "learning_node_public_ids": ["ln-math-1"],
        "data": {
            "prompt": "Select the correct operator: 5 __ 3 = 8",
            "template": "5 {{0}} 3 = 8",
            "options": [["+", "-", "*", "/"]]
        },
        "solution": {
            "correct_answer": {"correct_selections": ["+"]},
            "solution_steps": [{"text": "5 + 3 = 8."}],
        },
    },
    {
        "public_id": "ex-highlight-1",
        "exercise_type": ExerciseTypeEnum.HIGHLIGHT,
        "learning_node_public_ids": ["ln-vocab-1"],
        "data": {
            "prompt": "Highlight the specified words in the sentence.",
            "parts": ["The", "highlight_1", "quick", "highlight_3", "brown", "fox"], # Changed parts
            "correct_indices": [1, 3]  # Changed correctIndices
        },
        "solution": {
            "correct_answer": {"correct_indices": [1, 3]}, # Changed correctIndices
            "solution_steps": [{"text": "'highlight_1' and 'highlight_3' are the target words."}], # Updated solution step
        },
    },
    {
        "public_id": "ex-matching-1",
        "exercise_type": ExerciseTypeEnum.MATCHING_PAIRS,
        "learning_node_public_ids": ["ln-math-1"],
        "data": {
            "prompt": "Match the capitals to their countries.",
            "column_a": [
                {"public_id": "match_a_1", "text": "France"},
                {"public_id": "match_a_2", "text": "Germany"}
            ],
            "column_b": [
                {"public_id": "match_b_1", "text": "Berlin"},
                {"public_id": "match_b_2", "text": "Paris"}
            ]
        },
        "solution": {
            "correct_answer": {"correct_pairs": {"match_a_1": "match_b_2", "match_a_2": "match_b_1"}},
            "solution_steps": [{"text": "France -> Paris, Germany -> Berlin."}],
        },
    },
    {
        "public_id": "ex-categorize-1",
        "exercise_type": ExerciseTypeEnum.CATEGORIZE,
        "learning_node_public_ids": ["ln-vocab-1"],
        "data": {
            "prompt": "Categorize the words as 'Fruit' or 'Vegetable'.",
            "categories": [
                {"public_id": "cat_fruit", "text": "Fruit"},
                {"public_id": "cat_veg", "text": "Vegetable"}
            ],
            "options": [
                {"public_id": "item_apple", "text": "Apple"},
                {"public_id": "item_carrot", "text": "Carrot"}
            ]
        },
        "solution": {
            "correct_answer": {"correct_categories": {"item_apple": "cat_fruit", "item_carrot": "cat_veg"}},
            "solution_steps": [{"text": "Apple is a fruit, Carrot is a vegetable."}],
        },
    },
    {
        "public_id": "ex-error-correction-1",
        "exercise_type": ExerciseTypeEnum.ERROR_CORRECTION,
        "learning_node_public_ids": ["ln-vocab-1"],
        "data": {
            "prompt": "Correct the misspelled word: I love to eat banannas.",
            "parts": ["I", "love", "to", "eat", "banannas", "."],
            "error_indices": [4] # "banannas" is at index 4
        },
        "solution": {
            "correct_answer": {"corrections": [{"index": 4, "text": "bananas"}]}, # ErrorCorrectionSolutionAnswer
            "solution_steps": [{"text": "The correct spelling is 'bananas'."}],
        },
    }
]