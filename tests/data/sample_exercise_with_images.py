# tests/data/sample_exercise_with_images.py
"""Sample exercise data using the new image ID approach"""

# This is how your exercise data should look with the new image_id approach
ANIMAL_MC_EXERCISE_DATA = {
    "prompt": "Which animal is a cat?",
    "prompt_image_id": None,  # Changed from prompt_image_url
    "options": [
        {
            "public_id": "img-opt-cat-1",
            "image_id": "img-cat-photo-1",  # References ImageFile.public_id
            "text": None  # Optional text for accessibility
        },
        {
            "public_id": "img-opt-dog-1", 
            "image_id": "img-dog-photo-1",  # References ImageFile.public_id
            "text": None
        },
        {
            "public_id": "img-opt-bird-1",
            "image_id": "img-bird-photo-1",  # References ImageFile.public_id
            "text": None
        },
        {
            "public_id": "img-opt-fish-1",
            "image_id": "img-fish-photo-1",  # References ImageFile.public_id
            "text": None
        }
    ]
}

# Example with a prompt image
GEOMETRY_MC_EXERCISE_DATA = {
    "prompt": "Which shape is a triangle?",
    "prompt_image_id": "img-math-diagram-1",  # References ImageFile.public_id
    "options": [
        {
            "public_id": "opt-1",
            "text": "Shape A"
        },
        {
            "public_id": "opt-2",
            "text": "Shape B"
        },
        {
            "public_id": "opt-3",
            "text": "Shape C"
        },
        {
            "public_id": "opt-4",
            "text": "Shape D"
        }
    ]
}

# Solution with image in steps
EXERCISE_SOLUTION_WITH_IMAGE = {
    "correct_answer": {
        "correct_option_id": "img-opt-cat-1"
    },
    "solution_steps": [
        {
            "text": "The correct answer is the cat.",
            "math": None,
            "image_id": "img-cat-photo-1"  # References ImageFile.public_id
        },
        {
            "text": "Cats are feline animals with whiskers and retractable claws.",
            "math": None,
            "image_id": None
        }
    ],
    "video_id": None
}