import pytest
from fastapi import HTT<PERSON>Exception
from sqlalchemy.orm import Session
from unittest.mock import Mock, patch

from dependencies.auth_dependencies.editorial_auth_dependency import (
    EditorAuthDependency, RequireEditor, RequireAdmin, EditorAuthResponse
)
from db.models import EditorAccount, EditorRole
from tests.fixtures.editorial_fixtures import Editor<PERSON><PERSON><PERSON>
from tests.utils.editorial_test_utils import <PERSON><PERSON><PERSON>estHelper
from core.exception_handling.exceptions.custom_exceptions import (
    AuthenticationError, AuthorizationError
)
from api.v1.common.schemas import AppErrorCode

class TestEditorAuthDependency:
    """Test editor authentication dependency"""
    
    def test_editor_auth_dependency_valid_token(self, db_session: Session):
        """Test valid token extracts editor info"""
        # Create test editor
        editor = EditorFixtures.create_editor_account(
            db_session,
            email="<EMAIL>",
            role=EditorRole.EDITOR
        )
        
        # Create valid token
        token = JWTTestHelper.create_editor_token(
            editor_id=editor.id,
            email=editor.email,
            role=editor.role
        )
        
        # Test dependency
        dependency = EditorAuthDependency()
        result = dependency(x_editor_token=token, db=db_session)
        
        assert isinstance(result, EditorAuthResponse)
        assert result.editor_id == editor.id
        assert result.editor_public_id == editor.public_id
        assert result.email == editor.email
        assert result.role == EditorRole.EDITOR
        assert result.editor.id == editor.id
    
    def test_editor_auth_dependency_missing_token(self, db_session: Session):
        """Test missing token raises AuthenticationError"""
        dependency = EditorAuthDependency()
        
        with pytest.raises(AuthenticationError) as exc_info:
            dependency(x_editor_token=None, db=db_session)
        
        assert exc_info.value.message == "Authentication required"
        assert exc_info.value.error_code == AppErrorCode.AUTHENTICATION_REQUIRED
    
    def test_editor_auth_dependency_invalid_token(self, db_session: Session):
        """Test invalid token raises AuthenticationError"""
        dependency = EditorAuthDependency()
        
        with pytest.raises(AuthenticationError) as exc_info:
            dependency(x_editor_token="invalid.token.here", db=db_session)
        
        assert exc_info.value.message == "Invalid token"
        assert exc_info.value.error_code == AppErrorCode.INVALID_TOKEN
    
    def test_editor_auth_dependency_expired_token(self, db_session: Session):
        """Test expired token raises AuthenticationError"""
        editor = EditorFixtures.create_editor_account(db_session)
        
        # Create expired token
        expired_token = JWTTestHelper.create_expired_token(editor.id)
        
        dependency = EditorAuthDependency()
        
        with pytest.raises(AuthenticationError) as exc_info:
            dependency(x_editor_token=expired_token, db=db_session)
        
        assert exc_info.value.message == "Token expired"
        assert exc_info.value.error_code == AppErrorCode.TOKEN_EXPIRED
    
    def test_editor_auth_dependency_inactive_editor(self, db_session: Session):
        """Test inactive editor raises AuthenticationError"""
        # Create inactive editor
        editor = EditorFixtures.create_editor_account(
            db_session,
            email="<EMAIL>",
            is_active=False
        )
        
        token = JWTTestHelper.create_editor_token(
            editor_id=editor.id,
            email=editor.email,
            role=editor.role
        )
        
        dependency = EditorAuthDependency()
        
        with pytest.raises(AuthenticationError) as exc_info:
            dependency(x_editor_token=token, db=db_session)
        
        assert exc_info.value.message == "Editor not found or inactive"
        assert exc_info.value.error_code == AppErrorCode.USER_NOT_FOUND
    
    def test_editor_auth_dependency_nonexistent_editor(self, db_session: Session):
        """Test non-existent editor ID raises AuthenticationError"""
        # Create token with non-existent editor ID
        token = JWTTestHelper.create_editor_token(
            editor_id=99999,
            email="<EMAIL>",
            role=EditorRole.EDITOR
        )
        
        dependency = EditorAuthDependency()
        
        with pytest.raises(AuthenticationError) as exc_info:
            dependency(x_editor_token=token, db=db_session)
        
        assert exc_info.value.message == "Editor not found or inactive"
        assert exc_info.value.error_code == AppErrorCode.USER_NOT_FOUND
    
    def test_admin_auth_dependency_admin_allowed(self, db_session: Session):
        """Test admin token accepted for admin endpoints"""
        # Create admin
        admin = EditorFixtures.create_editor_account(
            db_session,
            email="<EMAIL>",
            role=EditorRole.ADMIN
        )
        
        token = JWTTestHelper.create_editor_token(
            editor_id=admin.id,
            email=admin.email,
            role=admin.role
        )
        
        # Test admin dependency
        dependency = EditorAuthDependency(required_role=EditorRole.ADMIN)
        result = dependency(x_editor_token=token, db=db_session)
        
        assert result.role == EditorRole.ADMIN
    
    def test_admin_auth_dependency_editor_rejected(self, db_session: Session):
        """Test editor token rejected for admin endpoints"""
        # Create regular editor
        editor = EditorFixtures.create_editor_account(
            db_session,
            email="<EMAIL>",
            role=EditorRole.EDITOR
        )
        
        token = JWTTestHelper.create_editor_token(
            editor_id=editor.id,
            email=editor.email,
            role=editor.role
        )
        
        # Test admin dependency
        dependency = EditorAuthDependency(required_role=EditorRole.ADMIN)
        
        with pytest.raises(AuthorizationError) as exc_info:
            dependency(x_editor_token=token, db=db_session)
        
        assert exc_info.value.message == "Requires ADMIN role"
        assert exc_info.value.error_code == AppErrorCode.PERMISSION_DENIED
    
    def test_require_editor_convenience_dependency(self, db_session: Session):
        """Test RequireEditor convenience dependency"""
        editor = EditorFixtures.create_editor_account(db_session)
        token = JWTTestHelper.create_editor_token(
            editor_id=editor.id,
            email=editor.email,
            role=editor.role
        )
        
        result = RequireEditor(x_editor_token=token, db=db_session)
        
        assert result.editor_id == editor.id
        assert result.role == EditorRole.EDITOR
    
    def test_require_admin_convenience_dependency(self, db_session: Session):
        """Test RequireAdmin convenience dependency"""
        admin = EditorFixtures.create_editor_account(
            db_session,
            role=EditorRole.ADMIN
        )
        token = JWTTestHelper.create_editor_token(
            editor_id=admin.id,
            email=admin.email,
            role=admin.role
        )
        
        result = RequireAdmin(x_editor_token=token, db=db_session)
        
        assert result.editor_id == admin.id
        assert result.role == EditorRole.ADMIN
    
    def test_malformed_token_payload(self, db_session: Session):
        """Test token with missing editor_id raises error"""
        # Create token without editor_id
        import jwt
        from datetime import datetime, timedelta, UTC
        from core.config import settings
        
        payload = {
            "email": "<EMAIL>",
            "role": "editor",
            "type": "editor_access",
            "exp": datetime.now(UTC) + timedelta(hours=1)
        }
        
        bad_token = jwt.encode(
            payload,
            settings.SECRET_KEY,
            algorithm=settings.JWT_ALGORITHM
        )
        
        dependency = EditorAuthDependency()
        
        with pytest.raises(AuthenticationError) as exc_info:
            dependency(x_editor_token=bad_token, db=db_session)
        
        assert exc_info.value.message == "Invalid token"