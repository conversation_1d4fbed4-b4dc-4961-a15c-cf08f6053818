Let me walk through each fixture's scope and how they work together:
Fixture Scopes in the Testing Infrastructure

pg_container (scope="session"):

Created ONCE at the beginning of the entire test session
The same PostgreSQL container is used for all tests
Destroyed only after ALL tests have finished


test_engine (scope="session"):

Also created ONCE at the beginning of the test session
Uses the container connection from pg_container
Tables are created once at the start of all testing
Disposed of only after ALL tests have finished


db_session_factory (default scope="function"):

Created NEWLY for each test function
Returns a factory function that can create sessions
The factory itself is recreated for each test


db_session (default scope="function"):

Created NEWLY for each test function
Gets a fresh session from the session factory
Seeds test data for that specific test
Automatically rolled back after each test finishes



The key distinction is that while the container and engine are shared across all tests (session scope), each test gets its own isolated database session through the factory mechanism. This ensures that tests can't interfere with each other's data.
So your understanding is correct: the session-scoped fixtures (container and engine) are created once, while function-scoped fixtures (factory and session) are recreated for each test. This combination gives you both performance (by reusing expensive resources like the container) and isolation (by giving each test its own transaction that gets rolled back).
One additional detail: the db_session_factory fixture itself is using an async context manager (db_util.db_session_factory) which creates the factory function that individual tests will use to get database sessions. This is a nice pattern that keeps all the transaction management logic in one place.