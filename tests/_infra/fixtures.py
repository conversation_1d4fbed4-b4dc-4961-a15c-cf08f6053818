import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import Session

from main import app
from db.database import get_db, SqlAlchemyBase
from tests._infra.docker import postgres_container
from tests.data.mock_data_loader import seed_mock_data

# Import actual dependency functions for overriding
from dependencies.email_dependency import get_email_service, get_auth_mailer_service
from dependencies.list_dependency import get_list_service

from services.mailer.mailer_service import EmailService, NullMailer, TemplateLoader
from services.mailer.auth_mailer_service import AuthMailerService
from services.email_list.service import ListService, NullListService  # Added ListService, NullListService
import uuid  # For generating mock Stripe IDs
from unittest.mock import MagicMock  # For creating mock objects
# Remove PlaceholderDependencies as we will use actual functions for override keys
# class PlaceholderDependencies:
#     @staticmethod
#     def get_production_email_service(): pass
#     @staticmethod
#     def get_production_auth_mailer_service(): pass

# get_production_email_service = PlaceholderDependencies.get_production_email_service
# get_production_auth_mailer_service = PlaceholderDependencies.get_production_auth_mailer_service
from pytest_mock import MockerFixture

# Import subscription test mocks
from tests.mocks.stripe_subscription_mocks import (
    mock_stripe_subscription_retrieve,
    mock_stripe_subscription_modify,
    mock_stripe_price_retrieve
)
from tests.mocks.qstash_mocks import (
    mock_qstash_client,
    mock_qstash_scheduling_success,
    mock_qstash_scheduling_failure,
    mock_qstash_signature_valid,
    mock_qstash_signature_invalid
)


# 1) Bring up Postgres once
@pytest.fixture(scope="session")
def pg_container():
    with postgres_container() as container:
        yield container

# 2) Create engine + tables, seed data, all inside a single outer transaction
@pytest.fixture(scope="session")
def connection(pg_container):
    engine = create_engine(pg_container.get_connection_url(), echo=False)
    SqlAlchemyBase.metadata.drop_all(engine)  # Drop tables first
    SqlAlchemyBase.metadata.create_all(engine)

    conn = engine.connect()
    outer_tx = conn.begin()             # outer transaction
    session = Session(bind=conn)
    seed_mock_data(session)             # seed your “base” data exactly once
    session.close()

    yield conn

    outer_tx.rollback()                 # toss everything (tables too)
    conn.close()
    engine.dispose()

# 3) For each test, open a SAVEPOINT so we can rollback per-test
@pytest.fixture(scope="function")
def db_session(connection):
    nested = connection.begin_nested()  # SAVEPOINT
    session = Session(bind=connection)
    yield session                       # <-- this is what you use in tests
    session.close()
    nested.rollback()                   # back out only test’s writes

# 4) Override FastAPI’s get_db to use that nested session
@pytest.fixture(scope="function")
def client(
    db_session: Session, 
    test_email_service: EmailService, 
    test_auth_mailer_service: AuthMailerService,
    test_list_service: ListService,
    mock_stripe_customer_create: MagicMock,
    mock_stripe_customer_delete: MagicMock
):
    def _override_get_db():
        try:
            yield db_session
        finally:
            pass  # db_session is managed by its own fixture's try/finally

    app.dependency_overrides[get_db] = _override_get_db
    # Override mailer services using actual imported functions as keys
    app.dependency_overrides[get_email_service] = lambda: test_email_service
    app.dependency_overrides[get_auth_mailer_service] = lambda: test_auth_mailer_service
    # Override list service
    app.dependency_overrides[get_list_service] = lambda: test_list_service

    with TestClient(app) as c:
        yield c
    app.dependency_overrides.clear()

# 5) Fixtures for mocked mailer services

@pytest.fixture(scope="session")
def test_template_loader() -> TemplateLoader:
    """
    Provides a TemplateLoader instance for tests.
    Assumes TemplateLoader's default path is appropriate or configured globally.
    """
    return TemplateLoader('/services/mailer/templates')

@pytest.fixture(scope="function")
def test_email_service(test_template_loader: TemplateLoader) -> EmailService:
    """
    Provides a NullMailer instance for testing.
    This service logs email attempts instead of sending them.
    """
    return NullMailer(template_loader=test_template_loader)

@pytest.fixture(scope="function")
def test_auth_mailer_service(test_email_service: EmailService) -> AuthMailerService:
    """
    Provides an AuthMailerService instance configured with a NullMailer for tests.
    """
    return AuthMailerService(email_service=test_email_service)

# Fixture for mocked ListService
@pytest.fixture(scope="function")
def test_list_service() -> ListService:
    """
    Provides a NullListService instance for testing.
    This service logs list operations instead of performing them.
    """
    return NullListService()


# Fixture for mocking Stripe Customer.create
@pytest.fixture(scope="function")
def mock_stripe_customer_create(mocker):
    """
    Mocks stripe.Customer.create to prevent actual API calls during tests.
    Returns a mock customer object with a unique ID.
    """
    mock_customer_instance = MagicMock()
    # Stripe customer objects returned by create typically have an 'id' attribute
    mock_customer_instance.id = f"cus_mock_{uuid.uuid4().hex[:14]}"

    # Patch 'stripe.Customer.create' in the context of the 'account_creation.py' module
    mocker.patch(
        "api.v1.app.auth.parent.utils.account_creation.stripe.Customer.create",
        return_value=mock_customer_instance,
        autospec=True  # Ensures the mock's signature matches the real method
    )
    yield mock_customer_instance

# Fixture for mocking Stripe Customer.delete
@pytest.fixture(scope="function")
def mock_stripe_customer_delete(mocker: MockerFixture):
    """
    Mocks stripe.Customer.delete to prevent actual API calls during tests.
    """
    # Patch 'stripe.Customer.delete' in the context of the 'account_creation.py' module
    mock_delete = mocker.patch(
        "api.v1.app.auth.parent.utils.account_creation.stripe.Customer.delete",
        return_value=None,  # stripe.Customer.delete usually returns None or the deleted object which might have an 'id'
        autospec=True  # Ensures the mock's signature matches the real method
    )
    yield mock_delete


# Export all fixtures
__all__ = [
    # Core fixtures
    'pg_container',
    'connection',
    'db_session',
    'client',
    # Email/Auth fixtures  
    'test_template_loader',
    'test_email_service',
    'test_auth_mailer_service',
    'test_list_service',
    # Stripe mocks
    'mock_stripe_customer_create',
    'mock_stripe_customer_delete',
    'mock_stripe_subscription_retrieve',
    'mock_stripe_subscription_modify',
    'mock_stripe_price_retrieve',
    # QStash mocks
    'mock_qstash_client',
    'mock_qstash_scheduling_success',
    'mock_qstash_scheduling_failure',
    'mock_qstash_signature_valid',
    'mock_qstash_signature_invalid',
]
