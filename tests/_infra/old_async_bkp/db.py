"""
DB helpers: engine factory, metadata manager, and the **single**
override used by the FastAPI app.
"""
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from db.database import SqlAlchemyBase  # your declarative base
from contextlib import asynccontextmanager
from sqlalchemy.engine import make_url as sqlalchemy_make_url

def make_engine(database_url: str):
    parsed_url = sqlalchemy_make_url(database_url)

    if parsed_url.drivername.startswith("postgresql"):
        async_url = f"""postgresql+asyncpg://
        {parsed_url.username}:{parsed_url.password}
        @{parsed_url.host}:{parsed_url.port}/{parsed_url.database}"""
    else:
        async_url = database_url

    engine = create_async_engine(async_url)

    return engine


async def create_schema(engine):
    async with engine.begin() as conn:
        await conn.run_sync(SqlAlchemyBase.metadata.create_all)

async def drop_schema(engine):
    async with engine.begin() as conn:
        await conn.run_sync(SqlAlchemyBase.metadata.drop_all)


@asynccontextmanager
async def db_session_factory(engine):
    """
    Yields a *function* that itself is an async context manager.
    This function, when called (e.g., `async with actual_session_factory() as session:`),
    provides an AsyncSession operating within a transaction that's rolled back.
    """
    Session_cls = sessionmaker(
        engine,
        class_=AsyncSession,
        expire_on_commit=False,
        # Corrected mode:
        join_transaction_mode='control_fully'
    )

    @asynccontextmanager
    async def get_session_context_fn():
        """
        This is the actual async context manager function that will be used by
        the db_session fixture. It provides a single AsyncSession within a
        transaction that is rolled back upon exit.
        """
        async with engine.connect() as conn:
            # Begin a transaction on the connection. This transaction will be rolled back
            # when this 'async with' block exits.
            async with conn.begin():
                # Create an AsyncSession instance, specifically binding it to 'conn'.
                # With join_transaction_mode='control_fully', the session will
                # use and control the transaction already started on 'conn'.
                session = Session_cls(bind=conn)
                try:
                    yield session
                finally:
                    # Close the session to release its resources.
                    # This does not affect the transaction on 'conn' directly,
                    # as the transaction's fate is tied to the 'conn.begin()'
                    # context manager.
                    await session.close()
            # The transaction on 'conn' (started by 'conn.begin()') is rolled back here.
        # The connection 'conn' is closed and returned to the pool here.

    yield get_session_context_fn