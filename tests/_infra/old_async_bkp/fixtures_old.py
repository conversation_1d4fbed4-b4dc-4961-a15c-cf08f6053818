# tests/_infra/fixtures.py
import pytest
from httpx import AsyncClient, ASGITransport
import pytest_asyncio
from main import app
from db.database import get_db
from tests._infra import docker as docker_util
from tests._infra import db as db_util
from tests.data.mock_data_loader import seed_mock_data


#  Some mad shenanigans to get pytest to work with async fixtures
#  Need to add the loop_scope to the fixture
#  https://github.com/pytest-dev/pytest-asyncio/issues/706#issuecomment-2382703688

# Starting with v0.24 fixtures can specify different scopes for caching (scope) and
#  for the event_loop (fixture_scope). Try add loop_scope="session" to all fixture decorators
#  in your example. @pytest_asyncio.fixture(loop_scope="session", scope="function") 
# should give the desired behavior for db_session.


@pytest_asyncio.fixture(loop_scope="session", scope="session")
async def pg_container():
    async with docker_util.postgres_container() as container:
        yield container

@pytest_asyncio.fixture(loop_scope="session", scope="session")
async def test_engine(pg_container):
    db_url_from_container = pg_container.get_connection_url()
    engine = db_util.make_engine(db_url_from_container)
    await db_util.create_schema(engine)
    yield engine
    await db_util.drop_schema(engine)
    await engine.dispose()

@pytest.fixture
async def db_session_factory(test_engine):
    async with db_util.db_session_factory(test_engine) as get_session_context_fn:
        yield get_session_context_fn

@pytest.fixture
async def db_session(db_session_factory):
    async with db_session_factory() as session:
        await seed_mock_data(session)
        yield session

@pytest.fixture
async def client(db_session):
    async def _override():
        async with db_session.begin_nested():
            yield db_session
    app.dependency_overrides[get_db] = _override
    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as ac:
        yield ac
    app.dependency_overrides.clear()

@pytest.fixture()
def patch_external_services(mocker):  # Synchronous fixture
    mocker.patch('services.mailer.service.EmailService', autospec=True)
    mocker.patch('services.mailer.auth_mailer.AuthMailerService', autospec=True)
