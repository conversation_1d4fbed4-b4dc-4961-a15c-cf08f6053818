"""
Auth-enabled test client fixtures.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from main import app
from dependencies.auth_dependencies.auth_dependency import AuthDependency
from dependencies.auth_dependencies.subscription_dependency import SubscriptionDependency
from dependencies.auth_dependencies.editorial_auth_dependency import (
    EditorAuthDependency, RequireEditor, RequireAdmin
)
from tests.mocks.auth_dependency_mocks import (
    get_mock_parent_auth,
    get_mock_child_auth,
    get_mock_subscription
)
from tests.mocks.editorial_auth_mocks import (
    get_mock_editor_auth,
    get_mock_admin_auth
)
from tests.mocks.webhook_auth_mocks import mock_verify_qstash_signature
from api.v1.tasks.internal.routes.activate_pause import verify_qstash_signature


@pytest.fixture
def auth_client_parent(client, db_session: Session):
    """
    Test client with parent authentication automatically mocked.
    Use this for tests that need parent authentication but don't test auth itself.
    """
    # Override auth dependencies
    app.dependency_overrides[AuthDependency] = get_mock_parent_auth
    app.dependency_overrides[SubscriptionDependency] = get_mock_subscription
    
    yield client
    
    # Clean up overrides
    app.dependency_overrides.pop(AuthDependency, None)
    app.dependency_overrides.pop(SubscriptionDependency, None)


@pytest.fixture
def auth_client_child(client, db_session: Session):
    """
    Test client with child authentication automatically mocked.
    Use this for tests that need child authentication but don't test auth itself.
    """
    # Override auth dependencies
    app.dependency_overrides[AuthDependency] = get_mock_child_auth
    app.dependency_overrides[SubscriptionDependency] = get_mock_subscription
    
    yield client
    
    # Clean up overrides
    app.dependency_overrides.pop(AuthDependency, None)
    app.dependency_overrides.pop(SubscriptionDependency, None)


@pytest.fixture
def auth_client_no_subscription(client, db_session: Session):
    """
    Test client with parent authentication but no subscription.
    Use this for tests that need to test subscription-required endpoints.
    """
    # Override auth dependencies
    app.dependency_overrides[AuthDependency] = get_mock_parent_auth
    app.dependency_overrides[SubscriptionDependency] = lambda: get_mock_subscription(is_subscribed=False, trial_active=False)
    
    yield client
    
    # Clean up overrides
    app.dependency_overrides.pop(AuthDependency, None)
    app.dependency_overrides.pop(SubscriptionDependency, None)


@pytest.fixture
def auth_client_editor(client, db_session: Session):
    """
    Test client with editor authentication automatically mocked.
    Use this for tests that need editor authentication.
    """
    # Override editor auth dependencies
    app.dependency_overrides[EditorAuthDependency] = get_mock_editor_auth
    app.dependency_overrides[RequireEditor] = get_mock_editor_auth
    
    yield client
    
    # Clean up overrides
    app.dependency_overrides.pop(EditorAuthDependency, None)
    app.dependency_overrides.pop(RequireEditor, None)


@pytest.fixture
def auth_client_admin(client, db_session: Session):
    """
    Test client with admin authentication automatically mocked.
    Use this for tests that need admin authentication.
    """
    # Override admin auth dependencies
    app.dependency_overrides[EditorAuthDependency] = get_mock_admin_auth
    app.dependency_overrides[RequireEditor] = get_mock_admin_auth
    app.dependency_overrides[RequireAdmin] = get_mock_admin_auth
    
    yield client
    
    # Clean up overrides
    app.dependency_overrides.pop(EditorAuthDependency, None)
    app.dependency_overrides.pop(RequireEditor, None)
    app.dependency_overrides.pop(RequireAdmin, None)


@pytest.fixture
def auth_client_webhook(client, db_session: Session):
    """
    Test client with webhook authentication mocked.
    Use this for tests that need to simulate webhook calls.
    """
    # Override webhook auth dependency
    app.dependency_overrides[verify_qstash_signature] = mock_verify_qstash_signature
    
    yield client
    
    # Clean up override
    app.dependency_overrides.pop(verify_qstash_signature, None)