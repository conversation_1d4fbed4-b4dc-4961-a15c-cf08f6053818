from contextlib import contextmanager
from testcontainers.postgres import PostgresContainer

@contextmanager
def postgres_container():
    container = PostgresContainer(
        image="postgres:15-alpine",
        username="testuser",
        password="testpassword",
        dbname="db_test",
    )
    container.start()     # synchronous, blocks until healthy
    try:
        yield container
    finally:
        container.stop()
