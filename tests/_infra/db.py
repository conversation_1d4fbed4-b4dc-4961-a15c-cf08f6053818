"""
DB helpers: engine factory, metadata manager, and the
override used by the FastAPI app.
"""
from sqlalchemy import create_engine  # Changed from sqlalchemy.ext.asyncio
from sqlalchemy.orm import sessionmaker, Session  # Changed from AsyncSession
from db.database import SqlAlchemyBase  # your declarative base
from contextlib import contextmanager  # Changed from asynccontextmanager
from sqlalchemy.engine import make_url as sqlalchemy_make_url

def make_engine(database_url: str):  # Changed to synchronous
    parsed_url = sqlalchemy_make_url(database_url)

    # For synchronous engine, the original database_url should be fine,
    # or explicitly construct if needed, e.g., for driver adjustments.
    # The asyncpg specific URL is not needed.
    # Example: "postgresql://user:password@host:port/database"
    sync_url = str(parsed_url)  # Or directly use database_url if it's already a sync DSN

    engine = create_engine(sync_url)  # Changed to create_engine

    return engine


def create_schema(engine):  # Changed to synchronous
    with engine.begin() as conn:  # Changed from async with
        # conn.run_sync(SqlAlchemyBase.metadata.create_all) # run_sync is for async
        SqlAlchemyBase.metadata.create_all(conn)  # Use direct metadata method

def drop_schema(engine):  # Changed to synchronous
    with engine.begin() as conn:  # Changed from async with
        # await conn.run_sync(SqlAlchemyBase.metadata.drop_all) # run_sync is for async
        SqlAlchemyBase.metadata.drop_all(conn)  # Use direct metadata method


@contextmanager  # Changed from asynccontextmanager
def db_session_factory(engine):  # Changed to synchronous
    """
    Yields a *function* that itself is a context manager.
    This function, when called (e.g., `with actual_session_factory() as session:`),
    provides a Session operating within a transaction that's rolled back.
    """
    Session_cls = sessionmaker(
        engine,
        class_=Session,  # Changed from AsyncSession
        expire_on_commit=False,
        # join_transaction_mode='control_fully' # Specific to AsyncSession, remove or adjust if needed for sync
    )

    @contextmanager  # Changed from asynccontextmanager
    def get_session_context_fn():  # Changed to synchronous
        """
        This is the actual context manager function that will be used by
        the db_session fixture. It provides a single Session within a
        transaction that is rolled back upon exit.
        """
        with engine.connect() as conn:  # Changed from async with
            # Begin a transaction on the connection.
            with conn.begin():  # Changed from async with
                # Create a Session instance, binding it to 'conn'.
                session = Session_cls(bind=conn)
                try:
                    yield session
                finally:
                    # Close the session.
                    session.close()  # Changed from await session.close()
            # The transaction on 'conn' is rolled back here.
        # The connection 'conn' is closed and returned to the pool here.

    yield get_session_context_fn