"""Retry utilities for handling transient failures"""

import time
from typing import Type<PERSON>ar, Callable, Optional, Tuple, Type
from functools import wraps
from loguru import logger

T = TypeVar('T')


def exponential_backoff_retry(
    max_retries: int = 3,
    initial_delay: float = 1.0,
    max_delay: float = 60.0,
    exponential_base: float = 2.0,
    exceptions: Tuple[Type[Exception], ...] = (Exception,)
) -> Callable:
    """
    Decorator that implements exponential backoff retry logic.
    
    Args:
        max_retries: Maximum number of retry attempts
        initial_delay: Initial delay in seconds
        max_delay: Maximum delay between retries
        exponential_base: Base for exponential backoff calculation
        exceptions: Tuple of exception types to catch and retry
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        def wrapper(*args, **kwargs) -> T:
            attempt = 0
            delay = initial_delay
            
            while attempt <= max_retries:
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    attempt += 1
                    
                    if attempt > max_retries:
                        logger.error(
                            f"Failed after {max_retries} retries: {func.__name__} - {str(e)}"
                        )
                        raise
                    
                    # Calculate next delay with exponential backoff
                    delay = min(delay * exponential_base, max_delay)
                    
                    logger.warning(
                        f"Retry {attempt}/{max_retries} for {func.__name__} "
                        f"after {delay:.1f}s delay. Error: {str(e)}"
                    )
                    
                    time.sleep(delay)
            
            # This should never be reached
            raise RuntimeError("Unexpected retry logic error")
        
        return wrapper
    return decorator


def is_retryable_s3_error(error: Exception) -> bool:
    """
    Check if an S3/R2 error is retryable.
    
    Args:
        error: The exception to check
        
    Returns:
        True if the error is retryable, False otherwise
    """
    from botocore.exceptions import ClientError, ConnectionError
    
    # Connection errors are always retryable
    if isinstance(error, ConnectionError):
        return True
    
    # Check specific client errors
    if isinstance(error, ClientError):
        error_code = error.response.get('Error', {}).get('Code', '')
        
        # Retryable error codes
        retryable_codes = {
            'RequestTimeout',
            'ServiceUnavailable',
            'SlowDown',
            'ThrottlingException',
            'TooManyRequests',
            'InternalError',
            'IncompleteRead',
            'ConnectionError',
            'Timeout'
        }
        
        return error_code in retryable_codes
    
    return False