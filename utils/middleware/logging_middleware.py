from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger
import json
from typing import Optional

class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Get client IP - try different headers for DigitalOcean App Platform
        client_ip = None
        
        # Check DigitalOcean specific header variations
        do_headers = [
            'HTTP_DO_CONNECTING_IP',  # PHP-style header
            'http-do-connecting-ip',   # Normalized version
            'do-connecting-ip',        # Original version
            'DO-Connecting-IP'         # Uppercase version
        ]
        
        # Try DigitalOcean specific headers first
        for header in do_headers:
            if header in request.headers:
                client_ip = request.headers[header]
                break
        
        # Fallback to standard proxy headers if DO headers not found
        if not client_ip:
            client_ip = (
                request.headers.get('x-forwarded-for') or
                request.headers.get('x-real-ip') or
                request.headers.get('x-original-forwarded-for') or
                (request.client.host if request.client else None)
            )
        
        # Default to unknown if no IP found
        client_ip = client_ip or 'unknown'
        
        # If x-forwarded-for contains multiple IPs, get the first one
        if isinstance(client_ip, str) and ',' in client_ip:
            client_ip = client_ip.split(',')[0].strip()

        # Extract user identifiers from state (set by AuthMiddleware)
        parent_uuid = getattr(request.state, 'parent_uuid', None)
        child_token = getattr(request.state, 'decoded_child_token', None)
        
        # Create context dict with default values
        context = {
            "user_type": "anonymous",
            "user_id": "none",
            "path": str(request.url.path),  # Ensure path is string
            "method": str(request.method),   # Ensure method is string
            "client_ip": client_ip  # Add client IP to context
        }

        # Update context if we have authenticated user info
        if parent_uuid:
            context.update({
                "user_type": "parent",
                "user_id": str(parent_uuid)  # Ensure UUID is converted to string
            })
        elif child_token and isinstance(child_token, dict):
            context.update({
                "user_type": "child",
                "user_id": str(child_token.get('child_account_public_id', 'unknown'))
            })

        # Bind context to logger
        with logger.contextualize(**context):
            body_content: Optional[bytes] = None
            
            try:
                # Only attempt to read body for methods that typically have one
                if request.method in ["POST", "PUT", "PATCH"]:
                    body_content = await request.body()
                    if body_content:
                        try:
                            body_str = body_content.decode('utf-8')
                            payload = json.loads(body_str)
                            # Sanitize or truncate payload if needed
                            if isinstance(payload, dict):
                                # Truncate large payloads
                                payload_str = str({k: str(v)[:100] + '...' if isinstance(v, str) and len(str(v)) > 100 else v 
                                                 for k, v in payload.items()})
                            else:
                                payload_str = str(payload)[:200]
                            logger.info(f"Request {request.method} {request.url.path} with payload: {payload_str}")
                        except UnicodeDecodeError:
                            logger.warning(f"Request {request.method} {request.url.path} (binary content)")
                        except json.JSONDecodeError:
                            logger.warning(f"Request {request.method} {request.url.path} (non-JSON content)")
                else:
                    logger.info(f"Request {request.method} {request.url.path}")

                # Restore body content if it was read
                if body_content is not None:
                    async def receive():
                        return {"type": "http.request", "body": body_content, "more_body": False}
                    request._receive = receive

                # Process the request and catch any errors
                try:
                    response = await call_next(request)
                    logger.info(f"Response {request.method} {request.url.path} - Status: {response.status_code}")
                    return response
                except Exception as e:
                    logger.error(f"Error processing {request.method} {request.url.path}: {str(e)}")
                    raise  # Re-raise the exception after logging

            except Exception as e:
                logger.error(f"Middleware error for {request.method} {request.url.path}: {str(e)}")
                raise  # Re-raise the exception after logging 