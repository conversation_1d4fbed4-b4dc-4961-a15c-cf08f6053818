from fastapi import Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger
from ..exceptions import CustomException
import traceback
import os
from loguru import logger
# CORS middleware and exception handler responses don't seem to play well. The body of the error response cannot be read because of missing CORS headers.

# https://github.com/tiangolo/fastapi/issues/457#issuecomment-741870858


def get_app_middleware(app, middleware_class):
    middleware_index = None
    for index, middleware in enumerate(app.user_middleware):
        if middleware.cls == middleware_class:
            middleware_index = index
    return None if middleware_index is None else app.user_middleware[middleware_index]


DEV_MODE = True
ENVIRONMENT = os.environ.get("ENVIRONMENT", "prod")

class ExceptionHandlerMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        try:
            return await call_next(request)

        except CustomException as custom_exception:
            path = request.scope['root_path'] + request.scope['route'].path

            log_dict = {
                "error_type": custom_exception.error_type,
                "route": path,
                "status_code": int(custom_exception.status_code),
                "detail": custom_exception.log_message,
            }

            if ENVIRONMENT == 'dev':
                logger.debug(log_dict)

            with logger.contextualize(**log_dict):
                if custom_exception.log_level == "INFO":
                    logger.info(log_dict["detail"])
                elif custom_exception.log_level == "WARNING":
                    logger.warning(log_dict["detail"])
                elif custom_exception.log_level == "ERROR":
                    logger.error(log_dict["detail"])
                elif custom_exception.log_level == "CRITICAL":
                    logger.critical(log_dict["detail"])
                else:
                    logger.debug(log_dict["detail"])

            response = JSONResponse(
                status_code=int(custom_exception.status_code),
                content={"error": custom_exception.error_type,
                         "detail": custom_exception.return_message})

            # CORS middleware and exception handler responses don't seem to play well. The body of the error response cannot be read because of missing CORS headers.

            # https://github.com/tiangolo/fastapi/issues/457#issuecomment-741870858

            cors_middleware = get_app_middleware(
                app=request.app, middleware_class=CORSMiddleware)
            request_origin = request.headers.get("origin", "")
            if cors_middleware and "*" in cors_middleware.options["allow_origins"]:
                response.headers["Access-Control-Allow-Origin"] = "*"
            elif cors_middleware and request_origin in cors_middleware.options["allow_origins"]:
                response.headers["Access-Control-Allow-Origin"] = request_origin

            return response

        except Exception as e:
            path = request.scope['root_path'] + request.scope['route'].path
            log_dict = {
                "error_type": "server_error",
                "route": path,
                "status_code": 500,
                "detail": str(e),

            }
            if DEV_MODE:
                log_dict["traceback"] = traceback.format_exc()

            with logger.contextualize(**log_dict):
                logger.error(log_dict["detail"])
                if DEV_MODE:
                    logger.error("Route: " + log_dict["route"])
                    logger.error(log_dict["traceback"])

            response = JSONResponse(status_code=500, content={
                                    "error": "server_error", "detail": "Something went wrong!"})

            cors_middleware = get_app_middleware(
                app=request.app, middleware_class=CORSMiddleware)
            request_origin = request.headers.get("origin", "")
            if cors_middleware and "*" in cors_middleware.options["allow_origins"]:
                response.headers["Access-Control-Allow-Origin"] = "*"
            elif cors_middleware and request_origin in cors_middleware.options["allow_origins"]:
                response.headers["Access-Control-Allow-Origin"] = request_origin

            return response
