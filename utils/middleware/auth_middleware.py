
# from firebase_admin import auth
import os
from jose import J<PERSON><PERSON><PERSON><PERSON>, jwt
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from utils.exceptions import CustomException
from utils.auth.auth_utils import verify_cognito_jwt

SECRET_KEY = os.environ.get("SECRET_KEY")
ALGORITHM = os.environ.get("JWT_ALGORITHM")

# to get a string like this run:
# openssl rand -hex 32

# def get_firebase_uuid(id_token) -> str:
#         try:
#             decoded_token = auth.verify_id_token(id_token)
#             return decoded_token['uid']
#         # Returns  A dictionary of key-value pairs parsed from the decoded JWT.
#         # Returns  A dictionary of key-value pairs parsed from the decoded JWT.
#         # ValueError – If id_token is a not a string or is empty.
#         # InvalidIdTokenError – If id_token is not a valid Firebase ID token.
#         # ExpiredIdTokenError – If the specified ID token has expired.
#         # RevokedIdTokenError – If check_revoked is True and the ID token has been revoked.
#         # CertificateFetchError – If an error occurs while fetching the public key certificates required to verify the ID token.
#         # UserDisabledError – If check_revoked is True and the corresponding user record is disabled.

#         except Exception as e:
#             return False

def decode_cognito_token(token) -> dict:
    try:
        if not verify_cognito_jwt(token):
          raise PermissionError("Not authorised!")
        access_token_claims = jwt.get_unverified_claims(token)
        return access_token_claims
    except Exception as e:
        return False
    

def decode_custom_token(token) -> dict:
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        return False

def decode_access_token(token) -> dict: 
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        return False
    

class AuthMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        decoded_child_token = None
        parent_uuid = None

        try:
            relevant_header = request.headers["x-auth-child-token"]
            if relevant_header:
                decoded_child_token = decode_access_token(request.headers["x-auth-child-token"])
        except:
            decoded_child_token = None

        
        request.state.decoded_child_token = decoded_child_token

        try:
            relevant_header = request.headers["x-auth-parent-token"]
            if relevant_header:
                try:

                    # Try both cognito and custom token
                    try:
                        parent_uuid = decode_cognito_token(request.headers["x-auth-parent-token"])['sub']
                    except:
                        parent_uuid = decode_custom_token(request.headers["x-auth-parent-token"])['sub']
                except Exception as e:
                    parent_uuid = False
        except:
            parent_uuid = None

        request.state.parent_uuid = parent_uuid

        
        # # None and False are both considered "falsy" values.
        # if not decoded_child_token and not parent_uuid:
        #     raise CustomException("Invalid token", "Invalid token", 401, "INFO", 401)
        
        return await call_next(request)


# Ideally we'd have functions that takes parent or child usertype and then checks if the specific token is present and raises an exception if that is not the case