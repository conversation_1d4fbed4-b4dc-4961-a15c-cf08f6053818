class CustomException(Exception):
    def __init__(self, return_message: str = "", log_message: str = "", status_code : int =500, log_level: str ="INFO", error_type: str =""):
        super().__init__()
        self.return_message = return_message
        self.log_message = log_message
        self.status_code = status_code
        self.log_level = log_level
        self.error_type = error_type

# class CustomPermissionError(PermissionError):
#     def __init__(self, return_message="", log_message=""):
#         super().__init__()
#         self.return_message = return_message
#         self.log_message = log_message
