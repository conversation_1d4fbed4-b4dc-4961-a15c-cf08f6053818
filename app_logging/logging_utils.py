import logging
from gunicorn.glogging import Logger
from loguru import logger
from core.config import settings

# Get environment and log level from Pydantic settings
ENVIRONMENT = settings.ENVIRONMENT
# LOG_LEVEL is now primarily controlled by core.logging_config.py
# This file might still be used for Gunicorn logger stubbing if needed.

# The InterceptHandler might be redundant if all logging goes through Loguru directly
# and Gunicorn logs are also configured to use Loguru.
# For now, let's comment it out or remove if not used by Gunicorn setup.
# class InterceptHandler(logging.Handler):
#   # Intercepts the normal log messages and transforms it into a loguru one
#     def emit(self, record):
#         # Get corresponding Loguru level if it exists
#         try:
#             level = logger.level(record.levelname).name
#         except ValueError:
#             level = record.levelno

#         # Find caller from where originated the logged message
#         frame, depth = logging.currentframe(), 2
#         while frame and frame.f_code.co_filename == logging.__file__:
#             frame = frame.f_back
#             depth += 1

#         logger.opt(depth=depth, exception=record.exc_info).log(
#             level,
#             record.getMessage(),
#             **getattr(record, "extra", {})
#         )

class StubbedGunicornLogger(Logger):
    # This class can remain if <PERSON><PERSON>'s default logging needs to be silenced
    # or redirected, especially if Gunicorn logs are not configured to use Loguru directly.
    # The setup_logging in core.logging_config.py should handle standard Python logging
    # redirection to Loguru if gunicorn uses standard logging.

    def setup(self, cfg):
        # If core.logging_config.py configures the root logger to use Loguru,
        # Gunicorn's standard loggers might automatically use it.
        # This stubbing ensures Gunicorn doesn't set up its own handlers if not desired.
        log_level_str = settings.LOG_LEVEL.upper()
        
        # Get the Gunicorn loggers
        self.error_logger = logging.getLogger("gunicorn.error")
        self.access_logger = logging.getLogger("gunicorn.access")

        # If Loguru is intercepting standard logging, we might not need to add NullHandlers.
        # However, to be safe and prevent duplicate Gunicorn handlers:
        self.error_logger.handlers = [logging.NullHandler()]
        self.access_logger.handlers = [logging.NullHandler()]
        
        # Set levels (Loguru will also respect its own configured level)
        self.error_logger.setLevel(log_level_str)
        self.access_logger.setLevel(log_level_str)
        
        # Ensure propagation if Loguru is intercepting higher up
        self.error_logger.propagate = True
        self.access_logger.propagate = True

        logger.info(f"""Gunicorn loggers (error, access) stubbed. 
        Level: {log_level_str}. 
        Propagation enabled for Loguru.""")

# If InterceptHandler is removed, ensure standard logging is configured to be
# intercepted by Loguru in core/logging_config.py if Gunicorn logs are to be captured.
# This can be done via:
# import logging
# logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True) # in core.logging_config.py
# Or by configuring specific loggers like 'gunicorn.error'.
# For now, the StubbedGunicornLogger primarily silences default Gunicorn handlers.