# Backend Briefing Document - Project Edu Educational Platform

## 1. Executive Summary

Project Edu is a comprehensive educational platform designed for Luxembourg's school system, targeting primary and secondary education students. The platform provides structured learning content aligned with the national curriculum, supporting multiple languages (Luxembourgish, German, French, English) and offering both student learning interfaces and parent management capabilities.

**Primary Purpose**: Deliver curriculum-aligned educational content with progress tracking, subscription management, and multi-language support for Luxembourg's multilingual education system.

**Target Users**:
- Primary users: Students (primary and secondary school)
- Secondary users: Parents (account management, subscription, progress monitoring)
- Content creators: Editors and administrators managing educational content

**Current Status**: Production-ready backend with comprehensive API coverage, subscription management, and content delivery systems.

## 2. Technology Stack

### Backend Core
- **Language**: Python 3.11.x
- **Framework**: FastAPI v0.115.12
- **ASGI Server**: Uvicorn (development) / Gunicorn with Uvicorn workers (production)
- **Runtime**: CPython on Linux/Unix systems

### Database
- **Type**: PostgreSQL (version 14+)
- **ORM**: SQLAlchemy v2.0.40 with synchronous sessions
- **Migration Tool**: Alembic (integrated with SQLAlchemy)
- **Connection Pooling**: Configured per environment (dev: 5 connections, prod: 10+)

### Third-party Services
- **Stripe** v5.5.0: Payment processing, subscription management
- **AWS SDK (boto3)**: 
  - SES for transactional emails
  - S3-compatible storage via Cloudflare R2
- **MailerLite**: Email marketing and user segmentation
- **Cloudflare R2**: Object storage for media files
- **Bunny CDN**: Video content delivery
- **QStash**: Task scheduling and queue management
- **Sentry**: Error tracking (production only)

### Development Tools
- **Testing**: pytest v8.3.4 with testcontainers-postgres
- **Linting**: flake8, autopep8
- **Import Sorting**: isort
- **Logging**: Loguru with structured logging
- **Environment Management**: python-dotenv, pydantic-settings v2.7.0

### Key Dependencies
- **Authentication**: PyJWT v2.10.1, passlib with bcrypt
- **HTTP Client**: httpx v0.28.1
- **Data Validation**: Pydantic v2.10.5
- **Task Scheduling**: QStash Python SDK
- **Container Testing**: testcontainers v4.10.0

## 3. Architecture Overview

### High-Level Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Web Clients   │     │  Mobile Clients │     │ Editorial Portal│
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                         │
         └───────────────────────┴─────────────────────────┘
                                 │
                          ┌──────▼──────┐
                          │  CloudFlare │
                          │   (CDN/WAF) │
                          └──────┬──────┘
                                 │
                          ┌──────▼──────┐
                          │   FastAPI   │
                          │  Application│
                          └──────┬──────┘
                                 │
            ┌────────────────────┼────────────────────┐
            │                    │                    │
     ┌──────▼──────┐     ┌──────▼──────┐     ┌──────▼──────┐
     │ PostgreSQL  │     │   Redis/    │     │   Storage   │
     │  Database   │     │   Cache     │     │     (R2)    │
     └─────────────┘     └─────────────┘     └─────────────┘
```

### Design Patterns

1. **Service Layer Pattern**: Each route delegates business logic to dedicated service classes
2. **Repository Pattern**: Database operations abstracted through SQLAlchemy models
3. **Dependency Injection**: FastAPI's dependency system for auth, database sessions, email services
4. **Generic Models with JSON**: Flexible content storage for exercises and learning nodes
5. **Event-Driven Updates**: Webhook handling for external service events (Stripe)

### Data Flow

1. **Request Flow**:
   - Client → CloudFlare → FastAPI Router → Dependencies → Service Layer → Database
   - Response follows reverse path with serialization at service layer

2. **Authentication Flow**:
   - JWT tokens validated via dependency injection
   - Dual system: Parent (full JWT) and Child (PIN-based)
   - Session validation endpoints for token refresh

3. **Content Delivery**:
   - Static media served via CDN (Bunny/CloudFlare)
   - Dynamic content assembled from database
   - Caching headers set based on content type

### Authentication & Authorization

**Parent Accounts**:
- Email/PIN authentication
- JWT tokens (7-day expiration)
- Full account management capabilities
- Subscription management access

**Child Accounts**:
- PIN-based authentication
- Parent-assigned accounts
- Limited to learning functionality
- Progress tracking enabled

**Editorial System**:
- Separate authentication realm
- Role-based access (Editor/Admin)
- Hierarchical content permissions
- Audit logging for all actions

### Caching Strategies

1. **Database Connection Pooling**: Reduces connection overhead
2. **CDN Caching**: Static assets cached at edge
3. **Application-Level**: Prepared for Redis integration (infrastructure ready)

### Error Handling Patterns

1. **Custom Exceptions**:
   - `ServiceError`: Business logic errors
   - `NotFoundError`: Resource not found
   - `AuthenticationError`: Auth failures
   - `SubscriptionRequiredError`: Access control

2. **Global Exception Handlers**: Centralized error formatting and logging
3. **Structured Error Responses**: Consistent JSON error format

### Logging and Monitoring

- **Loguru Configuration**: Environment-specific formatting
- **Request Context**: Unique request IDs for tracing
- **Sentry Integration**: Production error tracking
- **Structured Logs**: JSON format in production, pretty print in development

## 4. Project Structure

```
/project_edu_backend
├── /api                    # API endpoints organized by version
│   └── /v1
│       ├── /app           # Main application endpoints
│       │   ├── /auth      # Authentication (parent/child)
│       │   ├── /parent    # Parent dashboard and management
│       │   └── /student   # Student learning interface
│       ├── /editorial     # Content management system
│       │   ├── /admin     # Admin-only endpoints
│       │   ├── /auth      # Editorial authentication
│       │   └── /editor    # Editor endpoints
│       ├── /landing       # Public website endpoints
│       ├── /other         # Miscellaneous (contact forms)
│       └── /tasks         # Internal task endpoints
│
├── /core                   # Core application configuration
│   ├── /config            # Settings and environment config
│   └── /exception_handling # Global exception handlers
│
├── /db                     # Database layer
│   ├── /models            # SQLAlchemy models by domain
│   │   ├── /associations  # Many-to-many relationship tables
│   │   ├── auth.py        # Authentication models
│   │   ├── content.py     # Learning content models
│   │   ├── subscription.py # Subscription models
│   │   └── editorial.py   # Editorial system models
│   └── database.py        # Database configuration
│
├── /dependencies          # Dependency injection
│   ├── /auth_dependencies # Authentication middleware
│   └── email_dependency.py # Email service injection
│
├── /middleware            # Custom middleware
│   └── request_context.py # Request ID tracking
│
├── /services              # Business logic layer
│   ├── /api              # Service implementations by route
│   ├── /mailer           # Email service with templates
│   ├── /email_list       # MailerLite integration
│   ├── /draft_management # Editorial content management
│   └── /media            # Media file handling
│
├── /tests                 # Test suite
│   ├── /_infra           # Test infrastructure
│   │   ├── fixtures.py   # Pytest fixtures
│   │   └── mocks/        # Service mocks
│   └── /api/v1/          # Tests mirroring API structure
│
├── /templates             # Email templates
│   └── /{lang}/          # Language-specific templates
│
├── main.py               # FastAPI application entry
├── run.py                # Development server runner
├── requirements_rewrite.txt # Python dependencies
├── pytest.ini            # Test configuration
├── .env                  # Environment variables
└── Procfile             # Deployment configuration
```

### Naming Conventions

- **Files**: Snake_case (e.g., `get_dashboard_service.py`)
- **Classes**: PascalCase (e.g., `GetDashboardService`)
- **Functions**: Snake_case (e.g., `get_student_dashboard`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `MAX_EMAIL_ATTEMPTS`)
- **Database Tables**: Snake_case singular (e.g., `account`, `child_account`)
- **API Routes**: Kebab-case (e.g., `/verify-email`, `/get-dashboard`)

## 5. Core Features & Functionalities

### 5.1 User Account Management

**Parent Account System**
- Email-based registration with verification
- PIN-based authentication for security
- Multi-child account management
- Language preference settings (LU, DE, FR, EN)
- Stripe customer integration

**Child Account System**
- Simplified PIN authentication
- Parent assignment workflow
- Academic year association
- Progress tracking
- Verification through parent email

**Technical Implementation**:
- JWT tokens with 7-day expiration
- PIN hashing with bcrypt
- Email verification codes (6 digits)
- Account lockout after failed attempts
- Rate limiting on auth endpoints

### 5.2 Subscription Management

**Subscription Types**:
- **SC (Subject Credits)**: Pay per subject access
- **YF (Year Full)**: Full year access

**Features**:
- Flexible billing periods (monthly/yearly)
- Pause/resume functionality
- Plan changes with proration
- Discount code system
- Free trial support

**Technical Details**:
- Stripe integration for payments
- Webhook processing for real-time updates
- Subscription state machine
- Proration calculation engine
- Price versioning system

### 5.3 Learning Content System

**Content Hierarchy**:
```
Year (Grade Level)
└── Subject
    └── Chapter
        └── Learning Node
            └── Exercises
```

**Learning Node Types**:
- MATH: Mathematical concepts
- GRAMMAR: Language grammar
- VOCABULARY: Word learning
- READING: Reading comprehension
- LISTENING: Audio comprehension
- SPEAKING: Speaking practice
- WRITING: Writing exercises
- VERB: Verb conjugation

**Exercise Types**:
- Multiple Choice (single/multi)
- Text Input
- Cloze (fill-in-the-blank)
- Dropdown Selection
- Text Highlighting
- Matching Pairs
- Categorization
- Error Correction
- True/False

**Progress Tracking**:
- Exercise attempt history
- Correct/incorrect status
- Attempt counting
- Completion timestamps
- Per-subject progress metrics

### 5.4 Editorial Content Management

**Workflow**:
1. Editor creates draft exercise
2. Editor submits for review
3. Admin reviews and approves/rejects
4. Approved content published to production
5. Audit trail maintained

**Features**:
- Hierarchical permission system
- Draft versioning
- Media file management
- Bulk operations
- Duplicate detection
- Performance metrics

**Access Control**:
- Subject-level assignments
- Chapter-level permissions
- Learning node ownership
- Role-based access (Editor/Admin)

### 5.5 Multi-language Support

**Supported Languages**:
- Luxembourgish (LU)
- German (DE)
- French (FR)
- English (EN)

**Implementation**:
- Language-specific email templates
- Content localization support
- User language preferences
- Dynamic template selection

### 5.6 Media Management

**Supported Types**:
- Images: Exercise visuals, options
- Audio: Pronunciation, listening exercises
- Video: Lectures, solutions (via Bunny CDN)

**Storage**:
- Cloudflare R2 for images/audio
- Bunny CDN for video streaming
- Metadata tracking in database
- Access control via signed URLs

### 5.7 Analytics & Reporting

**Student Analytics**:
- Exercise completion rates
- Time spent per subject
- Progress trends
- Difficulty analysis

**Subscription Analytics**:
- Active subscription tracking
- Churn analysis
- Revenue metrics
- Usage patterns

**Editorial Metrics**:
- Content production rates
- Editor performance
- Review turnaround times
- Quality metrics

## 6. API Documentation

### Base Configuration
- **Base URL**: `https://api.example.com/api/v1/`
- **Authentication**: JWT Bearer tokens in `x-auth-token` header
- **Content-Type**: `application/json`
- **Rate Limiting**: Applied per endpoint (see specific limits below)

### 6.1 Authentication Endpoints

#### Parent Authentication

**POST /auth/parent/signup**
```
Purpose: Create new parent account
Authentication: None
Request:
  {
    "email": "<EMAIL>",
    "pin": "1234",
    "language": "EN"
  }
Response (201):
  {
    "message": "Account created successfully",
    "requires_verification": true
  }
Rate limiting: Standard
```

**POST /auth/parent/login**
```
Purpose: Parent account login
Authentication: None
Request:
  {
    "email": "<EMAIL>",
    "pin": "1234"
  }
Response (200):
  {
    "access_token": "jwt.token.here",
    "account": {
      "public_id": "uuid",
      "email": "<EMAIL>",
      "language": "EN",
      "is_verified": true
    }
  }
Errors:
  - 401: Invalid credentials
  - 403: Account locked (too many attempts)
```

#### Child Authentication

**POST /auth/child/signup**
```
Purpose: Create child account
Authentication: None
Request:
  {
    "name": "Child Name",
    "email": "<EMAIL>",  // optional
    "year_id": 1,
    "language": "DE",
    "pin": "1234"
  }
Response (201):
  {
    "message": "Account created",
    "child_id": "uuid",
    "requires_parent_assignment": true
  }
Rate limiting: Email-based (3/min, 10/hour, 50/day)
```

### 6.2 Student Learning Endpoints

**GET /app/student/dashboard/get-dashboard**
```
Purpose: Get student dashboard with available subjects
Authentication: Required (Child)
Response (200):
  {
    "toasts": [...],
    "years": [{
      "id": 1,
      "name": "Year 5",
      "subjects": [{
        "id": 1,
        "name": "Mathematics",
        "image_url": "https://cdn.example.com/math.png",
        "is_unlocked": true,
        "progress_percentage": 75
      }]
    }],
    "active_trials": [...]
  }
```

**POST /app/student/exercises/submit**
```
Purpose: Submit exercise answer
Authentication: Required (Child)
Subscription: Required
Request:
  {
    "exercise_id": 123,
    "given_answer": {
      "type": "MC_SIMPLE",
      "value": "option_a"
    }
  }
Response (200):
  {
    "is_correct": true,
    "correct_answer": {...},
    "explanation": "Well done!",
    "attempts_count": 1
  }
```

### 6.3 Parent Management Endpoints

**GET /app/parent/subscription/current**
```
Purpose: Get current subscription details
Authentication: Required (Parent)
Response (200):
  {
    "has_active_subscription": true,
    "subscription": {
      "id": "sub_123",
      "status": "ACTIVE",
      "current_period_end": "2024-12-31T23:59:59Z",
      "plans": [{
        "type": "SC",
        "billing_period": "MONTHLY",
        "subjects": ["Mathematics", "German"],
        "price": 9.99
      }]
    },
    "pending_changes": []
  }
```

**POST /app/parent/subscription/pause**
```
Purpose: Pause subscription at period end
Authentication: Required (Parent)
Request:
  {
    "subscription_id": "sub_123",
    "pause_collection_behavior": "keep_as_draft"
  }
Response (200):
  {
    "success": true,
    "pause_date": "2024-12-31T23:59:59Z",
    "resume_date": null
  }
```

### 6.4 Editorial Endpoints

**GET /editorial/editor/drafts**
```
Purpose: Get editor's assigned drafts
Authentication: Required (Editor)
Query params:
  - status: NEW|IN_REVIEW|ACCEPTED_BY_EDITOR
  - page: 1
  - per_page: 20
Response (200):
  {
    "drafts": [{
      "id": 1,
      "exercise_type": "MC_SIMPLE",
      "difficulty": "MEDIUM",
      "status": "NEW",
      "created_at": "2024-01-01T10:00:00Z",
      "learning_node": {...}
    }],
    "total": 50,
    "page": 1
  }
```

### 6.5 Webhook Endpoints

**POST /app/parent/subscription/webhook**
```
Purpose: Stripe webhook handler
Authentication: Stripe signature verification
Headers:
  - stripe-signature: Required
Request: Raw Stripe event payload
Response (200): { "success": true }
Handled events:
  - checkout.session.completed
  - customer.subscription.updated
  - customer.subscription.deleted
  - invoice.payment_succeeded
```

### Rate Limiting Details

Email-based operations are rate limited to prevent abuse:
- **Per minute**: 3 emails max
- **Per hour**: 10 emails max
- **Per day**: 50 emails max

Applied to:
- Email verification codes
- Parent assignment emails
- PIN reset requests
- Contact form submissions

## 7. Database Schema

### Schema Design Principles

1. **Normalized Structure**: 3NF normalization with strategic denormalization
2. **Soft Deletes**: CASCADE for most relationships, SET NULL for audit trails
3. **UUID Public IDs**: External references use UUIDs, internal use integers
4. **Timestamp Tracking**: created_at on all tables, updated_at where needed
5. **Enum Types**: Extensive use for type safety
6. **JSON Fields**: Flexible content storage with Pydantic validation

### Core Tables

#### Account (Parent)
```sql
CREATE TABLE account (
    id SERIAL PRIMARY KEY,
    public_id UUID UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    language VARCHAR(2) NOT NULL,
    stripe_customer_id VARCHAR(255) UNIQUE,
    pin_hash VARCHAR(255) NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_code VARCHAR(6),
    created_at TIMESTAMP WITH TIME ZONE,
    INDEX idx_email (email),
    INDEX idx_stripe_customer (stripe_customer_id)
);
```

#### ChildAccount
```sql
CREATE TABLE child_account (
    id SERIAL PRIMARY KEY,
    public_id UUID UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    year_id INTEGER REFERENCES year(id),
    parent_account_id INTEGER REFERENCES account(id) ON DELETE CASCADE,
    language VARCHAR(2) NOT NULL,
    pin VARCHAR(4) NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE,
    INDEX idx_parent (parent_account_id),
    INDEX idx_year (year_id)
);
```

#### Exercise
```sql
CREATE TABLE exercise (
    id SERIAL PRIMARY KEY,
    public_id UUID UNIQUE NOT NULL,
    exercise_type VARCHAR(50) NOT NULL,
    difficulty VARCHAR(10) NOT NULL,
    _data JSONB NOT NULL,  -- Exercise content
    _solution JSONB NOT NULL,  -- Solution data
    created_at TIMESTAMP WITH TIME ZONE,
    INDEX idx_type (exercise_type),
    INDEX idx_difficulty (difficulty)
);
```

### Key Relationships

1. **Account → ChildAccount**: One-to-many (parent has multiple children)
2. **Account → ActiveSubscription**: One-to-many (multiple concurrent subscriptions)
3. **Year → Subject → Chapter → LearningNode**: Hierarchical content structure
4. **LearningNode ↔ Exercise**: Many-to-many through association table
5. **ChildAccount ↔ Exercise**: Many-to-many tracking attempts
6. **ActiveSubscription → SubscriptionOption**: Defines pricing and access

### Association Tables

```sql
-- Exercise attempts tracking
CREATE TABLE child_account_exercise_association (
    id SERIAL PRIMARY KEY,
    child_account_id INTEGER REFERENCES child_account(id),
    exercise_id INTEGER REFERENCES exercise(id),
    status VARCHAR(20) NOT NULL,
    given_answer JSONB,
    correct_answer JSONB,
    attempts_count INTEGER DEFAULT 0,
    last_attempted_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    UNIQUE (child_account_id, exercise_id)
);
```

## 8. External Service Integrations

### 8.1 Stripe Payment Processing

**Purpose**: Handle payments, subscriptions, and billing

**Configuration**:
```python
STRIPE_API_KEY = os.getenv("STRIPE_SECRET_KEY")
STRIPE_WEBHOOK_SECRET = os.getenv("STRIPE_WEBHOOK_SECRET")
STRIPE_PUBLISHABLE_KEY = os.getenv("STRIPE_PUBLISHABLE_KEY")
```

**Integration Points**:
- Customer creation on signup
- Checkout session creation
- Subscription management
- Webhook event processing
- Portal session generation

**Error Handling**:
- Webhook signature verification
- Idempotency for event processing
- Retry logic for failed API calls
- Fallback for network issues

### 8.2 AWS Services

**SES (Simple Email Service)**:
- Transactional email delivery
- Template management
- Bounce/complaint handling
- Configuration:
  ```python
  AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
  AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
  AWS_REGION = "eu-west-1"
  ```

**Error Handling**:
- Retry with exponential backoff
- Fallback to alternative email service
- Logging of all failures

### 8.3 Cloudflare R2 Storage

**Purpose**: Object storage for media files

**Configuration**:
```python
R2_ACCOUNT_ID = os.getenv("CLOUDFLARE_ACCOUNT_ID")
R2_ACCESS_KEY = os.getenv("CLOUDFLARE_R2_ACCESS_KEY_ID")
R2_SECRET_KEY = os.getenv("CLOUDFLARE_R2_SECRET_ACCESS_KEY")
R2_BUCKET_NAME = os.getenv("CLOUDFLARE_R2_BUCKET_NAME")
```

**Features**:
- S3-compatible API
- Direct upload support
- Signed URL generation
- CDN integration

### 8.4 MailerLite

**Purpose**: Email marketing and user segmentation

**Integration**:
- User synchronization
- Segment management
- Campaign triggers
- Unsubscribe handling

**API Usage**:
```python
MAILERLITE_API_KEY = os.getenv("MAILERLITE_API_KEY")
# Rate limits: 120 requests/minute
```

### 8.5 Bunny CDN

**Purpose**: Video content delivery

**Configuration**:
- Stream API for video upload
- Pull zones for distribution
- Token authentication
- Analytics integration

### 8.6 QStash

**Purpose**: Task scheduling and async processing

**Use Cases**:
- Scheduled subscription pauses
- Batch email sending
- Cleanup tasks
- Report generation

**Configuration**:
```python
QSTASH_URL = os.getenv("QSTASH_URL")
QSTASH_TOKEN = os.getenv("QSTASH_TOKEN")
```

## 9. Design Decisions & Rationale

### 9.1 Synchronous vs Asynchronous

**Decision**: Synchronous SQLAlchemy with connection pooling

**Rationale**:
- Simpler debugging and testing
- Adequate performance for current scale
- Easier integration with existing tools
- Clear transaction boundaries

**Trade-offs**:
- Lower theoretical throughput
- Blocking I/O operations
- Limited concurrent connections

### 9.2 JWT Token Strategy

**Decision**: 7-day expiration with validation endpoints

**Rationale**:
- Balance between security and UX
- Reduces database lookups
- Enables offline validation
- Simple revocation via validation

**Implementation**:
- No refresh tokens (simplicity)
- Session validation endpoints
- Token payload minimization

### 9.3 Multi-tenant Architecture

**Decision**: Single database with row-level isolation

**Rationale**:
- Simpler deployment
- Easier maintenance
- Cost-effective for current scale
- Straightforward backup strategy

**Future Considerations**:
- Schema-based isolation possible
- Read replicas for scaling
- Sharding by account if needed

### 9.4 Content Storage Strategy

**Decision**: JSON fields for flexible content

**Rationale**:
- Accommodates varied exercise types
- Easy schema evolution
- Type safety via Pydantic
- Avoids excessive joins

**Trade-offs**:
- No SQL querying of content
- Larger row sizes
- Application-level validation

### 9.5 Email Architecture

**Decision**: Template-based with language variants

**Rationale**:
- Consistent branding
- Easy localization
- Centralized management
- A/B testing capability

**Implementation**:
- HTML + text versions
- Variable substitution
- Preview functionality
- Tracking integration

## 10. Security Considerations

### 10.1 Authentication Security

**Implementation**:
- bcrypt for PIN hashing (cost factor 12)
- JWT tokens with HS256 signing
- Secure random for verification codes
- Time-based token expiration

**Account Protection**:
- Failed attempt tracking
- Progressive lockout periods
- Email verification required
- Parent-child assignment verification

### 10.2 Authorization

**Role-Based Access**:
- Parent: Full account access
- Child: Learning content only
- Editor: Assigned content only
- Admin: Full system access

**Subscription Validation**:
- Middleware-based checking
- Subject-level access control
- Trial period handling
- Graceful degradation

### 10.3 Data Protection

**In Transit**:
- HTTPS enforced
- TLS 1.2+ required
- Certificate pinning ready
- HSTS headers

**At Rest**:
- Database encryption
- Sensitive field hashing
- PII minimization
- Secure backups

### 10.4 Input Validation

**Strategies**:
- Pydantic models for all inputs
- SQL injection prevention via ORM
- XSS prevention in templates
- File upload restrictions

**Rate Limiting**:
- Email-based limits
- API endpoint throttling
- Brute force protection
- DDoS mitigation (CloudFlare)

### 10.5 OWASP Considerations

**Addressed Vulnerabilities**:
1. **Injection**: Parameterized queries via SQLAlchemy
2. **Broken Authentication**: Secure session management
3. **Sensitive Data Exposure**: Encryption and minimal logging
4. **XXE**: JSON-only APIs
5. **Broken Access Control**: Consistent auth checks
6. **Security Misconfiguration**: Environment-based configs
7. **XSS**: Template escaping
8. **Insecure Deserialization**: Pydantic validation
9. **Insufficient Logging**: Structured audit trails
10. **SSRF**: Restricted external calls

## 11. Configuration & Environment Variables

### Required Variables

```bash
# Database
DATABASE_URL=********************************/dbname
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Authentication
JWT_SECRET_KEY=your-secret-key-here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_DAYS=7

# Stripe
STRIPE_SECRET_KEY=sk_live_xxx
STRIPE_PUBLISHABLE_KEY=pk_live_xxx
STRIPE_WEBHOOK_SECRET=whsec_xxx

# AWS
AWS_ACCESS_KEY_ID=xxx
AWS_SECRET_ACCESS_KEY=xxx
AWS_REGION=eu-west-1
AWS_SES_SENDER_EMAIL=<EMAIL>

# Cloudflare R2
CLOUDFLARE_ACCOUNT_ID=xxx
CLOUDFLARE_R2_ACCESS_KEY_ID=xxx
CLOUDFLARE_R2_SECRET_ACCESS_KEY=xxx
CLOUDFLARE_R2_BUCKET_NAME=project-edu-media

# External Services
MAILERLITE_API_KEY=xxx
BUNNY_STREAM_API_KEY=xxx
QSTASH_URL=https://qstash.upstash.io
QSTASH_TOKEN=xxx

# Application
ENVIRONMENT=production|development|test
API_BASE_URL=https://api.example.com
FRONTEND_URL=https://app.example.com
LOG_LEVEL=INFO|DEBUG|WARNING|ERROR
```

### Optional Variables

```bash
# Sentry (Production)
SENTRY_DSN=https://<EMAIL>/xxx

# Development
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
SHOW_DOCS=true|false

# Feature Flags
ENABLE_TRIALS=true|false
ENABLE_DISCOUNTS=true|false
MAINTENANCE_MODE=false|true
```

### Environment-Specific Configs

**Development**:
- Permissive CORS
- Debug logging
- Swagger docs enabled
- Smaller connection pool

**Production**:
- Restrictive CORS
- Error-only logging
- Docs disabled
- Larger connection pool
- Sentry integration

## 12. Testing Strategy

### 12.1 Test Infrastructure

**Framework**: pytest with extensive fixtures

**Key Components**:
- Testcontainers for PostgreSQL
- Mock implementations for external services
- Fixture-based test data
- Request context simulation

### 12.2 Test Categories

**Unit Tests**:
- Service layer logic
- Model validations
- Utility functions
- Coverage target: 80%

**Integration Tests**:
- API endpoint testing
- Database operations
- External service mocks
- Authentication flows

**E2E Tests**:
- Critical user journeys
- Payment flows
- Subscription lifecycle
- Multi-language paths

### 12.3 Test Organization

```
/tests
├── /unit
│   ├── /services
│   ├── /models
│   └── /utils
├── /integration
│   ├── /api
│   ├── /db
│   └── /external
└── /e2e
    ├── /auth_flows
    ├── /subscription
    └── /learning
```

### 12.4 Test Data Management

**Fixtures**:
```python
@pytest.fixture
def parent_account(db_session):
    """Creates test parent account"""
    
@pytest.fixture
def active_subscription(parent_account, stripe_mock):
    """Creates test subscription"""
```

**Mocks**:
- Stripe API responses
- Email sending
- External storage
- Task scheduling

## 13. Deployment & DevOps

### 13.1 Environments

**Development**:
- Local PostgreSQL
- Local file storage
- Mock external services
- Debug mode enabled

**Staging**:
- Heroku deployment
- Shared PostgreSQL
- Test Stripe account
- Limited email sending

**Production**:
- Heroku with auto-scaling
- Dedicated PostgreSQL
- Production services
- Full monitoring

### 13.2 Build Process

```bash
# Install dependencies
pip install -r requirements_rewrite.txt

# Run migrations
alembic upgrade head

# Run tests
pytest

# Start server
gunicorn main:app -c gunicorn.conf.py
```

### 13.3 Deployment Pipeline

1. **Code Push**: Git push to main branch
2. **CI Pipeline**: GitHub Actions
   - Linting
   - Unit tests
   - Integration tests
3. **Staging Deploy**: Automatic to Heroku staging
4. **Production Deploy**: Manual promotion
5. **Post-Deploy**: 
   - Migration verification
   - Smoke tests
   - Monitoring alerts

### 13.4 Monitoring

**Application Monitoring**:
- Sentry for errors
- Custom metrics
- Performance tracking
- Uptime monitoring

**Infrastructure**:
- Database metrics
- Connection pool usage
- Memory/CPU usage
- Response times

**Business Metrics**:
- Active subscriptions
- User engagement
- Content completion
- Revenue tracking

## 14. Code Style & Conventions

### 14.1 Python Style

**PEP 8 Compliance** with exceptions:
- Line length: 100 characters
- Import grouping: stdlib, third-party, local

**Naming Conventions**:
```python
# Classes: PascalCase
class StudentDashboardService:
    pass

# Functions/Methods: snake_case
def get_student_progress():
    pass

# Constants: UPPER_SNAKE_CASE
MAX_LOGIN_ATTEMPTS = 5

# Private: Leading underscore
def _internal_helper():
    pass
```

### 14.2 Project Conventions

**Service Pattern**:
```python
class GetDashboardService:
    def __init__(self, db: Session):
        self.db = db
    
    def execute(self, child_id: int) -> DashboardResponse:
        # Business logic here
        pass
```

**Route Pattern**:
```python
@router.get("/dashboard")
def get_dashboard(
    service: GetDashboardService = Depends(get_dashboard_service),
    current_user: ChildAccount = Depends(auth_dependency)
):
    return service.execute(current_user.id)
```

### 14.3 Git Workflow

**Branch Strategy**:
- `main`: Production code
- `dev`: Development branch
- `feature/*`: New features
- `fix/*`: Bug fixes
- `hotfix/*`: Emergency fixes

**Commit Convention**:
```
type(scope): subject

body (optional)

footer (optional)
```

Types: feat, fix, docs, style, refactor, test, chore

## 15. Common Patterns & Utilities

### 15.1 Service Layer Pattern

```python
# Base service class
class BaseService:
    def __init__(self, db: Session):
        self.db = db
    
    def execute(self, *args, **kwargs):
        raise NotImplementedError

# Concrete implementation
class CreateExerciseService(BaseService):
    def execute(self, data: ExerciseCreate) -> Exercise:
        exercise = Exercise(**data.dict())
        self.db.add(exercise)
        self.db.commit()
        return exercise
```

### 15.2 Dependency Injection

```python
# Auth dependency
def get_current_user(
    token: str = Header(alias="x-auth-token"),
    db: Session = Depends(get_db)
) -> Account:
    payload = decode_jwt(token)
    user = db.query(Account).filter_by(
        public_id=payload["sub"]
    ).first()
    if not user:
        raise HTTPException(401, "Invalid token")
    return user

# Subscription dependency
def require_active_subscription(
    user: Account = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ActiveSubscription:
    subscription = db.query(ActiveSubscription).filter_by(
        parent_account_id=user.id,
        status="ACTIVE"
    ).first()
    if not subscription:
        raise HTTPException(402, "Subscription required")
    return subscription
```

### 15.3 Email Service Pattern

```python
class EmailService:
    def __init__(self, ses_client, templates_path: str):
        self.ses = ses_client
        self.templates = templates_path
    
    def send_verification_email(
        self, 
        to_email: str, 
        code: str, 
        language: str
    ):
        template = self._load_template(
            "verification", 
            language
        )
        html = template.render(code=code)
        
        self.ses.send_email(
            Source=SENDER_EMAIL,
            Destination={'ToAddresses': [to_email]},
            Message={
                'Subject': {'Data': 'Verify Your Email'},
                'Body': {'Html': {'Data': html}}
            }
        )
```

### 15.4 Error Handling Utilities

```python
# Custom exceptions
class ServiceError(Exception):
    def __init__(self, message: str, code: str = None):
        self.message = message
        self.code = code
        super().__init__(message)

class NotFoundError(ServiceError):
    def __init__(self, resource: str):
        super().__init__(
            f"{resource} not found",
            code="NOT_FOUND"
        )

# Global handler
@app.exception_handler(ServiceError)
async def service_error_handler(
    request: Request, 
    exc: ServiceError
):
    return JSONResponse(
        status_code=400,
        content={
            "error": exc.code or "SERVICE_ERROR",
            "message": exc.message
        }
    )
```

### 15.5 Database Utilities

```python
# Pagination helper
def paginate(
    query: Query, 
    page: int = 1, 
    per_page: int = 20
) -> Tuple[List, int]:
    total = query.count()
    items = query.offset(
        (page - 1) * per_page
    ).limit(per_page).all()
    
    return items, total

# Bulk operations
def bulk_create(
    db: Session, 
    model: Type[Base], 
    objects: List[dict]
):
    instances = [model(**obj) for obj in objects]
    db.bulk_save_objects(instances)
    db.commit()
    return instances
```

## 16. Performance Optimization

### 16.1 Database Optimizations

**Query Optimization**:
- Eager loading for relationships
- Query result caching
- Indexed foreign keys
- Composite indexes for common queries

**Connection Pooling**:
```python
# Production settings
SQLALCHEMY_POOL_SIZE = 10
SQLALCHEMY_MAX_OVERFLOW = 20
SQLALCHEMY_POOL_TIMEOUT = 30
SQLALCHEMY_POOL_RECYCLE = 3600
```

### 16.2 Caching Strategy

**Current Implementation**:
- CDN caching for static assets
- Database connection pooling
- Prepared statements

**Future Improvements**:
- Redis for session caching
- API response caching
- Computed field caching

### 16.3 API Optimizations

**Response Optimization**:
- Field filtering
- Pagination by default
- Lazy loading references
- Response compression

**Request Processing**:
- Request validation caching
- Middleware short-circuits
- Async I/O where beneficial

### 16.4 Media Delivery

**CDN Configuration**:
- Edge caching
- Image optimization
- Lazy loading support
- Progressive enhancement

**Storage Strategy**:
- Direct CDN uploads
- Signed URL generation
- Bandwidth optimization

## 17. Known Issues & Technical Debt

### 17.1 Current Limitations

1. **Synchronous ORM**: Blocking I/O operations
2. **No Caching Layer**: Direct database queries
3. **Limited Monitoring**: Basic error tracking only
4. **Manual Deployments**: No full CI/CD automation

### 17.2 Technical Debt Items

1. **Test Coverage**: Some services lack comprehensive tests
2. **API Versioning**: V1 only, no migration strategy
3. **Documentation**: API docs need updating
4. **Code Duplication**: Some service patterns repeated

### 17.3 Performance Bottlenecks

1. **N+1 Queries**: Some endpoints need optimization
2. **Large JSON Fields**: Exercise content can be large
3. **Email Sending**: Synchronous operation
4. **File Uploads**: No chunking support

### 17.4 Planned Improvements

1. **Async SQLAlchemy**: Migration to async ORM
2. **Redis Integration**: Caching and sessions
3. **GraphQL API**: Alternative to REST
4. **Microservices**: Service separation

## 18. Development Workflow

### 18.1 Local Setup

```bash
# Clone repository
git clone <repo-url>
cd project_edu_backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows

# Install dependencies
pip install -r requirements_rewrite.txt

# Setup environment
cp .env.example .env
# Edit .env with your settings

# Run migrations
alembic upgrade head

# Start development server
python run.py
```

### 18.2 Common Development Tasks

**Add New Model**:
1. Create model in appropriate file
2. Import in `__init__.py`
3. Create migration: `alembic revision --autogenerate`
4. Review and run migration

**Add New Endpoint**:
1. Create route file in appropriate directory
2. Create service class
3. Add tests
4. Update API documentation

**Run Tests**:
```bash
# All tests
pytest

# Specific module
pytest tests/api/v1/app/student/

# With coverage
pytest --cov=app --cov-report=html
```

### 18.3 Debugging Tips

1. **Enable SQL logging**:
   ```python
   SQLALCHEMY_ECHO = True
   ```

2. **Request context debugging**:
   - Check request_id in logs
   - Use correlation for tracing

3. **Email testing**:
   - Use MailHog locally
   - Check AWS SES sandbox

4. **Stripe testing**:
   - Use test mode keys
   - Webhook testing with CLI

## 19. Business Logic & Domain Knowledge

### 19.1 Educational Structure

**Luxembourg School System**:
- Years correspond to grade levels
- Multilingual curriculum (LU, DE, FR, EN)
- Subject-based progression
- Competency-based assessment

**Content Hierarchy**:
- Year → Subject → Chapter → Learning Node → Exercise
- Progressive difficulty levels
- Multiple exercise types per concept
- Spaced repetition support

### 19.2 Subscription Rules

**Plan Types**:
- **SC (Subject Credits)**: 
  - Pay per subject
  - Minimum 1 subject
  - Can add/remove subjects
  
- **YF (Year Full)**:
  - All subjects included
  - Fixed price
  - No subject selection

**Billing Rules**:
- Proration on upgrades
- No proration on downgrades
- Pause extends billing period
- Immediate cancellation option

### 19.3 Progress Tracking

**Exercise Scoring**:
- First attempt: Full points
- Subsequent attempts: Reduced points
- Completion requires correct answer
- No penalty for viewing solutions

**Progress Calculation**:
```python
progress = (completed_exercises / total_exercises) * 100
mastery = (correct_first_attempt / total_exercises) * 100
```

### 19.4 Editorial Workflow

**Content Lifecycle**:
1. **Draft Creation**: Editor creates content
2. **Review**: Self-review and submit
3. **Admin Review**: Accept/reject/revise
4. **Publishing**: Batch or individual
5. **Production**: Live for students

**Quality Standards**:
- Curriculum alignment required
- Difficulty validation
- Answer verification
- Media quality checks

## 20. Future Roadmap & Considerations

### 20.1 Planned Features

**Q1 2025**:
- Parent progress dashboard
- Advanced analytics
- Mobile app API support
- Gamification elements

**Q2 2025**:
- AI-powered recommendations
- Collaborative learning
- Teacher portal
- Real-time progress sync

### 20.2 Architectural Improvements

**Scalability Preparations**:
- Database sharding strategy
- Microservices migration
- Event-driven architecture
- Multi-region deployment

**Performance Enhancements**:
- Async Python migration
- GraphQL API layer
- Edge computing
- Smart caching

### 20.3 Technical Upgrades

**Infrastructure**:
- Kubernetes deployment
- Service mesh
- Distributed tracing
- Advanced monitoring

**Development**:
- Automated testing expansion
- CI/CD improvements
- Documentation automation
- Developer portal

### 20.4 Business Expansion

**Market Considerations**:
- Multi-country support
- Currency localization
- Regulatory compliance
- White-label options

---

## Appendices

### A. Common Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| AUTH_001 | Invalid credentials | 401 |
| AUTH_002 | Token expired | 401 |
| AUTH_003 | Account locked | 403 |
| SUB_001 | No active subscription | 402 |
| SUB_002 | Plan change not allowed | 400 |
| CONTENT_001 | Content not found | 404 |
| CONTENT_002 | Access denied | 403 |

### B. Database Indexes

```sql
-- Performance-critical indexes
CREATE INDEX idx_account_email ON account(email);
CREATE INDEX idx_child_parent ON child_account(parent_account_id);
CREATE INDEX idx_exercise_type ON exercise(exercise_type);
CREATE INDEX idx_subscription_status ON active_subscription(status);
CREATE INDEX idx_attempt_child_exercise ON child_account_exercise_association(child_account_id, exercise_id);
```

### C. API Response Examples

**Successful Response**:
```json
{
  "data": {
    "id": "uuid",
    "name": "Example"
  },
  "metadata": {
    "timestamp": "2024-01-01T10:00:00Z",
    "version": "1.0"
  }
}
```

**Error Response**:
```json
{
  "error": {
    "code": "AUTH_001",
    "message": "Invalid credentials",
    "details": {
      "field": "email"
    }
  }
}
```

---

*Last Updated: December 2024*
*Document Version: 1.0*
*For LLM-Assisted Development*