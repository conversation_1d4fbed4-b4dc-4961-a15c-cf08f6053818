[32m2025-06-25 12:13:38.505[0m | [1mINFO    [0m | [36mNO_REQ_ID[0m | [36mtests.conftest:pytest_configure:90[0m - [1mTest logging configured with <PERSON><PERSON>ru[0m
============================= test session starts ==============================
platform darwin -- Python 3.11.1, pytest-8.3.5, pluggy-1.6.0 -- /Library/Frameworks/Python.framework/Versions/3.11/bin/python3.11
cachedir: .pytest_cache
rootdir: /Users/<USER>/Coding/project_edu/project_luxedu/project_edu_backend
configfile: pytest.ini
plugins: mock-3.14.0, anyio-3.7.0
collecting ... collected 343 items

tests/api/v1/app/auth/child/test_assign_parent.py::test_assign_parent_success_new_assignment PASSED [  0%]
tests/api/v1/app/auth/child/test_assign_parent.py::test_assign_parent_unauthorized_no_token PASSED [  0%]
tests/api/v1/app/auth/child/test_assign_parent.py::test_assign_parent_with_parent_token PASSED [  0%]
tests/api/v1/app/auth/child/test_assign_parent.py::test_assign_parent_child_not_found_from_token PASSED [  1%]
tests/api/v1/app/auth/child/test_check_verification_status.py::test_check_verification_status_child_verified PASSED [  1%]
tests/api/v1/app/auth/child/test_check_verification_status.py::test_check_verification_status_child_not_verified_sends_email PASSED [  1%]
tests/api/v1/app/auth/child/test_check_verification_status.py::test_check_verification_status_child_not_verified_generates_code PASSED [  2%]
tests/api/v1/app/auth/child/test_check_verification_status.py::test_check_verification_status_child_not_found PASSED [  2%]
tests/api/v1/app/auth/child/test_forgot_pin.py::test_forgot_pin_success_sends_email PASSED [  2%]
tests/api/v1/app/auth/child/test_forgot_pin.py::test_forgot_pin_child_not_found PASSED [  2%]
tests/api/v1/app/auth/child/test_login.py::test_resend_verification_code_success[parent_for_resend0] PASSED [  3%]
tests/api/v1/app/auth/child/test_login.py::test_resend_verification_code_generates_code_if_none[parent_for_resend0] PASSED [  3%]
tests/api/v1/app/auth/child/test_login.py::test_resend_verification_code_account_not_found PASSED [  3%]
tests/api/v1/app/auth/child/test_login.py::test_resend_verification_code_already_verified[parent_for_resend0] PASSED [  4%]
tests/api/v1/app/auth/child/test_resend_parent_assignment_code.py::test_resend_parent_assignment_code_success PASSED [  4%]
tests/api/v1/app/auth/child/test_resend_parent_assignment_code.py::test_resend_parent_assignment_code_no_assignment_found PASSED [  4%]
tests/api/v1/app/auth/child/test_resend_parent_assignment_code.py::test_resend_parent_assignment_code_parent_does_not_exist PASSED [  4%]
tests/api/v1/app/auth/child/test_resend_verification_code.py::test_resend_verification_code_success PASSED [  5%]
tests/api/v1/app/auth/child/test_resend_verification_code.py::test_resend_verification_code_child_not_found PASSED [  5%]
tests/api/v1/app/auth/child/test_resend_verification_code.py::test_resend_verification_code_already_verified PASSED [  5%]
tests/api/v1/app/auth/child/test_resend_verification_code.py::test_resend_verification_code_no_code_on_record_error PASSED [  6%]
tests/api/v1/app/auth/child/test_signup.py::test_child_signup_success PASSED [  6%]
tests/api/v1/app/auth/child/test_signup.py::test_child_signup_validation_error PASSED [  6%]
tests/api/v1/app/auth/child/test_signup.py::test_child_signup_existing_child_email PASSED [  6%]
tests/api/v1/app/auth/child/test_signup.py::test_child_signup_existing_parent_email PASSED [  7%]
tests/api/v1/app/auth/child/test_validate_session.py::test_validate_child_session_success PASSED [  7%]
tests/api/v1/app/auth/child/test_validate_session.py::test_validate_child_session_unauthorized_no_token PASSED [  7%]
tests/api/v1/app/auth/child/test_validate_session.py::test_validate_child_session_unauthorized_invalid_token PASSED [  8%]
tests/api/v1/app/auth/child/test_validate_session.py::test_validate_child_session_unauthorized_parent_token PASSED [  8%]
tests/api/v1/app/auth/child/test_validate_session.py::test_validate_child_session_account_not_found_in_db PASSED [  8%]
tests/api/v1/app/auth/child/test_verify_email.py::test_child_verify_email_success PASSED [  9%]
tests/api/v1/app/auth/child/test_verify_email.py::test_child_verify_email_account_not_found PASSED [  9%]
tests/api/v1/app/auth/child/test_verify_email.py::test_child_verify_email_already_verified PASSED [  9%]
tests/api/v1/app/auth/child/test_verify_email.py::test_child_verify_email_incorrect_code PASSED [  9%]
tests/api/v1/app/auth/child/test_verify_parent_assignment.py::test_verify_parent_assignment_new_parent_success PASSED [ 10%]
tests/api/v1/app/auth/child/test_verify_parent_assignment.py::test_verify_parent_assignment_existing_parent_success PASSED [ 10%]
tests/api/v1/app/auth/child/test_verify_parent_assignment.py::test_verify_parent_assignment_invalid_code PASSED [ 10%]
tests/api/v1/app/auth/child/test_verify_parent_assignment.py::test_verify_parent_assignment_no_assignment_found PASSED [ 11%]
tests/api/v1/app/auth/parent/test_assign_child_account.py::test_assign_child_account_success PASSED [ 11%]
tests/api/v1/app/auth/parent/test_assign_child_account.py::test_assign_child_account_unauthorized PASSED [ 11%]
tests/api/v1/app/auth/parent/test_assign_child_account.py::test_assign_child_account_child_not_found PASSED [ 11%]
tests/api/v1/app/auth/parent/test_assign_child_account.py::test_assign_child_account_already_linked PASSED [ 12%]
tests/api/v1/app/auth/parent/test_assign_child_account.py::test_assign_child_account_no_pending_assignment PASSED [ 12%]
tests/api/v1/app/auth/parent/test_assign_child_account.py::test_assign_child_account_invalid_verification_code PASSED [ 12%]
tests/api/v1/app/auth/parent/test_check_verification_status.py::test_check_verification_status_child_verified PASSED [ 13%]
tests/api/v1/app/auth/parent/test_check_verification_status.py::test_check_verification_status_child_not_verified_sends_email PASSED [ 13%]
tests/api/v1/app/auth/parent/test_check_verification_status.py::test_check_verification_status_child_not_verified_generates_code PASSED [ 13%]
tests/api/v1/app/auth/parent/test_check_verification_status.py::test_check_verification_status_child_not_found PASSED [ 13%]
tests/api/v1/app/auth/parent/test_login.py::test_parent_login_success_migrated_verified PASSED [ 14%]
tests/api/v1/app/auth/parent/test_login.py::test_parent_login_account_not_found PASSED [ 14%]
tests/api/v1/app/auth/parent/test_login.py::test_parent_login_incorrect_pin PASSED [ 14%]
tests/api/v1/app/auth/parent/test_login.py::test_parent_login_unverified_account PASSED [ 15%]
tests/api/v1/app/auth/parent/test_login.py::test_parent_login_account_locked PASSED [ 15%]
tests/api/v1/app/auth/parent/test_login.py::test_parent_login_progressive_lockout PASSED [ 15%]
tests/api/v1/app/auth/parent/test_login.py::test_parent_login_not_migrated_triggers_pin_reset PASSED [ 16%]
tests/api/v1/app/auth/parent/test_resend_verification_code.py::test_resend_verification_code_success[parent_for_resend0] PASSED [ 16%]
tests/api/v1/app/auth/parent/test_resend_verification_code.py::test_resend_verification_code_generates_code_if_none[parent_for_resend0] PASSED [ 16%]
tests/api/v1/app/auth/parent/test_resend_verification_code.py::test_resend_verification_code_account_not_found PASSED [ 16%]
tests/api/v1/app/auth/parent/test_resend_verification_code.py::test_resend_verification_code_already_verified[parent_for_resend0] PASSED [ 17%]
tests/api/v1/app/auth/parent/test_reset_pin.py::test_reset_pin_success[parent_with_reset_token0] PASSED [ 17%]
tests/api/v1/app/auth/parent/test_reset_pin.py::test_reset_pin_invalid_token PASSED [ 17%]
tests/api/v1/app/auth/parent/test_reset_pin.py::test_reset_pin_expired_token[parent_with_reset_token0] PASSED [ 18%]
tests/api/v1/app/auth/parent/test_reset_pin.py::test_reset_pin_used_token[parent_with_reset_token0] PASSED [ 18%]
tests/api/v1/app/auth/parent/test_signup.py::test_parent_signup_success PASSED [ 18%]
tests/api/v1/app/auth/parent/test_signup.py::test_parent_signup_email_already_exists_parent PASSED [ 18%]
tests/api/v1/app/auth/parent/test_signup.py::test_parent_signup_email_already_exists_child PASSED [ 19%]
tests/api/v1/app/auth/parent/test_signup.py::test_parent_signup_stripe_error PASSED [ 19%]
tests/api/v1/app/auth/parent/test_trigger_pin_reset.py::test_trigger_pin_reset_success_new_token[parent_for_pin_reset0] PASSED [ 19%]
tests/api/v1/app/auth/parent/test_trigger_pin_reset.py::test_trigger_pin_reset_success_resend_existing_token[parent_for_pin_reset0] PASSED [ 20%]
tests/api/v1/app/auth/parent/test_trigger_pin_reset.py::test_trigger_pin_reset_account_not_found PASSED [ 20%]
tests/api/v1/app/auth/parent/test_trigger_pin_reset.py::test_trigger_pin_reset_account_locked[parent_for_pin_reset0] PASSED [ 20%]
tests/api/v1/app/auth/parent/test_trigger_pin_reset.py::test_trigger_pin_reset_too_many_requests[parent_for_pin_reset0] FAILED [ 20%]
tests/api/v1/app/auth/parent/test_validate_session.py::test_validate_parent_session_success PASSED [ 21%]
tests/api/v1/app/auth/parent/test_validate_session.py::test_validate_parent_session_unauthorized_no_token PASSED [ 21%]
tests/api/v1/app/auth/parent/test_validate_session.py::test_validate_parent_session_unauthorized_invalid_token PASSED [ 21%]
tests/api/v1/app/auth/parent/test_validate_session.py::test_validate_parent_session_unauthorized_when_child_token_used PASSED [ 22%]
tests/api/v1/app/auth/parent/test_validate_session.py::test_validate_parent_session_account_not_found_in_db PASSED [ 22%]
tests/api/v1/app/auth/parent/test_verify_email.py::test_parent_verify_email_success PASSED [ 22%]
tests/api/v1/app/auth/parent/test_verify_email.py::test_parent_verify_email_account_not_found PASSED [ 23%]
tests/api/v1/app/auth/parent/test_verify_email.py::test_parent_verify_email_already_verified PASSED [ 23%]
tests/api/v1/app/auth/parent/test_verify_email.py::test_parent_verify_email_incorrect_code PASSED [ 23%]
tests/api/v1/app/parent/account-settings/test_get_account_settings.py::test_get_account_settings_success_with_subscription FAILED [ 23%]
tests/api/v1/app/parent/account-settings/test_get_account_settings.py::test_get_account_settings_no_active_subscription FAILED [ 24%]
tests/api/v1/app/parent/account-settings/test_get_account_settings.py::test_get_account_settings_unauthorized_no_token FAILED [ 24%]
tests/api/v1/app/parent/account-settings/test_get_account_settings.py::test_get_account_settings_with_child_token FAILED [ 24%]
tests/api/v1/app/parent/account-settings/test_update_account_settings.py::test_update_account_settings_language_success FAILED [ 25%]
tests/api/v1/app/parent/account-settings/test_update_account_settings.py::test_update_account_settings_unauthorized_no_token FAILED [ 25%]
tests/api/v1/app/parent/account-settings/test_update_account_settings.py::test_update_account_settings_with_child_token FAILED [ 25%]
tests/api/v1/app/parent/account-settings/test_update_account_settings.py::test_update_account_settings_invalid_language_code FAILED [ 25%]
tests/api/v1/app/parent/dashboard/test_add_child.py::test_add_child_success PASSED [ 26%]
tests/api/v1/app/parent/dashboard/test_add_child.py::test_add_child_email_conflict_child PASSED [ 26%]
tests/api/v1/app/parent/dashboard/test_add_child.py::test_add_child_email_conflict_parent FAILED [ 26%]
tests/api/v1/app/parent/dashboard/test_add_child.py::test_add_child_invalid_year PASSED [ 27%]
tests/api/v1/app/parent/dashboard/test_add_child.py::test_add_child_unauthorized_no_token PASSED [ 27%]
tests/api/v1/app/parent/dashboard/test_add_child.py::test_add_child_with_child_token PASSED [ 27%]
tests/api/v1/app/parent/dashboard/test_delete_child.py::test_delete_child_success PASSED [ 27%]
tests/api/v1/app/parent/dashboard/test_delete_child.py::test_delete_child_not_found PASSED [ 28%]
tests/api/v1/app/parent/dashboard/test_delete_child.py::test_delete_child_belongs_to_another_parent PASSED [ 28%]
tests/api/v1/app/parent/dashboard/test_delete_child.py::test_delete_child_unauthorized_no_token PASSED [ 28%]
tests/api/v1/app/parent/dashboard/test_delete_child.py::test_delete_child_with_child_token PASSED [ 29%]
tests/api/v1/app/parent/dashboard/test_get_dashboard.py::test_get_dashboard_success PASSED [ 29%]
tests/api/v1/app/parent/dashboard/test_get_dashboard.py::test_get_dashboard_unauthorized_no_token PASSED [ 29%]
tests/api/v1/app/parent/dashboard/test_get_dashboard.py::test_get_dashboard_unauthorized_child_token PASSED [ 30%]
tests/api/v1/app/parent/dashboard/test_update_child.py::test_update_child_name_success PASSED [ 30%]
tests/api/v1/app/parent/dashboard/test_update_child.py::test_update_child_email_success PASSED [ 30%]
tests/api/v1/app/parent/dashboard/test_update_child.py::test_update_child_year_success PASSED [ 30%]
tests/api/v1/app/parent/dashboard/test_update_child.py::test_update_child_pin_success PASSED [ 31%]
tests/api/v1/app/parent/dashboard/test_update_child.py::test_update_child_no_changes PASSED [ 31%]
tests/api/v1/app/parent/dashboard/test_update_child.py::test_update_child_email_conflict_child PASSED [ 31%]
tests/api/v1/app/parent/dashboard/test_update_child.py::test_update_child_email_conflict_parent PASSED [ 32%]
tests/api/v1/app/parent/dashboard/test_update_child.py::test_update_child_invalid_year PASSED [ 32%]
tests/api/v1/app/parent/dashboard/test_update_child.py::test_update_child_not_found PASSED [ 32%]
tests/api/v1/app/parent/dashboard/test_update_child.py::test_update_child_unauthorized_no_token PASSED [ 32%]
tests/api/v1/app/parent/dashboard/test_update_child.py::test_update_child_with_child_token PASSED [ 33%]
tests/api/v1/app/parent/subscription/services/test_activate_pause_service.py::TestActivatePauseService::test_activate_pause_monthly_subscription ERROR [ 33%]
tests/api/v1/app/parent/subscription/services/test_activate_pause_service.py::TestActivatePauseService::test_activate_pause_yearly_subscription ERROR [ 33%]
tests/api/v1/app/parent/subscription/services/test_activate_pause_service.py::TestActivatePauseService::test_activate_pause_idempotency ERROR [ 34%]
tests/api/v1/app/parent/subscription/services/test_activate_pause_service.py::TestActivatePauseService::test_activate_pause_missing_subscription FAILED [ 34%]
tests/api/v1/app/parent/subscription/services/test_activate_pause_service.py::TestActivatePauseService::test_activate_pause_missing_pause_record ERROR [ 34%]
tests/api/v1/app/parent/subscription/services/test_activate_pause_service.py::TestActivatePauseService::test_activate_pause_mismatch ERROR [ 34%]
tests/api/v1/app/parent/subscription/services/test_pause_subscription_service.py::TestPauseSubscriptionService::test_pause_monthly_subscription_immediate ERROR [ 35%]
tests/api/v1/app/parent/subscription/services/test_pause_subscription_service.py::TestPauseSubscriptionService::test_pause_yearly_subscription_immediate ERROR [ 35%]
tests/api/v1/app/parent/subscription/services/test_pause_subscription_service.py::TestPauseSubscriptionService::test_pause_subscription_scheduled ERROR [ 35%]
tests/api/v1/app/parent/subscription/services/test_pause_subscription_service.py::TestPauseSubscriptionService::test_pause_already_paused_subscription ERROR [ 36%]
tests/api/v1/app/parent/subscription/services/test_pause_subscription_service.py::TestPauseSubscriptionService::test_pause_outside_visibility_window ERROR [ 36%]
tests/api/v1/app/parent/subscription/services/test_pause_subscription_service.py::TestPauseSubscriptionService::test_pause_nonexistent_subscription PASSED [ 36%]
tests/api/v1/app/parent/subscription/services/test_resume_subscription_service.py::TestResumeSubscriptionService::test_resume_monthly_subscription ERROR [ 37%]
tests/api/v1/app/parent/subscription/services/test_resume_subscription_service.py::TestResumeSubscriptionService::test_resume_yearly_subscription_with_credit ERROR [ 37%]
tests/api/v1/app/parent/subscription/services/test_resume_subscription_service.py::TestResumeSubscriptionService::test_resume_non_paused_subscription ERROR [ 37%]
tests/api/v1/app/parent/subscription/services/test_resume_subscription_service.py::TestResumeSubscriptionService::test_resume_scheduled_pause ERROR [ 37%]
tests/api/v1/app/parent/subscription/services/test_resume_subscription_service.py::TestResumeSubscriptionService::test_resume_nonexistent_subscription PASSED [ 38%]
tests/api/v1/app/parent/subscription/test_cancel_scheduled_pause.py::TestCancelScheduledPauseRoute::test_cancel_scheduled_pause_success ERROR [ 38%]
tests/api/v1/app/parent/subscription/test_cancel_scheduled_pause.py::TestCancelScheduledPauseRoute::test_cancel_scheduled_pause_no_scheduled_pause ERROR [ 38%]
tests/api/v1/app/parent/subscription/test_cancel_scheduled_pause.py::TestCancelScheduledPauseRoute::test_cancel_already_active_pause ERROR [ 39%]
tests/api/v1/app/parent/subscription/test_cancel_scheduled_pause.py::TestCancelScheduledPauseRoute::test_cancel_scheduled_pause_qstash_failure ERROR [ 39%]
tests/api/v1/app/parent/subscription/test_cancel_scheduled_pause.py::TestCancelScheduledPauseRoute::test_cancel_scheduled_pause_unauthorized ERROR [ 39%]
tests/api/v1/app/parent/subscription/test_cancel_scheduled_pause.py::TestCancelScheduledPauseRoute::test_cancel_scheduled_pause_wrong_parent ERROR [ 39%]
tests/api/v1/app/parent/subscription/test_cancel_scheduled_pause.py::TestCancelScheduledPauseRoute::test_cancel_multiple_scheduled_pauses ERROR [ 40%]
tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py::TestPauseResumeEdgeCases::test_pause_with_stripe_api_error ERROR [ 40%]
tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py::TestPauseResumeEdgeCases::test_pause_subscription_at_exact_window_boundaries ERROR [ 40%]
tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py::TestPauseResumeEdgeCases::test_resume_subscription_at_pause_end_date ERROR [ 41%]
tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py::TestPauseResumeEdgeCases::test_yearly_subscription_pause_with_zero_duration ERROR [ 41%]
tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py::TestPauseResumeEdgeCases::test_concurrent_pause_requests ERROR [ 41%]
tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py::TestPauseResumeEdgeCases::test_pause_with_invalid_subscription_status ERROR [ 41%]
tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py::TestPauseResumeEdgeCases::test_qstash_activation_with_deleted_subscription ERROR [ 42%]
tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py::TestPauseResumeEdgeCases::test_pause_with_stripe_subscription_missing_items ERROR [ 42%]
tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py::TestPauseResumeEdgeCases::test_database_rollback_on_pause_failure ERROR [ 42%]
tests/api/v1/app/parent/subscription/test_pause_subscription.py::TestPauseSubscriptionRoute::test_pause_subscription_success ERROR [ 43%]
tests/api/v1/app/parent/subscription/test_pause_subscription.py::TestPauseSubscriptionRoute::test_pause_subscription_scheduled ERROR [ 43%]
tests/api/v1/app/parent/subscription/test_pause_subscription.py::TestPauseSubscriptionRoute::test_pause_subscription_already_paused ERROR [ 43%]
tests/api/v1/app/parent/subscription/test_pause_subscription.py::TestPauseSubscriptionRoute::test_pause_subscription_unauthorized ERROR [ 44%]
tests/api/v1/app/parent/subscription/test_pause_subscription.py::TestPauseSubscriptionRoute::test_pause_subscription_wrong_parent ERROR [ 44%]
tests/api/v1/app/parent/subscription/test_pause_subscription.py::TestPauseSubscriptionRoute::test_pause_yearly_subscription_different_behavior ERROR [ 44%]
tests/api/v1/app/parent/subscription/test_resume_subscription.py::TestResumeSubscriptionRoute::test_resume_monthly_subscription_success ERROR [ 44%]
tests/api/v1/app/parent/subscription/test_resume_subscription.py::TestResumeSubscriptionRoute::test_resume_yearly_subscription_with_credit ERROR [ 45%]
tests/api/v1/app/parent/subscription/test_resume_subscription.py::TestResumeSubscriptionRoute::test_resume_non_paused_subscription ERROR [ 45%]
tests/api/v1/app/parent/subscription/test_resume_subscription.py::TestResumeSubscriptionRoute::test_resume_scheduled_pause_not_allowed ERROR [ 45%]
tests/api/v1/app/parent/subscription/test_resume_subscription.py::TestResumeSubscriptionRoute::test_resume_subscription_unauthorized ERROR [ 46%]
tests/api/v1/app/parent/subscription/test_resume_subscription.py::TestResumeSubscriptionRoute::test_resume_subscription_wrong_parent ERROR [ 46%]
tests/api/v1/app/parent/subscription/test_resume_subscription.py::TestResumeSubscriptionRoute::test_resume_subscription_stripe_error_handling ERROR [ 46%]
tests/api/v1/app/parent/subscription/test_webhook_subscription_deleted.py::TestWebhookSubscriptionDeleted::test_subscription_deleted_removes_price_eligibility ERROR [ 46%]
tests/api/v1/app/parent/subscription/test_webhook_subscription_deleted.py::TestWebhookSubscriptionDeleted::test_subscription_deleted_keeps_eligibility_with_other_active_subs ERROR [ 47%]
tests/api/v1/app/parent/subscription/test_webhook_subscription_deleted.py::TestWebhookSubscriptionDeleted::test_subscription_deleted_no_price_eligibility ERROR [ 47%]
tests/api/v1/app/parent/subscription/test_webhook_subscription_deleted.py::TestWebhookSubscriptionDeleted::test_subscription_deleted_no_active_subscription_in_db PASSED [ 47%]
tests/api/v1/app/parent/subscription/test_webhook_subscription_deleted.py::TestWebhookSubscriptionDeleted::test_webhook_endpoint_routes_deleted_event ERROR [ 48%]
tests/api/v1/app/parent/subscription/test_webhook_subscription_deleted.py::TestWebhookSubscriptionDeleted::test_event_handler_map_includes_deleted PASSED [ 48%]
tests/api/v1/app/student/test_get_dashboard.py::test_get_dashboard_success FAILED [ 48%]
tests/api/v1/app/student/test_get_dashboard.py::test_get_dashboard_unauthorized_no_token PASSED [ 48%]
tests/api/v1/app/student/test_get_dashboard.py::test_get_dashboard_unauthorized_parent_token PASSED [ 49%]
tests/api/v1/app/student/test_get_learning_node_content.py::test_get_learning_node_content_success FAILED [ 49%]
tests/api/v1/app/student/test_get_learning_node_content.py::test_get_learning_node_content_not_found PASSED [ 49%]
tests/api/v1/app/student/test_get_learning_node_content.py::test_get_learning_node_content_unauthorized_no_token PASSED [ 50%]
tests/api/v1/app/student/test_get_learning_node_content.py::test_get_ln_content_with_parent_token PASSED [ 50%]
tests/api/v1/app/student/test_get_progress_overview.py::test_get_progress_overview_success FAILED [ 50%]
tests/api/v1/app/student/test_get_progress_overview.py::test_get_progress_overview_unauthorized_no_token PASSED [ 51%]
tests/api/v1/app/student/test_get_progress_overview.py::test_get_progress_overview_other_student_id_in_path PASSED [ 51%]
tests/api/v1/app/student/test_get_progress_overview.py::test_get_progress_overview_with_parent_token PASSED [ 51%]
tests/api/v1/app/student/test_get_progress_overview.py::test_get_progress_overview_student_not_found_in_token_data PASSED [ 51%]
tests/api/v1/app/student/test_get_subject.py::test_get_subject_success ERROR [ 52%]
tests/api/v1/app/student/test_get_subject.py::test_get_subject_not_found ERROR [ 52%]
tests/api/v1/app/student/test_get_subject.py::test_get_subject_unauthorized_no_token ERROR [ 52%]
tests/api/v1/editorial/auth/test_editor_login.py::TestEditorLogin::test_editor_login_success FAILED [ 53%]
tests/api/v1/editorial/auth/test_editor_login.py::TestEditorLogin::test_admin_login_success FAILED [ 53%]
tests/api/v1/editorial/auth/test_editor_login.py::TestEditorLogin::test_editor_login_invalid_credentials PASSED [ 53%]
tests/api/v1/editorial/auth/test_editor_login.py::TestEditorLogin::test_editor_login_nonexistent_email PASSED [ 53%]
tests/api/v1/editorial/auth/test_editor_login.py::TestEditorLogin::test_editor_login_inactive_account FAILED [ 54%]
tests/api/v1/editorial/auth/test_editor_login.py::TestEditorLogin::test_editor_login_case_insensitive_email PASSED [ 54%]
tests/api/v1/editorial/auth/test_editor_login.py::TestEditorLogin::test_editor_login_token_expiration PASSED [ 54%]
tests/api/v1/editorial/auth/test_editor_login.py::TestEditorLogin::test_editor_login_multiple_scopes PASSED [ 55%]
tests/api/v1/editorial/auth/test_jwt_tokens.py::TestJWTTokens::test_jwt_token_generation PASSED [ 55%]
tests/api/v1/editorial/auth/test_jwt_tokens.py::TestJWTTokens::test_jwt_token_custom_expiration PASSED [ 55%]
tests/api/v1/editorial/auth/test_jwt_tokens.py::TestJWTTokens::test_jwt_refresh_token_generation PASSED [ 55%]
tests/api/v1/editorial/auth/test_jwt_tokens.py::TestJWTTokens::test_jwt_token_validation PASSED [ 56%]
tests/api/v1/editorial/auth/test_jwt_tokens.py::TestJWTTokens::test_expired_token_rejected PASSED [ 56%]
tests/api/v1/editorial/auth/test_jwt_tokens.py::TestJWTTokens::test_tampered_token_rejected PASSED [ 56%]
tests/api/v1/editorial/auth/test_jwt_tokens.py::TestJWTTokens::test_invalid_token_rejected PASSED [ 57%]
tests/api/v1/editorial/auth/test_jwt_tokens.py::TestJWTTokens::test_wrong_token_type_rejected PASSED [ 57%]
tests/api/v1/editorial/auth/test_jwt_tokens.py::TestJWTTokens::test_verify_token_type PASSED [ 57%]
tests/api/v1/editorial/auth/test_jwt_tokens.py::TestJWTTokens::test_token_with_different_secret_rejected PASSED [ 58%]
tests/api/v1/editorial/test_admin_endpoints.py::TestAdminEndpoints::test_publish_draft FAILED [ 58%]
tests/api/v1/editorial/test_admin_endpoints.py::TestAdminEndpoints::test_reject_draft PASSED [ 58%]
tests/api/v1/editorial/test_admin_endpoints.py::TestAdminEndpoints::test_bulk_publish FAILED [ 58%]
tests/api/v1/editorial/test_admin_endpoints.py::TestAdminEndpoints::test_non_admin_cannot_publish FAILED [ 59%]
tests/api/v1/editorial/test_admin_endpoints.py::TestAdminEndpoints::test_list_editors PASSED [ 59%]
tests/api/v1/editorial/test_admin_endpoints.py::TestAdminEndpoints::test_get_editor PASSED [ 59%]
tests/api/v1/editorial/test_admin_endpoints.py::TestAdminEndpoints::test_create_editor PASSED [ 60%]
tests/api/v1/editorial/test_admin_endpoints.py::TestAdminEndpoints::test_add_editor_scope FAILED [ 60%]
tests/api/v1/editorial/test_admin_endpoints.py::TestAdminEndpoints::test_query_audit_logs FAILED [ 60%]
tests/api/v1/editorial/test_admin_endpoints.py::TestAdminEndpoints::test_get_metrics_overview PASSED [ 60%]
tests/api/v1/editorial/test_admin_endpoints.py::TestAdminEndpoints::test_cleanup_report PASSED [ 61%]
tests/api/v1/editorial/test_admin_endpoints.py::TestAdminEndpoints::test_run_cleanup_tasks PASSED [ 61%]
tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_list_drafts FAILED [ 61%]
tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_list_drafts_with_filters PASSED [ 62%]
tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_get_draft_detail FAILED [ 62%]
tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_get_draft_detail_no_access PASSED [ 62%]
tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_claim_draft FAILED [ 62%]
tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_update_draft FAILED [ 63%]
tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_accept_draft FAILED [ 63%]
tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_release_draft FAILED [ 63%]
tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_upload_media FAILED [ 64%]
tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_list_draft_media FAILED [ 64%]
tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_unauthorized_access PASSED [ 64%]
tests/api/v1/other/test_student_contact.py::test_successful_student_contact_form_submission FAILED [ 65%]
tests/api/v1/other/test_student_contact.py::test_student_contact_form_unauthenticated FAILED [ 65%]
tests/api/v1/other/test_student_contact.py::test_student_contact_form_service_error FAILED [ 65%]
tests/api/v1/tasks/internal/test_activate_pause.py::TestActivatePauseInternal::test_activate_pause_monthly_success ERROR [ 65%]
tests/api/v1/tasks/internal/test_activate_pause.py::TestActivatePauseInternal::test_activate_pause_yearly_success ERROR [ 66%]
tests/api/v1/tasks/internal/test_activate_pause.py::TestActivatePauseInternal::test_activate_pause_idempotency ERROR [ 66%]
tests/api/v1/tasks/internal/test_activate_pause.py::TestActivatePauseInternal::test_activate_pause_invalid_signature ERROR [ 66%]
tests/api/v1/tasks/internal/test_activate_pause.py::TestActivatePauseInternal::test_activate_pause_missing_subscription FAILED [ 67%]
tests/api/v1/tasks/internal/test_activate_pause.py::TestActivatePauseInternal::test_activate_pause_missing_pause_record ERROR [ 67%]
tests/api/v1/tasks/internal/test_activate_pause.py::TestActivatePauseInternal::test_activate_pause_mismatch ERROR [ 67%]
tests/api/v1/tasks/internal/test_activate_pause.py::TestActivatePauseInternal::test_activate_pause_retry_behavior ERROR [ 67%]
tests/db/models/test_draft_exercise.py::TestDraftExercise::test_create_draft_exercise PASSED [ 68%]
tests/db/models/test_draft_exercise.py::TestDraftExercise::test_status_defaults_to_new PASSED [ 68%]
tests/db/models/test_draft_exercise.py::TestDraftExercise::test_json_fields_properly_stored PASSED [ 68%]
tests/db/models/test_draft_exercise.py::TestDraftExercise::test_draft_status_transitions PASSED [ 69%]
tests/db/models/test_draft_exercise.py::TestDraftExercise::test_draft_status_rejection_flow PASSED [ 69%]
tests/db/models/test_draft_exercise.py::TestDraftExercise::test_draft_editor_assignment PASSED [ 69%]
tests/db/models/test_draft_exercise.py::TestDraftExercise::test_draft_relationships PASSED [ 69%]
tests/db/models/test_draft_exercise.py::TestDraftExercise::test_published_exercise_relationship PASSED [ 70%]
tests/db/models/test_draft_exercise.py::TestDraftLearningNodeExercise::test_create_association PASSED [ 70%]
tests/db/models/test_draft_exercise.py::TestDraftLearningNodeExercise::test_unique_constraint PASSED [ 70%]
tests/db/models/test_draft_exercise.py::TestDraftLearningNodeExercise::test_cascade_delete_from_draft PASSED [ 71%]
tests/db/models/test_draft_media.py::TestDraftMediaFile::test_create_draft_media_image FAILED [ 71%]
tests/db/models/test_draft_media.py::TestDraftMediaFile::test_create_draft_media_audio FAILED [ 71%]
tests/db/models/test_draft_media.py::TestDraftMediaFile::test_required_storage_path PASSED [ 72%]
tests/db/models/test_draft_media.py::TestDraftMediaFile::test_type_enum_validation PASSED [ 72%]
tests/db/models/test_draft_media.py::TestDraftMediaFile::test_media_draft_association FAILED [ 72%]
tests/db/models/test_draft_media.py::TestDraftMediaFile::test_cascade_delete_when_draft_deleted FAILED [ 72%]
tests/db/models/test_draft_media.py::TestDraftMediaFile::test_media_metadata_storage PASSED [ 73%]
tests/db/models/test_draft_media.py::TestDraftMediaFile::test_published_media_reference FAILED [ 73%]
tests/db/models/test_draft_media.py::TestDraftMediaFile::test_default_metadata_empty_dict PASSED [ 73%]
tests/db/models/test_draft_media.py::TestDraftMediaFile::test_multiple_media_files_per_draft PASSED [ 74%]
tests/db/models/test_editorial.py::TestEditorAccount::test_create_editor_account PASSED [ 74%]
tests/db/models/test_editorial.py::TestEditorAccount::test_auto_generation_of_public_id PASSED [ 74%]
tests/db/models/test_editorial.py::TestEditorAccount::test_timestamp_auto_population FAILED [ 74%]
tests/db/models/test_editorial.py::TestEditorAccount::test_editor_role_enum_validation PASSED [ 75%]
tests/db/models/test_editorial.py::TestEditorAccount::test_editor_account_relationships PASSED [ 75%]
tests/db/models/test_editorial.py::TestEditorAccount::test_cascade_delete_removes_scopes PASSED [ 75%]
tests/db/models/test_editorial.py::TestEditorAccount::test_email_uniqueness_enforced PASSED [ 76%]
tests/db/models/test_editorial.py::TestEditorAccount::test_public_id_uniqueness_enforced PASSED [ 76%]
tests/db/models/test_editorial.py::TestEditorAccount::test_required_fields_validation PASSED [ 76%]
tests/db/models/test_editorial.py::TestEditorScope::test_create_editor_scope_at_subject_level PASSED [ 76%]
tests/db/models/test_editorial.py::TestEditorScope::test_create_editor_scope_at_chapter_level PASSED [ 77%]
tests/db/models/test_editorial.py::TestEditorScope::test_create_editor_scope_at_node_level PASSED [ 77%]
tests/db/models/test_editorial.py::TestEditorScope::test_scope_hierarchy_constraint FAILED [ 77%]
tests/db/models/test_editorial.py::TestEditorScope::test_scope_cascade_delete_from_editor PASSED [ 78%]
tests/db/models/test_editorial.py::TestEditorScope::test_scope_cascade_delete_from_subject PASSED [ 78%]
tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_editor_auth_dependency_valid_token FAILED [ 78%]
tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_editor_auth_dependency_missing_token PASSED [ 79%]
tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_editor_auth_dependency_invalid_token PASSED [ 79%]
tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_editor_auth_dependency_expired_token FAILED [ 79%]
tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_editor_auth_dependency_inactive_editor FAILED [ 79%]
tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_editor_auth_dependency_nonexistent_editor FAILED [ 80%]
tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_admin_auth_dependency_admin_allowed FAILED [ 80%]
tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_admin_auth_dependency_editor_rejected FAILED [ 80%]
tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_require_editor_convenience_dependency FAILED [ 81%]
tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_require_admin_convenience_dependency FAILED [ 81%]
tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_malformed_token_payload PASSED [ 81%]
tests/integration/test_editorial_workflow.py::TestEditorialWorkflowIntegration::test_complete_editorial_workflow ERROR [ 81%]
tests/integration/test_editorial_workflow.py::TestEditorialWorkflowIntegration::test_rejection_workflow ERROR [ 82%]
tests/integration/test_editorial_workflow.py::TestEditorialWorkflowIntegration::test_concurrent_editor_access ERROR [ 82%]
tests/integration/test_editorial_workflow.py::TestEditorialWorkflowIntegration::test_media_workflow ERROR [ 82%]
tests/integration/test_pause_resume_flow.py::TestPauseResumeFlow::test_complete_monthly_pause_resume_flow ERROR [ 83%]
tests/integration/test_pause_resume_flow.py::TestPauseResumeFlow::test_complete_yearly_pause_resume_flow ERROR [ 83%]
tests/integration/test_pause_resume_flow.py::TestPauseResumeFlow::test_scheduled_pause_activation_flow ERROR [ 83%]
tests/integration/test_pause_resume_flow.py::TestPauseResumeFlow::test_cancel_scheduled_pause_flow ERROR [ 83%]
tests/integration/test_pause_resume_flow.py::TestPauseResumeFlow::test_multiple_pause_attempts_blocked ERROR [ 84%]
tests/integration/test_pause_resume_flow.py::TestPauseResumeFlow::test_pause_window_enforcement ERROR [ 84%]
tests/services/draft_management/tasks/test_cleanup_tasks.py::TestDraftCleanupTasks::test_cleanup_old_draft_media FAILED [ 84%]
tests/services/draft_management/tasks/test_cleanup_tasks.py::TestDraftCleanupTasks::test_cleanup_stale_drafts FAILED [ 85%]
tests/services/draft_management/tasks/test_cleanup_tasks.py::TestDraftCleanupTasks::test_release_abandoned_assignments FAILED [ 85%]
tests/services/draft_management/tasks/test_cleanup_tasks.py::TestDraftCleanupTasks::test_generate_cleanup_report FAILED [ 85%]
tests/services/draft_management/tasks/test_cleanup_tasks.py::TestDraftCleanupTasks::test_run_all_cleanup_tasks FAILED [ 86%]
tests/services/draft_management/tasks/test_cleanup_tasks.py::TestDraftCleanupTasks::test_run_all_cleanup_tasks_with_errors FAILED [ 86%]
tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_release_draft_by_assigned_editor FAILED [ 86%]
tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_release_draft_by_admin FAILED [ 86%]
tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_release_draft_by_unassigned_editor FAILED [ 87%]
tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_release_draft_wrong_status FAILED [ 87%]
tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_check_editor_workload FAILED [ 87%]
tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_get_available_editors ERROR [ 88%]
tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_reassign_draft FAILED [ 88%]
tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_reassign_draft_non_admin FAILED [ 88%]
tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_auto_release_abandoned_drafts FAILED [ 88%]
tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_list_drafts_with_filters FAILED [ 89%]
tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_list_drafts_respects_scope FAILED [ 89%]
tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_claim_draft_success FAILED [ 89%]
tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_claim_draft_already_assigned FAILED [ 90%]
tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_claim_draft_no_scope FAILED [ 90%]
tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_update_draft_success FAILED [ 90%]
tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_update_draft_not_assigned FAILED [ 90%]
tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_accept_draft_success FAILED [ 91%]
tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_accept_draft_incomplete FAILED [ 91%]
tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_reject_draft_by_admin FAILED [ 91%]
tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_reject_draft_non_admin FAILED [ 92%]
tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_get_draft_detail FAILED [ 92%]
tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_get_draft_detail_no_access FAILED [ 92%]
tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_publish_draft_success FAILED [ 93%]
tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_publish_draft_wrong_status ERROR [ 93%]
tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_publish_draft_incomplete_data FAILED [ 93%]
tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_publish_draft_no_scope FAILED [ 93%]
tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_publish_draft_with_media FAILED [ 94%]
tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_bulk_publish_success FAILED [ 94%]
tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_bulk_publish_partial_failure FAILED [ 94%]
tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_process_media_references FAILED [ 95%]
tests/services/draft_management/test_scope_authorization.py::TestScopeBasedAuthorization::test_editor_can_access_drafts_in_subject_scope FAILED [ 95%]
tests/services/draft_management/test_scope_authorization.py::TestScopeBasedAuthorization::test_editor_can_access_drafts_in_chapter_scope FAILED [ 95%]
tests/services/draft_management/test_scope_authorization.py::TestScopeBasedAuthorization::test_editor_can_access_drafts_in_node_scope PASSED [ 95%]
tests/services/draft_management/test_scope_authorization.py::TestScopeBasedAuthorization::test_editor_cannot_access_drafts_outside_scope FAILED [ 96%]
tests/services/draft_management/test_scope_authorization.py::TestScopeBasedAuthorization::test_admin_can_access_all_drafts PASSED [ 96%]
tests/services/draft_management/test_scope_authorization.py::TestScopeBasedAuthorization::test_editor_with_multiple_scopes PASSED [ 96%]
tests/services/draft_management/test_scope_authorization.py::TestScopeBasedAuthorization::test_scope_hierarchy_enforcement FAILED [ 97%]
tests/services/media/test_draft_media_service.py::TestDraftMediaService::test_generate_storage_path PASSED [ 97%]
tests/services/media/test_draft_media_service.py::TestDraftMediaService::test_generate_storage_path_without_extension PASSED [ 97%]
tests/services/media/test_draft_media_service.py::TestDraftMediaService::test_get_draft_media_url_success PASSED [ 97%]
tests/services/media/test_draft_media_service.py::TestDraftMediaService::test_get_draft_media_url_fallback PASSED [ 98%]
tests/services/media/test_draft_media_service.py::TestDraftMediaService::test_upload_draft_media_success ERROR [ 98%]
tests/services/media/test_draft_media_service.py::TestDraftMediaService::test_upload_draft_media_s3_error ERROR [ 98%]
tests/services/media/test_draft_media_service.py::TestDraftMediaService::test_copy_to_production_image FAILED [ 99%]
tests/services/media/test_draft_media_service.py::TestDraftMediaService::test_delete_draft_media FAILED [ 99%]
tests/services/media/test_draft_media_service.py::TestDraftMediaService::test_list_draft_media PASSED [ 99%]
tests/services/media/test_draft_media_service.py::TestDraftMediaService::test_cleanup_old_draft_media FAILED [100%]

=============================== warnings summary ===============================
../../../../../../Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pydantic/fields.py:1045
  /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pydantic/fields.py:1045: PydanticDeprecatedSince20: `min_items` is deprecated and will be removed, use `min_length` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/
    warn('`min_items` is deprecated and will be removed, use `min_length` instead', DeprecationWarning)

../../../../../../Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pydantic/fields.py:1051
  /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pydantic/fields.py:1051: PydanticDeprecatedSince20: `max_items` is deprecated and will be removed, use `max_length` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/
    warn('`max_items` is deprecated and will be removed, use `max_length` instead', DeprecationWarning)

tests/api/v1/app/auth/child/test_verify_parent_assignment.py::test_verify_parent_assignment_new_parent_success
  /Users/<USER>/Coding/project_edu/project_luxedu/project_edu_backend/api/v1/app/auth/child/services/verify_parent_assignment.py:250: SAWarning: DELETE statement on table 'parent_assignment' expected to delete 1 row(s); 0 were matched.  Please set confirm_deleted_rows=False within the mapper configuration to prevent this warning.
    self.db.commit()

tests/api/v1/app/auth/child/test_verify_parent_assignment.py::test_verify_parent_assignment_existing_parent_success
  /Users/<USER>/Coding/project_edu/project_luxedu/project_edu_backend/api/v1/app/auth/child/services/verify_parent_assignment.py:159: SAWarning: DELETE statement on table 'parent_assignment' expected to delete 1 row(s); 0 were matched.  Please set confirm_deleted_rows=False within the mapper configuration to prevent this warning.
    self.db.commit()

tests/services/draft_management/tasks/test_cleanup_tasks.py::TestDraftCleanupTasks::test_cleanup_stale_drafts
  /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/compiler.py:3823: RuntimeWarning: coroutine 'DraftCleanupTasks.cleanup_old_draft_media' was never awaited
    ret = self.bindparam_string(
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/services/draft_management/tasks/test_cleanup_tasks.py::TestDraftCleanupTasks::test_generate_cleanup_report
  /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/unitofwork.py:608: RuntimeWarning: coroutine 'DraftCleanupTasks.cleanup_stale_drafts' was never awaited
    for mapper in self._mappers(uow):
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/services/draft_management/tasks/test_cleanup_tasks.py::TestDraftCleanupTasks::test_generate_cleanup_report
  /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/unitofwork.py:608: RuntimeWarning: coroutine 'DraftCleanupTasks.release_abandoned_assignments' was never awaited
    for mapper in self._mappers(uow):
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_release_draft_wrong_status
  /Users/<USER>/Coding/project_edu/project_luxedu/project_edu_backend/tests/services/draft_management/test_assignment_service.py:76: RuntimeWarning: coroutine 'AssignmentService.release_draft' was never awaited
    AssignmentService.release_draft(
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_claim_draft_already_assigned
  /Users/<USER>/Coding/project_edu/project_luxedu/project_edu_backend/tests/services/draft_management/test_draft_service.py:89: RuntimeWarning: coroutine 'DraftManagementService.claim_draft' was never awaited
    DraftManagementService.claim_draft(
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_claim_draft_no_scope
  /Users/<USER>/Coding/project_edu/project_luxedu/project_edu_backend/tests/services/draft_management/test_draft_service.py:103: RuntimeWarning: coroutine 'DraftManagementService.claim_draft' was never awaited
    DraftManagementService.claim_draft(
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_update_draft_success
  /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/session.py:1038: RuntimeWarning: coroutine 'DraftManagementService.claim_draft' was never awaited
    bind = self.session.get_bind(bindkey, **kwargs)
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_accept_draft_incomplete
  /Users/<USER>/Coding/project_edu/project_luxedu/project_edu_backend/tests/services/draft_management/test_draft_service.py:182: RuntimeWarning: coroutine 'DraftManagementService.accept_draft' was never awaited
    DraftManagementService.accept_draft(
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_reject_draft_non_admin
  /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/unitofwork.py:495: RuntimeWarning: coroutine 'DraftCleanupTasks.run_all_cleanup_tasks' was never awaited
    return iter(
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_reject_draft_non_admin
  /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/unitofwork.py:495: RuntimeWarning: coroutine 'DraftManagementService.accept_draft' was never awaited
    return iter(
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_reject_draft_non_admin
  /Users/<USER>/Coding/project_edu/project_luxedu/project_edu_backend/tests/services/draft_management/test_draft_service.py:226: RuntimeWarning: coroutine 'DraftManagementService.reject_draft_by_admin' was never awaited
    DraftManagementService.reject_draft_by_admin(
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_get_draft_detail_no_access
  /Users/<USER>/Coding/project_edu/project_luxedu/project_edu_backend/tests/services/draft_management/test_draft_service.py:264: RuntimeWarning: coroutine 'DraftManagementService.get_draft_detail' was never awaited
    DraftManagementService.get_draft_detail(
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_publish_draft_success
  /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/elements.py:691: RuntimeWarning: coroutine 'DraftManagementService.get_draft_detail' was never awaited
    elem_cache_key = self._generate_cache_key()
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_publish_draft_incomplete_data
  /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/result.py:276: RuntimeWarning: coroutine 'PublishingService.publish_draft' was never awaited
    recs_names = [
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_publish_draft_incomplete_data
  /Users/<USER>/Coding/project_edu/project_luxedu/project_edu_backend/tests/services/draft_management/test_publishing_service.py:110: RuntimeWarning: coroutine 'PublishingService.publish_draft' was never awaited
    PublishingService.publish_draft(
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_publish_draft_no_scope
  /Users/<USER>/Coding/project_edu/project_luxedu/project_edu_backend/tests/services/draft_management/test_publishing_service.py:129: RuntimeWarning: coroutine 'PublishingService.publish_draft' was never awaited
    PublishingService.publish_draft(
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_bulk_publish_partial_failure
  /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py:3947: RuntimeWarning: coroutine 'PublishingService.publish_draft' was never awaited
    visited_states: Set[InstanceState[Any]] = set()
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/services/draft_management/test_scope_authorization.py::TestScopeBasedAuthorization::test_editor_can_access_drafts_in_subject_scope
  /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/result.py:541: RuntimeWarning: coroutine 'PublishingService.bulk_publish' was never awaited
    return [make_row(row) for row in rows]
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/services/draft_management/test_scope_authorization.py::TestScopeBasedAuthorization::test_editor_can_access_drafts_in_subject_scope
  /Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/result.py:541: RuntimeWarning: coroutine 'PublishingService._process_media_references' was never awaited
    return [make_row(row) for row in rows]
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
=========================== short test summary info ============================
FAILED tests/api/v1/app/auth/parent/test_trigger_pin_reset.py::test_trigger_pin_reset_too_many_requests[parent_for_pin_reset0]
FAILED tests/api/v1/app/parent/account-settings/test_get_account_settings.py::test_get_account_settings_success_with_subscription
FAILED tests/api/v1/app/parent/account-settings/test_get_account_settings.py::test_get_account_settings_no_active_subscription
FAILED tests/api/v1/app/parent/account-settings/test_get_account_settings.py::test_get_account_settings_unauthorized_no_token
FAILED tests/api/v1/app/parent/account-settings/test_get_account_settings.py::test_get_account_settings_with_child_token
FAILED tests/api/v1/app/parent/account-settings/test_update_account_settings.py::test_update_account_settings_language_success
FAILED tests/api/v1/app/parent/account-settings/test_update_account_settings.py::test_update_account_settings_unauthorized_no_token
FAILED tests/api/v1/app/parent/account-settings/test_update_account_settings.py::test_update_account_settings_with_child_token
FAILED tests/api/v1/app/parent/account-settings/test_update_account_settings.py::test_update_account_settings_invalid_language_code
FAILED tests/api/v1/app/parent/dashboard/test_add_child.py::test_add_child_email_conflict_parent
FAILED tests/api/v1/app/parent/subscription/services/test_activate_pause_service.py::TestActivatePauseService::test_activate_pause_missing_subscription
FAILED tests/api/v1/app/student/test_get_dashboard.py::test_get_dashboard_success
FAILED tests/api/v1/app/student/test_get_learning_node_content.py::test_get_learning_node_content_success
FAILED tests/api/v1/app/student/test_get_progress_overview.py::test_get_progress_overview_success
FAILED tests/api/v1/editorial/auth/test_editor_login.py::TestEditorLogin::test_editor_login_success
FAILED tests/api/v1/editorial/auth/test_editor_login.py::TestEditorLogin::test_admin_login_success
FAILED tests/api/v1/editorial/auth/test_editor_login.py::TestEditorLogin::test_editor_login_inactive_account
FAILED tests/api/v1/editorial/test_admin_endpoints.py::TestAdminEndpoints::test_publish_draft
FAILED tests/api/v1/editorial/test_admin_endpoints.py::TestAdminEndpoints::test_bulk_publish
FAILED tests/api/v1/editorial/test_admin_endpoints.py::TestAdminEndpoints::test_non_admin_cannot_publish
FAILED tests/api/v1/editorial/test_admin_endpoints.py::TestAdminEndpoints::test_add_editor_scope
FAILED tests/api/v1/editorial/test_admin_endpoints.py::TestAdminEndpoints::test_query_audit_logs
FAILED tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_list_drafts
FAILED tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_get_draft_detail
FAILED tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_claim_draft
FAILED tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_update_draft
FAILED tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_accept_draft
FAILED tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_release_draft
FAILED tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_upload_media
FAILED tests/api/v1/editorial/test_editor_endpoints.py::TestEditorEndpoints::test_list_draft_media
FAILED tests/api/v1/other/test_student_contact.py::test_successful_student_contact_form_submission
FAILED tests/api/v1/other/test_student_contact.py::test_student_contact_form_unauthenticated
FAILED tests/api/v1/other/test_student_contact.py::test_student_contact_form_service_error
FAILED tests/api/v1/tasks/internal/test_activate_pause.py::TestActivatePauseInternal::test_activate_pause_missing_subscription
FAILED tests/db/models/test_draft_media.py::TestDraftMediaFile::test_create_draft_media_image
FAILED tests/db/models/test_draft_media.py::TestDraftMediaFile::test_create_draft_media_audio
FAILED tests/db/models/test_draft_media.py::TestDraftMediaFile::test_media_draft_association
FAILED tests/db/models/test_draft_media.py::TestDraftMediaFile::test_cascade_delete_when_draft_deleted
FAILED tests/db/models/test_draft_media.py::TestDraftMediaFile::test_published_media_reference
FAILED tests/db/models/test_editorial.py::TestEditorAccount::test_timestamp_auto_population
FAILED tests/db/models/test_editorial.py::TestEditorScope::test_scope_hierarchy_constraint
FAILED tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_editor_auth_dependency_valid_token
FAILED tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_editor_auth_dependency_expired_token
FAILED tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_editor_auth_dependency_inactive_editor
FAILED tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_editor_auth_dependency_nonexistent_editor
FAILED tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_admin_auth_dependency_admin_allowed
FAILED tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_admin_auth_dependency_editor_rejected
FAILED tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_require_editor_convenience_dependency
FAILED tests/dependencies/test_editorial_auth_dependency.py::TestEditorAuthDependency::test_require_admin_convenience_dependency
FAILED tests/services/draft_management/tasks/test_cleanup_tasks.py::TestDraftCleanupTasks::test_cleanup_old_draft_media
FAILED tests/services/draft_management/tasks/test_cleanup_tasks.py::TestDraftCleanupTasks::test_cleanup_stale_drafts
FAILED tests/services/draft_management/tasks/test_cleanup_tasks.py::TestDraftCleanupTasks::test_release_abandoned_assignments
FAILED tests/services/draft_management/tasks/test_cleanup_tasks.py::TestDraftCleanupTasks::test_generate_cleanup_report
FAILED tests/services/draft_management/tasks/test_cleanup_tasks.py::TestDraftCleanupTasks::test_run_all_cleanup_tasks
FAILED tests/services/draft_management/tasks/test_cleanup_tasks.py::TestDraftCleanupTasks::test_run_all_cleanup_tasks_with_errors
FAILED tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_release_draft_by_assigned_editor
FAILED tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_release_draft_by_admin
FAILED tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_release_draft_by_unassigned_editor
FAILED tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_release_draft_wrong_status
FAILED tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_check_editor_workload
FAILED tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_reassign_draft
FAILED tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_reassign_draft_non_admin
FAILED tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_auto_release_abandoned_drafts
FAILED tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_list_drafts_with_filters
FAILED tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_list_drafts_respects_scope
FAILED tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_claim_draft_success
FAILED tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_claim_draft_already_assigned
FAILED tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_claim_draft_no_scope
FAILED tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_update_draft_success
FAILED tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_update_draft_not_assigned
FAILED tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_accept_draft_success
FAILED tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_accept_draft_incomplete
FAILED tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_reject_draft_by_admin
FAILED tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_reject_draft_non_admin
FAILED tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_get_draft_detail
FAILED tests/services/draft_management/test_draft_service.py::TestDraftManagementService::test_get_draft_detail_no_access
FAILED tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_publish_draft_success
FAILED tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_publish_draft_incomplete_data
FAILED tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_publish_draft_no_scope
FAILED tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_publish_draft_with_media
FAILED tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_bulk_publish_success
FAILED tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_bulk_publish_partial_failure
FAILED tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_process_media_references
FAILED tests/services/draft_management/test_scope_authorization.py::TestScopeBasedAuthorization::test_editor_can_access_drafts_in_subject_scope
FAILED tests/services/draft_management/test_scope_authorization.py::TestScopeBasedAuthorization::test_editor_can_access_drafts_in_chapter_scope
FAILED tests/services/draft_management/test_scope_authorization.py::TestScopeBasedAuthorization::test_editor_cannot_access_drafts_outside_scope
FAILED tests/services/draft_management/test_scope_authorization.py::TestScopeBasedAuthorization::test_scope_hierarchy_enforcement
FAILED tests/services/media/test_draft_media_service.py::TestDraftMediaService::test_copy_to_production_image
FAILED tests/services/media/test_draft_media_service.py::TestDraftMediaService::test_delete_draft_media
FAILED tests/services/media/test_draft_media_service.py::TestDraftMediaService::test_cleanup_old_draft_media
ERROR tests/api/v1/app/parent/subscription/services/test_activate_pause_service.py::TestActivatePauseService::test_activate_pause_monthly_subscription
ERROR tests/api/v1/app/parent/subscription/services/test_activate_pause_service.py::TestActivatePauseService::test_activate_pause_yearly_subscription
ERROR tests/api/v1/app/parent/subscription/services/test_activate_pause_service.py::TestActivatePauseService::test_activate_pause_idempotency
ERROR tests/api/v1/app/parent/subscription/services/test_activate_pause_service.py::TestActivatePauseService::test_activate_pause_missing_pause_record
ERROR tests/api/v1/app/parent/subscription/services/test_activate_pause_service.py::TestActivatePauseService::test_activate_pause_mismatch
ERROR tests/api/v1/app/parent/subscription/services/test_pause_subscription_service.py::TestPauseSubscriptionService::test_pause_monthly_subscription_immediate
ERROR tests/api/v1/app/parent/subscription/services/test_pause_subscription_service.py::TestPauseSubscriptionService::test_pause_yearly_subscription_immediate
ERROR tests/api/v1/app/parent/subscription/services/test_pause_subscription_service.py::TestPauseSubscriptionService::test_pause_subscription_scheduled
ERROR tests/api/v1/app/parent/subscription/services/test_pause_subscription_service.py::TestPauseSubscriptionService::test_pause_already_paused_subscription
ERROR tests/api/v1/app/parent/subscription/services/test_pause_subscription_service.py::TestPauseSubscriptionService::test_pause_outside_visibility_window
ERROR tests/api/v1/app/parent/subscription/services/test_resume_subscription_service.py::TestResumeSubscriptionService::test_resume_monthly_subscription
ERROR tests/api/v1/app/parent/subscription/services/test_resume_subscription_service.py::TestResumeSubscriptionService::test_resume_yearly_subscription_with_credit
ERROR tests/api/v1/app/parent/subscription/services/test_resume_subscription_service.py::TestResumeSubscriptionService::test_resume_non_paused_subscription
ERROR tests/api/v1/app/parent/subscription/services/test_resume_subscription_service.py::TestResumeSubscriptionService::test_resume_scheduled_pause
ERROR tests/api/v1/app/parent/subscription/test_cancel_scheduled_pause.py::TestCancelScheduledPauseRoute::test_cancel_scheduled_pause_success
ERROR tests/api/v1/app/parent/subscription/test_cancel_scheduled_pause.py::TestCancelScheduledPauseRoute::test_cancel_scheduled_pause_no_scheduled_pause
ERROR tests/api/v1/app/parent/subscription/test_cancel_scheduled_pause.py::TestCancelScheduledPauseRoute::test_cancel_already_active_pause
ERROR tests/api/v1/app/parent/subscription/test_cancel_scheduled_pause.py::TestCancelScheduledPauseRoute::test_cancel_scheduled_pause_qstash_failure
ERROR tests/api/v1/app/parent/subscription/test_cancel_scheduled_pause.py::TestCancelScheduledPauseRoute::test_cancel_scheduled_pause_unauthorized
ERROR tests/api/v1/app/parent/subscription/test_cancel_scheduled_pause.py::TestCancelScheduledPauseRoute::test_cancel_scheduled_pause_wrong_parent
ERROR tests/api/v1/app/parent/subscription/test_cancel_scheduled_pause.py::TestCancelScheduledPauseRoute::test_cancel_multiple_scheduled_pauses
ERROR tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py::TestPauseResumeEdgeCases::test_pause_with_stripe_api_error
ERROR tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py::TestPauseResumeEdgeCases::test_pause_subscription_at_exact_window_boundaries
ERROR tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py::TestPauseResumeEdgeCases::test_resume_subscription_at_pause_end_date
ERROR tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py::TestPauseResumeEdgeCases::test_yearly_subscription_pause_with_zero_duration
ERROR tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py::TestPauseResumeEdgeCases::test_concurrent_pause_requests
ERROR tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py::TestPauseResumeEdgeCases::test_pause_with_invalid_subscription_status
ERROR tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py::TestPauseResumeEdgeCases::test_qstash_activation_with_deleted_subscription
ERROR tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py::TestPauseResumeEdgeCases::test_pause_with_stripe_subscription_missing_items
ERROR tests/api/v1/app/parent/subscription/test_pause_resume_edge_cases.py::TestPauseResumeEdgeCases::test_database_rollback_on_pause_failure
ERROR tests/api/v1/app/parent/subscription/test_pause_subscription.py::TestPauseSubscriptionRoute::test_pause_subscription_success
ERROR tests/api/v1/app/parent/subscription/test_pause_subscription.py::TestPauseSubscriptionRoute::test_pause_subscription_scheduled
ERROR tests/api/v1/app/parent/subscription/test_pause_subscription.py::TestPauseSubscriptionRoute::test_pause_subscription_already_paused
ERROR tests/api/v1/app/parent/subscription/test_pause_subscription.py::TestPauseSubscriptionRoute::test_pause_subscription_unauthorized
ERROR tests/api/v1/app/parent/subscription/test_pause_subscription.py::TestPauseSubscriptionRoute::test_pause_subscription_wrong_parent
ERROR tests/api/v1/app/parent/subscription/test_pause_subscription.py::TestPauseSubscriptionRoute::test_pause_yearly_subscription_different_behavior
ERROR tests/api/v1/app/parent/subscription/test_resume_subscription.py::TestResumeSubscriptionRoute::test_resume_monthly_subscription_success
ERROR tests/api/v1/app/parent/subscription/test_resume_subscription.py::TestResumeSubscriptionRoute::test_resume_yearly_subscription_with_credit
ERROR tests/api/v1/app/parent/subscription/test_resume_subscription.py::TestResumeSubscriptionRoute::test_resume_non_paused_subscription
ERROR tests/api/v1/app/parent/subscription/test_resume_subscription.py::TestResumeSubscriptionRoute::test_resume_scheduled_pause_not_allowed
ERROR tests/api/v1/app/parent/subscription/test_resume_subscription.py::TestResumeSubscriptionRoute::test_resume_subscription_unauthorized
ERROR tests/api/v1/app/parent/subscription/test_resume_subscription.py::TestResumeSubscriptionRoute::test_resume_subscription_wrong_parent
ERROR tests/api/v1/app/parent/subscription/test_resume_subscription.py::TestResumeSubscriptionRoute::test_resume_subscription_stripe_error_handling
ERROR tests/api/v1/app/parent/subscription/test_webhook_subscription_deleted.py::TestWebhookSubscriptionDeleted::test_subscription_deleted_removes_price_eligibility
ERROR tests/api/v1/app/parent/subscription/test_webhook_subscription_deleted.py::TestWebhookSubscriptionDeleted::test_subscription_deleted_keeps_eligibility_with_other_active_subs
ERROR tests/api/v1/app/parent/subscription/test_webhook_subscription_deleted.py::TestWebhookSubscriptionDeleted::test_subscription_deleted_no_price_eligibility
ERROR tests/api/v1/app/parent/subscription/test_webhook_subscription_deleted.py::TestWebhookSubscriptionDeleted::test_webhook_endpoint_routes_deleted_event
ERROR tests/api/v1/app/student/test_get_subject.py::test_get_subject_success
ERROR tests/api/v1/app/student/test_get_subject.py::test_get_subject_not_found
ERROR tests/api/v1/app/student/test_get_subject.py::test_get_subject_unauthorized_no_token
ERROR tests/api/v1/tasks/internal/test_activate_pause.py::TestActivatePauseInternal::test_activate_pause_monthly_success
ERROR tests/api/v1/tasks/internal/test_activate_pause.py::TestActivatePauseInternal::test_activate_pause_yearly_success
ERROR tests/api/v1/tasks/internal/test_activate_pause.py::TestActivatePauseInternal::test_activate_pause_idempotency
ERROR tests/api/v1/tasks/internal/test_activate_pause.py::TestActivatePauseInternal::test_activate_pause_invalid_signature
ERROR tests/api/v1/tasks/internal/test_activate_pause.py::TestActivatePauseInternal::test_activate_pause_missing_pause_record
ERROR tests/api/v1/tasks/internal/test_activate_pause.py::TestActivatePauseInternal::test_activate_pause_mismatch
ERROR tests/api/v1/tasks/internal/test_activate_pause.py::TestActivatePauseInternal::test_activate_pause_retry_behavior
ERROR tests/integration/test_editorial_workflow.py::TestEditorialWorkflowIntegration::test_complete_editorial_workflow
ERROR tests/integration/test_editorial_workflow.py::TestEditorialWorkflowIntegration::test_rejection_workflow
ERROR tests/integration/test_editorial_workflow.py::TestEditorialWorkflowIntegration::test_concurrent_editor_access
ERROR tests/integration/test_editorial_workflow.py::TestEditorialWorkflowIntegration::test_media_workflow
ERROR tests/integration/test_pause_resume_flow.py::TestPauseResumeFlow::test_complete_monthly_pause_resume_flow
ERROR tests/integration/test_pause_resume_flow.py::TestPauseResumeFlow::test_complete_yearly_pause_resume_flow
ERROR tests/integration/test_pause_resume_flow.py::TestPauseResumeFlow::test_scheduled_pause_activation_flow
ERROR tests/integration/test_pause_resume_flow.py::TestPauseResumeFlow::test_cancel_scheduled_pause_flow
ERROR tests/integration/test_pause_resume_flow.py::TestPauseResumeFlow::test_multiple_pause_attempts_blocked
ERROR tests/integration/test_pause_resume_flow.py::TestPauseResumeFlow::test_pause_window_enforcement
ERROR tests/services/draft_management/test_assignment_service.py::TestAssignmentService::test_get_available_editors
ERROR tests/services/draft_management/test_publishing_service.py::TestPublishingService::test_publish_draft_wrong_status
ERROR tests/services/media/test_draft_media_service.py::TestDraftMediaService::test_upload_draft_media_success
ERROR tests/services/media/test_draft_media_service.py::TestDraftMediaService::test_upload_draft_media_s3_error
=========== 90 failed, 182 passed, 23 warnings, 71 errors in 48.25s ============
sys:1: RuntimeWarning: coroutine 'DraftManagementService.reject_draft_by_admin' was never awaited
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
sys:1: RuntimeWarning: coroutine 'PublishingService.bulk_publish' was never awaited
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
