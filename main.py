from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

from core.config import settings
from core.config.logging_config import setup_logging
from core.exception_handling.exception_handlers import service_exception_handler, generic_exception_handler
from core.exception_handling.exceptions.custom_exceptions import ServiceError, NotFoundError 
from middleware.request_context_middleware import RequestContextMiddleware
from middleware.client_ip_middleware import ClientIPMiddleware

# Import API Routers
from api.v1.app.auth.child.router import router as child_auth_router
from api.v1.app.auth.parent.router import router as parent_auth_router
from api.v1.app.parent.account_settings.router import router as parent_account_settings_router
from api.v1.app.parent.dashboard.router import router as parent_dashboard_router
from api.v1.app.parent.subscription.router import router as parent_subscription_router
from api.v1.app.student.router import router as student_app_router 
from api.v1.landing.router import router as landing_router
from api.v1.other.router import router as other_router
from api.v1.tasks.internal.router import router as internal_tasks_router
from api.v1.editorial import editorial_router
import sentry_sdk
from sentry_sdk.integrations.sys_exit import SysExitIntegration

import db.models   # noqa: F401 Important as it registers the models

# Call setup_logging() very early, before FastAPI app initialization
setup_logging()

# Initialize Sentry in production environment
if settings.ENVIRONMENT not in ['dev', 'test'] and hasattr(settings, 'SENTRY_DSN') and settings.SENTRY_DSN:
    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        traces_sample_rate=1.0,
        profiles_sample_rate=1.0,
        integrations=[SysExitIntegration()]
    )

app = None
if settings.ENVIRONMENT == 'dev':
    app = FastAPI()
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
else:
    app = FastAPI(redoc_url=None, docs_url=None)
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# Middleware Registration Order (executed in reverse order)
# 1. RequestContextMiddleware runs second (needs client_ip from ClientIPMiddleware)
# 2. ClientIPMiddleware runs first (sets request.state.client_ip)
# Note: In FastAPI, middleware is executed in reverse order of registration
app.add_middleware(RequestContextMiddleware)
app.add_middleware(ClientIPMiddleware)


# Register new global exception handlers
app.add_exception_handler(NotFoundError, service_exception_handler)
app.add_exception_handler(ServiceError, service_exception_handler)
app.add_exception_handler(Exception, generic_exception_handler)


# Include API v1 Routers
app.include_router(child_auth_router)     # Prefix is defined within the router file
app.include_router(parent_auth_router)    # Prefix is defined within the router file
app.include_router(
    parent_account_settings_router,
    prefix="/api/v1/parent",
    tags=["Parent Account Settings"]
)  # Add prefix/tags here if not in router file
app.include_router(
    parent_dashboard_router,
    # prefix="/api/v1/parent",  # Prefix is defined within the router file
    tags=["Parent Dashboard"]
)  # Prefix is defined within the router file
app.include_router(
    parent_subscription_router,
    tags=["Parent Subscription"]
)  # Prefix is defined within the router file
app.include_router(
    student_app_router,
    prefix="/api/v1",
    tags=["Student App"]
)  # Correct prefix -> student router already has teh stuent prefix
app.include_router(
    landing_router,
    prefix="/api/v1",
    tags=["Landing"]
)  # Prefix is defined within the router file
app.include_router(
    other_router,
    prefix="/api/v1/other",
    tags=["Other"]
)  # Add prefix/tags here - includes both contact and student_contact
app.include_router(
    internal_tasks_router,
    tags=["Internal Tasks"]
)  # Prefix is defined within the router file
app.include_router(
    editorial_router,
    prefix="/api/v1",
    tags=["Editorial"]
)  # Editorial routes for admin and editor functionality
# Add other routers as needed, e.g., combined_auth_router if used.