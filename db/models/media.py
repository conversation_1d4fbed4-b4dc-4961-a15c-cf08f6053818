# /Users/<USER>/Coding/project_edu/project_edu_backend/db/models/audio_file.py

import enum
import uuid
from datetime import datetime, timezone

from sqlalchemy import (Column, DateTime, Enum, Float, ForeignKey, Index,
                        Integer, String, Text, UniqueConstraint)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.ext.hybrid import hybrid_property
from datetime import UTC

from db.database import SqlAlchemyBase

# Assuming you have a Base defined elsewhere, e.g., in a db.base module
# If not, you'd define it here:
# from sqlalchemy.ext.declarative import declarative_base
# Base = declarative_base()

# Re-import Base from exercise.py for standalone execution context if needed,
# but ideally, Base should come from a central location.

# ============================================================================
# Enums
# ============================================================================

class AudioFileTypeEnum(str, enum.Enum):
    """Categorizes the primary purpose or context of the audio file."""
    VOCABULARY_PRONUNCIATION = "vocabulary-pronunciation"       # Audio demonstrating pronunciation
    LISTENING_COMPREHENSION = "listening-comprehension" # Audio used for listening comprehension
    PRONUNCIATION = "pronunciation" # Audio explaining an exercise solution
    GENERAL = "general" # Audio that doesn't fit into any of the above categories

# ============================================================================
# SQLAlchemy Model for Audio Files
# ============================================================================

class AudioFile(SqlAlchemyBase):
    """
    Represents an audio file stored or referenced by the system.
    """
    __tablename__ = 'audio_file'

    # --- Core Identification ---
    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, index=True, nullable=False, default=lambda: str(uuid.uuid4()))

    # --- Storage Details ---
    # Path (folder/key) within the storage bucket where the file is located. The actual file is named with the public_id and an appropriate extension.
    storage_path = Column(String, nullable=False, comment="Path (folder/key) within the storage bucket where the audio file can be found. The file itself will be named using the public_id and appropriate extension.")

    # --- Metadata ---
    mime_type = Column(String, nullable=True, comment="MIME type of the audio file (e.g., 'audio/mp3', 'audio/wav').")

    duration_seconds = Column(Float, nullable=True, comment="Duration of the audio clip in seconds.")

    language_code = Column(String, nullable=True, index=True, comment="Language code (e.g., 'en', 'fr') if the audio contains speech.")

    description = Column(Text, nullable=True, comment="Optional description of the audio content or purpose.")

    transcription = Column(Text, nullable=True, comment="Optional transcription of the spoken content in the audio.")

    audio_type = Column(Enum(AudioFileTypeEnum), nullable=True, index=True,
        default=AudioFileTypeEnum.GENERAL,
        comment="Categorization of the audio file's primary purpose."
    )

    # --- Timestamps ---
    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))


    # --- Constraints and Indexes ---
    __table_args__ = (
        Index('ix_audio_files_storage_path', 'storage_path'),
    )

    def __repr__(self):
        return f"<AudioFile(id={self.id}, public_id='{self.public_id}', type='{self.audio_type}', path='{self.storage_path}')>"



# ============================================================================
# Video Enums
# ============================================================================

class VideoFileTypeEnum(str, enum.Enum):
    """Categorizes the primary purpose or context of the video file."""
    LECTURE = "lecture"                                 # Video content for a lecture
    EXERCISE_SOLUTION = "exercise-solution"             # Video explaining an exercise solution
    GENERAL = "general"                                 # Video that doesn't fit into any of the above categories

# ============================================================================
# SQLAlchemy Model for Video Files
# ============================================================================

class VideoFile(SqlAlchemyBase):
    """
    Represents a video file stored or referenced by the system.
    """
    __tablename__ = 'video_file'

    # --- Core Identification ---
    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, index=True, nullable=False, default=lambda: str(uuid.uuid4()))

    # --- Storage Details ---
    storage_id = Column(String, nullable=False, comment="ID of the video file within the storage destination.")
    storage_library_id = Column(String, nullable=False, comment="ID of the video file within the storage destination.")
    # --- Metadata ---
    mime_type = Column(String, nullable=True, comment="MIME type of the video file (e.g., 'video/mp4', 'video/webm').")
    duration_seconds = Column(Float, nullable=True, comment="Duration of the video clip in seconds.")
    language_code = Column(String, nullable=True, index=True, comment="Language code (e.g., 'en', 'fr') if the video contains speech or subtitles.")
    description = Column(Text, nullable=True, comment="Optional description of the video content or purpose.")
    transcription = Column(Text, nullable=True, comment="Optional transcription or subtitles of the spoken content in the video.")
    video_type = Column(Enum(VideoFileTypeEnum), nullable=True, index=True,
        default=VideoFileTypeEnum.GENERAL,
        comment="Categorization of the video file's primary purpose."
    )

    # --- Timestamps ---
    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))

    # --- Constraints and Indexes ---
    __table_args__ = (
        Index('ix_video_files_storage_id', 'storage_id'),
        Index('ix_video_files_storage_library_id', 'storage_library_id'),
    )

    def __repr__(self):
        return f"<VideoFile(id={self.id}, public_id='{self.public_id}', type='{self.video_type}', path='{self.storage_path}')>"


# ============================================================================
# Image Enums
# ============================================================================

class ImageFileTypeEnum(str, enum.Enum):
    """Categorizes the primary purpose or context of the image file."""
    EXERCISE_OPTION = "exercise-option"     # Image used as an option in multiple choice exercises
    EXERCISE_PROMPT = "exercise-prompt"     # Image used in exercise prompts
    SOLUTION_STEP = "solution-step"         # Image used in solution explanations
    GENERAL = "general"                     # Image that doesn't fit into any of the above categories

# ============================================================================
# SQLAlchemy Model for Image Files
# ============================================================================

class ImageFile(SqlAlchemyBase):
    """
    Represents an image file stored or referenced by the system.
    """
    __tablename__ = 'image_file'

    # --- Core Identification ---
    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, index=True, nullable=False, default=lambda: str(uuid.uuid4()))

    # --- Storage Details ---
    # Path (folder/key) within the storage bucket where the file is located. The actual file is named with the public_id and an appropriate extension.
    storage_path = Column(String, nullable=False, comment="Path (folder/key) within the storage bucket where the image file can be found. The file itself will be named using the public_id and appropriate extension.")

    # --- Metadata ---
    mime_type = Column(String, nullable=True, comment="MIME type of the image file (e.g., 'image/png', 'image/jpeg').")
    
    width = Column(Integer, nullable=True, comment="Width of the image in pixels.")
    
    height = Column(Integer, nullable=True, comment="Height of the image in pixels.")
    
    file_size_bytes = Column(Integer, nullable=True, comment="Size of the image file in bytes.")
    
    alt_text = Column(Text, nullable=True, comment="Alternative text for accessibility purposes.")
    
    description = Column(Text, nullable=True, comment="Optional description of the image content or purpose.")
    
    image_type = Column(
        Enum(ImageFileTypeEnum, name='imagefiletypeenum', create_constraint=True, 
             values_callable=lambda obj: [e.value for e in obj]), 
        nullable=True, index=True,
        default=ImageFileTypeEnum.GENERAL,
        comment="Categorization of the image file's primary purpose."
    )

    # --- Timestamps ---
    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))

    # --- Constraints and Indexes ---
    __table_args__ = (
        Index('ix_image_files_storage_path', 'storage_path'),
        Index('ix_image_files_public_id', 'public_id'),
    )

    def __repr__(self):
        return f"<ImageFile(id={self.id}, public_id='{self.public_id}', type='{self.image_type}', path='{self.storage_path}')>"