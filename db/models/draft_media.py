from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Enum as SqlEnum, JSON, Text
from sqlalchemy.orm import relationship
from datetime import datetime, UTC
from enum import Enum
import uuid
from db.database import SqlAlchemyBase

class DraftMediaType(str, Enum):
    IMAGE = "image"
    AUDIO = "audio"

class DraftMediaFile(SqlAlchemyBase):
    __tablename__ = 'draft_media_file'
    
    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, nullable=False, index=True, 
                       default=lambda: str(uuid.uuid4()))
    draft_exercise_id = Column(Integer, ForeignKey('draft_exercise.id', ondelete='CASCADE'), 
                              nullable=False, index=True)
    media_type = Column(SqlEnum(DraftMediaType, name='draft_media_type', create_constraint=False), nullable=False)
    storage_path = Column(String(512), nullable=False)
    original_filename = Column(String(255))
    content_type = Column(String(100))
    media_metadata = Column(JSON, default={})  # width/height for images, duration for audio
    production_media_id = Column(Integer)  # References to image_file.id or audio_file.id after publish
    copied_to_production_at = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC))
    
    # Relationships
    draft_exercise = relationship("DraftExercise", back_populates="media_files")