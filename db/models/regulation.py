from sqlalchemy import Inte<PERSON>, Text, Column, Integer, Foreign<PERSON>ey
from sqlalchemy.sql.sqltypes import DateTime
from db.database import SqlAlchemyBase
from datetime import datetime, UTC
import uuid

class CookiesConsent(SqlAlchemyBase):
    __tablename__ = 'cookies_consent'

    id = Column(Integer, primary_key=True)
    account_id = Column(Integer, ForeignKey(
        'account.id', ondelete='CASCADE'))

    account_email = Column(Text)

    public_id = Column(
        Text, default=lambda: str(uuid.uuid4()), unique=True, nullable=False)
    choices = Column(Text)
    
    user_agent = Column(Text)
    ip_address = Column(Text)

    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))


class MarketingConsent(SqlAlchemyBase):
    __tablename__ = 'marketing_consent'
    id = Column(Integer, primary_key=True)
    account_id = Column(Integer, Foreign<PERSON>ey(
        'account.id', ondelete='CASCADE'))
    email = Column(Text, nullable=False)

    public_id = Column(
        Text, default=lambda: str(uuid.uuid4()), unique=True, nullable=False)
    
    user_agent = Column(Text)
    privacy_policy_version = Column(Text)
    text = Column(Text)

    ip_address = Column(Text)
    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))
