from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Text
from sqlalchemy.orm import relationship
from datetime import datetime, UTC
from db.database import SqlAlchemyBase



class PublishBatch(SqlAlchemyBase):
    __tablename__ = 'publish_batch'
    
    id = Column(Integer, primary_key=True)
    created_by_id = Column(Integer, ForeignKey('editor_account.id'), nullable=False, index=True)
    batch_name = Column(String(255), nullable=False)
    batch_notes = Column(Text)
    draft_count = Column(Integer, default=0)
    success_count = Column(Integer, default=0)
    failure_count = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC))
    completed_at = Column(DateTime(timezone=True))
    
    # Relationships
    created_by = relationship("EditorAccount", back_populates="publish_batches")