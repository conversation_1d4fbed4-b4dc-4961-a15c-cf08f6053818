from sqlalchemy import Integer, Text, Column, ForeignKey, String
from sqlalchemy.sql.sqltypes import Boolean, DateTime
from sqlalchemy.orm import relationship
from db.database import SqlAlchemyBase
from datetime import datetime, UTC
import uuid
from enum import Enum
from sqlalchemy import Enum as SqlEnum



class LanguageEnum(str, Enum):
    LU = "lu"
    EN = "en"
    DE = "de"
    FR = "fr"


class Account(SqlAlchemyBase): # Parent Account
    __tablename__ = 'account'
    
    id = Column(Integer, primary_key=True)
    public_id = Column(String, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False, index=True)

    email = Column(Text, unique=True, nullable=False, index=True)
    language = Column(SqlEnum(LanguageEnum, name="language_enum", create_type=False), default=LanguageEnum.LU)

    stripe_customer_id = Column(String, unique=True, nullable=False, index=True)

    pin_hash = Column(Text)
    is_auth_migrated = Column(Boolean, default=False)
    is_verified = Column(Boolean, default=False)
    verification_code = Column(Text)

    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))

    # Relationship to ChildAccount
    children_accounts = relationship("ChildAccount", back_populates="parent_account", cascade="all, delete-orphan")
    
    # New/Updated Subscription Relationships
    active_subscriptions = relationship("ActiveSubscription", back_populates="parent_account", cascade="all, delete-orphan")
    price_eligibilities = relationship("PriceEligibility", back_populates="account", cascade="all, delete-orphan")
    discount_code_usages = relationship("DiscountCodeUsage", back_populates="account", cascade="all, delete-orphan")
    trials = relationship("Trial", back_populates="account", cascade="all, delete-orphan")
    # StripeCheckoutSessionData relationship (optional, if needed for direct access from Account)
    stripe_checkout_sessions = relationship("StripeCheckoutSessionData", back_populates="parent_account", foreign_keys="[StripeCheckoutSessionData.parent_account_public_id]", primaryjoin="Account.public_id == StripeCheckoutSessionData.parent_account_public_id")


class ChildAccount(SqlAlchemyBase):
    __tablename__ = 'child_account'

    id = Column(Integer, primary_key=True)
    public_id = Column(String, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False, index=True)
    
    name = Column(Text, nullable=False)
    email = Column(Text, unique=True, nullable=True, index=True)

    year_id = Column(Integer, ForeignKey('year.id'), nullable=True, index=True)
    parent_account_id = Column(Integer, ForeignKey(
        'account.id', ondelete='CASCADE'), index=True, nullable=True)
    
    language = Column(SqlEnum(LanguageEnum, name="child_language_enum", create_type=False), nullable=False) # Ensure name for enum if needed
    pin = Column(Text) # Assuming this is the hashed PIN or the raw PIN if handled differently for children
    verification_code = Column(Text)
    is_verified = Column(Boolean, default=False)
    
    exercise_associations = relationship("ChildAccountExerciseAssociation", back_populates="child_account", cascade="all, delete-orphan")
    parent_account = relationship("Account", back_populates="children_accounts")
    
    year = relationship("Year", back_populates="child_accounts")

    # New/Updated Subscription Relationships
    trials = relationship("Trial", back_populates="child_account", cascade="all, delete-orphan")

    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))


class ParentChildAssignment(SqlAlchemyBase):
    __tablename__ = 'parent_assignment'

    id = Column(Integer, primary_key=True)
    public_id = Column(String, default=lambda: str(uuid.uuid4()), unique=True, nullable=False, index=True)

    parent_email = Column(Text, index=True)
    child_account_id = Column(Integer, ForeignKey(
        'child_account.id', ondelete='CASCADE'), index=True, nullable=False)
    verification_code = Column(Text)
    language = Column(SqlEnum(LanguageEnum, name="assignment_language_enum", create_type=False)) # Use Enum
    
    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))