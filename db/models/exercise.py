# exercise.py
from sqlalchemy import <PERSON>umn, Integer, String, <PERSON><PERSON><PERSON>, Enum, JSON, DateTime
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.hybrid import hybrid_property
from datetime import datetime, UTC
from pydantic import BaseModel, Field
from typing import TypeVar, Generic, Type, List, Dict, Union, Optional, Any, Literal
import enum
import uuid

from db.database import SqlAlchemyBase
from db.models.associations.learning_node_exercise import LearningNodeExerciseAssociation

# ============================================================================
# Enums
# ============================================================================
class ExerciseTypeEnum(enum.Enum):
    MC_SIMPLE = "mc-simple"
    MC_MULTI = "mc-multi"
    INPUT = "input"
    CLOZE = "cloze"
    DROPDOWN = "dropdown"
    HIGHLIGHT = "highlight"
    MATCHING_PAIRS = "matching-pairs"
    CATEGORIZE = "categorize"
    ERROR_CORRECTION = "error-correction"
    TRUE_FALSE = "true-false"

class DifficultyEnum(enum.Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

# ============================================================================
# Pydantic Models for Exercise Data (matching your TypeScript schemas)
# ============================================================================

class ExerciseOptionModel(BaseModel):
    public_id: str
    text: str

class ExerciseImageOptionModel(BaseModel):
    public_id: str
    image_public_id: str  # References ImageFile.public_id
    text: Optional[str] = None

# Base Exercise Data model
class BaseExerciseData(BaseModel):
    prompt: str
    prompt_image_public_id: Optional[str] = None  # References ImageFile.public_id

# Multiple choice data
class MCExerciseData(BaseExerciseData):
    options: List[Union[ExerciseOptionModel, ExerciseImageOptionModel]]

# Input exercise data
class InputExerciseData(BaseExerciseData):
    pass

# Cloze exercise data
class ClozeExerciseData(BaseExerciseData):
    text_parts: List[str] = []
    hints: Optional[List[Optional[str]]] = None

# Dropdown exercise data
class DropdownExerciseData(BaseExerciseData):
    template: str
    options: List[List[str]]

# Highlight exercise data
class HighlightExerciseData(BaseExerciseData):
    parts: List[str] = []

# Matching pairs data
class MatchingPairItem(BaseModel):
    public_id: str
    text: str

class MatchingPairsExerciseData(BaseExerciseData):
    column_a: List[MatchingPairItem] = []
    column_b: List[MatchingPairItem] = []

# Categorize exercise data
class CategorizeItem(BaseModel):
    public_id: str
    text: str

class CategoryItem(BaseModel):
    public_id: str
    text: str

class CategorizeExerciseData(BaseExerciseData):
    prompt: Optional[str] = None  # Override to make prompt optional
    categories: List[CategoryItem] = []
    options: List[CategorizeItem] = []

# Error correction exercise data
class ErrorCorrectionExerciseData(BaseExerciseData):
    parts: List[str] = []

# True/False exercise data
class TrueFalseExerciseData(BaseExerciseData):
    # No additional fields needed - True/False options are implicit
    pass

# ============================================================================
# Solution Models
# ============================================================================

# Solution step model
class SolutionStepModel(BaseModel):
    text: Optional[str] = None
    math: Optional[str] = None
    image_public_id: Optional[str] = None  # References ImageFile.public_id

# Solution answer type models
class MCSimpleSolutionAnswer(BaseModel):
    correct_option_id: List[str]

class MCMultiSolutionAnswer(BaseModel):
    correct_option_ids: List[str]

class InputSolutionAnswer(BaseModel):
    correct_answer: str

class ClozeSolutionAnswer(BaseModel):
    correct_answers: List[str]

class DropdownSolutionAnswer(BaseModel):
    correct_selections: List[str]

class HighlightSolutionAnswer(BaseModel):
    correct_indices: List[int]

class MatchingPairsSolutionAnswer(BaseModel):
    correct_pairs: Dict[str, str] = Field(
        description="A mapping of column A IDs to column B IDs, where each key is a column A ID and each value is its matching column B ID"
    )

class CategorizeSolutionAnswer(BaseModel):
    correct_categories: Dict[str, str] = Field(
        description="A mapping of item IDs to category IDs, where each key is an item ID and each value is its matching category ID"
    )

class ErrorCorrectionText(BaseModel):
    index: int
    text: str

class ErrorCorrectionSolutionAnswer(BaseModel):
    corrections: List[ErrorCorrectionText]

class TrueFalseSolutionAnswer(BaseModel):
    is_true: bool  # True or False

# Combined type for solution answers
SolutionAnswerType = Union[
    MCSimpleSolutionAnswer,
    MCMultiSolutionAnswer,
    InputSolutionAnswer,
    ClozeSolutionAnswer,
    DropdownSolutionAnswer,
    HighlightSolutionAnswer,
    MatchingPairsSolutionAnswer,
    CategorizeSolutionAnswer,
    ErrorCorrectionSolutionAnswer,
    TrueFalseSolutionAnswer
]

# Exercise solution model with type-specific correctAnswer
class ExerciseSolutionModel(BaseModel):
    correct_answer: SolutionAnswerType
    solution_steps: Optional[List[SolutionStepModel]] = None
    video_public_id: Optional[str] = None

# # Exercise submission response
# class ExerciseSubmissionResponseModel(BaseModel):
#     isCorrect: bool
#     correctAnswer: Union[str, List[str], Dict[str, str], List[Dict[str, str]]]
#     solutionSteps: Optional[List[SolutionStepModel]] = None
#     videoId: Optional[str] = None


# ============================================================================
# Generic Exercise Model with Type-Safe Data Access
# ============================================================================

T = TypeVar('T', bound=BaseExerciseData)

class Exercise(SqlAlchemyBase, Generic[T]):
    __tablename__ = 'exercise'
    
    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, index=True)
    # Many-to-many relationship with LearningNodes through the association model
    learning_node_associations = relationship("LearningNodeExerciseAssociation", back_populates="exercise", cascade="all, delete-orphan")
    # Many-to-many relationship with ChildAccounts through the association model
    child_account_associations = relationship("ChildAccountExerciseAssociation", back_populates="exercise", cascade="all, delete-orphan")
    
    # Convenience property to access learning nodes directly
    @property
    def learning_nodes(self):
        return [assoc.learning_node for assoc in self.learning_node_associations]
        
    # Convenience property to access child accounts directly
    @property
    def child_accounts(self):
        return [assoc.child_account for assoc in self.child_account_associations]
    exercise_type = Column(Enum(ExerciseTypeEnum, values_callable=lambda obj: [e.value for e in obj]), nullable=False)
    difficulty = Column(Enum(DifficultyEnum, values_callable=lambda obj: [e.value for e in obj]), nullable=False, default=DifficultyEnum.MEDIUM)
    
   
    # Raw JSON data
    _data = Column('data', JSON, nullable=False)
    _solution = Column('solution', JSON, nullable=True)
    
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC))
    
    @hybrid_property
    def data(self) -> T:
        """Type-safe access to exercise data"""
        data_model = self._get_data_model()
        if self._data:
            return data_model.model_validate(self._data)
        return data_model() # Return empty model if no data

    @data.setter
    def data(self, value: Union[T, Dict[str, Any]]):
        """Type-safe setting of exercise data"""
        data_model_class = self._get_data_model()
        if isinstance(value, dict):
            validated_data = data_model_class.model_validate(value)
        elif isinstance(value, BaseModel):
            validated_data = value
        else:
            raise TypeError(f"Data must be a dict or an instance of {data_model_class.__name__}")
        self._data = validated_data.model_dump(mode='json') # Store as dict

    @data.expression
    def data(cls):
        """Provide the SQL expression for the 'data' attribute."""
        return cls._data

    @hybrid_property
    def solution(self) -> Optional[ExerciseSolutionModel]:
        """Access to solution data"""
        if not self._solution:
            return None
        
        return ExerciseSolutionModel.model_validate(self._solution)
    
    @solution.setter
    def solution(self, value: ExerciseSolutionModel):
        """Set solution data"""
        if value is None:
            self._solution = None
        elif isinstance(value, BaseModel):
            self._solution = value.model_dump()
        else:
            raise ValueError(f"Expected BaseModel instance or None, got {type(value)}")

    @solution.expression
    def solution(cls):
        """Provide the SQL expression for the 'solution' attribute."""
        return cls._solution

    def _get_data_model(self) -> Type[T]:
        """Map exercise type to appropriate data model"""
        data_model_mapping = {
            ExerciseTypeEnum.MC_SIMPLE: MCExerciseData,
            ExerciseTypeEnum.MC_MULTI: MCExerciseData,
            ExerciseTypeEnum.INPUT: InputExerciseData,
            ExerciseTypeEnum.CLOZE: ClozeExerciseData,
            ExerciseTypeEnum.DROPDOWN: DropdownExerciseData,
            ExerciseTypeEnum.HIGHLIGHT: HighlightExerciseData,
            ExerciseTypeEnum.MATCHING_PAIRS: MatchingPairsExerciseData,
            ExerciseTypeEnum.CATEGORIZE: CategorizeExerciseData,
            ExerciseTypeEnum.ERROR_CORRECTION: ErrorCorrectionExerciseData,
            ExerciseTypeEnum.TRUE_FALSE: TrueFalseExerciseData,
        }
        return data_model_mapping.get(self.exercise_type, BaseExerciseData)
    
    def get_expected_solution_type(self) -> Type[SolutionAnswerType]:
        """Get the expected solution type based on exercise type"""
        solution_type_mapping = {
            ExerciseTypeEnum.MC_SIMPLE: MCSimpleSolutionAnswer,
            ExerciseTypeEnum.MC_MULTI: MCMultiSolutionAnswer,
            ExerciseTypeEnum.INPUT: InputSolutionAnswer,
            ExerciseTypeEnum.CLOZE: ClozeSolutionAnswer,
            ExerciseTypeEnum.DROPDOWN: DropdownSolutionAnswer,
            ExerciseTypeEnum.HIGHLIGHT: HighlightSolutionAnswer,
            ExerciseTypeEnum.MATCHING_PAIRS: MatchingPairsSolutionAnswer,
            ExerciseTypeEnum.CATEGORIZE: CategorizeSolutionAnswer,
            ExerciseTypeEnum.ERROR_CORRECTION: ErrorCorrectionSolutionAnswer,
            ExerciseTypeEnum.TRUE_FALSE: TrueFalseSolutionAnswer,
        }
        return solution_type_mapping[self.exercise_type]
    
    def validate_solution(self) -> bool:
        """Validate that the solution format matches the exercise type"""
        if not self.solution or not self.solution.correct_answer:
            return True  # No solution to validate
            
        expected_type = self.get_expected_solution_type()
        
        # Check if the parsed correct_answer object is an instance of the expected type
        return isinstance(self.solution.correct_answer, expected_type)

# ============================================================================
# Type-Specific Exercise Classes
# ============================================================================

class MCExercise(Exercise[MCExerciseData]):
    """Multiple Choice Exercise with type-safe data access"""
    
    @classmethod
    def create(cls, learning_node: 'LearningNode', is_multi: bool, exercise_data: MCExerciseData, 
               solution: Optional[ExerciseSolutionModel] = None, difficulty: DifficultyEnum = DifficultyEnum.MEDIUM) -> 'MCExercise':
        exercise_type = ExerciseTypeEnum.MC_MULTI if is_multi else ExerciseTypeEnum.MC_SIMPLE
        exercise = cls(
            exercise_type=exercise_type,
            difficulty=difficulty,
            data=exercise_data
        )
        # Create association with learning node
        LearningNodeExerciseAssociation.create_association(learning_node, exercise)
        if solution:
            exercise.solution = solution
        return exercise

class InputExercise(Exercise[InputExerciseData]):
    """Input Exercise with type-safe data access"""
    
    @classmethod
    def create(cls, learning_node: 'LearningNode', exercise_data: InputExerciseData,
               solution: Optional[ExerciseSolutionModel] = None, difficulty: DifficultyEnum = DifficultyEnum.MEDIUM) -> 'InputExercise':
        exercise = cls(
            exercise_type=ExerciseTypeEnum.INPUT,
            difficulty=difficulty,
            data=exercise_data
        )
        # Create association with learning node
        LearningNodeExerciseAssociation.create_association(learning_node, exercise)
        if solution:
            exercise.solution = solution
        return exercise

class ClozeExercise(Exercise[ClozeExerciseData]):
    """Cloze Exercise with type-safe data access"""
    
    @classmethod
    def create(cls, learning_node: 'LearningNode', exercise_data: ClozeExerciseData,
               solution: Optional[ExerciseSolutionModel] = None, difficulty: DifficultyEnum = DifficultyEnum.MEDIUM) -> 'ClozeExercise':
        exercise = cls(
            exercise_type=ExerciseTypeEnum.CLOZE,
            difficulty=difficulty,
            data=exercise_data
        )
        # Create association with learning node
        LearningNodeExerciseAssociation.create_association(learning_node, exercise)
        if solution:
            exercise.solution = solution
        return exercise

class DropdownExercise(Exercise[DropdownExerciseData]):
    """Dropdown Exercise with type-safe data access"""
    
    @classmethod
    def create(cls, learning_node: 'LearningNode', exercise_data: DropdownExerciseData,
               solution: Optional[ExerciseSolutionModel] = None, difficulty: DifficultyEnum = DifficultyEnum.MEDIUM) -> 'DropdownExercise':
        exercise = cls(
            exercise_type=ExerciseTypeEnum.DROPDOWN,
            difficulty=difficulty,
            data=exercise_data
        )
        # Create association with learning node
        LearningNodeExerciseAssociation.create_association(learning_node, exercise)
        if solution:
            exercise.solution = solution
        return exercise

class HighlightExercise(Exercise[HighlightExerciseData]):
    """Highlight Exercise with type-safe data access"""
    
    @classmethod
    def create(cls, learning_node: 'LearningNode', exercise_data: HighlightExerciseData,
               solution: Optional[ExerciseSolutionModel] = None, difficulty: DifficultyEnum = DifficultyEnum.MEDIUM) -> 'HighlightExercise':
        exercise = cls(
            exercise_type=ExerciseTypeEnum.HIGHLIGHT,
            difficulty=difficulty,
            data=exercise_data
        )
        # Create association with learning node
        LearningNodeExerciseAssociation.create_association(learning_node, exercise)
        if solution:
            exercise.solution = solution
        return exercise

class MatchingPairsExercise(Exercise[MatchingPairsExerciseData]):
    """Matching Pairs Exercise with type-safe data access"""
    
    @classmethod
    def create(cls, learning_node: 'LearningNode', exercise_data: MatchingPairsExerciseData,
               solution: Optional[ExerciseSolutionModel] = None, difficulty: DifficultyEnum = DifficultyEnum.MEDIUM) -> 'MatchingPairsExercise':
        exercise = cls(
            exercise_type=ExerciseTypeEnum.MATCHING_PAIRS,
            difficulty=difficulty,
            data=exercise_data
        )
        # Create association with learning node
        LearningNodeExerciseAssociation.create_association(learning_node, exercise)
        if solution:
            exercise.solution = solution
        return exercise

class CategorizeExercise(Exercise[CategorizeExerciseData]):
    """Categorize Exercise with type-safe data access"""
    
    @classmethod
    def create(cls, learning_node: 'LearningNode', exercise_data: CategorizeExerciseData,
               solution: Optional[ExerciseSolutionModel] = None, difficulty: DifficultyEnum = DifficultyEnum.MEDIUM) -> 'CategorizeExercise':
        exercise = cls(
            exercise_type=ExerciseTypeEnum.CATEGORIZE,
            difficulty=difficulty,
            data=exercise_data
        )
        # Create association with learning node
        LearningNodeExerciseAssociation.create_association(learning_node, exercise)
        if solution:
            exercise.solution = solution
        return exercise

class ErrorCorrectionExercise(Exercise[ErrorCorrectionExerciseData]):
    """Error Correction Exercise with type-safe data access"""
    
    @classmethod
    def create(cls, learning_node: 'LearningNode', exercise_data: ErrorCorrectionExerciseData,
               solution: Optional[ExerciseSolutionModel] = None, difficulty: DifficultyEnum = DifficultyEnum.MEDIUM) -> 'ErrorCorrectionExercise':
        exercise = cls(
            exercise_type=ExerciseTypeEnum.ERROR_CORRECTION,
            difficulty=difficulty,
            data=exercise_data
        )
        # Create association with learning node
        LearningNodeExerciseAssociation.create_association(learning_node, exercise)
        if solution:
            exercise.solution = solution
        return exercise

class TrueFalseExercise(Exercise[TrueFalseExerciseData]):
    """True/False Exercise with type-safe data access"""
    
    @classmethod
    def create(cls, learning_node: 'LearningNode', exercise_data: TrueFalseExerciseData,
               solution: Optional[ExerciseSolutionModel] = None, difficulty: DifficultyEnum = DifficultyEnum.MEDIUM) -> 'TrueFalseExercise':
        exercise = cls(
            exercise_type=ExerciseTypeEnum.TRUE_FALSE,
            difficulty=difficulty,
            data=exercise_data
        )
        # Create association with learning node
        LearningNodeExerciseAssociation.create_association(learning_node, exercise)
        if solution:
            exercise.solution = solution
        return exercise

# ============================================================================
# Factory Pattern for Safe Exercise Creation
# ============================================================================

class ExerciseFactory:
    """Factory to create type-safe exercises"""
    
    @staticmethod
    def create_exercise(exercise_type: str, learning_node: 'LearningNode', data: dict, solution: Optional[dict] = None, difficulty: DifficultyEnum = DifficultyEnum.MEDIUM) -> Exercise:
        """Create an exercise based on type with validated data"""
        
        # Map exercise types to data models
        type_mapping = {
            "mc-simple": (MCExerciseData, ExerciseTypeEnum.MC_SIMPLE),
            "mc-multi": (MCExerciseData, ExerciseTypeEnum.MC_MULTI),
            "input": (InputExerciseData, ExerciseTypeEnum.INPUT),
            "cloze": (ClozeExerciseData, ExerciseTypeEnum.CLOZE),
            "dropdown": (DropdownExerciseData, ExerciseTypeEnum.DROPDOWN),
            "highlight": (HighlightExerciseData, ExerciseTypeEnum.HIGHLIGHT),
            "matching-pairs": (MatchingPairsExerciseData, ExerciseTypeEnum.MATCHING_PAIRS),
            "categorize": (CategorizeExerciseData, ExerciseTypeEnum.CATEGORIZE),
            "error-correction": (ErrorCorrectionExerciseData, ExerciseTypeEnum.ERROR_CORRECTION),
            "true-false": (TrueFalseExerciseData, ExerciseTypeEnum.TRUE_FALSE),
        }
        
        if exercise_type not in type_mapping:
            raise ValueError(f"Unknown exercise type: {exercise_type}")
        
        data_model_cls, exercise_type_enum = type_mapping[exercise_type]
        
        # Validate and parse data
        exercise_data = data_model_cls.model_validate(data)
        
        # Create solution model if provided
        solution_data = None
        if solution:
            solution_data = ExerciseSolutionModel.model_validate(solution)
            
        # Create base Exercise instance (not subclass)
        # This avoids SQLAlchemy polymorphic issues
        exercise = Exercise(
            exercise_type=exercise_type_enum,
            difficulty=difficulty,
            data=exercise_data
        )
        
        # Create association with learning node
        LearningNodeExerciseAssociation.create_association(learning_node, exercise)
        
        if solution_data:
            exercise.solution = solution_data
            
        # Validate solution format if provided
        if solution_data and not exercise.validate_solution():
            enum_type = exercise.exercise_type
            expected_type = exercise.get_expected_solution_type().__name__
            raise ValueError(f"Solution format does not match exercise type {enum_type}. Expected {expected_type}.")
            
        return exercise
