# /models/associations/child_account_exercise.py

import enum
import uuid
from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Text, Boolean, Index, JSON, Enum
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship
from datetime import datetime, UTC
from typing import Optional, Dict, Any, List

from db.database import SqlAlchemyBase


class ExerciseStatusEnum(str, enum.Enum):
    """Status of a child account's attempt at an exercise"""
    CORRECT = "correct"
    INCORRECT = "incorrect"
    NOT_ATTEMPTED = "notAttempted"


class ChildAccountExerciseAssociation(SqlAlchemyBase):
    """Association model tracking child accounts' progress on exercises"""
    __tablename__ = 'child_account_exercise_association'
    
    id = Column(Integer, primary_key=True)
    public_id = Column(String, default=lambda: str(uuid.uuid4()), unique=True, nullable=False) # Changed to String
    
    # Foreign keys
    child_account_id = Column(Inte<PERSON>, ForeignKey('child_account.id', ondelete='CASCADE'), nullable=False)
    exercise_id = Column(Integer, ForeignKey('exercise.id', ondelete='CASCADE'), nullable=False)

    # Progress data
    status = Column(Enum(ExerciseStatusEnum), nullable=False, default=ExerciseStatusEnum.NOT_ATTEMPTED)
    given_answer = Column(JSON, nullable=True)  # Stores the user's answer in a flexible format
    correct_answer = Column(JSON, nullable=True)  # Can store the correct answer for reference
    attempts_count = Column(Integer, default=0)  # Track number of attempts
    
    # Timestamps
    last_attempted_at = Column(DateTime(timezone=True), nullable=True) # Added timezone=True
    completed_at = Column(DateTime(timezone=True), nullable=True)  # When marked as correct, added timezone=True
    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC)) # Added timezone=True
    
    # Relationships
    child_account = relationship("ChildAccount", back_populates="exercise_associations")
    exercise = relationship("Exercise", back_populates="child_account_associations")

    # For query optimization
    __table_args__ = (
        Index('idx_child_exercise', child_account_id, exercise_id, unique=True),
        Index('idx_child_exercise_status', child_account_id, status),
        Index('idx_completed_exercises', child_account_id, completed_at.desc() if completed_at is not None else None), # Ensure completed_at is not None for desc()
    )
    
    @classmethod
    def create_association(cls, child_account, exercise, status=ExerciseStatusEnum.NOT_ATTEMPTED, answer=None):
        """Create or update an association between a child account and an exercise"""
        # Check if association already exists
        # This method would typically be used within a service that has access to a DB session
        # For now, assuming it's illustrative or used in a context where `cls.query` is available (e.g. Flask-SQLAlchemy pattern)
        # In pure SQLAlchemy, you'd pass the session: session.query(cls).filter(...).first()
        # assoc = cls.query.filter(
        #     cls.child_account_id == child_account.id,
        #     cls.exercise_id == exercise.id
        # ).first()
        
        now = datetime.now(UTC)
        
        # This method needs a session to query and add. For now, it's a conceptual representation.
        # Actual creation/update would happen in a service layer.
        # if not assoc:
        #     assoc = cls( ... )
        # else:
        #     ... update assoc ...
        # return assoc
        pass # Placeholder for actual DB interaction logic which belongs in a service
    
    def mark_correct(self, answer=None):
        """Mark this exercise as correctly completed"""
        now = datetime.now(UTC)
        self.status = ExerciseStatusEnum.CORRECT
        self.completed_at = now
        self.last_attempted_at = now
        if answer:
            self.given_answer = answer
        self.attempts_count += 1
        
    def mark_incorrect(self, answer=None):
        """Mark this exercise as incorrect"""
        now = datetime.now(UTC)
        self.status = ExerciseStatusEnum.INCORRECT
        self.last_attempted_at = now
        if answer:
            self.given_answer = answer
        self.attempts_count += 1