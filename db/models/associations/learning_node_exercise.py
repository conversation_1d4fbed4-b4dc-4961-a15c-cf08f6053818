# learning_node_exercise.py
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ey, DateTime
from sqlalchemy.orm import relationship
from datetime import datetime, UTC

from db.database import SqlAlchemyBase

# Association model for many-to-many relationship between learning nodes and exercises
class LearningNodeExerciseAssociation(SqlAlchemyBase):
    __tablename__ = 'learning_node_exercise_association'
    
    # Primary key
    id = Column(Integer, primary_key=True)
    
    # Foreign keys
    learning_node_id = Column(Integer, ForeignKey('learning_node.id', ondelete='CASCADE'), nullable=False, index=True)
    exercise_id = Column(Integer, ForeignKey('exercise.id', ondelete='CASCADE'), nullable=False, index=True)
    
    # Additional fields
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC))
    
    # Relationships
    learning_node = relationship("LearningNode", back_populates="exercise_associations")
    exercise = relationship("Exercise", back_populates="learning_node_associations")
    
    # Class methods for creating associations
    @classmethod
    def create_association(cls, learning_node, exercise):
        """Helper method to create an association between a learning node and an exercise"""
        association = cls(learning_node=learning_node, exercise=exercise)
        return association