
from sqlalchemy import Integer, Text, Column, Integer, Foreign<PERSON>ey
from sqlalchemy.sql.sqltypes import DateTime
from db.database import SqlAlchemyBase
from datetime import datetime, UTC


class SummaryDownload(SqlAlchemyBase):
    __tablename__ = 'summary_download'

    id = Column(Integer, primary_key=True)
    
    email = Column(Text)
    
    account = Column(
        Integer, ForeignKey('account.id'))
    child_account = Column(
        Integer, ForeignKey('child_account.id', ondelete='SET NULL'))
    
    downloaded_file = Column(Text)
    
    created_at = Column(
        DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))


class YearSummaryDownload(SqlAlchemyBase):
    __tablename__ = 'year_summary_download'
    id = Column(Integer, primary_key=True)

    email = Column(Text)
    
    account = Column(Integer, ForeignKey('account.id'))
    child_account = Column(Integer, Foreign<PERSON>ey('child_account.id', ondelete='SET NULL'))
    
    downloaded_file = Column(Text)
    
    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))