from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String, CheckConstraint, UniqueConstraint, Text, Index as SQLAIndex, Boolean
from sqlalchemy.sql.sqltypes import DateTime
from db.database import SqlAlchemyBase
from sqlalchemy.orm import relationship
from datetime import datetime, UTC
from sqlalchemy.dialects.postgresql import JSONB
import uuid
from sqlalchemy import Enum as SqlEnum
import enum
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .subscription_changes import SubscriptionPendingChange

# --- Enum Definitions ---
class PlanTypeEnum(str, enum.Enum):
    SC = "SC"  # Subject Credits
    YF = "YF"  # Year Full Access

class BillingPeriodEnum(str, enum.Enum):
    MONTHLY = "monthly"
    YEARLY = "yearly"

class SubscriptionStatusType(str, enum.Enum):
    ACTIVE = "active"
    PAUSED = "paused"  
    TRIALING = "trialing"
    PAST_DUE = "past_due"
    CANCELED = "canceled"
    UNPAID = "unpaid"
    INCOMPLETE = "incomplete"
    INCOMPLETE_EXPIRED = "incomplete_expired"

class DiscountApplicabilityType(str, enum.Enum):
    MONTHLY = "monthly"
    YEARLY = "yearly"
    BOTH = "both"

class SubscriptionPauseStatus(str, enum.Enum):
    SCHEDULED = "scheduled"  # Pause scheduled but not yet active
    PAUSED = "paused"       # Currently paused
    RESUMED = "resumed"     # Was paused, now resumed
    CANCELED = "canceled"   # Pause was canceled before it started

# --- Models ---

class PriceVersion(SqlAlchemyBase):
    __tablename__ = 'price_version'

    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, nullable=False, index=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False, unique=True, comment="Internal name, e.g., 'Q1 2024 Prices'")
    slug = Column(String, unique=True, nullable=True, index=True, comment="URL-friendly slug")
    display_name = Column(String, nullable=True, comment="User-facing name, e.g., 'Standard Pricing'")
    
    is_current = Column(Boolean, default=False, index=True, comment="Is this the current default price version to apply to new eligible customers?")
    is_active = Column(Boolean, default=True, index=True, comment="Is this price version scheme globally active and usable? If false, no options under it are available.")
    
    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))

    # Relationships
    subscription_options = relationship("SubscriptionOption", back_populates="price_version", cascade="all, delete-orphan")
    price_eligibilities = relationship("PriceEligibility", back_populates="price_version", cascade="all, delete-orphan")


class PriceEligibility(SqlAlchemyBase):
    __tablename__ = 'price_eligibility'
    id = Column(Integer, primary_key=True)
    public_id = Column(String, default=lambda: str(uuid.uuid4()), unique=True, nullable=False, index=True)
    account_id = Column(Integer, ForeignKey('account.id', ondelete="CASCADE"), nullable=False, index=True)
    price_version_id = Column(Integer, ForeignKey('price_version.id', ondelete="CASCADE"), nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))
    account = relationship("Account", back_populates="price_eligibilities")
    price_version = relationship("PriceVersion", back_populates="price_eligibilities")
    __table_args__ = (
        UniqueConstraint('account_id', 'price_version_id', name='uq_account_price_version'),
    )


class SubscriptionOption(SqlAlchemyBase):
    __tablename__ = 'subscription_option'
    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, nullable=False, index=True, default=lambda: str(uuid.uuid4()))

    year_id = Column(Integer, ForeignKey('year.id'), nullable=False, index=True)
    price_version_id = Column(Integer, ForeignKey('price_version.id', ondelete="RESTRICT"), nullable=False, index=True)

    sc_stripe_product_id = Column(String, nullable=True, index=True, comment="Stripe Product ID for 'Subject Credits' for this year.")
    yf_stripe_product_id = Column(String, nullable=True, index=True, comment="Stripe Product ID for 'Year Full Access' for this year.")

    sc_monthly_stripe_price_id = Column(String, unique=True, nullable=True, comment="Stripe Price ID for SC - Monthly. Can be tiered.")
    sc_yearly_stripe_price_id = Column(String, unique=True, nullable=True, comment="Stripe Price ID for SC - Yearly. Can be tiered.")
    yf_monthly_stripe_price_id = Column(String, unique=True, nullable=True, comment="Stripe Price ID for YF - Monthly. Flat rate.")
    yf_yearly_stripe_price_id = Column(String, unique=True, nullable=True, comment="Stripe Price ID for YF - Yearly. Flat rate.")
    
    display_price_config_json = Column(JSONB, nullable=False, comment="All pricing details for frontend display for this year/price_version.")

    is_active = Column(Boolean, default=True, index=True, comment="Is this specific set of SC/YF options for this Year under this PriceVersion available? Both this and PriceVersion.is_active must be true for it to be purchasable.")
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC), index=True)

    # Relationships
    year = relationship("Year", foreign_keys=[year_id], back_populates="subscription_options") 
    price_version = relationship("PriceVersion", back_populates="subscription_options")
    active_subscription_plan_links = relationship("ActiveSubscriptionPlanLink", back_populates="subscription_option")

    __table_args__ = (
        UniqueConstraint('year_id', 'price_version_id', name='uq_subscription_option_year_pv'),
        CheckConstraint(
            """
            (sc_monthly_stripe_price_id IS NOT NULL OR sc_yearly_stripe_price_id IS NOT NULL) OR
            (yf_monthly_stripe_price_id IS NOT NULL OR yf_yearly_stripe_price_id IS NOT NULL)
            """,
            name='cc_option_min_one_variant_defined'
        ),
    )


class ActiveSubscription(SqlAlchemyBase):
    __tablename__ = 'active_subscription'
    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, nullable=False, index=True, default=lambda: str(uuid.uuid4()))
    parent_account_id = Column(Integer, ForeignKey('account.id', ondelete="CASCADE"), nullable=False, index=True)
    stripe_subscription_id = Column(String, unique=True, nullable=False, index=True)
    status = Column(SqlEnum(SubscriptionStatusType), nullable=False, index=True)
    current_period_start = Column(DateTime(timezone=True), nullable=True)
    current_period_end = Column(DateTime(timezone=True), nullable=True)
    cancel_at_period_end = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    parent_account = relationship("Account", back_populates="active_subscriptions")
    plan_links = relationship("ActiveSubscriptionPlanLink", back_populates="active_subscription", cascade="all, delete-orphan")
    subscription_pauses = relationship("SubscriptionPause", back_populates="active_subscription", cascade="all, delete-orphan")
    pending_changes = relationship("SubscriptionPendingChange", back_populates="active_subscription", cascade="all, delete-orphan")


class ActiveSubscriptionPlanLink(SqlAlchemyBase): 
    __tablename__ = 'active_subscription_plan_link'
    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, nullable=False, index=True, default=lambda: str(uuid.uuid4()))
    active_subscription_id = Column(Integer, ForeignKey('active_subscription.id', ondelete="CASCADE"), nullable=False, index=True)
    
    subscription_option_id = Column(Integer, ForeignKey('subscription_option.id', ondelete="RESTRICT"), nullable=False, index=True)
    
    chosen_stripe_price_id = Column(String, nullable=False, index=True, comment="The actual Stripe Price ID used for this item.")
    chosen_plan_type = Column(SqlEnum(PlanTypeEnum), nullable=False, index=True)
    chosen_billing_period = Column(SqlEnum(BillingPeriodEnum), nullable=False, index=True)
    
    stripe_subscription_item_id = Column(String, unique=True, nullable=False, index=True)
    quantity = Column(Integer, default=1, nullable=False, comment="For SC plans, this is #subjects. For YF, 1.")
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC), index=True)

    active_subscription = relationship("ActiveSubscription", back_populates="plan_links")
    subscription_option = relationship("SubscriptionOption", back_populates="active_subscription_plan_links")
    selected_subjects = relationship("PlanSelectedSubject", back_populates="plan_link", cascade="all, delete-orphan")
    pending_changes = relationship("SubscriptionPendingChange", back_populates="plan_link", cascade="all, delete-orphan")


class PlanSelectedSubject(SqlAlchemyBase):
    __tablename__ = 'plan_selected_subject'
    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, nullable=False, index=True, default=lambda: str(uuid.uuid4()))
    plan_link_id = Column(Integer, ForeignKey('active_subscription_plan_link.id', ondelete="CASCADE"), nullable=False, index=True)
    subject_id = Column(Integer, ForeignKey('subject.id', ondelete="CASCADE"), nullable=False, index=True)
    selected_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC), index=True)
    plan_link = relationship("ActiveSubscriptionPlanLink", back_populates="selected_subjects")
    subject = relationship("Subject", back_populates="selected_subjects_links")
    __table_args__ = (
        UniqueConstraint('plan_link_id', 'subject_id', name='uq_plan_selected_subject'),
    )


class DiscountCode(SqlAlchemyBase):
    __tablename__ = 'discount_code'
    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, nullable=False, index=True, default=lambda: str(uuid.uuid4()))
    stripe_monthly_id = Column(String, nullable=True, index=True, comment="Stripe coupon ID for monthly subscriptions")
    stripe_yearly_id = Column(String, nullable=True, index=True, comment="Stripe coupon ID for yearly subscriptions")
    public_code = Column(String, unique=True, index=True, nullable=False)
    applicable_to = Column(SqlEnum(DiscountApplicabilityType), default=DiscountApplicabilityType.BOTH, nullable=False)
    min_account_age_days = Column(Integer, nullable=True, comment="Account must be at least this many days old.")
    max_account_age_days = Column(Integer, nullable=True, comment="Account must be no more than this many days old.")
    one_time = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True, index=True)
    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))
    usages = relationship("DiscountCodeUsage", back_populates="discount_code", cascade="all, delete-orphan")
    
    __table_args__ = (
        CheckConstraint(
            "(stripe_monthly_id IS NOT NULL OR stripe_yearly_id IS NOT NULL)",
            name='check_at_least_one_stripe_id'
        ),
    )


class DiscountCodeUsage(SqlAlchemyBase):
    __tablename__ = 'discount_code_usage'
    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, nullable=False, index=True, default=lambda: str(uuid.uuid4()))
    discount_code_id = Column(Integer, ForeignKey('discount_code.id', ondelete="CASCADE"), nullable=False, index=True)
    account_id = Column(Integer, ForeignKey('account.id', ondelete="CASCADE"), nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))
    discount_code = relationship("DiscountCode", back_populates="usages")
    account = relationship("Account", back_populates="discount_code_usages")
    __table_args__ = (
        UniqueConstraint('discount_code_id', 'account_id', name='uq_discount_code_account_usage'),
    )


class Trial(SqlAlchemyBase):
    __tablename__ = 'trial'
    id = Column(Integer, primary_key=True)
    public_id = Column(String, default=lambda: str(uuid.uuid4()), unique=True, nullable=False, index=True)
    account_id = Column(Integer, ForeignKey('account.id', ondelete="CASCADE"), nullable=True, index=True) # Parent account for trial
    # child_account_id can remain if you want to log which child might have triggered a trial,
    # but the trial itself grants access at the parent level.
    child_account_id = Column(Integer, ForeignKey('child_account.id', ondelete='SET NULL'), nullable=True, index=True)
    start_date = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))
    end_date = Column(DateTime(timezone=True), index=True, nullable=False)
    status = Column(String, default="active", index=True, nullable=False)
    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))
    account = relationship("Account", back_populates="trials")
    child_account = relationship("ChildAccount", back_populates="trials")


class SubscriptionPause(SqlAlchemyBase):
    __tablename__ = 'subscription_pause'
    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, nullable=False, index=True, default=lambda: str(uuid.uuid4()))
    active_subscription_id = Column(Integer, ForeignKey('active_subscription.id', ondelete="CASCADE"), nullable=False, index=True)
    
    start_date = Column(DateTime(timezone=True), nullable=False, index=True, comment="When the pause actually started (not policy start)")
    end_date = Column(DateTime(timezone=True), nullable=False, index=True, comment="Policy end date (e.g., Sept 1) - NOT actual resume time. See resumed_at for actual end.")
    paused_seconds = Column(Integer, nullable=False, comment="Duration of pause in seconds")
    
    # Billing information for extending subscription on resume
    original_period_end = Column(DateTime(timezone=True), nullable=False, comment="Original billing cycle end before pause")
    new_period_end = Column(DateTime(timezone=True), nullable=False, comment="New billing cycle end after extending for pause duration")
    
    # Track actual pause duration for early resume scenarios
    actual_paused_seconds = Column(Integer, nullable=True, comment="Actual pause duration if resumed early (vs planned paused_seconds)")
    resumed_at = Column(DateTime(timezone=True), nullable=True, comment="When the pause was actually resumed")
    
    # For webhook-based trial_end setting
    intended_trial_end = Column(DateTime(timezone=True), nullable=True, comment="Intended trial_end timestamp for billing extension - set via webhook")
    
    # DEPRECATED in v3: No longer used. We extend billing by actual pause duration instead.
    remaining_seconds = Column(Integer, nullable=True, comment="DEPRECATED: Was used to store remaining subscription time. v3 uses actual pause duration instead.")
    
    status = Column(SqlEnum(SubscriptionPauseStatus), nullable=False, index=True, default=SubscriptionPauseStatus.SCHEDULED)
    qstash_message_id = Column(String, nullable=True, comment="QStash message ID for scheduled pause tracking")
    
    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    
    # Relationships
    active_subscription = relationship("ActiveSubscription", back_populates="subscription_pauses")


class StripeCheckoutSessionData(SqlAlchemyBase):
    __tablename__ = 'stripe_checkout_session_data'
    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, nullable=False, index=True, default=lambda: str(uuid.uuid4()))


    stripe_checkout_session_id = Column(String, unique=True, nullable=False, index=True)
    line_item_details_json = Column(JSONB, nullable=False) # Contains the line items from the checkout session, including any relevant subject public IDs.
    parent_account_public_id = Column(String, ForeignKey('account.public_id'), nullable=False, index=True)
    processed_at = Column(DateTime(timezone=True), nullable=True, index=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC), index=True)


    parent_account = relationship("Account", foreign_keys=[parent_account_public_id], back_populates="stripe_checkout_sessions")
    __table_args__ = (
        SQLAIndex('ix_stripe_checkout_session_data_created_processed', "created_at", processed_at.desc().nulls_last()),
    )