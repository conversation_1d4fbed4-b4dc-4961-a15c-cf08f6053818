from sqlalchemy import Integer, Text, Column, ForeignKey, Index, String, Boolean
from sqlalchemy.sql.sqltypes import DateTime
from db.database import SqlAlchemyBase
from datetime import datetime, UTC
import uuid
from sqlalchemy.orm import relationship
from .subscription import SubscriptionOption # Assuming SubscriptionOption is in subscription.py in the same directory


class Year(SqlAlchemyBase):
    __tablename__ = 'year'

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False, unique=True)
    public_id = Column(String, default=lambda: str(uuid.uuid4()), unique=True, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))


    is_classique = Column(Boolean, default=False)
    is_general = Column(Boolean, default=False)

    child_accounts = relationship("ChildAccount", back_populates="year")
    subjects = relationship("Subject", back_populates="year", cascade="all, delete-orphan")
    subscription_options = relationship("SubscriptionOption", back_populates="year", cascade="all, delete-orphan")


class Subject(SqlAlchemyBase):
    __tablename__ = 'subject'

    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, nullable=False, index=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False)
    year_id = Column(Integer, ForeignKey('year.id'), nullable=False, index=True)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, index=True)  # Added is_active
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC), index=True)
    image_url = Column(String, nullable=True)

    year = relationship("Year", back_populates="subjects")

    chapters = relationship("Chapter", back_populates="subject", cascade="all, delete-orphan")

    selected_subjects_links = relationship("PlanSelectedSubject",
                                           back_populates="subject",
                                           cascade="all, delete-orphan")


class Chapter(SqlAlchemyBase):
    __tablename__ = 'chapter'

    id = Column(Integer, primary_key=True)
    public_id = Column(String, nullable=False, unique=True, default=lambda: str(uuid.uuid4()), index=True)
    title = Column(Text, nullable=False)
    description = Column(Text, nullable=True)
    ordering = Column(Integer)  

    subject_id = Column(Integer, ForeignKey("subject.id"), index=True)

    subject = relationship("Subject", back_populates="chapters")
    learning_nodes = relationship("LearningNode", back_populates="chapter", cascade="all, delete-orphan")

    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))


    __table_args__ = (
        Index('idx_chapter_subject_id_ordering', "subject_id", "ordering"),
    )