# learning_node.py
from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ey, Enum, JSON, DateTime
# Removed declarative_base import as it's not needed here if SqlAlchemyBase is used elsewhere
# from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship, Mapped, mapped_column # Added Mapped
from datetime import datetime, UTC
from pydantic import BaseModel, Field, ConfigDict
from typing import TypeVar, Generic, Type, List, Dict, Optional, Union, Literal, Any
import enum
from sqlalchemy import func, delete

# Assuming SqlAlchemyBase is defined in db.database or similar
# If not, uncomment the Base = declarative_base() line
# Base = declarative_base()
from db.database import SqlAlchemyBase # Make sure this import is correct
# Forward declaration for Chapter
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from .content import Chapter
    from .exercise import Exercise # Assuming Exercise is in .exercise

# ============================================================================
# Enums
# ============================================================================

class LearningNodeTypeEnum(str, enum.Enum): # Inherit from str for easier JSON serialization/comparison
    MATH = "MATH"
    GRAMMAR = "GRAMMAR"
    VOCABULARY = "VOCABULARY"
    READING = "READING"
    LISTENING = "LISTENING"
    SPEAKING = "SPEAKING"
    WRITING = "WRITING"
    VERB = "VERB"

# ============================================================================
# Pydantic Models (matching your TypeScript schemas)
# (Keep these exactly as they are - they define the structure of the 'content' JSON)
# ============================================================================

# Vocabulary Node Models
class VocabWordModel(BaseModel):
    base_language: str
    target_language: str
    gender: Optional[Literal['masculine', 'feminine', 'neuter']] = None
    example_sentence: Optional[str] = None
    explanation: Optional[str] = None
    audio_file_id: Optional[int] = None

class VocabGroupModel(BaseModel):
    id: str
    title: str
    words: List[VocabWordModel]

class VocabContentModel(BaseModel):
    base_language: str
    target_language: str
    groups: List[VocabGroupModel]
    notes: Optional[str] = None

# Verb Node Models
class VerbFormModel(BaseModel):
    person: str
    form: str
    irregular: Optional[bool] = None

class VerbConjugationModel(BaseModel):
    tense_id: str
    tense_name: str
    is_compound: bool
    auxiliary: Optional[str] = None
    participle: Optional[str] = None
    forms: List[VerbFormModel]

class VerbModel(BaseModel):
    infinitive: str
    translation: str
    explanation: str
    example: str
    verb_family: Literal['er', 'ir', 're']
    is_irregular: bool
    is_reflexive: bool
    conjugations: List[VerbConjugationModel]

class VerbTenseModel(BaseModel):
    tense_id: str
    tense_name: str

class VerbMoodModel(BaseModel):
    mood_id: str
    mood_name: str
    tenses: List[VerbTenseModel]

class VerbContentModel(BaseModel):
    moods: List[VerbMoodModel]
    verbs: List[VerbModel]
    notes: Optional[str] = None

# Reading Node Models
class ReadingExplanationModel(BaseModel):
    public_id: str
    term: str
    match_term: str
    word_type: Optional[str] = None
    base_language: Optional[str] = None
    target_language: Optional[str] = None
    translation: Optional[str] = None
    explanation: Optional[str] = None
    examples: Optional[List[str]] = None
    audio_file_id: Optional[int] = None

class ReadingContentModel(BaseModel):
    markdown_text: str
    explanations: Optional[List[ReadingExplanationModel]] = None
    video_public_id: Optional[str] = None
    notes: Optional[str] = None

# Listening Node Models
class ListeningContentModel(BaseModel):
    audio_file_id: Optional[int] = None
    transcription: Optional[str] = None
    video_public_id: Optional[str] = None
    notes: Optional[str] = None

# Math Node Models
class MathContentModel(BaseModel):
    video_public_id: Optional[str] = None
    notes: Optional[str] = None
    # For now, math content can be empty since exercises are separate
    pass

# Grammar Node Models
class GrammarContentModel(BaseModel):
    video_public_id: Optional[str] = None
    notes: Optional[str] = None

# Union type for all learning node content Pydantic models
NodeContentType = Union[
    MathContentModel,
    GrammarContentModel,
    VocabContentModel,
    ReadingContentModel,
    ListeningContentModel,
    VerbContentModel,
]

# ============================================================================
# Generic Type for Content
# ============================================================================

T = TypeVar('T', bound=BaseModel) # Keep this for the Generic type hint

# ============================================================================
# SQLAlchemy Models
# ============================================================================

class LearningNodeRelationship(SqlAlchemyBase): # Use SqlAlchemyBase
    __tablename__ = 'learning_node_relationship'

    id = Column(Integer, primary_key=True)
    parent_id = Column(Integer, ForeignKey('learning_node.id', ondelete='CASCADE'), nullable=False, index=True) # Added index
    child_id = Column(Integer, ForeignKey('learning_node.id', ondelete='CASCADE'), nullable=False, index=True) # Added index
    ordering = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC))

    # Optional: Define relationships back to LearningNode if needed for easier traversal
    parent = relationship("LearningNode", foreign_keys=[parent_id], back_populates="child_relationships")
    child = relationship("LearningNode", foreign_keys=[child_id], back_populates="parent_relationships")


# The single, generic LearningNode SQLAlchemy model
class LearningNode(SqlAlchemyBase, Generic[T]): # Use SqlAlchemyBase
    __tablename__ = 'learning_node'

    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, index=True, nullable=False) # Added nullable=False

    node_type = Column(Enum(LearningNodeTypeEnum), nullable=False, index=True) # Added index

    title = Column(String, nullable=False)
    description = Column(String, nullable=True)
    ordering = Column(Integer, default=0)  # New ordering field for consistent display order
    # JSON column to store all content
    _content = Column('content', JSON, nullable=False)
    _data = Column('data', JSON, nullable=True)

    chapter_id: Mapped[Optional[int]] = mapped_column(ForeignKey("chapter.id", ondelete="SET NULL"), nullable=True, index=True) # New field

    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC), index=True) # Added index
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))

    # Optional: Define relationships to relationships table if needed
    parent_relationships = relationship("LearningNodeRelationship", foreign_keys=[LearningNodeRelationship.child_id], back_populates="child", cascade="all, delete-orphan")
    child_relationships = relationship("LearningNodeRelationship", foreign_keys=[LearningNodeRelationship.parent_id], back_populates="parent", cascade="all, delete-orphan")

    # Many-to-many relationship with Exercises through the association model
    exercise_associations = relationship("LearningNodeExerciseAssociation", back_populates="learning_node", cascade="all, delete-orphan")
    
    chapter: Mapped[Optional["Chapter"]] = relationship(back_populates="learning_nodes") # New relationship
    
    # Convenience property to access exercises directly
    @property
    def exercises(self) -> List["Exercise"]: # Added type hint for Exercise
        return [assoc.exercise for assoc in self.exercise_associations if assoc.exercise is not None]


    @hybrid_property
    def content(self) -> T:
        """Type-safe access to learning node content"""
        content_model = self._get_content_model()
        if self._content:
            return content_model.model_validate(self._content)
        return content_model() # Return an empty model if no content

    @content.setter
    def content(self, value: Union[T, Dict[str, Any]]):
        """Type-safe setting of learning node content"""
        # Ensure the provided value matches the expected content model for the node's type
        expected_model = self._get_content_model()
        if isinstance(value, dict):
            try:
                validated_content = expected_model.model_validate(value)
            except Exception as e:
                raise TypeError(f"Content must be a dict or an instance of {content_model_class.__name__}")
        elif isinstance(value, expected_model):
            validated_content = value
        else:
            raise TypeError(f"Content must be a dict or an instance of {content_model_class.__name__}")
        self._content = validated_content.model_dump(mode='json') # Store as dict

    @content.expression
    def content(cls):
        """Provide the SQL expression for the 'content' attribute."""
        return cls._content

    # Helper method to get the correct Pydantic model based on node_type
    def _get_content_model(self) -> Type[T]:
        """Map node type to appropriate Pydantic content model"""
        content_model_mapping: Dict[LearningNodeTypeEnum, Type[NodeContentType]] = {
            LearningNodeTypeEnum.MATH: MathContentModel,
            LearningNodeTypeEnum.GRAMMAR: GrammarContentModel,
            LearningNodeTypeEnum.VOCABULARY: VocabContentModel,
            LearningNodeTypeEnum.READING: ReadingContentModel,
            LearningNodeTypeEnum.LISTENING: ListeningContentModel,
            LearningNodeTypeEnum.VERB: VerbContentModel,
            # Add Speaking and Writing if they have distinct models, otherwise map to a generic or existing one
            LearningNodeTypeEnum.SPEAKING: GrammarContentModel, # Example: reuse GrammarContentModel
            LearningNodeTypeEnum.WRITING: GrammarContentModel,  # Example: reuse GrammarContentModel
        }
        model = content_model_mapping.get(self.node_type)
        if model is None:
             # This should not happen if node_type is enforced by the Enum
             raise ValueError(f"Unknown node type '{self.node_type}' encountered.")
        return model

  

class LearningNodeFactory:
    """Factory to create type-safe learning nodes using the single LearningNode model"""

    # Map node type strings to content models
    _content_model_mapping: Dict[str, Type[NodeContentType]] = {
        lt.value: model for lt, model in {
            LearningNodeTypeEnum.MATH: MathContentModel,
            LearningNodeTypeEnum.GRAMMAR: GrammarContentModel,
            LearningNodeTypeEnum.VOCABULARY: VocabContentModel,
            LearningNodeTypeEnum.READING: ReadingContentModel,
            LearningNodeTypeEnum.LISTENING: ListeningContentModel,
            LearningNodeTypeEnum.VERB: VerbContentModel,
            LearningNodeTypeEnum.SPEAKING: GrammarContentModel, # Example
            LearningNodeTypeEnum.WRITING: GrammarContentModel,  # Example
        }.items()
    }

    @staticmethod
    def create_node(node_type: str, public_id: str, title: str, content: Dict[str, Any],
                    description: Optional[str] = None, video_public_id: Optional[str] = None, notes: Optional[str] = None) -> LearningNode:
        """
        Create a new learning node with type safety.
        
        Args:
            node_type: Type of the node (must match a value in LearningNodeTypeEnum)
            public_id: Unique public identifier for the node
            title: Node title
            content: Dictionary containing node content (will be validated against appropriate model)
            description: Optional node description
            video_public_id: Optional video public ID (will be added to content for supported node types)
            notes: Optional notes (will be added to content for supported node types)
            
        Returns:
            A new LearningNode instance
            
        Raises:
            ValueError: If node_type is invalid or content doesn't match the expected model
        """
        # Validate node type
        try:
            enum_node_type = LearningNodeTypeEnum(node_type)
        except ValueError:
            valid_types = [t.value for t in LearningNodeTypeEnum]
            raise ValueError(f"Invalid node type: {node_type}. Must be one of: {', '.join(valid_types)}")
        
        # Get the appropriate content model
        content_model_class = LearningNodeFactory._content_model_mapping.get(node_type)
        if not content_model_class:
            raise ValueError(f"No content model defined for node type: {node_type}")
            
        # Add video_public_id and notes to content if applicable
        content_copy = content.copy()
        if video_public_id is not None and 'video_public_id' in content_model_class.model_fields:
            content_copy['video_public_id'] = video_public_id
            
        if notes is not None and 'notes' in content_model_class.model_fields:
            content_copy['notes'] = notes
            
        # Create and validate the content model
        try:
            content_model = content_model_class.model_validate(content_copy)
        except Exception as e:
            raise ValueError(f"Invalid content for {node_type} node: {str(e)}")
            
        # Create the node
        node = LearningNode(
            node_type=enum_node_type,
            public_id=public_id,
            title=title,
            description=description,
            _content=content_model.model_dump(mode='json')
        )
        
        return node

# ============================================================================
# Helper Functions (Remain largely the same, operating on LearningNode)
# ============================================================================

# Assuming 'session' is a SQLAlchemy Session object
def get_node_children(session, node_id: int) -> List[LearningNode]:
    """Get all child nodes of a given node, ordered."""
    relationships = session.query(LearningNodeRelationship)\
        .filter(LearningNodeRelationship.parent_id == node_id)\
        .order_by(LearningNodeRelationship.ordering)\
        .all()

    child_ids = [rel.child_id for rel in relationships]
    if not child_ids:
        return []

    # Fetch children and maintain order
    children_map = {node.id: node for node in session.query(LearningNode).filter(LearningNode.id.in_(child_ids)).all()}
    ordered_children = [children_map[child_id] for child_id in child_ids if child_id in children_map]
    return ordered_children


def get_node_parents(session, node_id: int) -> List[LearningNode]:
    """Get all parent nodes of a given node."""
    relationships = session.query(LearningNodeRelationship)\
        .filter(LearningNodeRelationship.child_id == node_id)\
        .all()

    parent_ids = [rel.parent_id for rel in relationships]
    if not parent_ids:
        return []
    return session.query(LearningNode)\
        .filter(LearningNode.id.in_(parent_ids))\
        .all()

def add_child_to_node(session, parent_id: int, child_id: int, order: Optional[int] = None) -> LearningNodeRelationship:
    """Add a child node relationship to a parent node."""
    # Optional: Check if relationship already exists
    existing = session.query(LearningNodeRelationship).filter_by(parent_id=parent_id, child_id=child_id).first()
    if existing:
        # Decide behavior: update order or raise error? Let's update order if provided.
        if order is not None and existing.ordering != order:
            existing.ordering = order
            session.add(existing) # Mark as dirty
        return existing

    if order is None:
        # Default order: append to the end
        max_order = session.query(func.max(LearningNodeRelationship.ordering))\
                           .filter(LearningNodeRelationship.parent_id == parent_id)\
                           .scalar()
        order = (max_order or -1) + 1 # Start from 0 if no children exist

    relationship = LearningNodeRelationship(
        parent_id=parent_id,
        child_id=child_id,
        ordering=order
    )
    session.add(relationship)
    # Flush to get the ID if needed immediately, otherwise commit later
    # session.flush()
    return relationship

def remove_child_from_node(session, parent_id: int, child_id: int) -> int:
    """Remove a specific child node relationship from a parent node."""
    # Use execute for potentially better performance on simple deletes
    result = session.execute(
        delete(LearningNodeRelationship).where(
            LearningNodeRelationship.parent_id == parent_id,
            LearningNodeRelationship.child_id == child_id
        )
    )
    # result.rowcount gives the number of deleted rows
    return result.rowcount