from sqlalchemy import Integer, Text, Column, Integer, ForeignKey, Index, String
from sqlalchemy.sql.sqltypes import <PERSON>ole<PERSON>, DateTime
from db.database import SqlAlchemyBase
from datetime import datetime, timedelta, UTC
import random
import string

class AccountAuthSecurity(SqlAlchemyBase):
    __tablename__ = 'account_auth_security'
    id = Column(Integer, primary_key=True)
    
    account_id = Column(Integer, ForeignKey('account.id', ondelete='CASCADE'), unique=True)
    
    failed_attempts = Column(Integer, default=0)
    last_failed_attempt = Column(DateTime(timezone=True))
    locked_until = Column(DateTime(timezone=True))
    
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))

    __table_args__ = (
        Index('idx_auth_security_account', account_id),
        Index('idx_auth_security_locked', locked_until),
    )

# We want a short more user-friendly reset code
def generate_parent_pin_reset_code():
    return ''.join(random.choices(string.ascii_letters + string.digits, k=9))

class PinResetRequest(SqlAlchemyBase):
    __tablename__ = 'pin_reset_request'
    id = Column(Integer, primary_key=True)
    account_id = Column(Integer, ForeignKey('account.id', ondelete='CASCADE'))
    
    request_token = Column(Text, unique=True, default=lambda: generate_parent_pin_reset_code())
    expires_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC) + timedelta(minutes=30))
    used = Column(Boolean, default=False)
    
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC))

    __table_args__ = (
        Index('idx_pinreset_account', account_id),
        Index('idx_pinreset_token', request_token),
    )

class EmailRateLimit(SqlAlchemyBase):
    __tablename__ = 'email_rate_limit'
    id = Column(Integer, primary_key=True)
    
    email = Column(String(255), nullable=False)
    email_type = Column(String(50), nullable=False)  # 'verification', 'pin_reset', etc.
    
    # Track attempts in different time windows
    attempts_last_minute = Column(Integer, default=0)
    attempts_last_hour = Column(Integer, default=0)
    attempts_last_day = Column(Integer, default=0)
    
    # Track when the windows reset
    minute_window_start = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC))
    hour_window_start = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC))
    day_window_start = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC))
    
    last_attempt = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC))
    
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))

    __table_args__ = (
        Index('idx_email_rate_limit_email_type', 'email', 'email_type'),
        Index('idx_email_rate_limit_last_attempt', 'last_attempt'),
    )
