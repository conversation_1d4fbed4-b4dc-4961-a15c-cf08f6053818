from sqlalchemy import Integer, Text, Column, Integer, ForeignKey, DateTime
from db.database import SqlAlchemyBase
from datetime import datetime, UTC


class SignUp(SqlAlchemyBase):
    __tablename__ = 'sign_up'

    id = Column(Integer, primary_key=True)
    account_id = Column(Integer, ForeignKey('account.id'))
    email = Column(Text, nullable=False)
    language = Column(Text, nullable=False)
    source = Column(Text)
    searchparams = Column(Text)
    
    year_options = Column(Text)
    system_options = Column(Text)
    

    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))


