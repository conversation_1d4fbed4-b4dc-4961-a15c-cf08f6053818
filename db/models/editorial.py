from sqlalchemy import Column, Integer, <PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON><PERSON>, DateTime, Enum as SqlEnum, CheckConstraint
from sqlalchemy.orm import relationship
from datetime import datetime, UTC
from enum import Enum
import uuid
from db.database import SqlAlchemyBase

class EditorRole(str, Enum):
    EDITOR = "EDITOR"
    ADMIN = "ADMIN"

class EditorAccount(SqlAlchemyBase):
    __tablename__ = 'editor_account'

    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, nullable=False, index=True,
                      default=lambda: str(uuid.uuid4()))
    email = Column(String(255), unique=True, nullable=False, index=True)
    pwd_hash = Column(String, nullable=False)
    role = Column(SqlEnum(EditorRole, name='editor_role', create_constraint=False), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC),
                       onupdate=lambda: datetime.now(UTC))
    
    # Relationships
    scopes = relationship("EditorScope", back_populates="editor", cascade="all, delete-orphan")
    assigned_drafts = relationship("DraftExercise", back_populates="assigned_editor")

    publish_batches = relationship("PublishBatch", back_populates="created_by")

class EditorScope(SqlAlchemyBase):
    __tablename__ = 'editor_scope'
    
    id = Column(Integer, primary_key=True)
    editor_id = Column(Integer, ForeignKey('editor_account.id', ondelete='CASCADE'), 
                      nullable=False, index=True)
    subject_id = Column(Integer, ForeignKey('subject.id', ondelete='CASCADE'), 
                       nullable=True, index=True)
    chapter_id = Column(Integer, ForeignKey('chapter.id', ondelete='CASCADE'), 
                       nullable=True)
    learning_node_id = Column(Integer, ForeignKey('learning_node.id', ondelete='CASCADE'), 
                             nullable=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC))
    
    # Relationships
    editor = relationship("EditorAccount", back_populates="scopes")
    subject = relationship("Subject")
    chapter = relationship("Chapter")
    learning_node = relationship("LearningNode")
    
    __table_args__ = (
        CheckConstraint(
            """(subject_id IS NOT NULL AND chapter_id IS NULL AND learning_node_id IS NULL) OR
               (chapter_id IS NOT NULL AND learning_node_id IS NULL) OR
               (learning_node_id IS NOT NULL)""",
            name='check_scope_hierarchy'
        ),
    )