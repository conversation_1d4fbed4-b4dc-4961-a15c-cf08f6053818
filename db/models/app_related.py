from sqlalchemy import Integer, Text, Column, Integer, ForeignKey, Index
from sqlalchemy.sql.sqltypes import Boolean, DateTime
from db.database import SqlAlchemyBase
from datetime import datetime, UTC
import uuid


class Toast(SqlAlchemyBase):
    __tablename__ = 'toast'
    id = Column(Integer, primary_key=True)
    public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)

    type = Column(Text, default='info')
    content = Column(Text)
    priority = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    account_type = Column(Text, default='parent')
    
    temporary_dismissable = Column(Boolean, default=True)
    permanently_dismissable = Column(Boolean, default=False)
    
    hide_from_subscribers = Column(Boolean, default=True)
    show_to_subscribers_only = Column(Boolean, default=False)
    
    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))

class DismissedToast(SqlAlchemyBase):
    __tablename__ = 'dismissed_toast'
    id = Column(Integer, primary_key=True)
    
    toast_id = Column(Integer, ForeignKey('toast.id'))
    account_id = Column(Integer, ForeignKey('account.id'))
    child_account_id = Column(Integer, ForeignKey('child_account.id', ondelete='CASCADE'))

    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))


class TaskInvocation(SqlAlchemyBase):
    __tablename__ = 'task_invocation'
    id = Column(Integer, primary_key=True)
    public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)

    task_type = Column(Text, nullable=False)  # e.g., 'weekly_activity_summary'
    task_status = Column(Text, nullable=False)  # 'success', 'failed', 'in_progress'

    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))

    __table_args__ = (
        Index('idx_task_type_created', task_type, created_at),
    )
