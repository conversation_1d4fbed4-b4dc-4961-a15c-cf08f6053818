from sqlalchemy import Inte<PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON>, String, CheckConstraint, UniqueConstraint, Text, Index as SQLAIndex, Boolean
from sqlalchemy.sql.sqltypes import DateTime
from db.database import SqlAlchemyBase
from sqlalchemy.orm import relationship
from datetime import datetime, UTC
from sqlalchemy.dialects.postgresql import JSONB
import uuid
from sqlalchemy import Enum as SqlEnum
import enum
from .subscription import PlanTypeEnum, BillingPeriodEnum

# --- Enum Definitions ---
class SubscriptionChangeType(str, enum.Enum):
    QUANTITY_CHANGE = "quantity_change"
    BILLING_PERIOD_CHANGE = "billing_period_change"
    PLAN_TYPE_CHANGE = "plan_type_change"
    CANCEL = "cancel"

class SubscriptionChangeStatus(str, enum.Enum):
    PENDING = "pending"          # Created but not yet sent to Stripe
    SCHEDULED = "scheduled"      # Scheduled with Stripe (for downgrades)
    PROCESSING = "processing"    # Currently being processed
    COMPLETED = "completed"      # Successfully applied
    FAILED = "failed"           # Failed to apply
    CANCELLED = "cancelled"     # Cancelled by user or system

# --- Models ---

class SubscriptionPendingChange(SqlAlchemyBase):
    """
    Tracks pending changes to subscriptions that will take effect at a future date.
    This includes upgrades, downgrades, billing period changes, etc.
    """
    __tablename__ = 'subscription_pending_change'
    
    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, nullable=False, index=True, default=lambda: str(uuid.uuid4()))
    
    # References
    active_subscription_id = Column(Integer, ForeignKey('active_subscription.id', ondelete="CASCADE"), nullable=False, index=True)
    plan_link_id = Column(Integer, ForeignKey('active_subscription_plan_link.id', ondelete="CASCADE"), nullable=False, index=True)
    
    # Change details
    change_type = Column(SqlEnum(SubscriptionChangeType), nullable=False, index=True)
    
    # Current state (what we're changing from)
    current_quantity = Column(Integer, nullable=False)
    current_stripe_price_id = Column(String, nullable=False)
    current_plan_type = Column(SqlEnum(PlanTypeEnum), nullable=False)
    current_billing_period = Column(SqlEnum(BillingPeriodEnum), nullable=False)
    
    # New state (what we're changing to)
    new_quantity = Column(Integer, nullable=False)
    new_stripe_price_id = Column(String, nullable=False)
    new_plan_type = Column(SqlEnum(PlanTypeEnum), nullable=False)
    new_billing_period = Column(SqlEnum(BillingPeriodEnum), nullable=False)
    
    # Scheduling
    effective_date = Column(DateTime(timezone=True), nullable=False, index=True)
    stripe_subscription_schedule_id = Column(String, nullable=True, index=True)
    stripe_schedule_phase_id = Column(String, nullable=True)  # For complex schedules
    
    # Status tracking
    status = Column(SqlEnum(SubscriptionChangeStatus), default=SubscriptionChangeStatus.PENDING, nullable=False, index=True)
    
    # Proration details (for upgrades)
    proration_amount_cents = Column(Integer, nullable=True)  # Amount to be charged/credited
    proration_currency = Column(String, nullable=True)
    proration_date = Column(DateTime(timezone=True), nullable=True)
    
    # Metadata
    metadata_json = Column(JSONB, nullable=True, comment="Additional data like error messages, Stripe responses, etc.")
    created_by = Column(String, nullable=True, comment="User or system that created this change")
    cancelled_by = Column(String, nullable=True, comment="User or system that cancelled this change")
    cancelled_at = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    processed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    active_subscription = relationship("ActiveSubscription", back_populates="pending_changes")
    plan_link = relationship("ActiveSubscriptionPlanLink", back_populates="pending_changes")
    pending_selected_subjects = relationship("SubscriptionPendingSelectedSubject", back_populates="pending_change", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        
        # Only one pending change per plan link at a time
        SQLAIndex(
            'idx_unique_pending_change_per_link',
            plan_link_id,
            unique=True,
            postgresql_where=(status.in_([SubscriptionChangeStatus.PENDING, SubscriptionChangeStatus.SCHEDULED]))
        ),
    )


class SubscriptionPendingSelectedSubject(SqlAlchemyBase):
    """
    Tracks which subjects will be active after a pending change takes effect.
    Only relevant for SC (Subject Credit) plans.
    """
    __tablename__ = 'subscription_pending_selected_subject'
    
    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, nullable=False, index=True, default=lambda: str(uuid.uuid4()))
    
    # References
    pending_change_id = Column(Integer, ForeignKey('subscription_pending_change.id', ondelete="CASCADE"), nullable=False, index=True)
    subject_id = Column(Integer, ForeignKey('subject.id', ondelete="CASCADE"), nullable=False, index=True)
    
    # Order for display purposes
    display_order = Column(Integer, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))
    
    # Relationships
    pending_change = relationship("SubscriptionPendingChange", back_populates="pending_selected_subjects")
    subject = relationship("Subject")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('pending_change_id', 'subject_id', name='uq_pending_change_subject'),
    )


class SubscriptionChangeLog(SqlAlchemyBase):
    """
    Audit log for all subscription changes (completed, failed, or cancelled).
    Provides history of what changes were attempted and their outcomes.
    """
    __tablename__ = 'subscription_change_log'
    
    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, nullable=False, index=True, default=lambda: str(uuid.uuid4()))
    
    # References (may be null if records are deleted)
    active_subscription_id = Column(Integer, ForeignKey('active_subscription.id', ondelete="SET NULL"), nullable=True, index=True)
    plan_link_id = Column(Integer, ForeignKey('active_subscription_plan_link.id', ondelete="SET NULL"), nullable=True, index=True)
    pending_change_id = Column(Integer, ForeignKey('subscription_pending_change.id', ondelete="SET NULL"), nullable=True)
    
    # Copy of key fields for historical record
    change_type = Column(SqlEnum(SubscriptionChangeType), nullable=False)
    final_status = Column(SqlEnum(SubscriptionChangeStatus), nullable=False, index=True)
    
    # What changed
    old_state_json = Column(JSONB, nullable=False, comment="Complete state before change")
    new_state_json = Column(JSONB, nullable=False, comment="Complete state after change")
    
    # Outcome
    success = Column(Boolean, nullable=False, index=True)
    error_message = Column(Text, nullable=True)
    stripe_event_id = Column(String, nullable=True, index=True)
    
    # Timestamps
    change_initiated_at = Column(DateTime(timezone=True), nullable=False)
    change_completed_at = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(UTC), index=True)
    
    # Relationships
    active_subscription = relationship("ActiveSubscription")
    plan_link = relationship("ActiveSubscriptionPlanLink")
    pending_change = relationship("SubscriptionPendingChange")