from sqlalchemy import Integer, Text, Column, Integer
from sqlalchemy.sql.sqltypes import DateTime
from db.database import SqlAlchemyBase
from datetime import datetime, UTC
import uuid


class BlogPost(SqlAlchemyBase):
    __tablename__ = 'blog_post'
    id = Column(Integer, primary_key=True)
    public_id = Column(Text, default=lambda: str(
        uuid.uuid4()), unique=True, nullable=False)
    
    title = Column(Text, nullable=False)
    language = Column(Text, nullable=False)
    summary = Column(Text, nullable=False)
    content = Column(Text, nullable=False)
    slug = Column(Text, unique=True, nullable=False)
    image_name = Column(Text)
    ordering = Column(Integer)
    reading_time = Column(Integer)

    created_at = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))
    last_updated = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(UTC))