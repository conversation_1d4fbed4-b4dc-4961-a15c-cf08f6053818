from sqlalchemy import Column, Integer, String, Text, DateTime
from sqlalchemy.sql import func
from datetime import datetime, timezone, UTC

from db.database import SqlAlchemyBase

class ContactSubmission(SqlAlchemyBase):
    __tablename__ = "contact_submission"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, nullable=False, index=True)
    message = Column(Text, nullable=False)
    submitted_at = Column(DateTime(timezone=True), server_default=func.now(), default=lambda: datetime.now(UTC))
    user_agent = Column(String, nullable=True)
    ip_address = Column(String, nullable=True)

    def __repr__(self):
        return f"<ContactSubmission(id={self.id}, email='{self.email}', submitted_at='{self.submitted_at}')>"