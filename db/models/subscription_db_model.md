# LuxEdu Subscription Platform – Updated Technical Overview (May 2025)

> **Scope**
> This document supersedes all previous "Subscription Model Refactor" drafts. It reflects the *current* database schema under `db/models/subscription.py` and the FastAPI v1 routes under `api/v1/app/parent/subscription/`.

---

## 1 ▪︎ Business Goals & Guiding Principles

| # | Goal                                                                                                            | Design Consequence                                                                                                                             |
| - | --------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------- |
| 1 | **Single Parent Billing** – every paying parent gets *one* Stripe `Subscription`.                               | DB table `active_subscription` is 1‑to‑1 with Stripe subscription ID. All add‑ons are modelled as **items** (`active_subscription_plan_link`). |
| 2 | **Granular access** – parents can buy either full‑year access (YF) *or* pick N subjects (SC) per academic year. | Two plan types only: `PlanTypeEnum.YF` and `PlanTypeEnum.SC`. No legacy bundles remain.                                                        |
| 3 | **Cohort‑based Pricing** – we can roll out price increases safely.                                              | `price_version` + `price_eligibility` gate every `subscription_option`.                                                                        |
| 4 | **Child‑level entitlements** – optional assignment of a plan item to a specific child profile.                  | `active_subscription_plan_link.target_child_account_id` is **nullable**; a value means the entitlement is scoped.                              |
| 5 | **Self‑service first** – parents edit things in the portal, never via support tickets.                          | Rich API surface: create, update, cancel, manage selected subjects.                                                                            |

---

## 2 ▪︎ Key Enums

```python
class PlanTypeEnum(str, enum.Enum):
    SC = "SC"   # Subject Credits
    YF = "YF"   # Year‑Full (all subjects)

class BillingPeriodEnum(str, enum.Enum):
    MONTHLY = "monthly"
    YEARLY  = "yearly"

class SubscriptionStatusType(str, enum.Enum):
    ACTIVE = "active"
    TRIALING = "trialing"
    PAST_DUE = "past_due"
    CANCELED = "canceled"
    # …
```

---

## 3 ▪︎ Database Entities (pipeline view)

```mermaid
erDiagram
    account ||..o{ price_eligibility : "has"
    account ||--o{ active_subscription : "owns"
    active_subscription ||--o{ active_subscription_plan_link : "contains"
    active_subscription_plan_link ||--o{ user_subscription_selected_subject : "selects"
    price_version ||--o{ subscription_option : "defines prices for"
    subscription_option ||..o{ active_subscription_plan_link : "instantiates"
    subscription_option }o--|| year : "for"
```

| Table                                     | Purpose                                                          | Notes                                                                                           |
| ----------------------------------------- | ---------------------------------------------------------------- | ----------------------------------------------------------------------------------------------- |
| **price\_version**                        | Cohort/epoch of pricing (e.g. *"Q1 2025 Standard"*).             | One row flagged `is_current = True`.                                                            |
| **price\_eligibility**                    | Link *Account* → *PriceVersion*.                                 | Inserted lazily on first pricing query.                                                         |
| **subscription\_option**                  | Concrete price points per academic year.                         | Holds up to four Stripe **Price** IDs:<br>`sc_monthly`, `sc_yearly`, `yf_monthly`, `yf_yearly`. |
| **active\_subscription**                  | Mirror of Stripe subscription.                                   | Always exists exactly once per paying parent.                                                   |
| **active\_subscription\_plan\_link**      | Mirror of Stripe **subscription item**.                          | Contains quantity (for SC = #subjects, YF = 1).                                                 |
| **user\_subscription\_selected\_subject** | Junction of SC plan‑link → individual *Subject*.                 | Ensures quantity==row‑count invariant.                                                          |
| **stripe\_checkout\_session\_data**       | Temporary storage for Checkout sessions until webhook finalises. | Writes `processed_at` timestamp once handled.                                                   |

---

## 4 ▪︎ API Surface (v1)

| Route                           | Verb | Auth | Brief                                                                                               |
| ------------------------------- | ---- | ---- | --------------------------------------------------------------------------------------------------- |
| `/plans`                        | GET  | ✅    | Returns eligible `subscription_option` records mapped to presentable plan cards.                    |
| `/create-checkout-session`      | POST | ✅    | Accepts *array* `selections[]` (year, subjects\[], full\_year\_flag, billing) → returns Stripe URL. |
| `/checkout-session-status/{id}` | GET  | ✅    | Poll after redirect; merges Stripe live status with DB record.                                      |
| `/create-portal-session`        | POST | ✅    | Opens Stripe Billing Portal for self‑service changes.                                               |
| `/update`                       | POST | ✅    | Add/remove/change quantities on the live subscription.                                              |
| `/manage-selected-subjects`     | POST | ✅    | Replace the chosen subjects for an **SC** plan‑link.                                                |
| `/verify-discount-code`         | POST | ✅    | Validates discount before checkout.                                                                 |
| `/webhook`                      | POST | ⚠️   | Stripe → server. Handles 5 event types.                                                             |

---

## 5 ▪︎ End‑to‑End Flow Charts

### 5.1 Getting plans

1. **Client** hits `/plans`.
2. **Service** finds/creates `price_eligibility` → resolves one `price_version`.
3. Returns curated list of plan cards (`SubscriptionPlanDetailSchema`).

### 5.2 Checkout

1. Parent posts `CreateCheckoutSessionRequest`.
2. Service validates subject IDs, year alignment, discount code, tax rates.
3. Builds Stripe Checkout Session (mode=subscription) with *metadata* per line item.
4. Records draft line‑item JSON in `stripe_checkout_session_data`.
5. Parent completes payment → redirected.
6. Stripe triggers `checkout.session.completed` webhook ⇒ `webhook_service.handle_checkout_session_completed`:

   * Creates/updates `active_subscription`.
   * Creates an `active_subscription_plan_link` for each item using stored JSON.
   * Syncs SC subjects.
   * Ends any running trials.

### 5.3 Lifecycle sync

* `customer.subscription.updated` keeps DB in lock‑step with Stripe (item adds, removals, pauses, quantity changes).
* `customer.subscription.deleted` marks status `canceled`.
* `invoice.payment_failed` records delinquency + notifies admins.

### 5.4 Post‑purchase management

* **Change quantity / add year / cancel at period end** → `/update` route → Stripe `Subscription.modify` → DB refresh.
* **Select/Deselect subjects** (SC only) → `/manage-selected-subjects` (pure DB).
* **Billing portal** changes (e.g. payment method) happen entirely on Stripe’s side; we never store card details.

---

## 6 ▪︎ Discount Codes

* Table `discount_code` defines the coupon and its guard‑rails: `applicable_to` (monthly/yearly/both), min/max account age, one‑time flag.
* `verify_discount_code_service` performs *all* validations before exposing the underlying Stripe coupon ID.
* Stripe applies the discount either via **Checkout** (`discounts` param) or **Subscription.modify\`**.

---

## 7 ▪︎ Price Version Algorithm (server‑side)

```text
if account has active PriceEligibility pointing to an *active* version → use that
else → fallback to PriceVersion where is_current & is_active → create PriceEligibility row
```

*Service* `_get_or_create_eligible_price_version()` encapsulates this logic and is reused by **Get Plans**, **Checkout**, **Update**.

---

## 8 ▪︎ Security & Data‑Integrity Guardrails

* All write routes depend on `AuthDependency(required=True)`; parent token must be valid.
* Every mutation double‑checks that the `parent_public_id` extracted from JWT matches the DB row of the resource being changed.
* Quantitative invariants:

  * `plan_link.quantity == len(user_subscription_selected_subject)` for SC links ‑ enforced in service layer.
  * Quantity **must** be ≥ 1; 0 converts to delete.
* Stripe IDs (`price`, `subscription_item_id`, `customer`) are never trusted blindly – always validated against local mapping.

---

## 9 ▪︎ Migration Notes from Previous Draft

| Old concept                                                     | Status in 2025 | Migration path                                                              |
| --------------------------------------------------------------- | -------------- | --------------------------------------------------------------------------- |
| `SubscriptionPlan` table with multiple complex plan\_type enums | **Deleted**    | Replaced by *SubscriptionOption* (year‑scoped) + simple SC/YF PlanTypeEnum. |
| `OfferingPrice` table                                           | **Merged**     | Fields moved into `subscription_option` (four dedicated columns).           |
| `FixedBundleItem` / `specific_subjects_fixed_bundle`            | **Removed**    | Not required – bundling handled by price quantities or separate products.   |
| Multiple Stripe Subscriptions per parent                        | **Removed**    | Consolidated into single `active_subscription`.                             |

---

## 10 ▪︎ Open Todos

* **Usage‑based add‑ons** – tutoring session packs to be modelled as `service_pack` plan type in a future schema increment.

---

### Appendix A – Table Quick‑reference (columns abridged)

```text
price_version(id, name, slug, is_current, is_active)
price_eligibility(id, account_id*, price_version_id*)
subscription_option(id, year_id*, price_version_id*, sc_monthly_price_id, …, yf_yearly_price_id)
active_subscription(id, parent_account_id*, stripe_subscription_id, status, …)
active_subscription_plan_link(id, active_subscription_id*, subscription_option_id*, chosen_plan_type, quantity, …)
user_subscription_selected_subject(id, plan_link_id*, subject_id*)
stripe_checkout_session_data(id, session_id, parent_account_public_id, line_item_details_json, processed_at)
```

*(“*” denotes FK)\*

---

© LuxEdu 2025 – Internal use only.
