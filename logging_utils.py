import logging
from gunicorn.glogging import Logger
from loguru import logger
import os

ENVIRONMENT = os.environ.get("ENVIRONMENT")
if ENVIRONMENT == "dev":
  LOG_LEVEL = 'DEBUG'
else:
  LOG_LEVEL = "INFO"


class InterceptHandler(logging.Handler):
  # Intercepts the normal log messages and transforms it into a loguru one
    def emit(self, record):
        # Get corresponding Loguru level if it exists
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 2
        while frame and frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(
            level,
            record.getMessage(),
            **getattr(record, "extra", {})  # Add this to preserve context
        )

class StubbedGunicornLogger(Logger):

    # Inherits from the gunicorn Logger, we overwrite the setup method, which gets called in the Logger's __init__
    # def __init__(self, log_level):
    #   self.log_level = log_level

    def setup(self, cfg):
        handler = logging.NullHandler()
        # Assign the gunicorn loggers to this object and adding nullhandlers
        self.error_logger = logging.getLogger("gunicorn.error")
        self.error_logger.addHandler(handler)
        self.access_logger = logging.getLogger("gunicorn.access")
        self.access_logger.addHandler(handler)
        # Set the log level
        self.error_log.setLevel(LOG_LEVEL)
        self.access_log.setLevel(LOG_LEVEL)
