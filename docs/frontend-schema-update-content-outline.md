# Frontend Schema Update: Content Outline for Non-Subscribers

## Date: 2025-07-01

## Overview
We've updated all content type APIs to provide non-subscribed users with a content outline showing section headers from markdown content, in addition to the truncated content preview. This allows users to see the structure and topics covered without revealing the full content.

## Schema Changes

### Updated Response Models

The following response models now include an optional `content_outline` field:

1. **ReadingNodeContentResponse** - For the `markdown_text` field
2. **MathNodeContentResponse** - For the `notes` field
3. **GrammarNodeContentResponse** - For the `notes` field
4. **VerbNodeContentResponse** - For the `notes` field
5. **VocabularyNodeContentResponse** - For the `notes` field
6. **ListeningNodeContentResponse** - For the `notes` field

### Example: ReadingNodeContentResponse

```typescript
interface ReadingNodeContentResponse {
  node_type: "READING";
  video_url?: string;
  video_duration?: number;
  markdown_text?: string;
  notes?: string;
  exercises: AnyExerciseResponse[];
  explanations?: ReadingExplanationResponse[];
  content_outline?: ContentOutlineItem[];  // NEW FIELD
}
```

### New Type: `ContentOutlineItem`

```typescript
interface ContentOutlineItem {
  level: number;  // 1-6 for H1-H6 markdown headers
  title: string;  // The header text
  order: number;  // Position in the document (0-based index)
}
```

## Behavior

### For Subscribed Users
- Full content in relevant fields (`markdown_text` for Reading, `notes` for others)
- `content_outline`: Will be `null` or empty array (not needed since they have full content)

### For Non-Subscribed Users
- Relevant content fields contain truncated preview (first few paragraphs)
  - Reading: `markdown_text` is truncated
  - All others: `notes` is truncated
- `content_outline`: Contains array of all section headers from the complete document

## Example Responses

### Reading Node

```json
{
  "node_type": "READING",
  "markdown_text": "# Introduction to French Grammar\n\nFrench grammar forms the foundation...",
  "content_outline": [
    {
      "level": 1,
      "title": "Introduction to French Grammar",
      "order": 0
    },
    {
      "level": 2,
      "title": "Basic Sentence Structure",
      "order": 1
    },
    {
      "level": 2,
      "title": "Noun-Adjective Agreement",
      "order": 2
    },
    {
      "level": 3,
      "title": "Gender Rules",
      "order": 3
    },
    {
      "level": 3,
      "title": "Plural Forms",
      "order": 4
    },
    {
      "level": 2,
      "title": "Common Mistakes to Avoid",
      "order": 5
    }
  ],
  "exercises": [...],
  "explanations": [...]
}
```

### Grammar Node

```json
{
  "node_type": "GRAMMAR",
  "video_url": null,
  "video_duration": null,
  "notes": "## À la Découverte des Mots et de Leurs Rôles 🧐\n\nBonjour ! Chaque mot que tu utilises...",
  "content_outline": [
    {
      "level": 2,
      "title": "À la Découverte des Mots et de Leurs Rôles 🧐",
      "order": 0
    },
    {
      "level": 3,
      "title": "Les Mots : Une Grande Famille Organisée 👨‍👩‍👧‍👦",
      "order": 1
    }
  ],
  "exercises": [...]
}
```

## Implementation Notes

1. **Outline Extraction**: Headers are extracted using markdown syntax (# for H1, ## for H2, etc.)
2. **Order**: The `order` field represents the sequence of headers as they appear in the document
3. **Backward Compatibility**: The field is optional, so existing frontend code will continue to work

## Recommended Frontend Implementation

You might want to display the outline as a table of contents or expandable section list to show users what topics are covered in the full content. This can help drive conversions by showing the value of the subscription.

## Affected Endpoints

- `GET /api/v1/app/student/learning-nodes/{node_id}` - For all node types with markdown content

## Questions or Concerns

Please reach out if you need any clarification or have concerns about these changes.