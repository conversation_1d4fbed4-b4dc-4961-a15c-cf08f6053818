-- Add missing fields to subscription_pause table for better pause/resume tracking
ALTER TABLE subscription_pause 
ADD COLUMN IF NOT EXISTS actual_paused_seconds INTEGER,
ADD COLUMN IF NOT EXISTS resumed_at TIMESTAMP WITH TIME ZONE;

-- Add comments to the new columns
COMMENT ON COLUMN subscription_pause.actual_paused_seconds IS 'Actual pause duration if resumed early (vs planned paused_seconds)';
COMMENT ON COLUMN subscription_pause.resumed_at IS 'When the pause was actually resumed';