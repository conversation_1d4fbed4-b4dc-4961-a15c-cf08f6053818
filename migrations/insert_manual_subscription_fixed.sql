-- Manual subscription insertion for local development (FIXED ENUM VERSION)
-- This simulates what the webhook would create for:
-- LuxEdu Subject Credits • price_1RTMeyPUd25oMWa6V0B5PKB8
-- €360.00 EUR / year for Year ID 1, Subject ID 4

-- IMPORTANT: Replace the parent public ID with your actual one!

-- Step 1: Create the ActiveSubscription
INSERT INTO active_subscription (
    public_id,
    parent_account_id,
    stripe_subscription_id,
    status,
    current_period_start,
    current_period_end,
    cancel_at_period_end,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid()::text,
    (SELECT id FROM account WHERE public_id = '********-2eea-4a93-8e95-9a4a5502ed2d'), -- Replace with actual parent public_id
    'sub_1RTlNYPUd25oMWa6JaquROgf', -- Actual Stripe subscription ID
    'ACTIVE', -- Try uppercase first
    NOW(),
    NOW() + INTERVAL '1 year',
    false,
    NOW(),
    NOW()
);

-- Step 2: Get the subscription option for Year 1
WITH subscription_vars AS (
    SELECT 
        (SELECT id FROM active_subscription ORDER BY created_at DESC LIMIT 1) as sub_id,
        (SELECT so.id FROM subscription_option so 
         INNER JOIN year y ON so.year_id = y.id 
         INNER JOIN price_version pv ON so.price_version_id = pv.id
         WHERE y.id = 1 AND pv.is_current = true AND pv.is_active = true
         LIMIT 1) as subscription_option_id
)
-- Step 3: Create the ActiveSubscriptionPlanLink
INSERT INTO active_subscription_plan_link (
    public_id,
    active_subscription_id,
    subscription_option_id,
    chosen_stripe_price_id,
    chosen_plan_type,
    chosen_billing_period,
    stripe_subscription_item_id,
    quantity,
    created_at
)
SELECT 
    gen_random_uuid()::text,
    sv.sub_id,
    sv.subscription_option_id,
    'price_1RTMeyPUd25oMWa6V0B5PKB8', -- The actual Stripe price ID
    'SC', -- Subject Credits
    'yearly', -- Annual billing
    'si_1RTlNYPUd25oMWa6item_001', -- Mock Stripe subscription item ID
    1, -- Quantity = 1 subject
    NOW()
FROM subscription_vars sv;

-- Step 4: Create the PlanSelectedSubject entry for subject ID 4
WITH plan_link_vars AS (
    SELECT id as plan_link_id 
    FROM active_subscription_plan_link 
    ORDER BY created_at DESC 
    LIMIT 1
)
INSERT INTO plan_selected_subject (
    public_id,
    plan_link_id,
    subject_id,
    selected_at
)
SELECT 
    gen_random_uuid()::text,
    plv.plan_link_id,
    4, -- Subject ID 4
    NOW()
FROM plan_link_vars plv;

-- Step 5: Verification queries
SELECT 'Subscription created successfully' as status;

SELECT 
    'Active Subscription' as type,
    public_id,
    stripe_subscription_id,
    status,
    current_period_start,
    current_period_end
FROM active_subscription 
ORDER BY created_at DESC 
LIMIT 1;

SELECT 
    'Plan Link' as type,
    aspl.public_id,
    aspl.chosen_stripe_price_id,
    aspl.chosen_plan_type,
    aspl.chosen_billing_period,
    aspl.quantity,
    y.name as year_name
FROM active_subscription_plan_link aspl
INNER JOIN subscription_option so ON aspl.subscription_option_id = so.id
INNER JOIN year y ON so.year_id = y.id
ORDER BY aspl.created_at DESC 
LIMIT 1;

SELECT 
    'Selected Subject' as type,
    pss.public_id,
    s.name as subject_name,
    pss.selected_at
FROM plan_selected_subject pss
INNER JOIN subject s ON pss.subject_id = s.id
ORDER BY pss.selected_at DESC 
LIMIT 1;