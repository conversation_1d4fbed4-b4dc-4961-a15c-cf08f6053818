-- This migration fixes the exercisetypeenum to use consistent lowercase-with-hyphens values
-- and adds the new TRUE_FALSE type

-- 1. Check current enum values
SELECT enumlabel 
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid FROM pg_type WHERE typname = 'exercisetypeenum'
)
ORDER BY enumsortorder;

-- 2. Create a new temporary enum with the correct consistent values
CREATE TYPE exercisetypeenum_new AS ENUM (
    'mc-simple',
    'mc-multi', 
    'input',
    'cloze',
    'dropdown',
    'highlight',
    'matching-pairs',
    'categorize',
    'error-correction',
    'true-false'
);

-- 3. Add a temporary column with the new enum type
ALTER TABLE exercise ADD COLUMN exercise_type_new exercisetypeenum_new;

-- 4. Copy data from old column to new, mapping the values
-- Based on your query, current values are: MC_SIMPLE, MC_MULTI, INPUT, CLOZE, DROPDOWN, HIGHLIGHT, MATCHING_PAIRS, CATEGORIZE, ERROR_CORRECTION, true-false
UPDATE exercise 
SET exercise_type_new = CASE 
    WHEN exercise_type = 'MC_SIMPLE' THEN 'mc-simple'::exercisetypeenum_new
    WHEN exercise_type = 'MC_MULTI' THEN 'mc-multi'::exercisetypeenum_new
    WHEN exercise_type = 'INPUT' THEN 'input'::exercisetypeenum_new
    WHEN exercise_type = 'CLOZE' THEN 'cloze'::exercisetypeenum_new
    WHEN exercise_type = 'DROPDOWN' THEN 'dropdown'::exercisetypeenum_new
    WHEN exercise_type = 'HIGHLIGHT' THEN 'highlight'::exercisetypeenum_new
    WHEN exercise_type = 'MATCHING_PAIRS' THEN 'matching-pairs'::exercisetypeenum_new
    WHEN exercise_type = 'CATEGORIZE' THEN 'categorize'::exercisetypeenum_new
    WHEN exercise_type = 'ERROR_CORRECTION' THEN 'error-correction'::exercisetypeenum_new
    WHEN exercise_type = 'true-false' THEN 'true-false'::exercisetypeenum_new
    ELSE 'input'::exercisetypeenum_new  -- Default fallback
END;

-- 5. Verify all values were mapped correctly
SELECT exercise_type, exercise_type_new, COUNT(*) 
FROM exercise 
GROUP BY exercise_type, exercise_type_new
ORDER BY exercise_type;

-- 6. Drop the old column
ALTER TABLE exercise DROP COLUMN exercise_type;

-- 7. Rename the new column to the original name
ALTER TABLE exercise RENAME COLUMN exercise_type_new TO exercise_type;

-- 8. Drop the old enum type
DROP TYPE exercisetypeenum;

-- 9. Rename the new enum type to the original name
ALTER TYPE exercisetypeenum_new RENAME TO exercisetypeenum;

-- 10. Verify the result - check enum values
SELECT enumlabel 
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid FROM pg_type WHERE typname = 'exercisetypeenum'
)
ORDER BY enumsortorder;

-- 11. Verify exercise data
SELECT exercise_type, COUNT(*) as count 
FROM exercise 
GROUP BY exercise_type 
ORDER BY exercise_type;