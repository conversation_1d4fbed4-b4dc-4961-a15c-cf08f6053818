-- Migration: Create Audit and Batch Tracking Tables
-- Description: Creates tables for audit logging and batch publishing tracking

-- Audit log for draft exercises
CREATE TABLE IF NOT EXISTS draft_exercise_audit (
    id SERIAL PRIMARY KEY,
    draft_exercise_id INTEGER NOT NULL REFERENCES draft_exercise(id) ON DELETE CASCADE,
    actor_id INTEGER NOT NULL REFERENCES editor_account(id),
    actor_role VARCHAR(20) NOT NULL,
    action VARCHAR(50) NOT NULL, -- 'create', 'assign', 'patch', 'accept', 'reject', 'publish'
    diff_json JSONB DEFAULT '{}',
    reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for draft_exercise_audit
CREATE INDEX IF NOT EXISTS idx_audit_draft ON draft_exercise_audit(draft_exercise_id);
CREATE INDEX IF NOT EXISTS idx_audit_actor ON draft_exercise_audit(actor_id);
CREATE INDEX IF NOT EXISTS idx_audit_action ON draft_exercise_audit(action);
CREATE INDEX IF NOT EXISTS idx_audit_created ON draft_exercise_audit(created_at);

-- Publish batch tracking table
CREATE TABLE IF NOT EXISTS publish_batch (
    id SERIAL PRIMARY KEY,
    created_by_id INTEGER NOT NULL REFERENCES editor_account(id),
    batch_name VARCHAR(255) NOT NULL,
    batch_notes TEXT,
    draft_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for publish_batch
CREATE INDEX IF NOT EXISTS idx_publish_batch_created_by ON publish_batch(created_by_id);
CREATE INDEX IF NOT EXISTS idx_publish_batch_created_at ON publish_batch(created_at);
CREATE INDEX IF NOT EXISTS idx_publish_batch_completed_at ON publish_batch(completed_at);

-- Add comment explaining the table
COMMENT ON TABLE publish_batch IS 'Tracks batch publishing operations by admins';
COMMENT ON COLUMN publish_batch.draft_count IS 'Total number of drafts in the batch';
COMMENT ON COLUMN publish_batch.success_count IS 'Number of successfully published drafts';
COMMENT ON COLUMN publish_batch.failure_count IS 'Number of drafts that failed to publish';
