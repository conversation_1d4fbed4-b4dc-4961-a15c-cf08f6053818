-- Add remaining_seconds field to subscription_pause table
-- This field tracks the remaining subscription time when pause started
-- Used for calculating trial_end when resuming

ALTER TABLE subscription_pause
ADD COLUMN IF NOT EXISTS remaining_seconds INTEGER 
COMMENT 'Remaining subscription time when pause started (for calculating trial_end on resume)';

-- Update any existing paused records with a calculated value
-- This ensures backwards compatibility
-- Note: This is a simple calculation that doesn't account for trial periods
-- For existing pauses, we assume they were from normal billing periods
UPDATE subscription_pause
SET remaining_seconds = GREATEST(0, 
    EXTRACT(EPOCH FROM (original_period_end - start_date))::INTEGER
)
WHERE status IN ('scheduled', 'paused') 
  AND remaining_seconds IS NULL;

-- Add index for performance when querying active pauses
CREATE INDEX IF NOT EXISTS idx_subscription_pause_status_dates 
ON subscription_pause(status, start_date, end_date) 
WHERE status IN ('scheduled', 'paused');