-- Migration: Rename learning_nodes tables to singular form
-- Date: 2025-06-25
-- Description: Rename learning_nodes and learning_node_relationships tables to singular form for consistency

-- Step 1: Rename the main learning_nodes table to learning_node
ALTER TABLE learning_nodes RENAME TO learning_node;

-- Step 2: Rename the relationships table to singular form
ALTER TABLE learning_node_relationships RENAME TO learning_node_relationship;

-- Add comments to document the change
COMMENT ON TABLE learning_node IS 'Main table for learning nodes (renamed from learning_nodes for consistency)';
COMMENT ON TABLE learning_node_relationship IS 'Self-referential relationships between learning nodes (renamed from learning_node_relationships for consistency)';