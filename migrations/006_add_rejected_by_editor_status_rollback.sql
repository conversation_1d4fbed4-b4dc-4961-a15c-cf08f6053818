-- Rollback Migration: Remove REJECTED_BY_EDITOR status
-- Date: 2025-06-23
-- WARNING: This will fail if any drafts have the REJECTED_BY_EDITOR status

-- First, update any drafts with REJECTED_BY_EDITOR status to NEW
-- (This ensures we don't lose any drafts)
UPDATE draft_exercise 
SET status = 'NEW', 
    assigned_editor_id = NULL
WHERE status = 'REJECTED_BY_EDITOR';

-- Unfortunately, PostgreSQL doesn't support removing values from enums directly.
-- You would need to:
-- 1. Create a new enum type without REJECTED_BY_EDITOR
-- 2. Update all columns to use the new type
-- 3. Drop the old type
-- 4. Rename the new type to the old name

-- This is complex and risky, so it's better to handle this manually if needed.
-- For now, we'll just document what would need to be done:

/*
-- Create new enum without REJECTED_BY_EDITOR
CREATE TYPE draft_exercise_status_new AS ENUM (
    'NEW',
    'IN_REVIEW', 
    'ACCEPTED_BY_EDITOR',
    'REJECTED_BY_ADMIN',
    'PUBLISHED'
);

-- Update the column to use the new enum
ALTER TABLE draft_exercise 
ALTER COLUMN status TYPE draft_exercise_status_new 
USING status::text::draft_exercise_status_new;

-- Drop the old enum
DROP TYPE draft_exercise_status;

-- Rename the new enum to the old name
ALTER TYPE draft_exercise_status_new RENAME TO draft_exercise_status;
*/