-- Add missing plan links for test subscription
-- This fixes the issue where active subscription exists but no plan links

-- Step 1: Check current state
SELECT 
    asub.id,
    asub.public_id,
    asub.stripe_subscription_id,
    asub.status,
    COUNT(aspl.id) as plan_link_count
FROM active_subscription asub
LEFT JOIN active_subscription_plan_link aspl ON aspl.active_subscription_id = asub.id
WHERE asub.stripe_subscription_id = 'sub_1RUSxNPUd25oMWa6bGa9KPW1'
GROUP BY asub.id, asub.public_id, asub.stripe_subscription_id, asub.status;

-- Step 2: Check available subscription options
SELECT 
    so.id,
    so.public_id,
    so.sc_monthly_stripe_price_id,
    so.sc_yearly_stripe_price_id,
    so.yf_monthly_stripe_price_id,
    so.yf_yearly_stripe_price_id,
    y.name as year_name,
    pv.name as price_version_name,
    so.is_active
FROM subscription_option so
JOIN year y ON y.id = so.year_id
JOIN price_version pv ON pv.id = so.price_version_id
WHERE so.is_active = TRUE
  AND pv.is_active = TRUE
ORDER BY y.name;

-- Step 3: Create a plan link for the test subscription
-- This assumes you want a Subject Credits (SC) monthly plan
-- Adjust the subscription_option_id based on your actual data from Step 2
INSERT INTO active_subscription_plan_link (
    public_id,
    active_subscription_id,
    subscription_option_id,
    chosen_stripe_price_id,
    chosen_plan_type,
    chosen_billing_period,
    stripe_subscription_item_id,
    quantity,
    created_at
) 
SELECT 
    gen_random_uuid()::TEXT,
    asub.id,
    1, -- CHANGE THIS: Use the actual subscription_option id from Step 2
    'price_1234567890', -- CHANGE THIS: Use actual price ID from your Stripe account
    'SC'::plan_type_enum, -- or 'YF' for Year Full access
    'monthly'::billing_period_enum, -- or 'yearly'
    'si_test_' || substr(md5(random()::text), 1, 20), -- Mock subscription item ID
    3, -- quantity (number of subjects for SC plan, 1 for YF plan)
    NOW()
FROM active_subscription asub
WHERE asub.stripe_subscription_id = 'sub_1RUSxNPUd25oMWa6bGa9KPW1'
  AND NOT EXISTS (
    SELECT 1 FROM active_subscription_plan_link aspl 
    WHERE aspl.active_subscription_id = asub.id
  );

-- Step 4: If using SC plan, add selected subjects
-- First, check available subjects
SELECT 
    s.id,
    s.public_id,
    s.name,
    y.name as year_name
FROM subject s
JOIN year y ON y.id = s.year_id
WHERE s.is_active = TRUE
ORDER BY y.name, s.name;

-- Step 5: Add selected subjects for SC plan (skip if using YF plan)
-- This adds 3 subjects to match the quantity in the plan link
/*
INSERT INTO plan_selected_subject (
    public_id,
    plan_link_id,
    subject_id,
    selected_at
)
SELECT 
    gen_random_uuid()::TEXT,
    aspl.id,
    s.id,
    NOW()
FROM active_subscription_plan_link aspl
CROSS JOIN (
    SELECT id FROM subject 
    WHERE is_active = TRUE 
    LIMIT 3  -- Select first 3 active subjects
) s
WHERE aspl.active_subscription_id = (
    SELECT id FROM active_subscription 
    WHERE stripe_subscription_id = 'sub_1RUSxNPUd25oMWa6bGa9KPW1'
)
AND aspl.chosen_plan_type = 'SC'
AND NOT EXISTS (
    SELECT 1 FROM plan_selected_subject pss 
    WHERE pss.plan_link_id = aspl.id AND pss.subject_id = s.id
);
*/

-- Step 6: Verify the fix
SELECT 
    asub.id,
    asub.public_id,
    asub.stripe_subscription_id,
    asub.status,
    aspl.public_id as plan_link_public_id,
    aspl.chosen_plan_type,
    aspl.chosen_billing_period,
    aspl.quantity,
    COUNT(pss.id) as selected_subjects_count
FROM active_subscription asub
LEFT JOIN active_subscription_plan_link aspl ON aspl.active_subscription_id = asub.id
LEFT JOIN plan_selected_subject pss ON pss.plan_link_id = aspl.id
WHERE asub.stripe_subscription_id = 'sub_1RUSxNPUd25oMWa6bGa9KPW1'
GROUP BY asub.id, asub.public_id, asub.stripe_subscription_id, asub.status,
         aspl.public_id, aspl.chosen_plan_type, aspl.chosen_billing_period, aspl.quantity;

-- Alternative: Simple fix with minimal data
-- If you don't have subscription_options set up, use this instead:
/*
-- First, ensure you have at least one year
INSERT INTO year (public_id, name, is_active, created_at)
SELECT 'year_2024_2025', '2024-2025', TRUE, NOW()
WHERE NOT EXISTS (SELECT 1 FROM year WHERE public_id = 'year_2024_2025');

-- Create a basic price version if needed
INSERT INTO price_version (public_id, name, slug, is_current, is_active, created_at)
SELECT 'pv_2024_standard', 'Standard 2024 Pricing', 'standard-2024', TRUE, TRUE, NOW()
WHERE NOT EXISTS (SELECT 1 FROM price_version WHERE public_id = 'pv_2024_standard');

-- Create a subscription option if needed
INSERT INTO subscription_option (
    public_id,
    year_id,
    price_version_id,
    sc_monthly_stripe_price_id,
    display_price_config_json,
    is_active,
    created_at
)
SELECT 
    'so_test_' || substr(md5(random()::text), 1, 10),
    (SELECT id FROM year WHERE public_id = 'year_2024_2025'),
    (SELECT id FROM price_version WHERE public_id = 'pv_2024_standard'),
    'price_test_sc_monthly',
    '{"sc_monthly": {"amount": 360, "currency": "eur"}}'::jsonb,
    TRUE,
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM subscription_option so
    JOIN year y ON y.id = so.year_id
    JOIN price_version pv ON pv.id = so.price_version_id
    WHERE y.public_id = 'year_2024_2025' 
      AND pv.public_id = 'pv_2024_standard'
);
*/