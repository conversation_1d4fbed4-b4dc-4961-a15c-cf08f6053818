-- Drop the existing table and enum type
DROP TABLE IF EXISTS image_file CASCADE;
DROP TYPE IF EXISTS imagefiletypeenum CASCADE;

-- Create the enum type with the correct values (matching Python enum values)
CREATE TYPE imagefiletypeenum AS ENUM (
    'exercise-option',
    'exercise-prompt',
    'solution-step',
    'general'
);

-- Create the image_file table with the proper enum type
CREATE TABLE image_file (
    id SERIAL PRIMARY KEY,
    public_id VARCHAR NOT NULL UNIQUE,
    storage_path VARCHAR NOT NULL,
    mime_type VARCHAR,
    width INTEGER,
    height INTEGER,
    file_size_bytes INTEGER,
    alt_text TEXT,
    description TEXT,
    image_type imagefiletypeenum,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX ix_image_files_storage_path ON image_file(storage_path);
CREATE INDEX ix_image_files_public_id ON image_file(public_id);
CREATE INDEX ix_image_file_image_type ON image_file(image_type);
CREATE INDEX ix_image_file_created_at ON image_file(created_at);
CREATE INDEX ix_image_file_updated_at ON image_file(updated_at);

-- Now you can run the insert script again with the correct values
-- The insert_test_images.sql file already has the correct string values ('exercise-option', etc.)