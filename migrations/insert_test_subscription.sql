-- Insert test subscription data based on the checkout session
-- This simulates what the webhook handler would do

-- First, let's check if the parent account exists
-- If not, you'll need to create it first
DO $$
DECLARE
    v_parent_account_id INTEGER;
    v_subscription_id INTEGER;
    v_plan_link_id INTEGER;
    v_stripe_subscription_id TEXT := 'sub_1RUSxNPUd25oMWa6bGa9KPW1';
    v_stripe_customer_id TEXT := 'cus_SOYC9UTj6X8Vbw';
    v_parent_public_id TEXT := '********-2eea-4a93-8e95-9a4a5502ed2d';
    v_current_time TIMESTAMP WITH TIME ZONE := NOW();
BEGIN
    -- Get the parent account ID
    SELECT id INTO v_parent_account_id
    FROM account
    WHERE public_id = v_parent_public_id;
    
    IF v_parent_account_id IS NULL THEN
        RAISE EXCEPTION 'Parent account with public_id % not found. Please create the account first.', v_parent_public_id;
    END IF;
    
    -- Update the parent account with Stripe customer ID if not already set
    UPDATE account
    SET stripe_customer_id = v_stripe_customer_id
    WHERE id = v_parent_account_id
      AND stripe_customer_id IS NULL;
    
    -- Check if subscription already exists
    SELECT id INTO v_subscription_id
    FROM active_subscription
    WHERE stripe_subscription_id = v_stripe_subscription_id;
    
    IF v_subscription_id IS NOT NULL THEN
        RAISE NOTICE 'Subscription % already exists with ID %', v_stripe_subscription_id, v_subscription_id;
        RETURN;
    END IF;
    
    -- Insert the active subscription
    INSERT INTO active_subscription (
        public_id,
        parent_account_id,
        stripe_subscription_id,
        status,
        current_period_start,
        current_period_end,
        cancel_at_period_end,
        created_at,
        updated_at
    ) VALUES (
        gen_random_uuid()::TEXT,
        v_parent_account_id,
        v_stripe_subscription_id,
        'active'::subscription_status_type,
        v_current_time,
        v_current_time + INTERVAL '1 month', -- Assuming monthly subscription
        FALSE,
        v_current_time,
        v_current_time
    ) RETURNING id INTO v_subscription_id;
    
    RAISE NOTICE 'Created active_subscription with ID %', v_subscription_id;
    
    -- Note: To properly create the plan links, we need to know:
    -- 1. Which subscription_option was purchased (based on price IDs)
    -- 2. Which subjects were selected (if SC plan)
    -- 
    -- For now, we'll create a basic entry. You'll need to adjust based on your actual data
    
    -- Insert plan link (example - adjust based on actual purchase)
    -- This assumes you have subscription_options set up in your database
    INSERT INTO active_subscription_plan_link (
        public_id,
        active_subscription_id,
        subscription_option_id,
        chosen_stripe_price_id,
        chosen_plan_type,
        chosen_billing_period,
        stripe_subscription_item_id,
        quantity,
        created_at
    ) åå
    SELECT 
        gen_random_uuid()::TEXT,
        v_subscription_id,
        so.id, -- This needs to match your actual subscription_option
        'price_XXX', -- Replace with actual price ID from checkout
        'SC'::plan_type_enum, -- or 'YF' depending on what was purchased
        'monthly'::billing_period_enum, -- or 'yearly'
        'si_' || substr(må≈d5(random()::text), 1, 24), -- Mock subscription item ID
        3, -- quantity (number of subjects for SC plan)
        v_current_time
    FROM subscription_option so
    WHERE so.is_active = TRUE
    LIMIT 1
    RETURNING id INTO v_plan_link_id;
    
    IF v_plan_link_id IS NULL THEN
        RAISE WARNING 'Could not create plan link - no active subscription_options found';
    ELSE
        RAISE NOTICE 'Created plan link with ID %', v_plan_link_id;
    END IF;
    
    -- Insert checkout session data for reference
    INSERT INTO stripe_checkout_session_data (
        public_id,
        stripe_checkout_session_id,
        line_item_details_json,
        parent_account_public_id,
        processed_at,
        created_at
    ) VALUES (
        gen_random_uuid()::TEXT,
        'cs_test_b1lnHGKK6CvesIrkPGpzF9620ppMeoADfjwXunUkLyWGnDZFoH4IanO49V',
        jsonb_build_object(
            'amount_total', 36000,
            'currency', 'eur',
            'customer', v_stripe_customer_id,
            'subscription', v_stripe_subscription_id,
            'metadata', jsonb_build_object(
                'parent_account_public_id', v_parent_public_id,
                'price_version_public_id', 'pv_2024_standard'
            )
        ),
        v_parent_public_id,
        v_current_time,
        v_current_time
    );
    
    RAISE NOTICE 'Successfully created test subscription data';
    
END $$;

-- To test pause/resume functionality, you can now:
-- 1. Call the pause endpoint with the parent account's auth token
-- 2. The subscription should pause successfully
-- 3. Call the resume endpoint to test the trial_end behavior

-- If you need to set specific dates for testing:
/*
UPDATE active_subscription
SET current_period_start = '2025-01-01'::timestamptz,
    current_period_end = '2025-02-01'::timestamptz
WHERE stripe_subscription_id = 'sub_1RUSxNPUd25oMWa6bGa9KPW1';
*/

-- To check the created data:
/*
SELECT 
    asub.id,
    asub.public_id,
    asub.stripe_subscription_id,
    asub.status,
    asub.current_period_start,
    asub.current_period_end,
    a.public_id as parent_public_id,
    a.email
FROM active_subscription asub
JOIN account a ON a.id = asub.parent_account_id
WHERE asub.stripe_subscription_id = 'sub_1RUSxNPUd25oMWa6bGa9KPW1';
*/