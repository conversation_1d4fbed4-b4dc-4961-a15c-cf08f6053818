-- 1. First, let's see what enum values SQLAlchemy created
SELECT enumlabel 
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid 
    FROM pg_type 
    WHERE typname = 'imagefiletypeenum'
)
ORDER BY enumsortorder;

-- Based on the error message, it looks like the enum values are:
-- EXERCISE_OPTION, EXERCISE_PROMPT, SOLUTION_STEP, GENERAL

-- 2. Update the existing data to use the correct enum values
UPDATE image_file 
SET image_type = CASE 
    WHEN image_type = 'exercise-option' THEN 'EXERCISE_OPTION'
    WHEN image_type = 'exercise-prompt' THEN 'EXERCISE_PROMPT'
    WHEN image_type = 'solution-step' THEN 'SOLUTION_STEP'
    WHEN image_type = 'general' THEN 'GENERAL'
    ELSE image_type
END
WHERE image_type IN ('exercise-option', 'exercise-prompt', 'solution-step', 'general');

-- 3. Convert the column back to use the enum type
ALTER TABLE image_file 
ALTER COLUMN image_type TYPE imagefiletypeenum 
USING image_type::imagefiletypeenum;

-- 4. Verify the data
SELECT public_id, image_type FROM image_file;