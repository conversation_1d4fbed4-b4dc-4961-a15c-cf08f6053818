-- 1. First, check what enum values are defined in the database
SELECT enumlabel 
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid 
    FROM pg_type 
    WHERE typname = 'imagefiletypeenum'
);

-- 2. Check the actual column type
SELECT column_name, data_type, udt_name
FROM information_schema.columns
WHERE table_name = 'image_file' 
AND column_name = 'image_type';

-- 3. The safest fix is to convert the column to VARCHAR
-- This allows the Python enum to validate values without database-level enum constraints
ALTER TABLE image_file 
ALTER COLUMN image_type TYPE VARCHAR(50) 
USING image_type::text;

-- 4. After running the ALTER, your inserts should work and SQLAlchemy should be able to read the data
-- The Python ImageFileTypeEnum will still validate the values at the application level