-- Template for manually inserting editor accounts
-- First generate the password hash using: python generate_password_hash.py

-- Example insert for a new editor account
INSERT INTO editor_account (
    public_id,
    email,
    pwd_hash,
    first_name,
    last_name,
    is_active,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid()::text,  -- Generates a UUID for public_id
    '<EMAIL>',     -- Email address
    '$2b$12$...',            -- Password hash from generate_password_hash.py
    'John',                   -- First name
    'Doe',                    -- Last name
    true,                     -- Is active
    CURRENT_TIMESTAMP,        -- Created at
    CURRENT_TIMESTAMP         -- Updated at
);

-- Example with all optional fields
INSERT INTO editor_account (
    public_id,
    email,
    pwd_hash,
    first_name,
    last_name,
    is_active,
    phone,
    notes,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid()::text,
    '<EMAIL>',
    '$2b$12$...',  -- Replace with actual hash
    'Jane',
    '<PERSON>',
    true,
    '+**********',
    'Senior editor with admin privileges',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);