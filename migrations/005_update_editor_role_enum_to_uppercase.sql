-- Migration to update editor_role enum from lowercase to uppercase values
-- This migration needs to be run carefully as it involves changing an enum type

BEGIN;

-- Step 1: Create a new enum type with uppercase values
CREATE TYPE editor_role_new AS ENUM ('EDITOR', 'ADMIN');

-- Step 2: Add a temporary column with the new enum type
ALTER TABLE editor_account ADD COLUMN role_temp editor_role_new;

-- Step 3: Update the temporary column with uppercase values
UPDATE editor_account 
SET role_temp = CASE 
    WHEN role = 'editor' THEN 'EDITOR'::editor_role_new
    WHEN role = 'admin' THEN 'ADMIN'::editor_role_new
END;

-- Step 4: Drop the old column
ALTER TABLE editor_account DROP COLUMN role;

-- Step 5: Rename the temporary column to the original name
ALTER TABLE editor_account RENAME COLUMN role_temp TO role;

-- Step 6: Drop the old enum type
DROP TYPE editor_role;

-- Step 7: Rename the new enum type to the original name
ALTER TYPE editor_role_new RENAME TO editor_role;

-- Step 8: Re-add any constraints if needed (adjust based on your schema)
ALTER TABLE editor_account ALTER COLUMN role SET NOT NULL;

COMMIT;

-- To rollback if needed:
-- BEGIN;
-- CREATE TYPE editor_role_old AS ENUM ('editor', 'admin');
-- ALTER TABLE editor_account ADD COLUMN role_temp editor_role_old;
-- UPDATE editor_account SET role_temp = CASE 
--     WHEN role = 'EDITOR' THEN 'editor'::editor_role_old
--     WHEN role = 'ADMIN' THEN 'admin'::editor_role_old
-- END;
-- ALTER TABLE editor_account DROP COLUMN role;
-- ALTER TABLE editor_account RENAME COLUMN role_temp TO role;
-- DROP TYPE editor_role;
-- ALTER TYPE editor_role_old RENAME TO editor_role;
-- ALTER TABLE editor_account ALTER COLUMN role SET NOT NULL;
-- COMMIT;