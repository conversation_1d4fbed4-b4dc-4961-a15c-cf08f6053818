-- Manual subscription insertion for local development (FINAL VERSION)
-- This simulates what the webhook would create for:
-- LuxEdu Subject Credits • price_1RTMeyPUd25oMWa6V0B5PKB8
-- €360.00 EUR / year for Year ID 1, Subject ID 4

-- Step 1: Create the ActiveSubscription
INSERT INTO active_subscription (
    public_id,
    parent_account_id,
    stripe_subscription_id,
    status,
    current_period_start,
    current_period_end,
    cancel_at_period_end,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid()::text,
    (SELECT id FROM account WHERE public_id = '********-2eea-4a93-8e95-9a4a5502ed2d'),
    'sub_1RTlNYPUd25oMWa6JaquROgf', -- Actual Stripe subscription ID
    'ACTIVE',
    NOW(),
    NOW() + INTERVAL '1 year',
    false,
    NOW(),
    NOW()
);

-- Step 2: Create the ActiveSubscriptionPlanLink with explicit subscription_option_id
INSERT INTO active_subscription_plan_link (
    public_id,
    active_subscription_id,
    subscription_option_id,
    chosen_stripe_price_id,
    chosen_plan_type,
    chosen_billing_period,
    stripe_subscription_item_id,
    quantity,
    created_at
) VALUES (
    gen_random_uuid()::text,
    (SELECT id FROM active_subscription ORDER BY created_at DESC LIMIT 1), -- Get the subscription we just created
    23, -- Explicit subscription option ID you provided
    'price_1RTMeyPUd25oMWa6V0B5PKB8', -- The actual Stripe price ID
    'SC', -- Subject Credits
    'YEARLY', -- Annual billing (uppercase to match enum)
    'si_1RTlNYPUd25oMWa6item_001', -- Mock Stripe subscription item ID
    1, -- Quantity = 1 subject
    NOW()
);

-- Step 3: Create the PlanSelectedSubject entry for subject ID 4
INSERT INTO plan_selected_subject (
    public_id,
    plan_link_id,
    subject_id,
    selected_at
) VALUES (
    gen_random_uuid()::text,
    (SELECT id FROM active_subscription_plan_link ORDER BY created_at DESC LIMIT 1), -- Get the plan link we just created
    4, -- Subject ID 4
    NOW()
);

-- Step 4: Verification queries
SELECT 'Subscription created successfully' as status;

SELECT 
    'Active Subscription' as type,
    public_id,
    stripe_subscription_id,
    status,
    current_period_start,
    current_period_end
FROM active_subscription 
ORDER BY created_at DESC 
LIMIT 1;

SELECT 
    'Plan Link' as type,
    aspl.public_id,
    aspl.chosen_stripe_price_id,
    aspl.chosen_plan_type,
    aspl.chosen_billing_period,
    aspl.quantity,
    COALESCE(y.name, 'Year not found') as year_name
FROM active_subscription_plan_link aspl
LEFT JOIN subscription_option so ON aspl.subscription_option_id = so.id
LEFT JOIN year y ON so.year_id = y.id
ORDER BY aspl.created_at DESC 
LIMIT 1;

SELECT 
    'Selected Subject' as type,
    pss.public_id,
    COALESCE(s.name, 'Subject not found') as subject_name,
    pss.selected_at
FROM plan_selected_subject pss
LEFT JOIN subject s ON pss.subject_id = s.id
ORDER BY pss.selected_at DESC 
LIMIT 1;