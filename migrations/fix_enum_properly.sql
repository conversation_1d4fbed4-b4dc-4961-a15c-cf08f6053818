-- This migration fixes the imagefiletypeenum to use the correct values

-- 1. Create a new temporary enum with the correct values
CREATE TYPE imagefiletypeenum_new AS ENUM (
    'exercise-option',
    'exercise-prompt',
    'solution-step',
    'general'
);

-- 2. Add a temporary column with the new enum type
ALTER TABLE image_file ADD COLUMN image_type_new imagefiletypeenum_new;

-- 3. Copy data from old column to new, mapping the values
UPDATE image_file 
SET image_type_new = CASE 
    WHEN image_type = 'EXERCISE_OPTION' THEN 'exercise-option'::imagefiletypeenum_new
    WHEN image_type = 'EXERCISE_PROMPT' THEN 'exercise-prompt'::imagefiletypeenum_new
    WHEN image_type = 'SOLUTION_STEP' THEN 'solution-step'::imagefiletypeenum_new
    WHEN image_type = 'GENERAL' THEN 'general'::imagefiletypeenum_new
    WHEN image_type = 'exercise-option' THEN 'exercise-option'::imagefiletypeenum_new
    WHEN image_type = 'exercise-prompt' THEN 'exercise-prompt'::imagefiletypeenum_new
    WHEN image_type = 'solution-step' THEN 'solution-step'::imagefiletypeenum_new
    WHEN image_type = 'general' THEN 'general'::imagefiletypeenum_new
    ELSE 'general'::imagefiletypeenum_new
END;

-- 4. Drop the old column
ALTER TABLE image_file DROP COLUMN image_type;

-- 5. Rename the new column to the original name
ALTER TABLE image_file RENAME COLUMN image_type_new TO image_type;

-- 6. Drop the old enum type if it exists
DROP TYPE IF EXISTS imagefiletypeenum;

-- 7. Rename the new enum type to the original name
ALTER TYPE imagefiletypeenum_new RENAME TO imagefiletypeenum;

-- 8. Re-create the index
CREATE INDEX IF NOT EXISTS ix_image_file_image_type ON image_file(image_type);

-- 9. Verify the result
SELECT public_id, image_type FROM image_file;

-- 10. Check the enum values
SELECT enumlabel 
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid 
    FROM pg_type 
    WHERE typname = 'imagefiletypeenum'
)
ORDER BY enumsortorder;