-- Query to find the parent account public ID for manual subscription creation

SELECT 
    public_id,
    email,
    first_name,
    last_name,
    created_at
FROM account 
WHERE account_type = 'parent'
ORDER BY created_at DESC;

-- Also check what years and subjects are available
SELECT 
    'Available Years' as type,
    id as year_id,
    name as year_name,
    public_id as year_public_id
FROM year 
WHERE is_active = true
ORDER BY id;

SELECT 
    'Available Subjects for Year 1' as type,
    s.id as subject_id,
    s.name as subject_name,
    s.public_id as subject_public_id
FROM subject s
INNER JOIN year y ON s.year_id = y.id
WHERE y.id = 1 AND s.is_active = true
ORDER BY s.id;

-- Check subscription options for Year 1
SELECT 
    'Subscription Options for Year 1' as type,
    so.id as option_id,
    so.public_id as option_public_id,
    so.sc_yearly_stripe_price_id,
    pv.name as price_version_name
FROM subscription_option so
INNER JOIN year y ON so.year_id = y.id
INNER JOIN price_version pv ON so.price_version_id = pv.id
WHERE y.id = 1 AND so.is_active = true AND pv.is_active = true;