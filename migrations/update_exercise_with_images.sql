-- Update an existing exercise to use the new image-based content
-- Replace 'your-exercise-public-id' with the actual public_id of the exercise you want to update

UPDATE exercise 
SET data = '{
    "prompt": "Which animal is a cat?",
    "prompt_image_public_id": null,
    "options": [
        {
            "public_id": "img-opt-cat-1",
            "image_public_id": "img-cat-photo-1",
            "text": null
        },
        {
            "public_id": "img-opt-dog-1",
            "image_public_id": "img-dog-photo-1",
            "text": null
        },
        {
            "public_id": "img-opt-bird-1",
            "image_public_id": "img-bird-photo-1",
            "text": null
        },
        {
            "public_id": "img-opt-fish-1",
            "image_public_id": "img-fish-photo-1",
            "text": null
        }
    ]
}'::jsonb
WHERE public_id = 'your-exercise-public-id' 
AND exercise_type = 'mc-simple';

-- If you also want to update the solution with image references:
UPDATE exercise 
SET solution = '{
    "correct_answer": {
        "correct_option_id": "img-opt-cat-1"
    },
    "solution_steps": [
        {
            "text": "The correct answer is the cat.",
            "math": null,
            "image_public_id": "img-cat-photo-1"
        },
        {
            "text": "Cats are feline animals with whiskers and retractable claws.",
            "math": null,
            "image_public_id": null
        }
    ],
    "video_public_id": null
}'::jsonb
WHERE public_id = 'your-exercise-public-id' 
AND exercise_type = 'mc-simple';