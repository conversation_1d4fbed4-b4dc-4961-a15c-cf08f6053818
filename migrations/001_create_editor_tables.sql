-- Migration: Create Editor Account and Scope Tables
-- Description: Creates tables for editor authentication and scope management

-- Create editor role enum
CREATE TYPE editor_role AS ENUM ('editor', 'admin');

-- Editor accounts table
CREATE TABLE IF NOT EXISTS editor_account (
    id SERIAL PRIMARY KEY,
    public_id VARCHAR(255) UNIQUE NOT NULL DEFAULT gen_random_uuid()::text,
    email VARCHAR(255) UNIQUE NOT NULL,
    pwd_hash TEXT NOT NULL,
    role editor_role NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for editor_account
CREATE INDEX IF NOT EXISTS idx_editor_account_email ON editor_account(email);
CREATE INDEX IF NOT EXISTS idx_editor_account_public_id ON editor_account(public_id);
CREATE INDEX IF NOT EXISTS idx_editor_account_role ON editor_account(role);
CREATE INDEX IF NOT EXISTS idx_editor_account_is_active ON editor_account(is_active);

-- Editor scope permissions table
CREATE TABLE IF NOT EXISTS editor_scope (
    id SERIAL PRIMARY KEY,
    editor_id INTEGER NOT NULL REFERENCES editor_account(id) ON DELETE CASCADE,
    subject_id INTEGER REFERENCES subject(id) ON DELETE CASCADE,
    chapter_id INTEGER REFERENCES chapter(id) ON DELETE CASCADE,
    learning_node_id INTEGER REFERENCES learning_node(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT check_scope_hierarchy CHECK (
        (subject_id IS NOT NULL AND chapter_id IS NULL AND learning_node_id IS NULL) OR
        (chapter_id IS NOT NULL AND learning_node_id IS NULL) OR
        (learning_node_id IS NOT NULL)
    )
);

-- Create indexes for editor_scope
CREATE INDEX IF NOT EXISTS idx_editor_scope_editor ON editor_scope(editor_id);
CREATE INDEX IF NOT EXISTS idx_editor_scope_subject ON editor_scope(subject_id) WHERE subject_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_editor_scope_chapter ON editor_scope(chapter_id) WHERE chapter_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_editor_scope_learning_node ON editor_scope(learning_node_id) WHERE learning_node_id IS NOT NULL;

-- Add unique constraint to prevent duplicate scopes
CREATE UNIQUE INDEX IF NOT EXISTS idx_editor_scope_unique ON editor_scope(editor_id, COALESCE(subject_id, -1), COALESCE(chapter_id, -1), COALESCE(learning_node_id, -1));

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_editor_account_updated_at BEFORE UPDATE
    ON editor_account FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();