-- Migration: Add ordering column to learning_nodes table
-- Date: 2024-01-19
-- Description: Adds an ordering field to learning_nodes to maintain consistent display order within chapters

-- Add the ordering column to learning_nodes table
ALTER TABLE learning_node 
ADD COLUMN ordering INTEGER DEFAULT 0;

-- Create an index on chapter_id and ordering for efficient queries
CREATE INDEX idx_learning_node_chapter_ordering 
ON learning_node(chapter_id, ordering);

-- Update existing learning nodes with sequential ordering within each chapter
-- This assigns order based on alphabetical title order as a reasonable default
WITH ordered_nodes AS (
    SELECT 
        id,
        chapter_id,
        ROW_NUMBER() OVER (PARTITION BY chapter_id ORDER BY title) - 1 as new_order
    FROM learning_node
    WHERE chapter_id IS NOT NULL
)
UPDATE learning_node 
SET ordering = ordered_nodes.new_order
FROM ordered_nodes
WHERE learning_node.id = ordered_nodes.id;

-- Add a comment to document the column
COMMENT ON COLUMN learning_node.ordering IS 'Display order of learning node within its chapter (0-based)';