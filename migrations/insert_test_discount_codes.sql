-- Insert test discount codes with new structure
-- This includes the EARLY20 code mentioned in the error

INSERT INTO discount_code (
    public_id,
    public_code,
    stripe_monthly_id,
    stripe_yearly_id,
    applicable_to,
    min_account_age_days,
    max_account_age_days,
    one_time,
    is_active,
    created_at
) VALUES 
-- Early bird discount - different amounts for monthly vs yearly
(
    gen_random_uuid()::text,
    'EARLY20',
    'stripe_coupon_early_monthly_15eur', -- 15€ off monthly
    'stripe_coupon_early_yearly_50eur',  -- 50€ off yearly
    'both',
    NULL,
    7, -- Only for accounts created within 7 days
    false,
    true,
    NOW()
),
-- Welcome discount - percentage based
(
    gen_random_uuid()::text,
    'WELCOME10',
    'stripe_coupon_welcome_10pct', -- 10% off for both
    'stripe_coupon_welcome_10pct', -- Same coupon for both periods
    'both',
    NULL,
    14, -- Only for accounts created within 14 days
    true, -- One-time use
    true,
    NOW()
),
-- Summer promo - yearly only
(
    gen_random_uuid()::text,
    'SUMMER2024',
    NULL, -- Not available for monthly
    'stripe_coupon_summer_25pct', -- 25% off yearly
    'yearly',
    NULL,
    NULL,
    false,
    true,
    NOW()
),
-- Student discount - monthly only
(
    gen_random_uuid()::text,
    'STUDENT15',
    'stripe_coupon_student_15pct', -- 15% off monthly
    NULL, -- Not available for yearly
    'monthly',
    NULL,
    NULL,
    false,
    true,
    NOW()
);