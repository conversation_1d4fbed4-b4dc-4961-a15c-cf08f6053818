-- First, check if the enum type exists and what values it has
-- SELECT enum_range(NULL::imagefiletypeenum);

-- If the enum doesn't exist or has wrong values, we need to create/update it
-- Drop the enum type if it exists with wrong values (be careful, this will fail if column is using it)
-- DROP TYPE IF EXISTS imagefiletypeenum CASCADE;

-- Create the enum type with the correct values
DO $$ 
BEGIN
    -- Check if the type already exists
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'imagefiletypeenum') THEN
        CREATE TYPE imagefiletypeenum AS ENUM (
            'exercise-option',
            'exercise-prompt', 
            'solution-step',
            'general'
        );
    END IF;
END$$;

-- If you need to alter the column to use text instead of enum (safer approach):
-- ALTER TABLE image_file ALTER COLUMN image_type TYPE VARCHAR USING image_type::text;

-- Or if you want to keep using enum but need to add/change values:
-- ALTER TYPE imagefiletypeenum ADD VALUE IF NOT EXISTS 'exercise-option';
-- ALTER TYPE imagefiletypeenum ADD VALUE IF NOT EXISTS 'exercise-prompt';
-- ALTER TYPE imagefiletypeenum ADD VALUE IF NOT EXISTS 'solution-step';
-- ALTER TYPE imagefiletypeenum ADD VALUE IF NOT EXISTS 'general';