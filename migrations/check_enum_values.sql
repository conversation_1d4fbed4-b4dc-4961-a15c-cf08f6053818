-- Check what enum values actually exist in the database

-- Check the subscription status enum values
SELECT 
    t.typname AS enum_name,
    array_agg(e.enumlabel ORDER BY e.enumsortorder) AS enum_values
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid  
WHERE t.typname = 'subscriptionstatustype'
GROUP BY t.typname;

-- Also check plan type and billing period enums
SELECT 
    t.typname AS enum_name,
    array_agg(e.enumlabel ORDER BY e.enumsortorder) AS enum_values
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid  
WHERE t.typname IN ('plantypeenum', 'billingperiodenum')
GROUP BY t.typname;