-- Migration: Create Draft Exercise and Media Tables
-- Description: Creates tables for draft exercises and associated media files

-- Create draft exercise status enum
CREATE TYPE draft_exercise_status AS ENUM ('NEW', 'IN_REVIEW', 'ACCEPTED_BY_EDITOR', 'REJECTED_BY_ADMIN', 'PUBLISHED');

-- Draft exercises table
CREATE TABLE IF NOT EXISTS draft_exercise (
    id SERIAL PRIMARY KEY,
    public_id VARCHAR(255) UNIQUE NOT NULL,
    exercise_type VARCHAR(50) NOT NULL,
    difficulty VARCHAR(20),
    data_json JSONB NOT NULL,
    solution_json JSONB,
    status draft_exercise_status DEFAULT 'NEW',
    assigned_editor_id INTEGER REFERENCES editor_account(id) ON DELETE SET NULL,
    reject_reason TEXT,
    published_exercise_id INTEGER REFERENCES exercise(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    published_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for draft_exercise
CREATE INDEX IF NOT EXISTS idx_draft_exercise_public_id ON draft_exercise(public_id);
CREATE INDEX IF NOT EXISTS idx_draft_exercise_status ON draft_exercise(status);
CREATE INDEX IF NOT EXISTS idx_draft_exercise_editor ON draft_exercise(assigned_editor_id);
CREATE INDEX IF NOT EXISTS idx_draft_exercise_published_id ON draft_exercise(published_exercise_id);
CREATE INDEX IF NOT EXISTS idx_draft_exercise_created_at ON draft_exercise(created_at);
CREATE INDEX IF NOT EXISTS idx_draft_exercise_updated_at ON draft_exercise(updated_at);

-- Draft learning node associations
CREATE TABLE IF NOT EXISTS draft_learning_node_exercise (
    id SERIAL PRIMARY KEY,
    learning_node_id INTEGER NOT NULL REFERENCES learning_node(id) ON DELETE CASCADE,
    draft_exercise_id INTEGER NOT NULL REFERENCES draft_exercise(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(learning_node_id, draft_exercise_id)
);

-- Create indexes for draft_learning_node_exercise
CREATE INDEX IF NOT EXISTS idx_draft_ln_exercise_node ON draft_learning_node_exercise(learning_node_id);
CREATE INDEX IF NOT EXISTS idx_draft_ln_exercise_draft ON draft_learning_node_exercise(draft_exercise_id);

-- Create draft media type enum
CREATE TYPE draft_media_type AS ENUM ('image', 'audio');

-- Draft media files table
CREATE TABLE IF NOT EXISTS draft_media_file (
    id SERIAL PRIMARY KEY,
    public_id VARCHAR(255) UNIQUE NOT NULL,
    media_type draft_media_type NOT NULL,
    storage_path VARCHAR(512) NOT NULL,
    content_type VARCHAR(100),
    original_filename VARCHAR(255),
    metadata JSONB DEFAULT '{}', -- width/height for images, duration for audio
    draft_exercise_id INTEGER NOT NULL REFERENCES draft_exercise(id) ON DELETE CASCADE,
    production_media_id INTEGER, -- References to image_file.id or audio_file.id after publish
    copied_to_production_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for draft_media_file
CREATE INDEX IF NOT EXISTS idx_draft_media_public_id ON draft_media_file(public_id);
CREATE INDEX IF NOT EXISTS idx_draft_media_exercise ON draft_media_file(draft_exercise_id);
CREATE INDEX IF NOT EXISTS idx_draft_media_type ON draft_media_file(media_type);

-- Add trigger to update updated_at timestamp for draft_exercise
CREATE TRIGGER update_draft_exercise_updated_at BEFORE UPDATE
    ON draft_exercise FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();