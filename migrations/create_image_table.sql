-- Create image_file table
CREATE TABLE IF NOT EXISTS image_file (
    id SERIAL PRIMARY KEY,
    public_id VARCHAR NOT NULL UNIQUE,
    storage_path VARCHAR NOT NULL,
    mime_type VARCHAR,
    width INTEGER,
    height INTEGER,
    file_size_bytes INTEGER,
    alt_text TEXT,
    description TEXT,
    image_type VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS ix_image_files_storage_path ON image_file(storage_path);
CREATE INDEX IF NOT EXISTS ix_image_files_public_id ON image_file(public_id);
CREATE INDEX IF NOT EXISTS ix_image_file_image_type ON image_file(image_type);
CREATE INDEX IF NOT EXISTS ix_image_file_created_at ON image_file(created_at);
CREATE INDEX IF NOT EXISTS ix_image_file_updated_at ON image_file(updated_at);