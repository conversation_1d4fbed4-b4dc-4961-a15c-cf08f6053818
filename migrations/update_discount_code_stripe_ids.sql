-- Migration to update discount_code table to support separate monthly/yearly Stripe IDs

-- Step 1: Add new columns
ALTER TABLE discount_code 
ADD COLUMN stripe_monthly_id VARCHAR(255),
ADD COLUMN stripe_yearly_id VARCHAR(255);

-- Step 2: Create indexes for the new columns
CREATE INDEX idx_discount_code_stripe_monthly_id ON discount_code(stripe_monthly_id);
CREATE INDEX idx_discount_code_stripe_yearly_id ON discount_code(stripe_yearly_id);

-- Step 3: Migrate existing stripe_id data (if any) based on applicable_to
-- This assumes if there's existing data, we'll copy it to the appropriate column(s)
UPDATE discount_code 
SET 
    stripe_monthly_id = CASE 
        WHEN applicable_to IN ('monthly', 'both') THEN stripe_id 
        ELSE NULL 
    END,
    stripe_yearly_id = CASE 
        WHEN applicable_to IN ('yearly', 'both') THEN stripe_id 
        ELSE NULL 
    END
WHERE stripe_id IS NOT NULL;

-- Step 4: Drop the old stripe_id column and its index
DROP INDEX IF EXISTS ix_discount_code_stripe_id;
ALTER TABLE discount_code DROP COLUMN stripe_id;

-- Step 5: Add a check constraint to ensure at least one Stripe ID is provided
ALTER TABLE discount_code 
ADD CONSTRAINT check_at_least_one_stripe_id 
CHECK (stripe_monthly_id IS NOT NULL OR stripe_yearly_id IS NOT NULL);

-- Step 6: Add comment to the table
COMMENT ON COLUMN discount_code.stripe_monthly_id IS 'Stripe coupon ID to apply for monthly subscriptions';
COMMENT ON COLUMN discount_code.stripe_yearly_id IS 'Stripe coupon ID to apply for yearly subscriptions';