-- Rollback Migration: Rename learning node tables back to plural form
-- Date: 2025-06-25
-- Description: Rollback script to rename tables back to their original plural names

-- Step 1: Rename learning_node back to learning_nodes
ALTER TABLE learning_node RENAME TO learning_nodes;

-- Step 2: Rename learning_node_relationship back to learning_node_relationships
ALTER TABLE learning_node_relationship RENAME TO learning_node_relationships;

-- Restore original comments
COMMENT ON TABLE learning_nodes IS NULL;
COMMENT ON TABLE learning_node_relationships IS NULL;