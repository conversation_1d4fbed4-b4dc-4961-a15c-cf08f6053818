-- Migration to rename user_subscription_selected_subject table and improve naming

-- Step 1: Rename the table
ALTER TABLE user_subscription_selected_subject 
RENAME TO plan_selected_subject;

-- Step 2: Rename the column
ALTER TABLE plan_selected_subject 
RENAME COLUMN active_subscription_plan_link_id TO plan_link_id;

-- Step 3: Update the constraint name to match the new naming
ALTER TABLE plan_selected_subject 
DROP CONSTRAINT uq_user_selected_subject_link_subject;

ALTER TABLE plan_selected_subject 
ADD CONSTRAINT uq_plan_selected_subject 
UNIQUE (plan_link_id, subject_id);

-- Step 4: Update the index names (if any exist)
-- Note: PostgreSQL automatically renames indexes when tables are renamed,
-- but we should verify the foreign key index exists with the correct name
DROP INDEX IF EXISTS ix_user_subscription_selected_subject_active_subscription_plan_link_id;
CREATE INDEX IF NOT EXISTS ix_plan_selected_subject_plan_link_id 
ON plan_selected_subject(plan_link_id);

-- Step 5: Update subscription_pending_change table column name
ALTER TABLE subscription_pending_change 
RENAME COLUMN active_subscription_plan_link_id TO plan_link_id;

-- Step 6: Update subscription_change_log table column name
ALTER TABLE subscription_change_log 
RENAME COLUMN active_subscription_plan_link_id TO plan_link_id;

-- Step 7: Update index names for subscription changes
DROP INDEX IF EXISTS idx_unique_pending_change_per_link;
CREATE UNIQUE INDEX idx_unique_pending_change_per_link 
ON subscription_pending_change(plan_link_id) 
WHERE status IN ('pending', 'scheduled');

-- Step 8: Add comments for clarity
COMMENT ON TABLE plan_selected_subject IS 'Tracks which subjects are selected for each subscription plan link (used for SC plans)';
COMMENT ON COLUMN plan_selected_subject.plan_link_id IS 'References the specific plan within a subscription';
COMMENT ON COLUMN plan_selected_subject.subject_id IS 'References the selected subject';
COMMENT ON COLUMN plan_selected_subject.selected_at IS 'When this subject was selected for the plan';