-- Check for duplicate active subscriptions
-- The error "Multiple rows were found when one or none was required" indicates duplicate subscriptions

-- Step 1: Check how many subscriptions exist for this parent account
SELECT 
    asub.id,
    asub.public_id,
    asub.stripe_subscription_id,
    asub.status,
    asub.created_at,
    asub.current_period_start,
    asub.current_period_end,
    a.public_id as parent_public_id,
    a.email
FROM active_subscription asub
JOIN account a ON a.id = asub.parent_account_id
WHERE a.public_id = '********-2eea-4a93-8e95-9a4a5502ed2d'
ORDER BY asub.created_at DESC;

-- Step 2: Check if there are multiple ACTIVE subscriptions (this is likely the issue)
SELECT 
    COUNT(*) as active_count,
    a.public_id as parent_public_id,
    a.email
FROM active_subscription asub
JOIN account a ON a.id = asub.parent_account_id
WHERE a.public_id = '********-2eea-4a93-8e95-9a4a5502ed2d'
  AND asub.status = 'active'
GROUP BY a.public_id, a.email;

-- Step 3: Keep only the most recent active subscription, cancel others
-- First, let's see what we would update
WITH ranked_subs AS (
    SELECT 
        asub.id,
        asub.stripe_subscription_id,
        asub.status,
        asub.created_at,
        ROW_NUMBER() OVER (ORDER BY asub.created_at DESC) as rn
    FROM active_subscription asub
    JOIN account a ON a.id = asub.parent_account_id
    WHERE a.public_id = '********-2eea-4a93-8e95-9a4a5502ed2d'
      AND asub.status = 'active'
)
SELECT 
    id,
    stripe_subscription_id,
    status,
    created_at,
    CASE 
        WHEN rn = 1 THEN 'KEEP (most recent)'
        ELSE 'CANCEL (duplicate)'
    END as action
FROM ranked_subs;

-- Step 4: Actually fix the duplicates (uncomment to execute)
/*
-- Cancel all but the most recent active subscription
WITH ranked_subs AS (
    SELECT 
        asub.id,
        ROW_NUMBER() OVER (ORDER BY asub.created_at DESC) as rn
    FROM active_subscription asub
    JOIN account a ON a.id = asub.parent_account_id
    WHERE a.public_id = '********-2eea-4a93-8e95-9a4a5502ed2d'
      AND asub.status = 'active'
)
UPDATE active_subscription
SET 
    status = 'canceled',
    cancel_at_period_end = true,
    updated_at = NOW()
WHERE id IN (
    SELECT id 
    FROM ranked_subs 
    WHERE rn > 1
);
*/

-- Step 5: Alternative - Delete duplicates entirely (more aggressive)
/*
-- Delete all but the most recent subscription
WITH ranked_subs AS (
    SELECT 
        asub.id,
        ROW_NUMBER() OVER (ORDER BY asub.created_at DESC) as rn
    FROM active_subscription asub
    JOIN account a ON a.id = asub.parent_account_id
    WHERE a.public_id = '********-2eea-4a93-8e95-9a4a5502ed2d'
)
DELETE FROM active_subscription
WHERE id IN (
    SELECT id 
    FROM ranked_subs 
    WHERE rn > 1
);
*/

-- Step 6: If you want to keep only the specific test subscription
/*
-- Cancel all subscriptions except the test one
UPDATE active_subscription
SET 
    status = 'canceled',
    cancel_at_period_end = true,
    updated_at = NOW()
FROM account a
WHERE active_subscription.parent_account_id = a.id
  AND a.public_id = '********-2eea-4a93-8e95-9a4a5502ed2d'
  AND active_subscription.stripe_subscription_id != 'sub_1RUSxNPUd25oMWa6bGa9KPW1';
*/

-- Step 7: Verify the fix
SELECT 
    asub.id,
    asub.public_id,
    asub.stripe_subscription_id,
    asub.status,
    asub.created_at,
    asub.current_period_start,
    asub.current_period_end
FROM active_subscription asub
JOIN account a ON a.id = asub.parent_account_id
WHERE a.public_id = '********-2eea-4a93-8e95-9a4a5502ed2d'
ORDER BY asub.created_at DESC;