-- Insert test images for the animal exercise
INSERT INTO image_file (
    public_id, 
    storage_path, 
    mime_type, 
    width, 
    height, 
    file_size_bytes, 
    alt_text, 
    description, 
    image_type
) VALUES 
    -- Cat image
    (
        'img-cat-photo-1',
        'exercise_images/animals/img-cat-photo-1.jpg',
        'image/jpeg',
        400,
        300,
        45000,
        'A cat sitting and looking at camera',
        'Cat image for multiple choice exercise - Original from Unsplash photo-1514888286974-6c03e2ca1dba',
        'exercise-option'
    ),
    -- Dog image
    (
        'img-dog-photo-1',
        'exercise_images/animals/img-dog-photo-1.jpg',
        'image/jpeg',
        400,
        300,
        52000,
        'A golden retriever sitting outdoors',
        'Dog image for multiple choice exercise - Original from Unsplash photo-1552053831-71594a27632d',
        'exercise-option'
    ),
    -- Bird image
    (
        'img-bird-photo-1',
        'exercise_images/animals/img-bird-photo-1.jpg',
        'image/jpeg',
        400,
        300,
        38000,
        'A colorful bird perched on a branch',
        'Bird image for multiple choice exercise - Original from Unsplash photo-1444464666168-49d633b86797',
        'exercise-option'
    ),
    -- Fish image
    (
        'img-fish-photo-1',
        'exercise_images/animals/img-fish-photo-1.jpg',
        'image/jpeg',
        400,
        300,
        41000,
        'A tropical fish swimming',
        'Fish image for multiple choice exercise - Original from Unsplash photo-1544551763-46a013bb70d5',
        'exercise-option'
    );

-- You can verify the insertions with:
-- SELECT * FROM image_file;