-- Simple script to insert test subscription for pause/resume testing
-- Based on the Stripe checkout session data provided

-- Step 1: Check if parent account exists
SELECT id, public_id, email, stripe_customer_id 
FROM account 
WHERE public_id = '********-2eea-4a93-8e95-9a4a5502ed2d';

-- If the account doesn't exist, create it first:
/*
INSERT INTO account (
    public_id,
    email,
    stripe_customer_id,
    type,
    created_at,
    updated_at
) VALUES (
    '********-2eea-4a93-8e95-9a4a5502ed2d',
    '<EMAIL>',
    'cus_SOYC9UTj6X8Vbw',
    'parent',
    NOW(),
    NOW()
);
*/

-- Step 2: Insert the active subscription
INSERT INTO active_subscription (
    public_id,
    parent_account_id,
    stripe_subscription_id,
    status,
    current_period_start,
    current_period_end,
    cancel_at_period_end,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid()::TEXT,
    (SELECT id FROM account WHERE public_id = '********-2eea-4a93-8e95-9a4a5502ed2d'),
    'sub_1RUSxNPUd25oMWa6bGa9KPW1',
    'active',
    '2025-01-31 14:06:15+00'::timestamptz, -- Based on checkout session created time
    '2025-02-28 14:06:15+00'::timestamptz, -- One month later for monthly subscription
    FALSE,
    NOW(),
    NOW()
)
ON CONFLICT (stripe_subscription_id) 
DO UPDATE SET 
    status = 'active',
    updated_at = NOW();

-- Step 3: Insert checkout session data (optional, for record keeping)
INSERT INTO stripe_checkout_session_data (
    public_id,
    stripe_checkout_session_id,
    line_item_details_json,
    parent_account_public_id,
    processed_at,
    created_at
) VALUES (
    gen_random_uuid()::TEXT,
    'cs_test_b1lnHGKK6CvesIrkPGpzF9620ppMeoADfjwXunUkLyWGnDZFoH4IanO49V',
    '{
        "amount_total": 36000,
        "currency": "eur",
        "customer": "cus_SOYC9UTj6X8Vbw",
        "subscription": "sub_1RUSxNPUd25oMWa6bGa9KPW1",
        "invoice": "in_1RUSxNPUd25oMWa6BUzBuUPr",
        "metadata": {
            "parent_account_public_id": "********-2eea-4a93-8e95-9a4a5502ed2d",
            "price_version_public_id": "pv_2024_standard"
        }
    }'::jsonb,
    '********-2eea-4a93-8e95-9a4a5502ed2d',
    NOW(),
    NOW()
)
ON CONFLICT (stripe_checkout_session_id) DO NOTHING;

-- Step 4: Verify the subscription was created
SELECT 
    asub.id,
    asub.public_id,
    asub.stripe_subscription_id,
    asub.status,
    asub.current_period_start,
    asub.current_period_end,
    a.public_id as parent_public_id,
    a.email,
    a.stripe_customer_id
FROM active_subscription asub
JOIN account a ON a.id = asub.parent_account_id
WHERE asub.stripe_subscription_id = 'sub_1RUSxNPUd25oMWa6bGa9KPW1';

-- Optional: Create plan link if you need it for testing
-- This is a minimal version just to satisfy foreign key constraints
/*
INSERT INTO active_subscription_plan_link (
    public_id,
    active_subscription_id,
    subscription_option_id,
    chosen_stripe_price_id,
    chosen_plan_type,
    chosen_billing_period,
    stripe_subscription_item_id,
    quantity,
    created_at
) VALUES (
    gen_random_uuid()::TEXT,
    (SELECT id FROM active_subscription WHERE stripe_subscription_id = 'sub_1RUSxNPUd25oMWa6bGa9KPW1'),
    1, -- Assumes you have at least one subscription_option with id=1
    'price_test_123', -- Mock price ID
    'SC',
    'monthly',
    'si_test_' || substr(md5(random()::text), 1, 20),
    3,
    NOW()
);
*/

-- To test pause functionality:
-- 1. Get auth token for parent account with public_id = '********-2eea-4a93-8e95-9a4a5502ed2d'
-- 2. POST to /api/v1/app/parent/subscription/pause
-- 3. Check subscription_pause table for created record
-- 4. POST to /api/v1/app/parent/subscription/resume
-- 5. Verify trial_end is set correctly in Stripe