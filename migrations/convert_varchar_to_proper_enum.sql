-- Since the column is currently VARCHAR, let's convert it to use the proper enum

-- 1. Drop the old enum type if it exists (from previous SQLAlchemy creation)
DROP TYPE IF EXISTS imagefiletypeenum CASCADE;

-- 2. Create the enum with the correct values (matching Python enum values)
CREATE TYPE imagefiletypeenum AS ENUM (
    'exercise-option',
    'exercise-prompt',
    'solution-step',
    'general'
);

-- 3. Convert the VARCHAR column to use the enum type
-- The data should already have the correct values since you inserted them as strings
ALTER TABLE image_file 
ALTER COLUMN image_type TYPE imagefiletypeenum 
USING image_type::imagefiletypeenum;

-- 4. Verify the data
SELECT public_id, image_type FROM image_file;

-- 5. Check that the enum has the correct values
SELECT enumlabel 
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid 
    FROM pg_type 
    WHERE typname = 'imagefiletypeenum'
)
ORDER BY enumsortorder;