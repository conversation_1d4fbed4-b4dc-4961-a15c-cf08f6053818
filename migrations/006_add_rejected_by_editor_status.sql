-- Migration: Add REJECTED_BY_EDITOR status to draft_exercise_status enum
-- Date: 2025-06-23
-- Description: Adds a new status for drafts rejected by editors due to quality issues

-- Add the new enum value
-- Note: In PostgreSQL, you cannot add enum values within a transaction,
-- so this must be run separately
ALTER TYPE draft_exercise_status ADD VALUE 'REJECTED_BY_EDITOR' AFTER 'ACCEPTED_BY_EDITOR';

-- Optional: Add comment to document the enum
COMMENT ON TYPE draft_exercise_status IS 'Status of draft exercises: NEW (available for claiming), IN_REVIEW (being worked on), ACCEPTED_BY_EDITOR (ready for admin review), REJECTED_BY_EDITOR (quality issues, flagged for admin), REJECTED_BY_ADMIN (returned to editor), PUBLISHED (live)';