import mailerlite as MailerLite
from pydantic import BaseModel, EmailStr
from typing import Optional, Dict, Any
from abc import ABC, abstractmethod
from loguru import logger
from datetime import datetime
from core.config import settings

class ListContext(BaseModel):
    email: EmailStr
    language: str = "de"
    group_type: Optional[str] = None
    ip_address: Optional[str] = None
    privacy_policy_version: Optional[str] = None
    optin_text: Optional[str] = None
    fields: Optional[Dict[str, Any]] = None

class ListService(ABC):
    @abstractmethod
    def add_to_list(self, ctx: ListContext) -> bool:
        """Adds a user to a specified mailing list."""
        pass
    
    @abstractmethod
    def remove_from_list(self, ctx: ListContext) -> bool:
        """Removes a user from a specified mailing list."""
        pass
    
    @abstractmethod
    def add_to_all_lists(self, ctx: ListContext) -> bool:
        """Adds a user to all standard lists based on context."""
        pass

class MailerLiteService(ListService):
    def __init__(self):
        self.api_key = settings.MAILERLITE_API_KEY
        if not self.api_key:
            logger.error("MailerLite API key not configured in settings.")
            self.client = None
        else:
            try:
                self.client = MailerLite.Client({
                    'api_key': self.api_key
                })
                logger.info("MailerLite client initialized successfully.")
            except Exception as e:
                logger.error(f"Failed to initialize MailerLite client: {e}")
                self.client = None
        
        # Define group mappings from settings
        self.list_mapping = {
            'all_users': settings.MAILERLITE_ALL_USERS_GROUP_ID,
            'children': settings.MAILERLITE_CHILDREN_GROUP_ID,
            'subscribers': settings.MAILERLITE_SUBSCRIBERS_GROUP_ID,
            'welcome': {
                'de': settings.MAILERLITE_WELCOME_DE_GROUP_ID,
                'en': settings.MAILERLITE_WELCOME_EN_GROUP_ID,
                'fr': settings.MAILERLITE_WELCOME_FR_GROUP_ID,
                'lu': settings.MAILERLITE_WELCOME_LU_GROUP_ID,
                'child': settings.MAILERLITE_WELCOME_CHILD_GROUP_ID,
            },
            'language_specific': {
                'de': settings.MAILERLITE_DE_GROUP_ID,
                'en': settings.MAILERLITE_EN_GROUP_ID,
                'lu': settings.MAILERLITE_LU_GROUP_ID,
                'fr': settings.MAILERLITE_FR_GROUP_ID,
            },
            'marketing': {
                'de': settings.MAILERLITE_MARKETING_DE_GROUP_ID,
                'en': settings.MAILERLITE_MARKETING_EN_GROUP_ID,
                'lu': settings.MAILERLITE_MARKETING_LU_GROUP_ID,
                'fr': settings.MAILERLITE_MARKETING_FR_GROUP_ID,
            },
            'post_purchase': {
                'de': settings.MAILERLITE_POST_PURCHASE_DE_GROUP_ID,
                'en': settings.MAILERLITE_POST_PURCHASE_EN_GROUP_ID,
                'lu': settings.MAILERLITE_POST_PURCHASE_LU_GROUP_ID,
                'fr': settings.MAILERLITE_POST_PURCHASE_FR_GROUP_ID,
            },
        }

    def _get_group_id(self, group_type: str, language: str = 'de') -> str:
        """Helper method to get the correct group ID based on type and language."""
        if group_type in ['all_users', 'children', 'subscribers']:
            return self.list_mapping[group_type]
        else:
            return self.list_mapping[group_type][language]

    def add_to_list(self, ctx: ListContext) -> bool:
        """Add a user to a specific mailing list."""
        if not self.client:
            logger.error("MailerLite client is not initialized. Cannot add to list.")
            return False
        
        if not ctx.group_type:
            logger.error("Group type must be specified when adding to a list.")
            return False
        
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        try:
            group_id = self._get_group_id(ctx.group_type, ctx.language)
            
            params = {
                'email': ctx.email,
                'subscribed_at': timestamp,
                'groups': [group_id]
            }
            
            # Add optional fields if provided
            if ctx.ip_address:
                params['ip_address'] = ctx.ip_address
                params['optin_ip'] = ctx.ip_address
                params['opted_in_at'] = timestamp
            
            if ctx.fields or (ctx.privacy_policy_version and ctx.optin_text):
                fields = ctx.fields or {}
                if ctx.privacy_policy_version:
                    fields['privacy_policy_version'] = ctx.privacy_policy_version
                if ctx.optin_text:
                    fields['optin_text'] = ctx.optin_text
                params['fields'] = fields
            
            logger.info(f"Successfully added {ctx.email} to {ctx.group_type} list")
            return True
            
        except Exception as e:
            logger.error(f"Error adding {ctx.email} to {ctx.group_type} list: {str(e)}")
            return False

    def remove_from_list(self, ctx: ListContext) -> bool:
        """Remove a user from a specific mailing list."""
        if not self.client:
            logger.error("MailerLite client is not initialized. Cannot remove from list.")
            return False
        
        if not ctx.group_type:
            logger.error("Group type must be specified when removing from a list.")
            return False
            
        try:
            # Fetch subscriber details
            response = self.client.subscribers.get(ctx.email)
            subscriber_data = response['data']
            subscriber_id = int(subscriber_data['id'])
            
            # Get the group ID
            group_id = int(self._get_group_id(ctx.group_type, ctx.language))
            
            # Unassign the subscriber from the group
            response = self.client.subscribers.unassign_subscriber_from_group(
                subscriber_id, group_id
            )
            
            logger.info(f"Successfully removed {ctx.email} from {ctx.group_type} list")
            return True
            
        except Exception as e:
            logger.error(f"Error removing {ctx.email} from {ctx.group_type} list: {str(e)}")
            return False
    
    def add_to_all_lists(self, ctx: ListContext) -> bool:
        """Add user to standard lists based on user type."""
        results = []
        
        # Add to all_users list
        all_users_ctx = ListContext(
            email=ctx.email,
            group_type='all_users'
        )
        results.append(self.add_to_list(all_users_ctx))
        
        # Add to welcome list with language
        welcome_ctx = ListContext(
            email=ctx.email,
            language=ctx.language,
            group_type='welcome'
        )
        results.append(self.add_to_list(welcome_ctx))
        
        # Add to language specific list
        lang_ctx = ListContext(
            email=ctx.email,
            language=ctx.language,
            group_type='language_specific'
        )
        results.append(self.add_to_list(lang_ctx))
        
        # All operations successful if all results are True
        return all(results)
    
    def add_child_to_lists(self, ctx: ListContext) -> bool:
        """Add a child to the appropriate lists."""
        results = []
        
        # Add to all_users list
        all_users_ctx = ListContext(
            email=ctx.email,
            group_type='all_users'
        )
        results.append(self.add_to_list(all_users_ctx))
        
        # Add to children list
        children_ctx = ListContext(
            email=ctx.email,
            group_type='children'
        )
        results.append(self.add_to_list(children_ctx))
        
        # Add to welcome list with 'child' language
        welcome_ctx = ListContext(
            email=ctx.email,
            language='child',
            group_type='welcome'
        )
        results.append(self.add_to_list(welcome_ctx))
        
        # All operations successful if all results are True
        return all(results)
    
    def handle_post_purchase(self, ctx: ListContext) -> bool:
        """Handle post-purchase email list operations."""
        results = []
        
        # Add to subscribers list
        sub_ctx = ListContext(
            email=ctx.email,
            group_type='subscribers'
        )
        results.append(self.add_to_list(sub_ctx))
        
        # Add to post_purchase list
        post_ctx = ListContext(
            email=ctx.email,
            language=ctx.language,
            group_type='post_purchase'
        )
        results.append(self.add_to_list(post_ctx))
        
        # Remove from welcome list
        welcome_ctx = ListContext(
            email=ctx.email,
            language=ctx.language,
            group_type='welcome'
        )
        results.append(self.remove_from_list(welcome_ctx))
        
        # All operations successful if all results are True
        return all(results)
    
    def add_to_marketing_list(self, ctx: ListContext) -> bool:
        """Add user to marketing list with consent information."""
        if not ctx.ip_address or not ctx.privacy_policy_version or not ctx.optin_text:
            logger.error("Missing required fields for marketing consent")
            return False
            
        marketing_ctx = ListContext(
            email=ctx.email,
            language=ctx.language,
            group_type='marketing',
            ip_address=ctx.ip_address,
            privacy_policy_version=ctx.privacy_policy_version,
            optin_text=ctx.optin_text
        )
        
        return self.add_to_list(marketing_ctx)

class NullListService(ListService):
    """A null implementation for testing or if MailerLite is not configured."""
    
    def __init__(self):
        logger.info("NullListService initialized. List operations will be logged but not performed.")
    
    def add_to_list(self, ctx: ListContext) -> bool:
        logger.info(f"NullListService: Would add {ctx.email} to {ctx.group_type} list")
        return True
        
    def remove_from_list(self, ctx: ListContext) -> bool:
        logger.info(f"NullListService: Would remove {ctx.email} from {ctx.group_type} list")
        return True
        
    def add_to_all_lists(self, ctx: ListContext) -> bool:
        logger.info(f"NullListService: Would add {ctx.email} to all standard lists")
        return True 