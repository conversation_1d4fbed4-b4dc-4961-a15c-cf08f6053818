# Email List Service

This service provides a clean, standardized interface for interacting with MailerLite for email list management operations.

## Overview

The service follows the same pattern as the email service, using a dependency injection pattern to provide either a real MailerLiteService or a NullListService for testing/development.

## Usage Examples

### Basic Usage

```python
from fastapi import Depends
from dependencies.list_dependency import get_list_service
from services.email_list.service import ListContext

@router.post('/some-endpoint')
async def some_function(email_param: str, list_service = Depends(get_list_service)):
    # Create a context with the required information
    list_ctx = ListContext(
        email=email_param,
        language="en"  # or other language code
    )
    
    # Add to standard lists (all_users, welcome, language_specific)
    await list_service.add_to_all_lists(list_ctx)
```

### Adding to Marketing Lists (with <PERSON>sent)

```python
# For marketing lists which require consent data:
marketing_ctx = ListContext(
    email="<EMAIL>",
    language="de",
    ip_address="************",
    privacy_policy_version="1.0",
    optin_text="I agree to receive marketing emails"
)
await list_service.add_to_marketing_list(marketing_ctx)
```

### For Child Accounts

```python
# For child accounts:
child_ctx = ListContext(
    email="<EMAIL>"
)
await list_service.add_child_to_lists(child_ctx)
```

### Post-Purchase Operations

```python
# After a purchase:
purchase_ctx = ListContext(
    email="<EMAIL>",
    language="en"
)
await list_service.handle_post_purchase(purchase_ctx)
```

### Removing from Lists

```python
# To remove from a specific list:
remove_ctx = ListContext(
    email="<EMAIL>",
    language="en",
    group_type="welcome"  # The list to remove from
)
await list_service.remove_from_list(remove_ctx)
```

## Migration Guide

This service is a direct replacement for the following utility functions:

- `add_to_mailerlite(email, language)` → `list_service.add_to_all_lists(ctx)`
- `add_child_to_mailerlite(email)` → `list_service.add_child_to_lists(ctx)`
- `handle_post_purchase_email(email, language)` → `list_service.handle_post_purchase(ctx)`
- `add_to_marketing_list(email, language, ip_address, privacy_policy_version, optin_text)` → `list_service.add_to_marketing_list(ctx)`
- `remove_from_mailing_list(email, language, group_type)` → `list_service.remove_from_list(ctx)`

## Implementation Notes

- All methods are asynchronous and should be awaited
- The service uses a singleton pattern through the dependency injection system
- A NullListService is available for testing environments
- The service gracefully handles errors and logs appropriate messages 