from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from db.models import DraftExercise, DraftExerciseStatus, DraftMediaFile
from services.media.draft_media_service import DraftMediaService
from loguru import logger
from typing import Dict, Any


class DraftCleanupTasks:
    """Scheduled tasks for cleaning up old draft data"""
    
    @staticmethod
    async def cleanup_old_draft_media(
        db: Session,
        retention_days: int = 30
    ) -> Dict[str, int]:
        """
        Clean up draft media files older than retention period.
        
        Only cleans up media that hasn't been published to production.
        
        Args:
            db: Database session
            retention_days: Days to retain draft media (default: 30)
            
        Returns:
            Dict with cleanup statistics
        """
        logger.info(f"Starting draft media cleanup (retention: {retention_days} days)")
        
        media_service = DraftMediaService()
        
        try:
            deleted_count = media_service.cleanup_old_draft_media(
                db=db,
                days=retention_days
            )
            
            logger.info(f"Cleaned up {deleted_count} old draft media files")
            
            return {
                "deleted_media_count": deleted_count,
                "retention_days": retention_days,
                "completed_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error during draft media cleanup: {str(e)}")
            raise
    
    @staticmethod
    async def cleanup_stale_drafts(
        db: Session,
        stale_days: int = 90
    ) -> Dict[str, Any]:
        """
        Clean up stale drafts that haven't been updated in a long time.
        
        Deletes drafts that are:
        - In NEW status and older than stale_days
        - In REJECTED_BY_ADMIN status and older than stale_days
        
        Args:
            db: Database session
            stale_days: Days before considering draft stale (default: 90)
            
        Returns:
            Dict with cleanup statistics
        """
        logger.info(f"Starting stale draft cleanup (threshold: {stale_days} days)")
        
        cutoff_date = datetime.utcnow() - timedelta(days=stale_days)
        
        # Find stale drafts
        stale_drafts = db.query(DraftExercise).filter(
            DraftExercise.updated_at < cutoff_date,
            DraftExercise.status.in_([
                DraftExerciseStatus.NEW,
                DraftExerciseStatus.REJECTED_BY_ADMIN
            ])
        ).all()
        
        deleted_count = 0
        deleted_by_status = {
            DraftExerciseStatus.NEW.value: 0,
            DraftExerciseStatus.REJECTED_BY_ADMIN.value: 0
        }
        
        for draft in stale_drafts:
            try:
                status = draft.status.value
                
                # Delete associated media first
                media_count = db.query(DraftMediaFile).filter_by(
                    draft_exercise_id=draft.id
                ).count()
                
                # Delete the draft (cascade will handle associations)
                db.delete(draft)
                
                deleted_count += 1
                deleted_by_status[status] += 1
                
                logger.info(f"Deleted stale draft {draft.id} (status: {status}, media: {media_count})")
                
            except Exception as e:
                logger.error(f"Failed to delete stale draft {draft.id}: {str(e)}")
        
        db.commit()
        
        logger.info(f"Cleaned up {deleted_count} stale drafts")
        
        return {
            "deleted_draft_count": deleted_count,
            "deleted_by_status": deleted_by_status,
            "stale_threshold_days": stale_days,
            "completed_at": datetime.utcnow().isoformat()
        }
    
    @staticmethod
    async def release_abandoned_assignments(
        db: Session,
        abandoned_days: int = 7
    ) -> Dict[str, Any]:
        """
        Release draft assignments that have been abandoned by editors.
        
        Releases drafts that are:
        - In IN_REVIEW status
        - Haven't been updated in abandoned_days
        
        Args:
            db: Database session
            abandoned_days: Days before considering assignment abandoned (default: 7)
            
        Returns:
            Dict with release statistics
        """
        logger.info(f"Starting abandoned assignment cleanup (threshold: {abandoned_days} days)")
        
        cutoff_date = datetime.utcnow() - timedelta(days=abandoned_days)
        
        # Find abandoned assignments
        abandoned_drafts = db.query(DraftExercise).filter(
            DraftExercise.status == DraftExerciseStatus.IN_REVIEW,
            DraftExercise.updated_at < cutoff_date,
            DraftExercise.assigned_editor_id.isnot(None)
        ).all()
        
        released_count = 0
        
        for draft in abandoned_drafts:
            try:
                # Release the assignment
                draft.status = DraftExerciseStatus.NEW
                draft.assigned_editor_id = None
                
                released_count += 1
                
                logger.info(f"Released abandoned draft {draft.id} (last updated: {draft.updated_at})")
                
            except Exception as e:
                logger.error(f"Failed to release abandoned draft {draft.id}: {str(e)}")
        
        db.commit()
        
        logger.info(f"Released {released_count} abandoned assignments")
        
        return {
            "released_count": released_count,
            "abandoned_threshold_days": abandoned_days,
            "completed_at": datetime.utcnow().isoformat()
        }
    
    @staticmethod
    async def generate_cleanup_report(
        db: Session
    ) -> Dict[str, Any]:
        """
        Generate a report of items that would be cleaned up.
        
        Useful for dry-run or reporting purposes.
        
        Args:
            db: Database session
            
        Returns:
            Dict with cleanup statistics
        """
        # 30-day old media
        media_30_days = datetime.utcnow() - timedelta(days=30)
        old_media_count = db.query(DraftMediaFile).filter(
            DraftMediaFile.created_at < media_30_days,
            DraftMediaFile.production_media_id.is_(None)
        ).count()
        
        # 90-day stale drafts
        draft_90_days = datetime.utcnow() - timedelta(days=90)
        stale_new_count = db.query(DraftExercise).filter(
            DraftExercise.updated_at < draft_90_days,
            DraftExercise.status == DraftExerciseStatus.NEW
        ).count()
        
        stale_rejected_count = db.query(DraftExercise).filter(
            DraftExercise.updated_at < draft_90_days,
            DraftExercise.status == DraftExerciseStatus.REJECTED_BY_ADMIN
        ).count()
        
        # 7-day abandoned assignments
        abandoned_7_days = datetime.utcnow() - timedelta(days=7)
        abandoned_count = db.query(DraftExercise).filter(
            DraftExercise.status == DraftExerciseStatus.IN_REVIEW,
            DraftExercise.updated_at < abandoned_7_days,
            DraftExercise.assigned_editor_id.isnot(None)
        ).count()
        
        # Draft statistics
        total_drafts = db.query(DraftExercise).count()
        drafts_by_status = {}
        for status in DraftExerciseStatus:
            count = db.query(DraftExercise).filter(
                DraftExercise.status == status
            ).count()
            drafts_by_status[status.value] = count
        
        return {
            "cleanup_candidates": {
                "old_media_files": old_media_count,
                "stale_new_drafts": stale_new_count,
                "stale_rejected_drafts": stale_rejected_count,
                "abandoned_assignments": abandoned_count
            },
            "current_statistics": {
                "total_drafts": total_drafts,
                "drafts_by_status": drafts_by_status,
                "total_media_files": db.query(DraftMediaFile).count()
            },
            "generated_at": datetime.utcnow().isoformat()
        }
    
    @staticmethod
    async def run_all_cleanup_tasks(
        db: Session,
        media_retention_days: int = 30,
        stale_draft_days: int = 90,
        abandoned_assignment_days: int = 7
    ) -> Dict[str, Any]:
        """
        Run all cleanup tasks in sequence.
        
        Args:
            db: Database session
            media_retention_days: Days to retain draft media
            stale_draft_days: Days before considering draft stale
            abandoned_assignment_days: Days before considering assignment abandoned
            
        Returns:
            Combined results from all cleanup tasks
        """
        logger.info("Starting comprehensive draft cleanup")
        
        results = {
            "started_at": datetime.utcnow().isoformat(),
            "tasks": {}
        }
        
        # Run each cleanup task
        try:
            # 1. Release abandoned assignments first
            results["tasks"]["abandoned_assignments"] = await DraftCleanupTasks.release_abandoned_assignments(
                db, abandoned_assignment_days
            )
        except Exception as e:
            logger.error(f"Abandoned assignment cleanup failed: {str(e)}")
            results["tasks"]["abandoned_assignments"] = {"error": str(e)}
        
        try:
            # 2. Clean up stale drafts
            results["tasks"]["stale_drafts"] = await DraftCleanupTasks.cleanup_stale_drafts(
                db, stale_draft_days
            )
        except Exception as e:
            logger.error(f"Stale draft cleanup failed: {str(e)}")
            results["tasks"]["stale_drafts"] = {"error": str(e)}
        
        try:
            # 3. Clean up old media
            results["tasks"]["old_media"] = await DraftCleanupTasks.cleanup_old_draft_media(
                db, media_retention_days
            )
        except Exception as e:
            logger.error(f"Media cleanup failed: {str(e)}")
            results["tasks"]["old_media"] = {"error": str(e)}
        
        results["completed_at"] = datetime.utcnow().isoformat()
        
        logger.info("Comprehensive draft cleanup completed")
        
        return results