from sqlalchemy.orm import Session, joinedload
from db.models import (
    DraftExercise, DraftExerciseStatus, EditorAccount, PublishBatch,
    Exercise, ExerciseTypeEnum, DifficultyEnum, 
    DraftLearningNodeExercise, DraftMediaFile, DraftMediaType,
    LearningNode, LearningNodeExerciseAssociation,
    ImageFile, AudioFile
)
from db.models.exercise import ExerciseFactory
from typing import Dict, Any, List, Optional
from datetime import datetime
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ValidationError, PermissionDeniedError
)
from .scope_service import ScopeService
from services.media.draft_media_service import DraftMediaService
from loguru import logger
import uuid


class PublishingService:
    """Service for publishing drafts to production"""
    
    @staticmethod
    async def publish_draft(
        db: Session,
        draft_id: int,
        admin: EditorAccount,
        publish_notes: Optional[str] = None,
        batch_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Publish a single draft to production.
        
        Creates a new exercise in production and marks draft as published.
        """
        # Get draft with associations using eager loading
        draft = db.query(DraftExercise).options(
            joinedload(DraftExercise.learning_node_associations).joinedload(
                DraftLearningNodeExercise.learning_node
            ),
            joinedload(DraftExercise.media_files),
            joinedload(DraftExercise.assigned_editor)
        ).filter_by(id=draft_id).first()
        if not draft:
            raise NotFoundError(f"Draft {draft_id} not found")
        
        # Verify admin has scope
        if not await ScopeService.editor_has_scope_for_draft(db, admin, draft):
            raise PermissionDeniedError("Admin does not have scope for this draft")
        
        # Verify draft is in ACCEPTED status
        if draft.status != DraftExerciseStatus.ACCEPTED_BY_EDITOR:
            raise ValidationError(f"Draft must be in ACCEPTED_BY_EDITOR status, current status: {draft.status}")
        
        # Verify draft has complete data
        if not draft.data_json or not draft.solution_json:
            raise ValidationError("Draft must have complete exercise and solution data")
        
        # Use the already loaded associations instead of querying again
        draft_associations = draft.learning_node_associations
        
        if not draft_associations:
            raise ValidationError("Draft must be associated with at least one learning node")
        
        # Process media references in exercise data
        media_service = DraftMediaService()
        exercise_data = await PublishingService._process_media_references(
            db, draft, draft.data_json, media_service
        )
        
        # Process media references in solution data
        solution_data = await PublishingService._process_media_references(
            db, draft, draft.solution_json, media_service
        )

        # Transform solution data to match ExerciseSolutionModel format
        solution_data = PublishingService._transform_solution_data(solution_data)
        
        # Create production exercise
        try:
            # Generate new public_id for production exercise
            exercise_public_id = str(uuid.uuid4())

            # Get the first learning node for factory creation (required parameter)
            first_learning_node = None
            for draft_assoc in draft_associations:
                learning_node = db.query(LearningNode).filter_by(
                    id=draft_assoc.learning_node_id
                ).first()
                if learning_node:
                    first_learning_node = learning_node
                    break

            if not first_learning_node:
                raise ValidationError("No valid learning nodes found for draft")

            # Create exercise using factory to ensure type-specific initialization
            exercise = ExerciseFactory.create_exercise(
                exercise_type=draft.exercise_type,
                learning_node=first_learning_node,
                data=exercise_data,
                solution=solution_data,
                difficulty=DifficultyEnum(draft.difficulty)
            )

            # Set the public_id after creation
            exercise.public_id = exercise_public_id

            db.add(exercise)
            db.flush()  # Get the exercise ID

            # Create associations with remaining learning nodes
            for draft_assoc in draft_associations:
                # Get the learning node
                learning_node = db.query(LearningNode).filter_by(
                    id=draft_assoc.learning_node_id
                ).first()

                if learning_node and learning_node != first_learning_node:
                    # Create production association (first one already created by factory)
                    LearningNodeExerciseAssociation.create_association(
                        learning_node, exercise
                    )
            
            # Mark draft as published
            draft.status = DraftExerciseStatus.PUBLISHED
            draft.published_at = datetime.utcnow()
            draft.published_exercise_id = exercise.id
            
            # Copy all media files to production (use already loaded media files)
            for media_file in draft.media_files:
                if not media_file.production_media_id:
                    try:
                        production_media_id = media_service.copy_to_production(
                            db, media_file
                        )
                        logger.info(f"Copied media {media_file.id} to production as {production_media_id}")
                    except Exception as e:
                        logger.error(f"Failed to copy media {media_file.id}: {e}")
            

            
            # Note: Commit should be done by the calling route handler
            
            logger.info(f"Published draft {draft_id} as exercise {exercise.id}")
            
            return {
                "exercise_id": exercise.id,
                "exercise_public_id": exercise_public_id,
                "published_at": draft.published_at,
                "batch_id": batch_id
            }
            
        except Exception as e:
            db.rollback()
            logger.error(f"Failed to publish draft {draft_id}: {str(e)}")
            raise Exception(f"Failed to publish draft: {str(e)}")
    
    @staticmethod
    async def _process_media_references(
        db: Session,
        draft: DraftExercise,
        data: Dict[str, Any],
        media_service: DraftMediaService
    ) -> Dict[str, Any]:
        """
        Process media references in exercise/solution data.
        
        Replaces draft media public_ids with production media public_ids.
        """
        import copy
        
        # Create a deep copy to avoid modifying the original
        processed_data = copy.deepcopy(data)
        
        # Use already loaded media files instead of querying again
        draft_media_files = draft.media_files
        
        # Create a mapping of draft public_id to production media reference
        media_mapping = {}
        for media_file in draft_media_files:
            if media_file.production_media_id:
                # Get the production media reference based on type
                if media_file.media_type == DraftMediaType.IMAGE:
                    production_media = db.query(ImageFile).filter_by(
                        id=media_file.production_media_id
                    ).first()
                    if production_media:
                        media_mapping[media_file.public_id] = production_media.public_id
                elif media_file.media_type == DraftMediaType.AUDIO:
                    production_media = db.query(AudioFile).filter_by(
                        id=media_file.production_media_id
                    ).first()
                    if production_media:
                        media_mapping[media_file.public_id] = production_media.public_id
        
        # Recursively process the data structure
        def replace_media_refs(obj):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    # Common media reference field names (updated to match schema changes)
                    if key in ['image_url', 'prompt_image_url', 'audio_public_id', 'media_public_id', 'public_id']:
                        if isinstance(value, str) and value in media_mapping:
                            obj[key] = media_mapping[value]
                    else:
                        replace_media_refs(value)
            elif isinstance(obj, list):
                for item in obj:
                    replace_media_refs(item)
        
        replace_media_refs(processed_data)
        return processed_data

    @staticmethod
    def _transform_solution_data(solution_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform draft solution data to match ExerciseSolutionModel format.

        Ensures that the solution data structure matches what the ExerciseFactory expects.
        This handles any field name mismatches or structural differences between
        draft solution data and published exercise solution data.
        """
        if not solution_data:
            return solution_data

        import copy
        transformed = copy.deepcopy(solution_data)

        # Ensure correct_answer field exists and is properly structured
        if 'correct_answer' not in transformed:
            # Handle legacy camelCase field names
            if 'correctAnswer' in transformed:
                transformed['correct_answer'] = transformed.pop('correctAnswer')
            else:
                # If no correct_answer field, this might be an incomplete solution
                logger.warning("Solution data missing 'correct_answer' field")
                return transformed

        # Ensure solution_steps field uses correct format
        if 'solutionSteps' in transformed:
            transformed['solution_steps'] = transformed.pop('solutionSteps')

        # Ensure video_public_id field uses correct format
        if 'videoId' in transformed:
            transformed['video_public_id'] = transformed.pop('videoId')
        elif 'videoPublicId' in transformed:
            transformed['video_public_id'] = transformed.pop('videoPublicId')

        return transformed
    
    @staticmethod
    async def bulk_publish(
        db: Session,
        draft_ids: List[int],
        admin: EditorAccount,
        batch_name: str,
        batch_notes: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Publish multiple drafts in a batch.
        
        Creates a batch record and processes each draft.
        """
        # Create batch record
        batch = PublishBatch(
            batch_name=batch_name,
            batch_notes=batch_notes,
            created_by_id=admin.id,
            draft_count=len(draft_ids),
            success_count=0,
            failure_count=0
        )
        db.add(batch)
        db.flush()
        
        results = {}
        successful_count = 0
        failed_count = 0
        
        # Process each draft
        for draft_id in draft_ids:
            try:
                result = await PublishingService.publish_draft(
                    db=db,
                    draft_id=draft_id,
                    admin=admin,
                    publish_notes=f"Part of batch: {batch_name}",
                    batch_id=batch.id
                )
                results[draft_id] = {
                    "success": True,
                    "exercise_id": result["exercise_id"]
                }
                successful_count += 1
            except Exception as e:
                logger.error(f"Failed to publish draft {draft_id}: {str(e)}")
                results[draft_id] = {
                    "success": False,
                    "error": str(e)
                }
                failed_count += 1
        
        # Update batch counts
        batch.success_count = successful_count
        batch.failure_count = failed_count
        batch.completed_at = datetime.utcnow()
        
        # Note: Commit should be done by the calling route handler
        
        return {
            "batch_id": batch.id,
            "successful_count": successful_count,
            "failed_count": failed_count,
            "results": results
        }