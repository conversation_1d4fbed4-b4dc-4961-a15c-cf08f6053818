from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from db.models import (
    DraftExercise, DraftExerciseStatus, EditorAccount, EditorRole
)
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta, UTC


class MetricsService:
    """Service for editorial workflow metrics and analytics"""
    
    @staticmethod
    async def get_overview_metrics(
        db: Session,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        subject_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get comprehensive overview metrics."""
        # Base query
        query = db.query(DraftExercise)
        
        # Apply filters
        if date_from:
            query = query.filter(DraftExercise.created_at >= date_from)
        if date_to:
            query = query.filter(DraftExercise.created_at <= date_to)
        # TODO: Implement subject filtering via learning node relationships
        # if subject_id:
        #     query = query.filter(DraftExercise.subject_id == subject_id)
        
        # Draft metrics by status
        draft_metrics = {}
        for status in DraftExerciseStatus:
            count = query.filter(DraftExercise.status == status).count()
            draft_metrics[status.value] = count
        
        # Editor metrics
        active_editors = db.query(EditorAccount).filter(
            EditorAccount.is_active == True,
            EditorAccount.role == EditorRole.EDITOR
        ).count()
        
        active_admins = db.query(EditorAccount).filter(
            EditorAccount.is_active == True,
            EditorAccount.role == EditorRole.ADMIN
        ).count()
        
        editor_metrics = {
            "active_editors": active_editors,
            "active_admins": active_admins,
            "total_active": active_editors + active_admins
        }
        
        # Publishing metrics
        published_query = query.filter(DraftExercise.status == DraftExerciseStatus.PUBLISHED)
        if date_from and date_to:
            published_count = published_query.filter(
                DraftExercise.published_at >= date_from,
                DraftExercise.published_at <= date_to
            ).count()
        else:
            published_count = published_query.count()
        
        publishing_metrics = {
            "total_published": published_count,
            "pending_review": draft_metrics.get(DraftExerciseStatus.ACCEPTED_BY_EDITOR.value, 0),
            "in_progress": draft_metrics.get(DraftExerciseStatus.IN_REVIEW.value, 0)
        }
        
        # Processing times (placeholder - would calculate from audit logs)
        processing_times = {
            "average_review_hours": 24.5,
            "average_publish_hours": 2.5,
            "median_total_hours": 48.0
        }
        
        return {
            "draft_metrics": draft_metrics,
            "editor_metrics": editor_metrics,
            "publishing_metrics": publishing_metrics,
            "processing_times": processing_times
        }
    
    @staticmethod
    async def get_editor_performance(
        db: Session,
        editor_id: int,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get performance metrics for a specific editor."""
        editor = db.query(EditorAccount).filter_by(id=editor_id).first()
        if not editor:
            raise ValueError(f"Editor {editor_id} not found")
        
        # Base query for editor's drafts
        query = db.query(DraftExercise).filter(
            DraftExercise.assigned_editor_id == editor_id
        )
        
        if date_from:
            query = query.filter(DraftExercise.updated_at >= date_from)
        if date_to:
            query = query.filter(DraftExercise.updated_at <= date_to)
        
        # Count by action
        reviewed = query.filter(
            DraftExercise.status.in_([
                DraftExerciseStatus.ACCEPTED_BY_EDITOR,
                DraftExerciseStatus.REJECTED_BY_ADMIN,
                DraftExerciseStatus.PUBLISHED
            ])
        ).count()
        
        accepted = query.filter(
            DraftExercise.status == DraftExerciseStatus.ACCEPTED_BY_EDITOR
        ).count()
        
        # Current assignments
        current_assignments = db.query(DraftExercise).filter(
            DraftExercise.assigned_editor_id == editor_id,
            DraftExercise.status == DraftExerciseStatus.IN_REVIEW
        ).count()
        
        # Calculate rates
        rejected = reviewed - accepted
        acceptance_rate = (accepted / reviewed * 100) if reviewed > 0 else 0
        
        return {
            "editor_name": editor.email,
            "drafts_reviewed": reviewed,
            "drafts_accepted": accepted,
            "drafts_rejected": rejected,
            "average_review_time_hours": 24.0,  # Placeholder
            "acceptance_rate": round(acceptance_rate, 2),
            "current_assignments": current_assignments
        }
    
    @staticmethod
    async def get_draft_status_metrics(
        db: Session,
        subject_id: Optional[str] = None,
        editor_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Get draft distribution metrics."""
        query = db.query(DraftExercise)
        
        # TODO: Implement subject filtering via learning node relationships
        # if subject_id:
        #     query = query.filter(DraftExercise.subject_id == subject_id)
        if editor_id:
            query = query.filter(DraftExercise.assigned_editor_id == editor_id)
        
        total_drafts = query.count()
        
        # By status
        by_status = {}
        for status in DraftExerciseStatus:
            count = query.filter(DraftExercise.status == status).count()
            by_status[status.value] = count
        
        # By subject - TODO: Implement via learning node relationships
        by_subject = {}
        # subject_counts = db.query(
        #     DraftExercise.subject_id,
        #     func.count(DraftExercise.id).label('count')
        # ).group_by(DraftExercise.subject_id).all()
        # 
        # for subject_id, count in subject_counts:
        #     by_subject[subject_id] = count
        
        # By assigned editor
        by_editor = {}
        editor_counts = db.query(
            DraftExercise.assigned_editor_id,
            func.count(DraftExercise.id).label('count')
        ).filter(
            DraftExercise.assigned_editor_id.isnot(None)
        ).group_by(DraftExercise.assigned_editor_id).all()
        
        for editor_id, count in editor_counts:
            editor = db.query(EditorAccount).filter_by(id=editor_id).first()
            if editor:
                by_editor[editor.email] = count
        
        # Aging report
        now = datetime.now(UTC)
        aging_report = {
            "0-24_hours": query.filter(
                DraftExercise.created_at >= now - timedelta(days=1)
            ).count(),
            "1-3_days": query.filter(
                and_(
                    DraftExercise.created_at < now - timedelta(days=1),
                    DraftExercise.created_at >= now - timedelta(days=3)
                )
            ).count(),
            "3-7_days": query.filter(
                and_(
                    DraftExercise.created_at < now - timedelta(days=3),
                    DraftExercise.created_at >= now - timedelta(days=7)
                )
            ).count(),
            "7+_days": query.filter(
                DraftExercise.created_at < now - timedelta(days=7)
            ).count()
        }
        
        return {
            "total_drafts": total_drafts,
            "by_status": by_status,
            "by_subject": by_subject,
            "by_assigned_editor": by_editor,
            "aging_report": aging_report
        }
    
    @staticmethod
    async def get_editor_leaderboard(
        db: Session,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get top performing editors for the current month."""
        # Get current month boundaries
        now = datetime.now(UTC)
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        # Query editors with their monthly performance
        editors = db.query(EditorAccount).filter(
            EditorAccount.is_active == True,
            EditorAccount.role == EditorRole.EDITOR
        ).all()
        
        leaderboard = []
        for editor in editors:
            performance = await MetricsService.get_editor_performance(
                db=db,
                editor_id=editor.id,
                date_from=month_start,
                date_to=now
            )
            
            if performance["drafts_accepted"] > 0:
                leaderboard.append({
                    "editor_id": editor.id,
                    "editor_name": editor.email,
                    **performance
                })
        
        # Sort by drafts accepted (descending)
        leaderboard.sort(key=lambda x: x["drafts_accepted"], reverse=True)
        
        return leaderboard[:limit]