from sqlalchemy.orm import Session
from sqlalchemy import func
from db.models import EditorAccount, EditorR<PERSON>, EditorScope
from typing import Dict, Any, List, Optional
from datetime import datetime
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ValidationError, ConflictError
)
from loguru import logger
import math


class EditorManagementService:
    """Service for managing editor accounts and scopes"""
    
    @staticmethod
    async def list_editors(
        db: Session,
        page: int = 1,
        limit: int = 20,
        is_active: Optional[bool] = None,
        role: Optional[str] = None
    ) -> Dict[str, Any]:
        """List editor accounts with pagination and filtering."""
        query = db.query(EditorAccount)

        # Apply filters
        # Only filter by is_active if explicitly specified
        if is_active is not None:
            query = query.filter(EditorAccount.is_active == is_active)
        # If is_active is None/undefined, return all accounts (both active and inactive)

        if role:
            query = query.filter(EditorAccount.role == EditorRole[role])
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        offset = (page - 1) * limit
        editors = query.offset(offset).limit(limit).all()
        
        # Get additional stats for each editor
        editor_list = []
        for editor in editors:
            # Count drafts reviewed and accepted
            from db.models import DraftExercise, DraftExerciseStatus
            reviewed = db.query(DraftExercise).filter(
                DraftExercise.assigned_editor_id == editor.id,
                DraftExercise.status.in_([
                    DraftExerciseStatus.ACCEPTED_BY_EDITOR,
                    DraftExerciseStatus.REJECTED_BY_ADMIN,
                    DraftExerciseStatus.PUBLISHED
                ])
            ).count()
            
            accepted = db.query(DraftExercise).filter(
                DraftExercise.assigned_editor_id == editor.id,
                DraftExercise.status == DraftExerciseStatus.ACCEPTED_BY_EDITOR
            ).count()
            
            # Get scopes
            scopes = []
            for scope in editor.scopes:
                scope_dict = {
                    "id": scope.id,
                    "editor_id": scope.editor_id,
                    "created_at": scope.created_at.isoformat()
                }
                if scope.subject:
                    scope_dict["scope_type"] = "SUBJECT"
                    scope_dict["scope_value"] = scope.subject.public_id
                    scope_dict["name"] = scope.subject.name
                elif scope.chapter:
                    scope_dict["scope_type"] = "CHAPTER"
                    scope_dict["scope_value"] = scope.chapter.public_id
                    scope_dict["name"] = scope.chapter.title
                elif scope.learning_node:
                    scope_dict["scope_type"] = "LEARNING_NODE"
                    scope_dict["scope_value"] = scope.learning_node.public_id
                    scope_dict["name"] = scope.learning_node.title
                scope_dict["is_active"] = True  # Assumed active since no is_active field exists
                scopes.append(scope_dict)
            
            editor_list.append({
                "id": editor.id,
                "public_id": editor.public_id,
                "email": editor.email,
                "role": editor.role.value,
                "is_active": editor.is_active,
                "created_at": editor.created_at,
                "last_login_at": None,  # last_login_at field doesn't exist in model
                "total_drafts_reviewed": reviewed,
                "total_drafts_accepted": accepted,
                "scopes": scopes
            })
        
        return {
            "editors": editor_list,
            "total": total,
            "total_pages": math.ceil(total / limit)
        }
    
    @staticmethod
    async def create_editor(
        db: Session,
        email: str,
        role: str,
        created_by: EditorAccount,
        password: Optional[str] = None
    ) -> EditorAccount:
        """Create a new editor account."""
        from api.v1.editorial.auth.utils.password_utils import hash_password, generate_temporary_password

        # Check if email already exists
        existing = db.query(EditorAccount).filter_by(email=email).first()
        if existing:
            raise ConflictError(f"Editor with email {email} already exists")

        # Handle password - use provided password or generate a temporary one
        if password and password.strip():
            # Use the provided password and hash it properly
            pwd_hash = hash_password(password.strip())
            logger.info(f"Editor account created with provided password: {email} by admin {created_by.email}")
        else:
            # Generate a temporary password and hash it
            temp_password = generate_temporary_password()
            pwd_hash = hash_password(temp_password)
            logger.info(f"Editor account created with temporary password: {email} by admin {created_by.email}")
            # TODO: Send temporary password to editor via email or other secure method

        # Create editor
        editor = EditorAccount(
            email=email,
            role=EditorRole[role],
            is_active=True,
            pwd_hash=pwd_hash
        )

        db.add(editor)
        db.commit()
        db.refresh(editor)

        return editor
    
    @staticmethod
    async def get_editor_by_public_id(
        db: Session,
        editor_public_id: str
    ) -> EditorAccount:
        """Get an editor account by public ID."""
        editor = db.query(EditorAccount).filter_by(public_id=editor_public_id).first()
        if not editor:
            raise NotFoundError(f"Editor with public ID {editor_public_id} not found")
        return editor

    @staticmethod
    async def update_editor(
        db: Session,
        editor_id: int,
        updates: Dict[str, Any],
        updated_by: EditorAccount
    ) -> EditorAccount:
        """Update an existing editor account."""
        editor = db.query(EditorAccount).filter_by(id=editor_id).first()
        if not editor:
            raise NotFoundError(f"Editor {editor_id} not found")

        # Update fields
        for field, value in updates.items():
            if field == "role":
                value = EditorRole[value]
            setattr(editor, field, value)

        editor.updated_at = datetime.utcnow()

        db.commit()
        db.refresh(editor)

        logger.info(f"Editor {editor_id} updated by admin {updated_by.email}")

        return editor

    @staticmethod
    async def update_editor_by_public_id(
        db: Session,
        editor_public_id: str,
        updates: Dict[str, Any],
        updated_by: EditorAccount
    ) -> EditorAccount:
        """Update an existing editor account by public ID."""
        editor = db.query(EditorAccount).filter_by(public_id=editor_public_id).first()
        if not editor:
            raise NotFoundError(f"Editor with public ID {editor_public_id} not found")

        # Update fields
        for field, value in updates.items():
            if field == "role":
                value = EditorRole[value]
            setattr(editor, field, value)

        editor.updated_at = datetime.utcnow()

        db.commit()
        db.refresh(editor)

        logger.info(f"Editor {editor_public_id} updated by admin {updated_by.email}")

        return editor
    
    @staticmethod
    async def add_editor_scope(
        db: Session,
        editor_id: int,
        scope_type: str,
        scope_value: str,
        added_by: EditorAccount
    ) -> EditorScope:
        """Add a scope to an editor account."""
        from db.models import Subject, Chapter, LearningNode

        # Verify editor exists
        editor = db.query(EditorAccount).filter_by(id=editor_id).first()
        if not editor:
            raise NotFoundError(f"Editor with ID {editor_id} not found")

        # Initialize scope fields
        subject_id = None
        chapter_id = None
        learning_node_id = None

        # Based on scope type, look up the entity and set appropriate IDs
        if scope_type == "SUBJECT":
            subject = db.query(Subject).filter_by(public_id=scope_value).first()
            if not subject:
                raise NotFoundError(f"Subject with ID {scope_value} not found")
            subject_id = subject.id

            # Check if scope already exists
            existing = db.query(EditorScope).filter_by(
                editor_id=editor_id,
                subject_id=subject_id,
                chapter_id=None,
                learning_node_id=None
            ).first()
            if existing:
                raise ConflictError(f"Editor already has access to subject {subject.name}")

        elif scope_type == "CHAPTER":
            chapter = db.query(Chapter).filter_by(public_id=scope_value).first()
            if not chapter:
                raise NotFoundError(f"Chapter with ID {scope_value} not found")
            chapter_id = chapter.id

            # Check if scope already exists
            existing = db.query(EditorScope).filter_by(
                editor_id=editor_id,
                chapter_id=chapter_id,
                learning_node_id=None
            ).first()
            if existing:
                raise ConflictError(f"Editor already has access to chapter {chapter.title}")

        elif scope_type == "LEARNING_NODE":
            node = db.query(LearningNode).filter_by(public_id=scope_value).first()
            if not node:
                raise NotFoundError(f"Learning node with ID {scope_value} not found")
            learning_node_id = node.id

            # Check if scope already exists
            existing = db.query(EditorScope).filter_by(
                editor_id=editor_id,
                learning_node_id=learning_node_id
            ).first()
            if existing:
                raise ConflictError(f"Editor already has access to learning node {node.title}")

        else:
            raise ValidationError(f"Invalid scope type: {scope_type}. Must be SUBJECT, CHAPTER, or LEARNING_NODE")

        # Create new scope
        scope = EditorScope(
            editor_id=editor_id,
            subject_id=subject_id,
            chapter_id=chapter_id,
            learning_node_id=learning_node_id
        )

        db.add(scope)
        db.commit()
        db.refresh(scope)

        logger.info(f"Scope added for editor {editor_id} by admin {added_by.email}: {scope_type}={scope_value}")

        # Return scope with calculated scope_type and scope_value for response
        scope.scope_type = scope_type
        scope.scope_value = scope_value
        scope.is_active = True  # For response compatibility

        return scope

    @staticmethod
    async def add_editor_scope_by_public_id(
        db: Session,
        editor_public_id: str,
        scope_type: str,
        scope_value: str,
        added_by: EditorAccount
    ) -> EditorScope:
        """Add a scope to an editor account by public ID."""
        # Verify editor exists
        editor = db.query(EditorAccount).filter_by(public_id=editor_public_id).first()
        if not editor:
            raise NotFoundError(f"Editor with public ID {editor_public_id} not found")

        # Use the existing method with the internal ID
        return await EditorManagementService.add_editor_scope(
            db=db,
            editor_id=editor.id,
            scope_type=scope_type,
            scope_value=scope_value,
            added_by=added_by
        )
    
    @staticmethod
    async def remove_editor_scope(
        db: Session,
        editor_id: int,
        scope_id: int,
        removed_by: EditorAccount
    ) -> None:
        """Remove a scope from an editor account."""
        # Verify editor exists
        editor = db.query(EditorAccount).filter_by(id=editor_id).first()
        if not editor:
            raise NotFoundError(f"Editor with ID {editor_id} not found")

        # Find and verify the scope
        scope = db.query(EditorScope).filter_by(
            id=scope_id,
            editor_id=editor_id
        ).first()

        if not scope:
            raise NotFoundError(f"Scope with ID {scope_id} not found for editor {editor_id}")

        # Log what's being removed for audit purposes
        scope_desc = "unknown"
        if scope.learning_node_id:
            scope_desc = f"learning_node_id={scope.learning_node_id}"
        elif scope.chapter_id:
            scope_desc = f"chapter_id={scope.chapter_id}"
        elif scope.subject_id:
            scope_desc = f"subject_id={scope.subject_id}"

        # Delete the scope
        db.delete(scope)
        db.commit()

        logger.info(f"Scope {scope_id} ({scope_desc}) removed from editor {editor_id} by admin {removed_by.email}")

    @staticmethod
    async def remove_editor_scope_by_public_id(
        db: Session,
        editor_public_id: str,
        scope_id: int,
        removed_by: EditorAccount
    ) -> None:
        """Remove a scope from an editor account by public ID."""
        # Verify editor exists
        editor = db.query(EditorAccount).filter_by(public_id=editor_public_id).first()
        if not editor:
            raise NotFoundError(f"Editor with public ID {editor_public_id} not found")

        # Use the existing method with the internal ID
        await EditorManagementService.remove_editor_scope(
            db=db,
            editor_id=editor.id,
            scope_id=scope_id,
            removed_by=removed_by
        )

    @staticmethod
    async def delete_editor_by_public_id(
        db: Session,
        editor_public_id: str,
        deleted_by: EditorAccount
    ) -> bool:
        """
        Delete an editor account by public ID.

        Business rules:
        - Cannot delete the last admin user
        - Cannot delete the currently logged-in admin
        - Only admins can delete editors
        """
        # Find the editor to delete
        editor_to_delete = db.query(EditorAccount).filter_by(public_id=editor_public_id).first()
        if not editor_to_delete:
            raise NotFoundError(f"Editor with public ID {editor_public_id} not found")

        # Cannot delete yourself
        if editor_to_delete.id == deleted_by.id:
            raise ValidationError("Cannot delete your own account")

        # If deleting an admin, ensure it's not the last admin
        if editor_to_delete.role == EditorRole.ADMIN:
            admin_count = db.query(EditorAccount).filter_by(
                role=EditorRole.ADMIN,
                is_active=True
            ).count()

            if admin_count <= 1:
                raise ValidationError("Cannot delete the last admin user")

        # Soft delete: set is_active to False instead of hard delete
        # This preserves audit trails and prevents foreign key issues
        editor_to_delete.is_active = False
        editor_to_delete.updated_at = datetime.utcnow()

        db.commit()

        logger.info(f"Editor {editor_public_id} ({editor_to_delete.email}) deleted by admin {deleted_by.email}")

        return True

    @staticmethod
    async def hard_delete_editor_by_public_id(
        db: Session,
        editor_public_id: str,
        deleted_by: EditorAccount
    ) -> bool:
        """
        Permanently delete an editor account by public ID (hard delete).

        Business rules:
        - Cannot delete the last admin user
        - Cannot delete the currently logged-in admin
        - Only admins can delete editors
        - Performs hard delete (removes record from database)
        - WARNING: This is irreversible and may cause foreign key issues
        """
        # Find the editor to delete
        editor_to_delete = db.query(EditorAccount).filter_by(public_id=editor_public_id).first()
        if not editor_to_delete:
            raise NotFoundError(f"Editor with public ID {editor_public_id} not found")

        # Cannot delete yourself
        if editor_to_delete.id == deleted_by.id:
            raise ValidationError("Cannot delete your own account")

        # If deleting an admin, ensure it's not the last admin
        if editor_to_delete.role == EditorRole.ADMIN:
            admin_count = db.query(EditorAccount).filter_by(
                role=EditorRole.ADMIN,
                is_active=True
            ).count()

            if admin_count <= 1:
                raise ValidationError("Cannot delete the last admin user")

        # Store email for logging before deletion
        editor_email = editor_to_delete.email

        # Hard delete: remove the record from the database
        # Note: This may cause foreign key constraint issues if there are related records
        db.delete(editor_to_delete)
        db.commit()

        logger.info(f"Editor {editor_public_id} ({editor_email}) permanently deleted by admin {deleted_by.email}")

        return True