import boto3
from botocore.exceptions import <PERSON><PERSON><PERSON><PERSON><PERSON>, ConnectionError
from typing import Dict, Any, Optional, BinaryIO, List
from core.config.settings import settings
from db.models import DraftMediaFile, DraftMediaType, ImageFile, AudioFile
from sqlalchemy.orm import Session
import uuid
from pathlib import Path
from datetime import datetime, timedelta
from loguru import logger
import mimetypes
import os
from utils.retry import exponential_backoff_retry


class DraftMediaService:
    """Service for managing draft media files in Cloudflare R2"""
    
    def __init__(self):
        """Initialize the draft media service with R2 client"""
        # Draft media uses a separate bucket from production
        self.draft_bucket_name = "luxedu-media-draft"
        self.production_image_bucket = settings.CLOUDFLARE_R2_IMAGE_BUCKET
        self.production_audio_bucket = settings.CLOUDFLARE_LANGUAGES_BUCKET
        
        # Initialize R2 client
        self.s3_client = boto3.client(
            's3',
            endpoint_url=settings.CLOUDFLARE_R2_ENDPOINT,
            aws_access_key_id=settings.CLOUDFLARE_R2_ACCESS_KEY_ID,
            aws_secret_access_key=settings.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
            region_name=settings.CLOUDFLARE_R2_REGION_NAME
        )
        
        # Ensure draft bucket exists
        self._ensure_bucket_exists(self.draft_bucket_name)
    
    @exponential_backoff_retry(
        max_retries=3,
        exceptions=(ClientError, ConnectionError)
    )
    def _ensure_bucket_exists(self, bucket_name: str) -> None:
        """Ensure the bucket exists, create if it doesn't"""
        try:
            self.s3_client.head_bucket(Bucket=bucket_name)
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == '404':
                try:
                    self.s3_client.create_bucket(Bucket=bucket_name)
                    logger.info(f"Created bucket: {bucket_name}")
                except ClientError as create_error:
                    logger.error(f"Failed to create bucket {bucket_name}: {create_error}")
    
    def _generate_storage_path(
        self, 
        draft_exercise_id: int, 
        media_type: DraftMediaType,
        filename: str
    ) -> str:
        """Generate a unique storage path for draft media"""
        # Extract file extension
        _, ext = os.path.splitext(filename)
        if not ext:
            # Try to determine extension from mime type
            mime_type = mimetypes.guess_type(filename)[0]
            if mime_type:
                ext = mimetypes.guess_extension(mime_type) or ''
        
        # Generate unique identifier
        unique_id = str(uuid.uuid4())
        
        # Create path structure: draft/{exercise_id}/{media_type}/{unique_id}{ext}
        path = f"draft/{draft_exercise_id}/{media_type.value}/{unique_id}{ext}"
        
        return path
    
    @exponential_backoff_retry(
        max_retries=3,
        exceptions=(ClientError, ConnectionError)
    )
    def _upload_with_retry(
        self,
        file: BinaryIO,
        bucket: str,
        key: str,
        content_type: str,
        metadata: Dict[str, str]
    ) -> None:
        """Upload file to S3/R2 with retry logic"""
        self.s3_client.upload_fileobj(
            file,
            bucket,
            key,
            ExtraArgs={
                'ContentType': content_type,
                'Metadata': metadata
            }
        )
    
    @exponential_backoff_retry(
        max_retries=3,
        exceptions=(ClientError, ConnectionError)
    )
    def get_draft_media_url(self, storage_path: str, expiry: int = 3600) -> str:
        """
        Generate a presigned URL for draft media
        
        Args:
            storage_path: The storage path of the media file
            expiry: URL expiry time in seconds (default: 1 hour)
            
        Returns:
            Presigned URL for accessing the draft media
        """
        try:
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': self.draft_bucket_name,
                    'Key': storage_path
                },
                ExpiresIn=expiry
            )
            return url
        except ClientError as e:
            logger.error(f"Error generating presigned URL for {storage_path}: {e}")
            # Return a fallback URL
            return f"https://{self.draft_bucket_name}.cloudflare.r2cdn.net/{storage_path}"
    
    def upload_draft_media(
        self,
        db: Session,
        draft_exercise_id: int,
        file: BinaryIO,
        filename: str,
        media_type: DraftMediaType,
        media_metadata: Optional[Dict[str, Any]] = None
    ) -> DraftMediaFile:
        """
        Upload media file to draft R2 storage
        
        Args:
            db: Database session
            draft_exercise_id: ID of the draft exercise
            file: File-like object to upload
            filename: Original filename
            media_type: Type of media (IMAGE or AUDIO)
            media_metadata: Additional metadata to store
            
        Returns:
            Created DraftMediaFile record
        """
        # Generate storage path
        storage_path = self._generate_storage_path(
            draft_exercise_id, 
            media_type, 
            filename
        )
        
        # Determine content type
        content_type = mimetypes.guess_type(filename)[0]
        if not content_type:
            if media_type == DraftMediaType.IMAGE:
                content_type = 'image/png'  # Default for images
            else:
                content_type = 'audio/mpeg'  # Default for audio
        
        try:
            # Upload to R2 with retry
            self._upload_with_retry(
                file,
                self.draft_bucket_name,
                storage_path,
                content_type,
                {
                    'draft_exercise_id': str(draft_exercise_id),
                    'original_filename': filename,
                    'media_type': media_type.value,
                    'uploaded_at': datetime.utcnow().isoformat()
                }
            )
            
            # Create database record
            draft_media = DraftMediaFile(
                draft_exercise_id=draft_exercise_id,
                media_type=media_type,
                storage_path=storage_path,
                original_filename=filename,
                content_type=content_type,
                media_metadata=media_metadata or {}
            )
            
            db.add(draft_media)
            db.flush()  # Use flush instead of commit
            db.refresh(draft_media)
            
            logger.info(f"Uploaded draft media: {storage_path}")
            
            return draft_media
            
        except ClientError as e:
            logger.error(f"Failed to upload draft media: {e}")
            raise Exception(f"Failed to upload media: {str(e)}")
    
    def copy_to_production(
        self,
        db: Session,
        draft_media: DraftMediaFile,
        production_path: Optional[str] = None
    ) -> int:
        """
        Copy draft media to production storage
        
        Args:
            db: Database session
            draft_media: Draft media file to copy
            production_path: Optional custom production path
            
        Returns:
            ID of created production media record
        """
        # Determine production bucket based on media type
        if draft_media.media_type == DraftMediaType.IMAGE:
            production_bucket = self.production_image_bucket
            model_class = ImageFile
        else:
            production_bucket = self.production_audio_bucket
            model_class = AudioFile
        
        # Generate production path if not provided
        if not production_path:
            # Remove 'draft/' prefix and exercise ID from path
            path_parts = draft_media.storage_path.split('/')
            if len(path_parts) >= 3:
                # Keep media type and filename
                production_path = '/'.join(path_parts[2:])
            else:
                production_path = draft_media.storage_path
        
        try:
            # Copy object from draft to production bucket with retry
            copy_source = {
                'Bucket': self.draft_bucket_name,
                'Key': draft_media.storage_path
            }
            
            self._copy_object_with_retry(
                copy_source,
                production_bucket,
                production_path
            )
            
            # Create production media record
            if draft_media.media_type == DraftMediaType.IMAGE:
                production_media = ImageFile(
                    storage_path=production_path,
                    mime_type=draft_media.content_type
                )
            else:
                production_media = AudioFile(
                    storage_path=production_path,
                    # AudioFile model might have different fields
                )
            
            db.add(production_media)
            db.flush()  # Use flush instead of commit
            db.refresh(production_media)
            
            # Update draft media with production reference
            draft_media.production_media_id = production_media.id
            draft_media.copied_to_production_at = datetime.utcnow()
            db.flush()  # Use flush instead of commit
            
            logger.info(f"Copied draft media to production: {production_path}")
            
            return production_media.id
            
        except ClientError as e:
            logger.error(f"Failed to copy media to production: {e}")
            raise Exception(f"Failed to copy media to production: {str(e)}")
    
    def delete_draft_media(
        self,
        db: Session,
        draft_media: DraftMediaFile
    ) -> None:
        """
        Delete draft media from R2 and database
        
        Args:
            db: Database session
            draft_media: Draft media file to delete
        """
        try:
            # Delete from R2 with retry
            self._delete_object_with_retry(
                self.draft_bucket_name,
                draft_media.storage_path
            )
            
            # Delete from database
            db.delete(draft_media)
            db.flush()  # Use flush instead of commit
            
            logger.info(f"Deleted draft media: {draft_media.storage_path}")
            
        except ClientError as e:
            logger.error(f"Failed to delete draft media: {e}")
            raise Exception(f"Failed to delete media: {str(e)}")
    
    def list_draft_media(
        self,
        draft_exercise_id: int
    ) -> List[str]:
        """
        List all media files for a draft exercise
        
        Args:
            draft_exercise_id: ID of the draft exercise
            
        Returns:
            List of storage paths
        """
        prefix = f"draft/{draft_exercise_id}/"
        
        try:
            response = self._list_objects_with_retry(
                self.draft_bucket_name,
                prefix
            )
            
            if 'Contents' not in response:
                return []
            
            return [obj['Key'] for obj in response['Contents']]
            
        except ClientError as e:
            logger.error(f"Failed to list draft media: {e}")
            return []
    
    def cleanup_old_draft_media(
        self,
        db: Session,
        days: int = 30
    ) -> int:
        """
        Clean up draft media older than specified days
        
        Args:
            db: Database session
            days: Age threshold in days (default: 30)
            
        Returns:
            Number of media files deleted
        """
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Find old draft media that hasn't been copied to production
        old_media = db.query(DraftMediaFile).filter(
            DraftMediaFile.created_at < cutoff_date,
            DraftMediaFile.production_media_id.is_(None)
        ).all()
        
        deleted_count = 0
        for media in old_media:
            try:
                self.delete_draft_media(db, media)
                deleted_count += 1
            except Exception as e:
                logger.error(f"Failed to delete old draft media {media.id}: {e}")
        
        logger.info(f"Cleaned up {deleted_count} old draft media files")
        
        return deleted_count
    
    @exponential_backoff_retry(
        max_retries=3,
        exceptions=(ClientError, ConnectionError)
    )
    def _copy_object_with_retry(
        self,
        copy_source: Dict[str, str],
        dest_bucket: str,
        dest_key: str
    ) -> None:
        """Copy S3/R2 object with retry logic"""
        self.s3_client.copy_object(
            CopySource=copy_source,
            Bucket=dest_bucket,
            Key=dest_key,
            MetadataDirective='COPY'
        )
    
    @exponential_backoff_retry(
        max_retries=3,
        exceptions=(ClientError, ConnectionError)
    )
    def _delete_object_with_retry(
        self,
        bucket: str,
        key: str
    ) -> None:
        """Delete S3/R2 object with retry logic"""
        self.s3_client.delete_object(
            Bucket=bucket,
            Key=key
        )
    
    @exponential_backoff_retry(
        max_retries=3,
        exceptions=(ClientError, ConnectionError)
    )
    def _list_objects_with_retry(
        self,
        bucket: str,
        prefix: str
    ) -> Dict[str, Any]:
        """List S3/R2 objects with retry logic"""
        return self.s3_client.list_objects_v2(
            Bucket=bucket,
            Prefix=prefix
        )