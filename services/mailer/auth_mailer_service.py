from services.mailer.mailer_service import EmailService, EmailContext
from loguru import logger
from core.config import settings # For support email
from datetime import datetime, timezone

class AuthMailerService:
    def __init__(self, email_service: EmailService):
        self.email_service = email_service

    def send_child_verification_email(self, to: str, verification_code: str, lang: str):
        # Build the verification link using the frontend URL and language-specific route
        frontend_url = settings.FRONTEND_URL.rstrip('/')
        verification_link = f"{frontend_url}/{lang}/auth/verify-email?verification_code={verification_code}&email={to}"
        
        ctx = EmailContext(
            to=to,
            template_type="child_verification",
            lang=lang,
            dynamic_data={
                "verification_link": verification_link,
                "verification_code": verification_code
            }
        )
        try:
            self.email_service.send(ctx)
            logger.info(f"Child verification email sent to {to} with link: {verification_link}")
        except Exception as e:
            logger.error(f"Failed to send child verification email to {to}: {str(e)}")
            # Optionally re-raise or handle as per application's error handling strategy

    def send_child_forgot_pin_email(self, to: str, pin: str, lang: str):
        ctx = EmailContext(
            to=to,
            template_type="child_forgot_pin",
            lang=lang,
            dynamic_data={"login_pin": pin}
        )
        try:
            self.email_service.send(ctx)
            logger.info(f"Child forgot PIN email sent to {to}")
        except Exception as e:
            logger.error(f"Failed to send child forgot PIN email to {to}: {str(e)}")

    def send_child_parent_assignment_email(self, to: str, verification_code: str, lang: str):
        ctx = EmailContext(
            to=to,
            template_type="child_parent_assignment",
            lang=lang,
            dynamic_data={"verification_code": verification_code}
        )
        try:
            self.email_service.send(ctx)
            logger.info(f"Child parent assignment email sent to {to}")
        except Exception as e:
            logger.error(f"Failed to send child parent assignment email to {to}: {str(e)}")

    def send_parent_verification_email(self, to: str, verification_code: str, lang: str):
        # Build the verification link using the frontend URL and language-specific route
        frontend_url = settings.FRONTEND_URL.rstrip('/')
        verification_link = f"{frontend_url}/{lang}/auth/verify-email?verification_code={verification_code}&email={to}"
        
        ctx = EmailContext(
            to=to,
            template_type="parent_verification",
            lang=lang,
            dynamic_data={
                "verification_link": verification_link,
                "verification_code": verification_code
            }
        )
        try:
            self.email_service.send(ctx)
            logger.info(f"Parent verification email sent to {to} with link: {verification_link}")
        except Exception as e:
            logger.error(f"Failed to send parent verification email to {to}: {str(e)}")

    def send_parent_pin_reset_email(self, to: str, reset_token: str, lang: str):
        # Build the verification link using the frontend URL and language-specific route
        # For PIN reset, we only send the link - no plain text code displayed
        frontend_url = settings.FRONTEND_URL.rstrip('/')
        verification_link = f"{frontend_url}/{lang}/auth/reset-parent-pin?reset_token={reset_token}&email={to}"
        
        ctx = EmailContext(
            to=to,
            template_type="parent_pin_reset",
            lang=lang,
            dynamic_data={
                "verification_link": verification_link
                # Note: No verification_code included - reset token only used in URL
            }
        )
        try:
            self.email_service.send(ctx)
            logger.info(f"Parent PIN reset email sent to {to} with link: {verification_link}")
        except Exception as e:
            logger.error(f"Failed to send parent PIN reset email to {to}: {str(e)}")

    def send_internal_contact_submission_email(
        self,
        user_name: str,
        user_email: str,
        user_message: str,
        lang: str = "en" # Default to English for internal mails, or use parent's lang
    ):
        """
        Sends an email to the internal support team about a new contact form submission.
        """
        support_email_to = settings.SUPPORT_EMAIL_INTERNAL
        if not support_email_to:
            logger.error("SUPPORT_EMAIL_INTERNAL is not configured. Cannot send contact submission email.")
            return

        submitted_at_utc = datetime.now(timezone.utc)
        # Format for display, e.g., "YYYY-MM-DD HH:MM:SS UTC"
        # You can adjust the format string as needed
        formatted_submitted_at = submitted_at_utc.strftime("%Y-%m-%d %H:%M:%S %Z")


        ctx = EmailContext(
            to=support_email_to,
            template_type="internal_contact_submission",
            lang=lang, # Or a fixed lang like "en" for internal team
            dynamic_data={
                "user_name": user_name,
                "user_email": user_email,
                "user_message": user_message,
                "submitted_at": formatted_submitted_at
            }
        )
        try:
            self.email_service.send(ctx)
            logger.info(f"Internal contact submission email sent to {support_email_to} regarding user {user_email}")
        except Exception as e:
            logger.error(f"Failed to send internal contact submission email to {support_email_to}: {str(e)}")