import boto3
from botocore.exceptions import <PERSON><PERSON><PERSON><PERSON><PERSON>, BotoCoreError
from pydantic import BaseModel, EmailStr
from typing import Optional, Dict, Any
from abc import ABC, abstractmethod
from loguru import logger

from core.config.settings import settings
from services.mailer.loader import TemplateLoader  # Assuming TemplateLoader is in mailer.loader

class EmailContext(BaseModel):
    to: EmailStr
    subject: Optional[str] = None
    template_type: str  # e.g., "verification", "reset_pin"
    lang: str
    dynamic_data: Dict[str, Any]

class EmailService(ABC):
    @abstractmethod
    def send(self, ctx: EmailContext) -> bool:
        """Sends an email based on the provided context."""
        pass
    
    @abstractmethod
    def get_sender_email(self) -> str:
        """Returns the sender email address."""
        pass

class SESMailer(EmailService):
    def __init__(self, template_loader: TemplateLoader):
        self.template_loader = template_loader
        self.sender_email = self.get_sender_email()
        
        if not all([settings.AWS_ACCESS_KEY, settings.AWS_SECRET_KEY, settings.AWS_SES_REGION, self.sender_email]):
            logger.error("AWS SES credentials or sender email not fully configured in settings.")
            self.ses_client = None
            return

        if not all([settings.AWS_ACCESS_KEY, settings.AWS_SECRET_KEY, settings.AWS_SES_REGION, self.sender_email]):
            logger.error("AWS SES credentials or sender email not fully configured in environment variables.")
            # Depending on desired behavior, could raise an error or operate in a "disabled" mode.
            # For now, it will fail when trying to initialize boto3 client if creds are missing.
            # raise ValueError("Missing AWS SES configuration in environment variables.")

        try:
            self.ses_client = boto3.client(
                'ses',
                aws_access_key_id=settings.AWS_ACCESS_KEY,
                aws_secret_access_key=settings.AWS_SECRET_KEY,
                region_name=settings.AWS_SES_REGION
            )
        except Exception as e:
            logger.error(f"Failed to initialize AWS SES client: {e}")
            self.ses_client = None  # Ensure it's None if init fails

    def get_sender_email(self) -> str:
        """Returns the sender email address based on environment."""
        if isinstance(settings.EMAIL_FROM, dict):
            return settings.EMAIL_FROM.get(settings.ENVIRONMENT, settings.EMAIL_FROM.get('dev', ''))
        else:
            return settings.EMAIL_FROM

    def send(self, ctx: EmailContext) -> bool:
        if not self.ses_client:
            logger.error("SESMailer is not initialized properly. Cannot send email.")
            return False
        if not self.sender_email:
            logger.error("Sender email (EMAIL_FROM) is not configured. Cannot send email.")
            return False

        try:
            html_body = self.template_loader.render(
                email_type=ctx.template_type,
                lang=ctx.lang,
                dynamic_data=ctx.dynamic_data
            )
        except Exception as e:
            logger.error(f"Failed to render email template {ctx.template_type} for {ctx.to}: {e}")
            return False

        email_subject = ctx.subject or self.template_loader.get_subject(
            email_type=ctx.template_type,
            lang=ctx.lang
        )

        try:
            response = self.ses_client.send_email(
                Destination={'ToAddresses': [ctx.to]},
                Message={
                    'Body': {
                        'Html': {'Charset': 'UTF-8', 'Data': html_body}
                        # Optionally, add a Text part if you generate one
                        # 'Text': {'Charset': 'UTF-8', 'Data': text_body}
                    },
                    'Subject': {'Charset': 'UTF-8', 'Data': email_subject}
                },
                Source=self.sender_email
            )
            logger.info(f"""Email sent to {ctx.to} with subject 
            '{email_subject}'. Message ID: {response.get('MessageId')}
            """)
            return True
        except ClientError as e:
            logger.error(f"AWS SES ClientError sending email to {ctx.to}: {e.response['Error']['Message']}")
        except BotoCoreError as e:
            logger.error(f"AWS SES BotoCoreError sending email to {ctx.to}: {e}")
        except Exception as e:
            logger.error(f"Unexpected error sending email to {ctx.to}: {e}")
        
        return False    

# Example of a Null/Dummy Mailer for testing or if SES is not configured
class NullMailer(EmailService):
    def __init__(self, template_loader: Optional[TemplateLoader] = None):
        logger.info("NullMailer initialized. Emails will be logged but not sent.")
        self.template_loader = template_loader

    def get_sender_email(self) -> str:
        """Returns the sender email address based on environment for logging purposes."""
        if isinstance(settings.EMAIL_FROM, dict):
            return settings.EMAIL_FROM.get(settings.ENVIRONMENT, settings.EMAIL_FROM.get('dev', '<EMAIL>'))
        else:
            return settings.EMAIL_FROM or '<EMAIL>'

    def send(self, ctx: EmailContext) -> bool:
        subject_log = ctx.subject
        body_log = "Dynamic Data: " + str(ctx.dynamic_data)

        if self.template_loader:
            try:
                html_body = self.template_loader.render(ctx.template_type, ctx.lang, ctx.dynamic_data)
                subject_log = ctx.subject or self.template_loader.get_subject(ctx.template_type, ctx.lang)
                body_log = html_body  # Log the rendered HTML for inspection
            except Exception as e:
                logger.warning(f"NullMailer: Error rendering template {ctx.template_type} for {ctx.to}: {e}")
        
        logger.info(f"NullMailer: Pretending to send email to: {ctx.to}")
        logger.info(f"NullMailer: Subject: {subject_log}")
        logger.info(f"NullMailer: Template Type: {ctx.template_type}, Lang: {ctx.lang}")
        logger.debug(f"NullMailer: Body/Dynamic Data:\n{body_log[:500]}...")  # Log part of the body
        return True