import os
import json
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
from loguru import logger

class TemplateLoader:
    def __init__(self, templates_base_dir: str, default_lang: str = "en"):
        self.templates_base_dir = templates_base_dir
        self.default_lang = default_lang
        self.jinja_env = Environment(
            loader=FileSystemLoader(self.templates_base_dir),
            autoescape=True  # Enable autoescaping for security
        )

    def _load_localized_strings(self, email_type: str, lang: str) -> dict:
        """Loads localized strings from a JSON file for a given email type and language."""
        strings = {}
        try:
            lang_file_path_relative = os.path.join(email_type, f"{lang}.json")
            # Use <PERSON><PERSON>'s loader to get source, respects search path and handles TemplateNotFound
            json_content = self.jinja_env.loader.get_source(self.jinja_env, lang_file_path_relative)[0]
            strings = json.loads(json_content)
        except TemplateNotFound:
            logger.warning(f"""Language file not found:
             {lang_file_path_relative}. Trying default language '{self.default_lang}'.""")
            if lang != self.default_lang:
                return self._load_localized_strings(email_type, self.default_lang)
            else:
                logger.error(f"""Default language file not found for {email_type}
                /{self.default_lang}.json. No strings loaded.""")
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from {lang_file_path_relative}: {e}")
            # Optionally, try default language if current one failed to parse
            if lang != self.default_lang:
                logger.info(f"Attempting to load default language '{self.default_lang}' due to JSON error in '{lang}'.")
                return self._load_localized_strings(email_type, self.default_lang)
        except Exception as e:
            logger.error(f"Unexpected error loading language file {lang_file_path_relative}: {e}")
            if lang != self.default_lang:
                return self._load_localized_strings(email_type, self.default_lang)
        return strings

    def render(self, email_type: str, lang: str, dynamic_data: dict) -> str:
        """Renders an email template with localized strings and dynamic data."""
        localized_strings = self._load_localized_strings(email_type, lang)
        
        context = {}
        context.update(localized_strings)  # Localized strings first
        context.update(dynamic_data)     # Dynamic data overrides if keys conflict

        html_template_path_relative = os.path.join(email_type, "template.html")
        
        try:
            template = self.jinja_env.get_template(html_template_path_relative)
            html_content = template.render(context)
            return html_content
        except TemplateNotFound:
            logger.error(f"HTML template not found: {html_template_path_relative}")
            # Fallback or error handling for missing HTML template
            # For now, return an error message or raise an exception
            raise  # Re-raise the exception to be handled by the caller
        except Exception as e:
            logger.error(f"Error rendering template {html_template_path_relative}: {e}")
            raise  # Re-raise for caller to handle

    def get_subject(self, email_type: str, lang: str) -> str:
        """Gets the email subject from the language JSON file."""
        localized_strings = self._load_localized_strings(email_type, lang)
        subject = localized_strings.get("email_subject", "Default Subject")  # Default if key missing
        
        if subject == "Default Subject" and not localized_strings:  # If strings failed to load entirely
            logger.warning(f"""Using generic default subject because 
            no strings were loaded for {email_type} in {lang} (or fallback).""")

        return subject