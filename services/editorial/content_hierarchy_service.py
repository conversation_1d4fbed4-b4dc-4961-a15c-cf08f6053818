from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Dict, Any
from db.models import Subject, Chapter, LearningNode
from core.exception_handling.exceptions.custom_exceptions import NotFoundError
from loguru import logger


class ContentHierarchyService:
    """Service for fetching content hierarchy data for scope assignment"""
    
    @staticmethod
    async def get_all_subjects(db: Session) -> List[Dict[str, Any]]:
        """Get all subjects with chapter and learning node counts."""
        # Query subjects with counts using subqueries
        subjects_data = db.query(
            Subject.id,
            Subject.public_id,
            Subject.name,
            Subject.description,
            func.count(func.distinct(Chapter.id)).label('chapter_count'),
            func.count(LearningNode.id).label('node_count')
        ).outerjoin(
            Chapter, Subject.id == Chapter.subject_id
        ).outerjoin(
            LearningNode, Chapter.id == LearningNode.chapter_id
        ).filter(
            Subject.is_active == True
        ).group_by(
            Subject.id
        ).order_by(
            Subject.name  # Order by name since there's no order field
        ).all()
        
        subjects = []
        for idx, row in enumerate(subjects_data):
            subjects.append({
                "id": row.public_id,
                "name": row.name,
                "public_id": row.public_id,
                "order": idx + 1,  # Generate order based on position
                "total_chapters": row.chapter_count,
                "total_learning_nodes": row.node_count
            })
        
        logger.info(f"Fetched {len(subjects)} subjects for scope assignment")
        return subjects
    
    @staticmethod
    async def get_subject_chapters(db: Session, subject_public_id: str) -> Dict[str, Any]:
        """Get all chapters for a specific subject."""
        # Get subject
        subject = db.query(Subject).filter(
            Subject.public_id == subject_public_id
        ).first()
        
        if not subject:
            raise NotFoundError(f"Subject with ID {subject_public_id} not found")
        
        # Get chapters with learning node counts
        chapters_data = db.query(
            Chapter.id,
            Chapter.public_id,
            Chapter.title,
            Chapter.ordering,
            func.count(LearningNode.id).label('node_count')
        ).outerjoin(
            LearningNode, Chapter.id == LearningNode.chapter_id
        ).filter(
            Chapter.subject_id == subject.id
        ).group_by(
            Chapter.id
        ).order_by(
            Chapter.ordering
        ).all()
        
        chapters = []
        for row in chapters_data:
            chapters.append({
                "id": row.public_id,
                "title": row.title,
                "public_id": row.public_id,
                "order": row.ordering or 0,
                "total_learning_nodes": row.node_count
            })
        
        logger.info(f"Fetched {len(chapters)} chapters for subject {subject.name}")
        
        return {
            "subject": {
                "id": subject.public_id,
                "name": subject.name
            },
            "chapters": chapters
        }
    
    @staticmethod
    async def get_chapter_learning_nodes(db: Session, chapter_public_id: str) -> Dict[str, Any]:
        """Get all learning nodes for a specific chapter."""
        # Get chapter with subject info
        chapter = db.query(Chapter).filter(
            Chapter.public_id == chapter_public_id
        ).first()
        
        if not chapter:
            raise NotFoundError(f"Chapter with ID {chapter_public_id} not found")
        
        # Get subject info
        subject = db.query(Subject).filter(
            Subject.id == chapter.subject_id
        ).first()
        
        # Get learning nodes
        nodes = db.query(LearningNode).filter(
            LearningNode.chapter_id == chapter.id
        ).order_by(
            LearningNode.ordering
        ).all()
        
        learning_nodes = []
        for node in nodes:
            learning_nodes.append({
                "id": node.public_id,
                "title": node.title,
                "public_id": node.public_id,
                "order": node.ordering
            })
        
        logger.info(f"Fetched {len(learning_nodes)} learning nodes for chapter {chapter.title}")
        
        return {
            "chapter": {
                "id": chapter.public_id,
                "title": chapter.title,
                "subject_name": subject.name if subject else "Unknown"
            },
            "learning_nodes": learning_nodes
        }