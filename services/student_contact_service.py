from datetime import datetime, timezone
from fastapi import Depends
from loguru import logger

from api.v1.other.contact.schemas.request import StudentContactFormRequest
from core.config.settings import settings
from core.exception_handling.exceptions.custom_exceptions import ServiceError
from api.v1.common.schemas import AppErrorCode 
from services.mailer.mailer_service import EmailService, EmailContext
from dependencies.email_dependency import get_email_service

class StudentContactService:
    def __init__(self, email_service: EmailService = Depends(get_email_service)):
        self.email_service = email_service

    def process_student_contact_form( # Removed async
        self,
        contact_form: StudentContactFormRequest,
        # account_public_id: str # account_public_id is not used
    ) -> None:
        try:
            submitted_at_dt = datetime.now(timezone.utc)
            formatted_submitted_at = submitted_at_dt.strftime("%B %d, %Y, %H:%M %Z")

            dynamic_data = {
                "user_name": contact_form.name,
                "user_email": contact_form.email,
                "user_message": contact_form.message,
                "submitted_at": formatted_submitted_at,
                "admin_email": settings.ADMIN_EMAIL # Added for the template
            }

            email_context = EmailContext(
                to=settings.ADMIN_EMAIL,
                template_type="internal_student_contact_submission",
                lang="en", 
                dynamic_data=dynamic_data
            )

            success = self.email_service.send(email_context) # Removed await
            if not success:
                raise ServiceError(
                    message="Failed to send student contact email.",
                    log_message="Student contact email sending failed at service level after mailer call.",
                    error_code=AppErrorCode.EXTERNAL_SERVICE_FAILURE 
                )
            logger.info(f"Student contact form processed for {contact_form.email}")

        except ServiceError:
            raise
        except Exception as e:
            logger.error(f"Error processing student contact form: {str(e)}")
            raise ServiceError(
                message="An unexpected error occurred while processing the student contact form.",
                log_message=f"Unexpected error in StudentContactService: {str(e)}",
                error_code=AppErrorCode.SERVICE_ERROR,
                original_exception=e
            )
