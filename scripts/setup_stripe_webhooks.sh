#!/bin/bash

# Setup Stripe CLI webhooks for local development

echo "🚀 Setting up Stripe webhooks for local development..."

# Check if Stripe CLI is installed
if ! command -v stripe &> /dev/null; then
    echo "❌ Stripe CLI not found. Installing..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        brew install stripe/stripe-cli/stripe
    else
        echo "Please install Stripe CLI from: https://stripe.com/docs/stripe-cli"
        exit 1
    fi
fi

# Check if logged in
if ! stripe config --list &> /dev/null; then
    echo "📝 Please login to Stripe..."
    stripe login
fi

echo "✅ Stripe CLI ready!"
echo ""
echo "📡 Starting webhook forwarding..."
echo "👉 Copy the webhook signing secret shown below to your .env file as STRIPE_WEBHOOK_SECRET"
echo ""

# Forward webhooks to local server
stripe listen --forward-to localhost:8000/api/v1/app/parent/subscription/webhook \
    --events checkout.session.completed,customer.subscription.created,customer.subscription.updated,customer.subscription.deleted,invoice.payment_succeeded,invoice.payment_failed

# To test specific events:
# stripe trigger checkout.session.completed
# stripe trigger customer.subscription.updated